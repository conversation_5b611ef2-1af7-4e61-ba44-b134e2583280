<linker>
	<assembly fullname="Assembly-CSharp">
		<type fullname="RCC_Camera/CameraTarget" preserve="nothing" serialized="true"/>
		<type fullname="RCC_CarControllerV3/Gear" preserve="nothing" serialized="true"/>
		<type fullname="RCC_ChangableWheels/ChangableWheels" preserve="nothing" serialized="true"/>
		<type fullname="RCC_Damage" preserve="nothing" serialized="true"/>
		<type fullname="RCC_GroundMaterials/GroundMaterialFrictions" preserve="nothing" serialized="true"/>
		<type fullname="RCC_GroundMaterials/TerrainFrictions" preserve="nothing" serialized="true"/>
		<type fullname="RCC_GroundMaterials/TerrainFrictions/SplatmapIndexes" preserve="nothing" serialized="true"/>
		<type fullname="RCC_Inputs" preserve="nothing" serialized="true"/>
		<type fullname="RCC_Settings/BehaviorType" preserve="nothing" serialized="true"/>
		<type fullname="RCC_TruckTrailer/TrailerWheel" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="DOTween">
		<type fullname="DG.Tweening.Core.DOTweenSettings/ModulesSetup" preserve="nothing" serialized="true"/>
		<type fullname="DG.Tweening.Core.DOTweenSettings/SafeModeOptions" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.InputSystem">
		<type fullname="UnityEngine.InputSystem.InputAction" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputActionMap" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputBinding" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputControlScheme" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputControlScheme/DeviceRequirement" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime">
		<type fullname="UnityEngine.Rendering.UI.DebugUIPrefabBundle" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.Timeline">
		<type fullname="UnityEngine.Timeline.MarkerList" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Timeline.TimelineAsset/EditorSettings" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.CoreModule">
		<type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.IMGUIModule">
		<type fullname="UnityEngine.GUISettings" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.GUIStyle" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventTrigger/Entry" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger/TriggerEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Dropdown/DropdownEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Dropdown/OptionData" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Dropdown/OptionDataList" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.InputField/EndEditEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.InputField/OnChangeEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.InputField/SubmitEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true"/>
	</assembly>
</linker>
