﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void DG.Tweening.Color2::.ctor(UnityEngine.Color,UnityEngine.Color)
extern void Color2__ctor_m0D4E285146629A09FA473F8FDE3D4A4F3889D58E (void);
// 0x00000002 DG.Tweening.Color2 DG.Tweening.Color2::op_Addition(DG.Tweening.Color2,DG.Tweening.Color2)
extern void Color2_op_Addition_m4BD6D878284D56DB00BF838BFF135155E70D6C1A (void);
// 0x00000003 DG.Tweening.Color2 DG.Tweening.Color2::op_Subtraction(DG.Tweening.Color2,DG.Tweening.Color2)
extern void Color2_op_Subtraction_m78CDC06AF474D662931568BFA73CD8C477BE2D99 (void);
// 0x00000004 DG.Tweening.Color2 DG.Tweening.Color2::op_Multiply(DG.Tweening.Color2,System.Single)
extern void Color2_op_Multiply_m1AE5DF5597AA4375991E023FA04AABAAB64870C0 (void);
// 0x00000005 System.Void DG.Tweening.TweenCallback::.ctor(System.Object,System.IntPtr)
extern void TweenCallback__ctor_m68CC9304423CBDE43001F9B1413B5DAAF70DB621 (void);
// 0x00000006 System.Void DG.Tweening.TweenCallback::Invoke()
extern void TweenCallback_Invoke_mE4105043678D7086C7740B0D7B7589B734C14E1F (void);
// 0x00000007 System.IAsyncResult DG.Tweening.TweenCallback::BeginInvoke(System.AsyncCallback,System.Object)
extern void TweenCallback_BeginInvoke_mC486E8964C6EF9E60782772AE59BC3B0A3217C30 (void);
// 0x00000008 System.Void DG.Tweening.TweenCallback::EndInvoke(System.IAsyncResult)
extern void TweenCallback_EndInvoke_m3C721FF86D9072053EA9BC5604B1B3D820329123 (void);
// 0x00000009 System.Void DG.Tweening.TweenCallback`1::.ctor(System.Object,System.IntPtr)
// 0x0000000A System.Void DG.Tweening.TweenCallback`1::Invoke(T)
// 0x0000000B System.IAsyncResult DG.Tweening.TweenCallback`1::BeginInvoke(T,System.AsyncCallback,System.Object)
// 0x0000000C System.Void DG.Tweening.TweenCallback`1::EndInvoke(System.IAsyncResult)
// 0x0000000D System.Void DG.Tweening.EaseFunction::.ctor(System.Object,System.IntPtr)
extern void EaseFunction__ctor_mD630BE102357BB21BD878DF5E98F90BFE785A0F8 (void);
// 0x0000000E System.Single DG.Tweening.EaseFunction::Invoke(System.Single,System.Single,System.Single,System.Single)
extern void EaseFunction_Invoke_mC30ABF785F84A8769541950EDC3C2CB0B8F6FB8D (void);
// 0x0000000F System.IAsyncResult DG.Tweening.EaseFunction::BeginInvoke(System.Single,System.Single,System.Single,System.Single,System.AsyncCallback,System.Object)
extern void EaseFunction_BeginInvoke_mFAB698BD64F636C31FCE27A231156FC2A9499DBC (void);
// 0x00000010 System.Single DG.Tweening.EaseFunction::EndInvoke(System.IAsyncResult)
extern void EaseFunction_EndInvoke_m7F85E0D3DAFBC71192CA9340D5B29C2FA4F32165 (void);
// 0x00000011 UnityEngine.Vector3 DG.Tweening.DOCurve/CubicBezier::GetPointOnSegment(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void CubicBezier_GetPointOnSegment_m696FD077A89C79276C401503AFF1DFB199CDBCEA (void);
// 0x00000012 UnityEngine.Vector3[] DG.Tweening.DOCurve/CubicBezier::GetSegmentPointCloud(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,System.Int32)
extern void CubicBezier_GetSegmentPointCloud_m7BD76991275FE643853C194A3E3CCC3D1D32AB14 (void);
// 0x00000013 System.Void DG.Tweening.DOCurve/CubicBezier::GetSegmentPointCloud(System.Collections.Generic.List`1<UnityEngine.Vector3>,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,System.Int32)
extern void CubicBezier_GetSegmentPointCloud_mB42A75DDB4383485731CFF005E10D1F9A51C1D9E (void);
// 0x00000014 DG.Tweening.LogBehaviour DG.Tweening.DOTween::get_logBehaviour()
extern void DOTween_get_logBehaviour_m50FAF61152D634B61FFA7D3B04F7C2A10E6E9B97 (void);
// 0x00000015 System.Void DG.Tweening.DOTween::set_logBehaviour(DG.Tweening.LogBehaviour)
extern void DOTween_set_logBehaviour_mD910C3B966CBE72E0ED43B7EA96BA06331090D6A (void);
// 0x00000016 System.Boolean DG.Tweening.DOTween::get_debugStoreTargetId()
extern void DOTween_get_debugStoreTargetId_m96F1367AD4955D84B2284A01EDD146DCE64B3A75 (void);
// 0x00000017 System.Void DG.Tweening.DOTween::set_debugStoreTargetId(System.Boolean)
extern void DOTween_set_debugStoreTargetId_m9A024F9090E856AC801B9DD5AECE4B8CAFBD96A5 (void);
// 0x00000018 System.Boolean DG.Tweening.DOTween::get_isQuitting()
extern void DOTween_get_isQuitting_m5511C54A110FA836D9B16B76ED0A390F1C5A7990 (void);
// 0x00000019 System.Void DG.Tweening.DOTween::set_isQuitting(System.Boolean)
extern void DOTween_set_isQuitting_m1F583636E361122DB50E89CC8CBDFA081DE1E28A (void);
// 0x0000001A DG.Tweening.IDOTweenInit DG.Tweening.DOTween::Init(System.Nullable`1<System.Boolean>,System.Nullable`1<System.Boolean>,System.Nullable`1<DG.Tweening.LogBehaviour>)
extern void DOTween_Init_mAD6E37B9B311DFFBCCACAB726DA36D40A7DB8C23 (void);
// 0x0000001B System.Void DG.Tweening.DOTween::AutoInit()
extern void DOTween_AutoInit_mF7B0D31019E4A0D0212902AC43E359A1BD763C29 (void);
// 0x0000001C DG.Tweening.IDOTweenInit DG.Tweening.DOTween::Init(DG.Tweening.Core.DOTweenSettings,System.Nullable`1<System.Boolean>,System.Nullable`1<System.Boolean>,System.Nullable`1<DG.Tweening.LogBehaviour>)
extern void DOTween_Init_m31648CA12FD2195F125B2B4773B7BF8DAFA11080 (void);
// 0x0000001D System.Void DG.Tweening.DOTween::SetTweensCapacity(System.Int32,System.Int32)
extern void DOTween_SetTweensCapacity_m0A87694AEF8A3F716EC1340496DCDE1A99E838EA (void);
// 0x0000001E System.Void DG.Tweening.DOTween::Clear(System.Boolean)
extern void DOTween_Clear_m6CFE7E673765E730176BF919B55B1D7C1A923075 (void);
// 0x0000001F System.Void DG.Tweening.DOTween::Clear(System.Boolean,System.Boolean)
extern void DOTween_Clear_m6585EFB74CE7D3B89776A06190F810BFFC31A32A (void);
// 0x00000020 System.Void DG.Tweening.DOTween::ClearCachedTweens()
extern void DOTween_ClearCachedTweens_mDECBBF6297745D2D6B6EE820A9FC13A99B7714B1 (void);
// 0x00000021 System.Int32 DG.Tweening.DOTween::Validate()
extern void DOTween_Validate_mCB9E29EEDB6CD44AB6D8CD527C7BA028B421C321 (void);
// 0x00000022 System.Void DG.Tweening.DOTween::ManualUpdate(System.Single,System.Single)
extern void DOTween_ManualUpdate_mF5092C3F5871F46377151A1117F4D341E9DCC00C (void);
// 0x00000023 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<System.Single>,DG.Tweening.Core.DOSetter`1<System.Single>,System.Single,System.Single)
extern void DOTween_To_m9C9EBC0FB6CF94364DD4FF85C476D8EE0A7FF4B1 (void);
// 0x00000024 DG.Tweening.Core.TweenerCore`3<System.Double,System.Double,DG.Tweening.Plugins.Options.NoOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<System.Double>,DG.Tweening.Core.DOSetter`1<System.Double>,System.Double,System.Single)
extern void DOTween_To_mEE7384557D7EB80C79A8D60638B4EC5315518AAA (void);
// 0x00000025 DG.Tweening.Core.TweenerCore`3<System.Int32,System.Int32,DG.Tweening.Plugins.Options.NoOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<System.Int32>,DG.Tweening.Core.DOSetter`1<System.Int32>,System.Int32,System.Single)
extern void DOTween_To_mE00A5CA8947AD59966A2CFE3810F1FEEC83E157B (void);
// 0x00000026 DG.Tweening.Core.TweenerCore`3<System.UInt32,System.UInt32,DG.Tweening.Plugins.Options.UintOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<System.UInt32>,DG.Tweening.Core.DOSetter`1<System.UInt32>,System.UInt32,System.Single)
extern void DOTween_To_mA2EB91A5D1B226DADC20E24A11AE2BA91087AEE0 (void);
// 0x00000027 DG.Tweening.Core.TweenerCore`3<System.Int64,System.Int64,DG.Tweening.Plugins.Options.NoOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<System.Int64>,DG.Tweening.Core.DOSetter`1<System.Int64>,System.Int64,System.Single)
extern void DOTween_To_m0A0AA071215B37E2ACF8070183723654980F8AE3 (void);
// 0x00000028 DG.Tweening.Core.TweenerCore`3<System.UInt64,System.UInt64,DG.Tweening.Plugins.Options.NoOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<System.UInt64>,DG.Tweening.Core.DOSetter`1<System.UInt64>,System.UInt64,System.Single)
extern void DOTween_To_mCF3CE02B6A8876B87B3487AF8C7A538FB67DD5A0 (void);
// 0x00000029 DG.Tweening.Core.TweenerCore`3<System.String,System.String,DG.Tweening.Plugins.Options.StringOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<System.String>,DG.Tweening.Core.DOSetter`1<System.String>,System.String,System.Single)
extern void DOTween_To_mA4E61D06204BD01537C08EEB9ED148C18ABC75ED (void);
// 0x0000002A DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<UnityEngine.Vector2>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector2>,UnityEngine.Vector2,System.Single)
extern void DOTween_To_mAF71D4F17109689870406A4565636216CCFE7680 (void);
// 0x0000002B DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,UnityEngine.Vector3,System.Single)
extern void DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8 (void);
// 0x0000002C DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<UnityEngine.Vector4>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector4>,UnityEngine.Vector4,System.Single)
extern void DOTween_To_mF9194C82EBAF83FF928C8F54ED13441A4AB39141 (void);
// 0x0000002D DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<UnityEngine.Quaternion>,DG.Tweening.Core.DOSetter`1<UnityEngine.Quaternion>,UnityEngine.Vector3,System.Single)
extern void DOTween_To_m7A731ADF3CCD5C4439F8710B2CD16BC6CEB051D0 (void);
// 0x0000002E DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<UnityEngine.Color>,DG.Tweening.Core.DOSetter`1<UnityEngine.Color>,UnityEngine.Color,System.Single)
extern void DOTween_To_mA1EBEAF49DD682B626CB60B4C53B7069F7933339 (void);
// 0x0000002F DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<UnityEngine.Rect>,DG.Tweening.Core.DOSetter`1<UnityEngine.Rect>,UnityEngine.Rect,System.Single)
extern void DOTween_To_mA77855459ADB369B17DB84A390E88A1CC27868F4 (void);
// 0x00000030 DG.Tweening.Tweener DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<UnityEngine.RectOffset>,DG.Tweening.Core.DOSetter`1<UnityEngine.RectOffset>,UnityEngine.RectOffset,System.Single)
extern void DOTween_To_mA55CD0AEE530AAB079E27FEEBB2623DD813477B6 (void);
// 0x00000031 DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.DOTween::To(DG.Tweening.Plugins.Core.ABSTweenPlugin`3<T1,T2,TPlugOptions>,DG.Tweening.Core.DOGetter`1<T1>,DG.Tweening.Core.DOSetter`1<T1>,T2,System.Single)
// 0x00000032 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.DOTween::ToAxis(DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,System.Single,System.Single,DG.Tweening.AxisConstraint)
extern void DOTween_ToAxis_mB32AF2179F60E4D7B59B182A82C376801256C01D (void);
// 0x00000033 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.DOTween::ToAlpha(DG.Tweening.Core.DOGetter`1<UnityEngine.Color>,DG.Tweening.Core.DOSetter`1<UnityEngine.Color>,System.Single,System.Single)
extern void DOTween_ToAlpha_m93AB0EDCF2956A0DD806D9537799F2727CA5E7C1 (void);
// 0x00000034 DG.Tweening.Tweener DG.Tweening.DOTween::To(DG.Tweening.Core.DOSetter`1<System.Single>,System.Single,System.Single,System.Single)
extern void DOTween_To_mD96259B51203D6105DAE33CE4E4CDF89023A5BB5 (void);
// 0x00000035 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> DG.Tweening.DOTween::Punch(DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,UnityEngine.Vector3,System.Single,System.Int32,System.Single)
extern void DOTween_Punch_mD470A46B3BEC0E312D0438C14C0980E853CA4D32 (void);
// 0x00000036 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> DG.Tweening.DOTween::Shake(DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,System.Single,System.Single,System.Int32,System.Single,System.Boolean,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void DOTween_Shake_m75BBA8491D91B639689EFFD42491E61556D9FF0B (void);
// 0x00000037 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> DG.Tweening.DOTween::Shake(DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,System.Single,UnityEngine.Vector3,System.Int32,System.Single,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void DOTween_Shake_m6C1929582C5F952ED98444CD22A37F54DD0A4345 (void);
// 0x00000038 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> DG.Tweening.DOTween::Shake(DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,System.Single,UnityEngine.Vector3,System.Int32,System.Single,System.Boolean,System.Boolean,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void DOTween_Shake_m5FD4EEB34398EB5F3C65443313CEC66788EFDFF2 (void);
// 0x00000039 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions> DG.Tweening.DOTween::ToArray(DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,UnityEngine.Vector3[],System.Single[])
extern void DOTween_ToArray_mEC8B5DBDFCBC6DE60994B6E6DD0CCDDA43F001AB (void);
// 0x0000003A DG.Tweening.Core.TweenerCore`3<DG.Tweening.Color2,DG.Tweening.Color2,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.DOTween::To(DG.Tweening.Core.DOGetter`1<DG.Tweening.Color2>,DG.Tweening.Core.DOSetter`1<DG.Tweening.Color2>,DG.Tweening.Color2,System.Single)
extern void DOTween_To_mE583287C4050FF3368FD9B043A7D60D78296CBBE (void);
// 0x0000003B DG.Tweening.Sequence DG.Tweening.DOTween::Sequence()
extern void DOTween_Sequence_mC3AE53A08EC5DFD7DB9AE18A2FD11226A6611F89 (void);
// 0x0000003C DG.Tweening.Sequence DG.Tweening.DOTween::Sequence(System.Object)
extern void DOTween_Sequence_mE0F7F846966AA538367AF365C620B0464A6D48E4 (void);
// 0x0000003D System.Int32 DG.Tweening.DOTween::CompleteAll(System.Boolean)
extern void DOTween_CompleteAll_mC914401379CE84151E8C91A5DE6C4038154D0E7E (void);
// 0x0000003E System.Int32 DG.Tweening.DOTween::Complete(System.Object,System.Boolean)
extern void DOTween_Complete_m12E2987F7D42218DCCE051E2FDB803E75FD8BA91 (void);
// 0x0000003F System.Int32 DG.Tweening.DOTween::CompleteAndReturnKilledTot()
extern void DOTween_CompleteAndReturnKilledTot_m9A22E74F449F88E7333C1117511D5F3912D30536 (void);
// 0x00000040 System.Int32 DG.Tweening.DOTween::CompleteAndReturnKilledTot(System.Object)
extern void DOTween_CompleteAndReturnKilledTot_m77F801701C03DB55B442098F543A09F2C5600452 (void);
// 0x00000041 System.Int32 DG.Tweening.DOTween::CompleteAndReturnKilledTot(System.Object,System.Object)
extern void DOTween_CompleteAndReturnKilledTot_mA7D31092FECF318CB6209CF9E855460D15DD81C9 (void);
// 0x00000042 System.Int32 DG.Tweening.DOTween::CompleteAndReturnKilledTotExceptFor(System.Object[])
extern void DOTween_CompleteAndReturnKilledTotExceptFor_m8AFEA53C85E3BAC276F66975737E9D3990495CCC (void);
// 0x00000043 System.Int32 DG.Tweening.DOTween::FlipAll()
extern void DOTween_FlipAll_m51A32BD6788A2EF321AAC2EB49D60BFBF6EFAF49 (void);
// 0x00000044 System.Int32 DG.Tweening.DOTween::Flip(System.Object)
extern void DOTween_Flip_mCE1C7CBA31A981D035B52150957C3A01B7F2A978 (void);
// 0x00000045 System.Int32 DG.Tweening.DOTween::GotoAll(System.Single,System.Boolean)
extern void DOTween_GotoAll_m49A8DD69B67F5E62428E616559183F545F84B3D2 (void);
// 0x00000046 System.Int32 DG.Tweening.DOTween::Goto(System.Object,System.Single,System.Boolean)
extern void DOTween_Goto_m19ABF14E2B1ACAFFA17E8AB48AEE2207B6FFA6C9 (void);
// 0x00000047 System.Int32 DG.Tweening.DOTween::KillAll(System.Boolean)
extern void DOTween_KillAll_mA9D826F93A89E061EABF30308378391E7163AEB0 (void);
// 0x00000048 System.Int32 DG.Tweening.DOTween::KillAll(System.Boolean,System.Object[])
extern void DOTween_KillAll_m83C060D22440D6C7A8CB7AD018F038E363B27C2A (void);
// 0x00000049 System.Int32 DG.Tweening.DOTween::Kill(System.Object,System.Boolean)
extern void DOTween_Kill_mAB4C96CE1F1BCF25E5347AE0FC295D064EA53FB2 (void);
// 0x0000004A System.Int32 DG.Tweening.DOTween::Kill(System.Object,System.Object,System.Boolean)
extern void DOTween_Kill_m53573B76803A245F23AFAC2AB22995FDA4A9235A (void);
// 0x0000004B System.Int32 DG.Tweening.DOTween::PauseAll()
extern void DOTween_PauseAll_mBEAEB6365AEA3EB8755BE21D8A45D0ABFC8F1694 (void);
// 0x0000004C System.Int32 DG.Tweening.DOTween::Pause(System.Object)
extern void DOTween_Pause_m498BECFBBC8FBD76425B8AE1F38E2ECC9AE296D4 (void);
// 0x0000004D System.Int32 DG.Tweening.DOTween::PlayAll()
extern void DOTween_PlayAll_m5510B90EBBCA172D2D1EC8FFE1CCF44E34B9E9C0 (void);
// 0x0000004E System.Int32 DG.Tweening.DOTween::Play(System.Object)
extern void DOTween_Play_m466F46F9DF6585E17C595438BA15147319540DC4 (void);
// 0x0000004F System.Int32 DG.Tweening.DOTween::Play(System.Object,System.Object)
extern void DOTween_Play_mEFD3A1E3CC218D3916032325F2E119C2004D9473 (void);
// 0x00000050 System.Int32 DG.Tweening.DOTween::PlayBackwardsAll()
extern void DOTween_PlayBackwardsAll_mF6926DA985F2523FFC77DC111A657CE1C6FCC7C4 (void);
// 0x00000051 System.Int32 DG.Tweening.DOTween::PlayBackwards(System.Object)
extern void DOTween_PlayBackwards_m3709E26071CCE5CB368584477DF2FC9A8B9D052B (void);
// 0x00000052 System.Int32 DG.Tweening.DOTween::PlayBackwards(System.Object,System.Object)
extern void DOTween_PlayBackwards_mC4FB110A49C220C2B5FE768989D133F0F8721FA7 (void);
// 0x00000053 System.Int32 DG.Tweening.DOTween::PlayForwardAll()
extern void DOTween_PlayForwardAll_mCF7A53806203F40BF65D18C029617319111C88E1 (void);
// 0x00000054 System.Int32 DG.Tweening.DOTween::PlayForward(System.Object)
extern void DOTween_PlayForward_m4F27092024989DEB74466D9D9C370D5FA6621DCF (void);
// 0x00000055 System.Int32 DG.Tweening.DOTween::PlayForward(System.Object,System.Object)
extern void DOTween_PlayForward_mC1FF85BCF9DC773A3E4FA3DC12377D1F9E1BADD4 (void);
// 0x00000056 System.Int32 DG.Tweening.DOTween::RestartAll(System.Boolean)
extern void DOTween_RestartAll_m68B018164D3C980A8B88D4501AB353C9A3BE621E (void);
// 0x00000057 System.Int32 DG.Tweening.DOTween::Restart(System.Object,System.Boolean,System.Single)
extern void DOTween_Restart_mEE2F85FC8741BBCE7C1E76C143B3DCE5B9C78DEF (void);
// 0x00000058 System.Int32 DG.Tweening.DOTween::Restart(System.Object,System.Object,System.Boolean,System.Single)
extern void DOTween_Restart_m89E03C91814831E13EA36751C4A8471CF41FA249 (void);
// 0x00000059 System.Int32 DG.Tweening.DOTween::RewindAll(System.Boolean)
extern void DOTween_RewindAll_m7F9B893866CDBDB4C9D416E6DEA1DC21622101E3 (void);
// 0x0000005A System.Int32 DG.Tweening.DOTween::Rewind(System.Object,System.Boolean)
extern void DOTween_Rewind_m5C4020E9007FAAF719C1BE01CE440EBB39193619 (void);
// 0x0000005B System.Int32 DG.Tweening.DOTween::SmoothRewindAll()
extern void DOTween_SmoothRewindAll_mD4DA180BE6BDD18A40F14422E2CC19A56BFC0192 (void);
// 0x0000005C System.Int32 DG.Tweening.DOTween::SmoothRewind(System.Object)
extern void DOTween_SmoothRewind_m36B53DA282A0BC7948453A6725939D60A33D5C22 (void);
// 0x0000005D System.Int32 DG.Tweening.DOTween::TogglePauseAll()
extern void DOTween_TogglePauseAll_m107930535CBAFB21956D555DCE987CFC19969455 (void);
// 0x0000005E System.Int32 DG.Tweening.DOTween::TogglePause(System.Object)
extern void DOTween_TogglePause_m8CE7CA00FE30F3C926F34362E2DE67A161536598 (void);
// 0x0000005F System.Boolean DG.Tweening.DOTween::IsTweening(System.Object,System.Boolean)
extern void DOTween_IsTweening_mD96EA6DEB69E80EE2948D2C561009E8DA9F9B820 (void);
// 0x00000060 System.Int32 DG.Tweening.DOTween::TotalActiveTweens()
extern void DOTween_TotalActiveTweens_mEFE6677CD80989E356252940A3CE46B710C6CC41 (void);
// 0x00000061 System.Int32 DG.Tweening.DOTween::TotalActiveTweeners()
extern void DOTween_TotalActiveTweeners_m14F743B11BECF70A64B57CE0631C5EA1021EA5CE (void);
// 0x00000062 System.Int32 DG.Tweening.DOTween::TotalActiveSequences()
extern void DOTween_TotalActiveSequences_mA5F412A7630E24EAAB30B3B275A97CD96F669B1D (void);
// 0x00000063 System.Int32 DG.Tweening.DOTween::TotalPlayingTweens()
extern void DOTween_TotalPlayingTweens_m2F68C3282E316EBF83F3D3AE5AE59DFE1040E23F (void);
// 0x00000064 System.Int32 DG.Tweening.DOTween::TotalTweensById(System.Object,System.Boolean)
extern void DOTween_TotalTweensById_m2DCD5FD3DF61F905001D1BC4B15D3C5ABF9A9511 (void);
// 0x00000065 System.Collections.Generic.List`1<DG.Tweening.Tween> DG.Tweening.DOTween::PlayingTweens(System.Collections.Generic.List`1<DG.Tweening.Tween>)
extern void DOTween_PlayingTweens_mA44E34F2DED68B5EA5E8861A150FCC4085DACE90 (void);
// 0x00000066 System.Collections.Generic.List`1<DG.Tweening.Tween> DG.Tweening.DOTween::PausedTweens(System.Collections.Generic.List`1<DG.Tweening.Tween>)
extern void DOTween_PausedTweens_mBF94B78D500712E0478E2AF3F66FC25462FA96D5 (void);
// 0x00000067 System.Collections.Generic.List`1<DG.Tweening.Tween> DG.Tweening.DOTween::TweensById(System.Object,System.Boolean,System.Collections.Generic.List`1<DG.Tweening.Tween>)
extern void DOTween_TweensById_m80980F3CDF268395EA51EEE8B012BB49A45E2988 (void);
// 0x00000068 System.Collections.Generic.List`1<DG.Tweening.Tween> DG.Tweening.DOTween::TweensByTarget(System.Object,System.Boolean,System.Collections.Generic.List`1<DG.Tweening.Tween>)
extern void DOTween_TweensByTarget_m1F6174B35FA2489CBDFAFF39A71187EC02908A72 (void);
// 0x00000069 System.Void DG.Tweening.DOTween::InitCheck()
extern void DOTween_InitCheck_mA3F71F5F48DEF60104F960552849E293CDDEBA7E (void);
// 0x0000006A DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.DOTween::ApplyTo(DG.Tweening.Core.DOGetter`1<T1>,DG.Tweening.Core.DOSetter`1<T1>,T2,System.Single,DG.Tweening.Plugins.Core.ABSTweenPlugin`3<T1,T2,TPlugOptions>)
// 0x0000006B System.Void DG.Tweening.DOTween::.ctor()
extern void DOTween__ctor_m21C0AF17F063BE0B93E6E82D2C9F1AC9573CC719 (void);
// 0x0000006C System.Void DG.Tweening.DOTween::.cctor()
extern void DOTween__cctor_m278575843F5C4300324E26C3AC8D91B665F2F155 (void);
// 0x0000006D System.Void DG.Tweening.DOTween/<>c__DisplayClass67_0::.ctor()
extern void U3CU3Ec__DisplayClass67_0__ctor_m7DA2265F9A2C0614A5BCCE53DAF356F6E21B5717 (void);
// 0x0000006E System.Single DG.Tweening.DOTween/<>c__DisplayClass67_0::<To>b__0()
extern void U3CU3Ec__DisplayClass67_0_U3CToU3Eb__0_m40D6775B16A95A4AC26C3B159301101DE5B4FC43 (void);
// 0x0000006F System.Void DG.Tweening.DOTween/<>c__DisplayClass67_0::<To>b__1(System.Single)
extern void U3CU3Ec__DisplayClass67_0_U3CToU3Eb__1_m96DBB5AD703E30C584801821DDF8C08C64138FD7 (void);
// 0x00000070 DG.Tweening.Tweener DG.Tweening.DOVirtual::Float(System.Single,System.Single,System.Single,DG.Tweening.TweenCallback`1<System.Single>)
extern void DOVirtual_Float_mA492E430AE72B6C1D8C71077C9BE7F82E98ED185 (void);
// 0x00000071 DG.Tweening.Tweener DG.Tweening.DOVirtual::Int(System.Int32,System.Int32,System.Single,DG.Tweening.TweenCallback`1<System.Int32>)
extern void DOVirtual_Int_m5835CD7A7EC7E4954BA30641CEA4B31FE4437EDD (void);
// 0x00000072 DG.Tweening.Tweener DG.Tweening.DOVirtual::Vector2(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,DG.Tweening.TweenCallback`1<UnityEngine.Vector2>)
extern void DOVirtual_Vector2_m96EE942293ECC9D9013DD8F7D94D427B0C2388CB (void);
// 0x00000073 DG.Tweening.Tweener DG.Tweening.DOVirtual::Vector3(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,DG.Tweening.TweenCallback`1<UnityEngine.Vector3>)
extern void DOVirtual_Vector3_mD1BD751546A5A4CB1AA8AF3744997FD7A9DCBEC4 (void);
// 0x00000074 DG.Tweening.Tweener DG.Tweening.DOVirtual::Color(UnityEngine.Color,UnityEngine.Color,System.Single,DG.Tweening.TweenCallback`1<UnityEngine.Color>)
extern void DOVirtual_Color_m1C5D178D63A7510AD9D451262A0F07FD4B42EDEB (void);
// 0x00000075 System.Single DG.Tweening.DOVirtual::EasedValue(System.Single,System.Single,System.Single,DG.Tweening.Ease)
extern void DOVirtual_EasedValue_m40F83FCD3705E8DD33558C84B3F7067D91F3B9DF (void);
// 0x00000076 System.Single DG.Tweening.DOVirtual::EasedValue(System.Single,System.Single,System.Single,DG.Tweening.Ease,System.Single)
extern void DOVirtual_EasedValue_m7A69BDAB0B236BBBF70E29013D1AB3E38D968CBC (void);
// 0x00000077 System.Single DG.Tweening.DOVirtual::EasedValue(System.Single,System.Single,System.Single,DG.Tweening.Ease,System.Single,System.Single)
extern void DOVirtual_EasedValue_mC076ABFEB3AE48B9AC2F2F5894F6283DC20CA05B (void);
// 0x00000078 System.Single DG.Tweening.DOVirtual::EasedValue(System.Single,System.Single,System.Single,UnityEngine.AnimationCurve)
extern void DOVirtual_EasedValue_m321C2569673A65C8404E336A38BE551761233A56 (void);
// 0x00000079 UnityEngine.Vector3 DG.Tweening.DOVirtual::EasedValue(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,DG.Tweening.Ease)
extern void DOVirtual_EasedValue_mB81B2D5233C9CE32914D5B4D7438CD23007DD030 (void);
// 0x0000007A UnityEngine.Vector3 DG.Tweening.DOVirtual::EasedValue(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,DG.Tweening.Ease,System.Single)
extern void DOVirtual_EasedValue_m76425A9DFA6F1E09795F814DB96DA9653B4A5B95 (void);
// 0x0000007B UnityEngine.Vector3 DG.Tweening.DOVirtual::EasedValue(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,DG.Tweening.Ease,System.Single,System.Single)
extern void DOVirtual_EasedValue_m244D9AD0AF382417C3C881AD80DD58944218D9BC (void);
// 0x0000007C UnityEngine.Vector3 DG.Tweening.DOVirtual::EasedValue(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.AnimationCurve)
extern void DOVirtual_EasedValue_mB2DF1EE529D5619FBF0FBD266150866D8490F39B (void);
// 0x0000007D DG.Tweening.Tween DG.Tweening.DOVirtual::DelayedCall(System.Single,DG.Tweening.TweenCallback,System.Boolean)
extern void DOVirtual_DelayedCall_m14018BD265A6BB37019E19FB5076D693C1B60112 (void);
// 0x0000007E System.Void DG.Tweening.DOVirtual/<>c__DisplayClass0_0::.ctor()
extern void U3CU3Ec__DisplayClass0_0__ctor_m49CA857D87CCE7F4D5FC0D7367EAED41314E8119 (void);
// 0x0000007F System.Single DG.Tweening.DOVirtual/<>c__DisplayClass0_0::<Float>b__0()
extern void U3CU3Ec__DisplayClass0_0_U3CFloatU3Eb__0_mEAED8273E9F7281F3F23328DB9C0608546C82739 (void);
// 0x00000080 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass0_0::<Float>b__1(System.Single)
extern void U3CU3Ec__DisplayClass0_0_U3CFloatU3Eb__1_mBFA0955B5DA8573DBA53BF87F177ABB1AC42BCB9 (void);
// 0x00000081 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass0_0::<Float>b__2()
extern void U3CU3Ec__DisplayClass0_0_U3CFloatU3Eb__2_m07C62CFF2717E629D136D622D3CB1399C82A38F2 (void);
// 0x00000082 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass1_0::.ctor()
extern void U3CU3Ec__DisplayClass1_0__ctor_m86626EAF02B0A51DA6AE0C66BC88A5A8B4131FA5 (void);
// 0x00000083 System.Int32 DG.Tweening.DOVirtual/<>c__DisplayClass1_0::<Int>b__0()
extern void U3CU3Ec__DisplayClass1_0_U3CIntU3Eb__0_mEF9AC3D0EA5F7B83064890D9A9AE99D022006262 (void);
// 0x00000084 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass1_0::<Int>b__1(System.Int32)
extern void U3CU3Ec__DisplayClass1_0_U3CIntU3Eb__1_m3FED03119653529A0BBAD4DD316EBC0C72FFA630 (void);
// 0x00000085 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass1_0::<Int>b__2()
extern void U3CU3Ec__DisplayClass1_0_U3CIntU3Eb__2_m78CD6CFF5518C7B5545D5AB204909B3B56F593B0 (void);
// 0x00000086 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass2_0::.ctor()
extern void U3CU3Ec__DisplayClass2_0__ctor_m6B443DD65DDFEA0AE484C48168FCB77DD0757FBB (void);
// 0x00000087 UnityEngine.Vector2 DG.Tweening.DOVirtual/<>c__DisplayClass2_0::<Vector2>b__0()
extern void U3CU3Ec__DisplayClass2_0_U3CVector2U3Eb__0_m0548111E0011A18D69A174AEE645E0A6FDA16C48 (void);
// 0x00000088 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass2_0::<Vector2>b__1(UnityEngine.Vector2)
extern void U3CU3Ec__DisplayClass2_0_U3CVector2U3Eb__1_mC5538B1F3805376CF610DC5E51085CAA2B784EA0 (void);
// 0x00000089 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass2_0::<Vector2>b__2()
extern void U3CU3Ec__DisplayClass2_0_U3CVector2U3Eb__2_m445DB7290517C829BA806FA806E41D63BD9A9F10 (void);
// 0x0000008A System.Void DG.Tweening.DOVirtual/<>c__DisplayClass3_0::.ctor()
extern void U3CU3Ec__DisplayClass3_0__ctor_mADD596F50E712B09718BBBC46C8473B263C7D345 (void);
// 0x0000008B UnityEngine.Vector3 DG.Tweening.DOVirtual/<>c__DisplayClass3_0::<Vector3>b__0()
extern void U3CU3Ec__DisplayClass3_0_U3CVector3U3Eb__0_mC03535DBBD1C002113422C01CDCEB101FB33FAD7 (void);
// 0x0000008C System.Void DG.Tweening.DOVirtual/<>c__DisplayClass3_0::<Vector3>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass3_0_U3CVector3U3Eb__1_m6331F9D5916248A66ECC0FF36DC1AC38A782A533 (void);
// 0x0000008D System.Void DG.Tweening.DOVirtual/<>c__DisplayClass3_0::<Vector3>b__2()
extern void U3CU3Ec__DisplayClass3_0_U3CVector3U3Eb__2_m844A8C367F62820E0F4D3CC5F83BA9D53F0A3C20 (void);
// 0x0000008E System.Void DG.Tweening.DOVirtual/<>c__DisplayClass4_0::.ctor()
extern void U3CU3Ec__DisplayClass4_0__ctor_mFBE34FE98D006ED51F6C71A4840E439F17FB7565 (void);
// 0x0000008F UnityEngine.Color DG.Tweening.DOVirtual/<>c__DisplayClass4_0::<Color>b__0()
extern void U3CU3Ec__DisplayClass4_0_U3CColorU3Eb__0_m5FBF415030A8478EF8DDABA735B599CE3413F28E (void);
// 0x00000090 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass4_0::<Color>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass4_0_U3CColorU3Eb__1_m1B5C8EF17DF1A096A1C6AD1B5B4BE6A190C0CE40 (void);
// 0x00000091 System.Void DG.Tweening.DOVirtual/<>c__DisplayClass4_0::<Color>b__2()
extern void U3CU3Ec__DisplayClass4_0_U3CColorU3Eb__2_m27CEC300AA827EF4CB0E62A4BD9F782A7485FD6A (void);
// 0x00000092 DG.Tweening.EaseFunction DG.Tweening.EaseFactory::StopMotion(System.Int32,System.Nullable`1<DG.Tweening.Ease>)
extern void EaseFactory_StopMotion_m4DC70923409B87F488623BD9BB3B3E509EAED212 (void);
// 0x00000093 DG.Tweening.EaseFunction DG.Tweening.EaseFactory::StopMotion(System.Int32,UnityEngine.AnimationCurve)
extern void EaseFactory_StopMotion_m6DD31A920F64A97DC82C3C75E0ABDA5E5F079F3F (void);
// 0x00000094 DG.Tweening.EaseFunction DG.Tweening.EaseFactory::StopMotion(System.Int32,DG.Tweening.EaseFunction)
extern void EaseFactory_StopMotion_m0CAF496284105D5C4AA42AF91BA176CCEC0F7C0B (void);
// 0x00000095 System.Void DG.Tweening.EaseFactory::.ctor()
extern void EaseFactory__ctor_mC5C91AF007C9B1B3B21F3B283F31B69731A283E0 (void);
// 0x00000096 System.Void DG.Tweening.EaseFactory/<>c__DisplayClass2_0::.ctor()
extern void U3CU3Ec__DisplayClass2_0__ctor_mB1FA14DE4DB023CCD4E2B70E49EAAB2604E223A8 (void);
// 0x00000097 System.Single DG.Tweening.EaseFactory/<>c__DisplayClass2_0::<StopMotion>b__0(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec__DisplayClass2_0_U3CStopMotionU3Eb__0_mB5A46C351CA279129DEA3054E00DF2DBC9C9EF58 (void);
// 0x00000098 DG.Tweening.IDOTweenInit DG.Tweening.IDOTweenInit::SetCapacity(System.Int32,System.Int32)
// 0x00000099 System.Void DG.Tweening.TweenExtensions::Complete(DG.Tweening.Tween)
extern void TweenExtensions_Complete_m27D13C838F7DCBFE14632132B60338F42B3ACB73 (void);
// 0x0000009A System.Void DG.Tweening.TweenExtensions::Complete(DG.Tweening.Tween,System.Boolean)
extern void TweenExtensions_Complete_m5B347FC17642C404A25E17D231836B2BE39E0632 (void);
// 0x0000009B T DG.Tweening.TweenExtensions::Done(T)
// 0x0000009C System.Void DG.Tweening.TweenExtensions::Flip(DG.Tweening.Tween)
extern void TweenExtensions_Flip_m7931C804A4B933A372DFAF3A1804A56ED959A443 (void);
// 0x0000009D System.Void DG.Tweening.TweenExtensions::ForceInit(DG.Tweening.Tween)
extern void TweenExtensions_ForceInit_mBE21B6CF98B217D09E814F598CCF6659D7660420 (void);
// 0x0000009E System.Void DG.Tweening.TweenExtensions::Goto(DG.Tweening.Tween,System.Single,System.Boolean)
extern void TweenExtensions_Goto_m0256CB1AA2FAB6786021BF3322D737AE4D37B2FC (void);
// 0x0000009F System.Void DG.Tweening.TweenExtensions::GotoWithCallbacks(DG.Tweening.Tween,System.Single,System.Boolean)
extern void TweenExtensions_GotoWithCallbacks_m8E3ED556DB6F69ED916D03C2D523B8DD2F21BDA4 (void);
// 0x000000A0 System.Void DG.Tweening.TweenExtensions::DoGoto(DG.Tweening.Tween,System.Single,System.Boolean,System.Boolean)
extern void TweenExtensions_DoGoto_mE805A8F7E5146DE10428CEFBBD34AEC5814EC002 (void);
// 0x000000A1 System.Void DG.Tweening.TweenExtensions::Kill(DG.Tweening.Tween,System.Boolean)
extern void TweenExtensions_Kill_mFFDE4DE45D8A91C357ABFF2F8FA95D412924F466 (void);
// 0x000000A2 System.Void DG.Tweening.TweenExtensions::ManualUpdate(DG.Tweening.Tween,System.Single,System.Single)
extern void TweenExtensions_ManualUpdate_m31E78BA6C0D5181085D9F5A82D1AC237E1DA48ED (void);
// 0x000000A3 T DG.Tweening.TweenExtensions::Pause(T)
// 0x000000A4 T DG.Tweening.TweenExtensions::Play(T)
// 0x000000A5 System.Void DG.Tweening.TweenExtensions::PlayBackwards(DG.Tweening.Tween)
extern void TweenExtensions_PlayBackwards_mF585684B8D5082A2A895C628F372FDABF690E0FB (void);
// 0x000000A6 System.Void DG.Tweening.TweenExtensions::PlayForward(DG.Tweening.Tween)
extern void TweenExtensions_PlayForward_mE9D0A1BCADDDFD7429F97CF8AE9BE9D1E81F6439 (void);
// 0x000000A7 System.Void DG.Tweening.TweenExtensions::Restart(DG.Tweening.Tween,System.Boolean,System.Single)
extern void TweenExtensions_Restart_m4080AE8184C33AFCB12BFBB850424D13836E6214 (void);
// 0x000000A8 System.Void DG.Tweening.TweenExtensions::Rewind(DG.Tweening.Tween,System.Boolean)
extern void TweenExtensions_Rewind_m0ECE9F671C1A1BE35270FD24F9AC81DC5645DAF1 (void);
// 0x000000A9 System.Void DG.Tweening.TweenExtensions::SmoothRewind(DG.Tweening.Tween)
extern void TweenExtensions_SmoothRewind_mA6788BEC5688F797A270A20E94F9FC150111936A (void);
// 0x000000AA System.Void DG.Tweening.TweenExtensions::TogglePause(DG.Tweening.Tween)
extern void TweenExtensions_TogglePause_m6AA5BB6139190D6F7A5B1516802D650DBC813322 (void);
// 0x000000AB System.Void DG.Tweening.TweenExtensions::GotoWaypoint(DG.Tweening.Tween,System.Int32,System.Boolean)
extern void TweenExtensions_GotoWaypoint_m5408B35F8A7546DDF5FFCAC1247921B6EC4176D4 (void);
// 0x000000AC UnityEngine.YieldInstruction DG.Tweening.TweenExtensions::WaitForCompletion(DG.Tweening.Tween)
extern void TweenExtensions_WaitForCompletion_m481B1D9B59FBFDB1040E57ABFEE5551EAF568A14 (void);
// 0x000000AD UnityEngine.YieldInstruction DG.Tweening.TweenExtensions::WaitForRewind(DG.Tweening.Tween)
extern void TweenExtensions_WaitForRewind_mA97F5969BBA948A3452A593469F5B6177F9992D8 (void);
// 0x000000AE UnityEngine.YieldInstruction DG.Tweening.TweenExtensions::WaitForKill(DG.Tweening.Tween)
extern void TweenExtensions_WaitForKill_mCD155C79A9FEC8B28D3E2DD06135D906A3664E84 (void);
// 0x000000AF UnityEngine.YieldInstruction DG.Tweening.TweenExtensions::WaitForElapsedLoops(DG.Tweening.Tween,System.Int32)
extern void TweenExtensions_WaitForElapsedLoops_m357B73AB3CE93158AB511B150DC5D6BA9B3356A5 (void);
// 0x000000B0 UnityEngine.YieldInstruction DG.Tweening.TweenExtensions::WaitForPosition(DG.Tweening.Tween,System.Single)
extern void TweenExtensions_WaitForPosition_mF6787D2CD4AC874CCB7F07DBF04B7AF17A93417A (void);
// 0x000000B1 UnityEngine.Coroutine DG.Tweening.TweenExtensions::WaitForStart(DG.Tweening.Tween)
extern void TweenExtensions_WaitForStart_mBD8031FD2F5FFEA975582085DEDDEA12F8FE8F19 (void);
// 0x000000B2 System.Int32 DG.Tweening.TweenExtensions::CompletedLoops(DG.Tweening.Tween)
extern void TweenExtensions_CompletedLoops_m5A7B5AEE691F491182E5FD7009C21E3BBC90CA8B (void);
// 0x000000B3 System.Single DG.Tweening.TweenExtensions::Delay(DG.Tweening.Tween)
extern void TweenExtensions_Delay_m64B89ACB80BB7813CA424659E612C7969A6AFA2F (void);
// 0x000000B4 System.Single DG.Tweening.TweenExtensions::ElapsedDelay(DG.Tweening.Tween)
extern void TweenExtensions_ElapsedDelay_m07C1B01E56C62D2614D2707969D6E3A464DF95D7 (void);
// 0x000000B5 System.Single DG.Tweening.TweenExtensions::Duration(DG.Tweening.Tween,System.Boolean)
extern void TweenExtensions_Duration_mFF4115983798994675D0431ADD2D333958714222 (void);
// 0x000000B6 System.Single DG.Tweening.TweenExtensions::Elapsed(DG.Tweening.Tween,System.Boolean)
extern void TweenExtensions_Elapsed_m62BF350941E0EEBF4B5282BC1523E52CC1E6E24D (void);
// 0x000000B7 System.Single DG.Tweening.TweenExtensions::ElapsedPercentage(DG.Tweening.Tween,System.Boolean)
extern void TweenExtensions_ElapsedPercentage_m2B88D6261A10FE69DA344E2EDF1D72F0DC7C4FA8 (void);
// 0x000000B8 System.Single DG.Tweening.TweenExtensions::ElapsedDirectionalPercentage(DG.Tweening.Tween)
extern void TweenExtensions_ElapsedDirectionalPercentage_m86020EAE1A0CA49FDE72191B7F45FEE990B9CFD5 (void);
// 0x000000B9 System.Boolean DG.Tweening.TweenExtensions::IsActive(DG.Tweening.Tween)
extern void TweenExtensions_IsActive_m7CB8E490D86B9E14B6B4B4004F1D199790397743 (void);
// 0x000000BA System.Boolean DG.Tweening.TweenExtensions::IsBackwards(DG.Tweening.Tween)
extern void TweenExtensions_IsBackwards_mF43EE5EB9C7A38949F1B1511E93D64FA2D7CA816 (void);
// 0x000000BB System.Boolean DG.Tweening.TweenExtensions::IsLoopingOrExecutingBackwards(DG.Tweening.Tween)
extern void TweenExtensions_IsLoopingOrExecutingBackwards_mF449DF8CB80AAF2C255A3F4C361622E4E5139AE1 (void);
// 0x000000BC System.Boolean DG.Tweening.TweenExtensions::IsComplete(DG.Tweening.Tween)
extern void TweenExtensions_IsComplete_mBB619B64C19A85AB4EEEBB3D37D3E720A9E0AF4C (void);
// 0x000000BD System.Boolean DG.Tweening.TweenExtensions::IsInitialized(DG.Tweening.Tween)
extern void TweenExtensions_IsInitialized_m8C2B24B55147A6773849EB32D4D00E128CAB211B (void);
// 0x000000BE System.Boolean DG.Tweening.TweenExtensions::IsPlaying(DG.Tweening.Tween)
extern void TweenExtensions_IsPlaying_m32EF28DEB59B931FA4607BAC3BED0DE275A1D843 (void);
// 0x000000BF System.Int32 DG.Tweening.TweenExtensions::Loops(DG.Tweening.Tween)
extern void TweenExtensions_Loops_m3EE2123D586F927EBFDF26FD4F9936A194B0C8C3 (void);
// 0x000000C0 UnityEngine.Vector3 DG.Tweening.TweenExtensions::PathGetPoint(DG.Tweening.Tween,System.Single)
extern void TweenExtensions_PathGetPoint_m99A277314FB3A2FE9283DC873010447CDAA36842 (void);
// 0x000000C1 UnityEngine.Vector3[] DG.Tweening.TweenExtensions::PathGetDrawPoints(DG.Tweening.Tween,System.Int32)
extern void TweenExtensions_PathGetDrawPoints_mFA9C28626C8FEB8E5A2793835713E763F8FDAA05 (void);
// 0x000000C2 System.Single DG.Tweening.TweenExtensions::PathLength(DG.Tweening.Tween)
extern void TweenExtensions_PathLength_m754255B76B1362A6F4EE6CC8F0D7E6E6D6108AB0 (void);
// 0x000000C3 System.Void DG.Tweening.Sequence::.ctor()
extern void Sequence__ctor_mFFB83C470D70B8512E2B393A0C07D90FEC2CBC84 (void);
// 0x000000C4 DG.Tweening.Sequence DG.Tweening.Sequence::DoPrepend(DG.Tweening.Sequence,DG.Tweening.Tween)
extern void Sequence_DoPrepend_mD160A1A92AB3AA419088A3DDA26497C6648CAC76 (void);
// 0x000000C5 DG.Tweening.Sequence DG.Tweening.Sequence::DoInsert(DG.Tweening.Sequence,DG.Tweening.Tween,System.Single)
extern void Sequence_DoInsert_mD948F47640F2159358EE6B18952EB77AEE6610F4 (void);
// 0x000000C6 DG.Tweening.Sequence DG.Tweening.Sequence::DoAppendInterval(DG.Tweening.Sequence,System.Single)
extern void Sequence_DoAppendInterval_m0C3E54F3B28A78293C67DD1AFFBDB27A665E59BE (void);
// 0x000000C7 DG.Tweening.Sequence DG.Tweening.Sequence::DoPrependInterval(DG.Tweening.Sequence,System.Single)
extern void Sequence_DoPrependInterval_m5349CCACB49068164F7EEDE05FB705AA7458D248 (void);
// 0x000000C8 DG.Tweening.Sequence DG.Tweening.Sequence::DoInsertCallback(DG.Tweening.Sequence,DG.Tweening.TweenCallback,System.Single)
extern void Sequence_DoInsertCallback_m54445936FAD5F03DA3B631C195FC393C1B80F0B2 (void);
// 0x000000C9 System.Single DG.Tweening.Sequence::UpdateDelay(System.Single)
extern void Sequence_UpdateDelay_m1036F784D7C13B47D60C95A67DB53C207FC16C25 (void);
// 0x000000CA System.Void DG.Tweening.Sequence::Reset()
extern void Sequence_Reset_m006B0E92244A1C149A954C5183E9826DC5C828AE (void);
// 0x000000CB System.Boolean DG.Tweening.Sequence::Validate()
extern void Sequence_Validate_m608AF33CC00272DAAD250F44DC2D974EF9931AFC (void);
// 0x000000CC System.Boolean DG.Tweening.Sequence::Startup()
extern void Sequence_Startup_m76F5EC2C0703BDAFA28DA67BB0AD4B97BE0A9D4E (void);
// 0x000000CD System.Boolean DG.Tweening.Sequence::ApplyTween(System.Single,System.Int32,System.Int32,System.Boolean,DG.Tweening.Core.Enums.UpdateMode,DG.Tweening.Core.Enums.UpdateNotice)
extern void Sequence_ApplyTween_mC85811FDB77E639F3665931DD46CCC17626354A7 (void);
// 0x000000CE System.Void DG.Tweening.Sequence::Setup(DG.Tweening.Sequence)
extern void Sequence_Setup_mDCF62E1E0C88A3090CBDD2D79A544EF03150202A (void);
// 0x000000CF System.Boolean DG.Tweening.Sequence::DoStartup(DG.Tweening.Sequence)
extern void Sequence_DoStartup_mEC96B51F5254BE451DD5CBFED2EFC13FE463F7F0 (void);
// 0x000000D0 System.Boolean DG.Tweening.Sequence::DoApplyTween(DG.Tweening.Sequence,System.Single,System.Int32,System.Int32,System.Boolean,DG.Tweening.Core.Enums.UpdateMode)
extern void Sequence_DoApplyTween_m06E4746BDB1F214259ADC078EDD812BBBFCFC54D (void);
// 0x000000D1 System.Boolean DG.Tweening.Sequence::ApplyInternalCycle(DG.Tweening.Sequence,System.Single,System.Single,DG.Tweening.Core.Enums.UpdateMode,System.Boolean,System.Boolean,System.Boolean)
extern void Sequence_ApplyInternalCycle_m2B145923EEC8BE7893BB8F17B217F26318AC8B94 (void);
// 0x000000D2 System.Void DG.Tweening.Sequence::StableSortSequencedObjs(System.Collections.Generic.List`1<DG.Tweening.Core.ABSSequentiable>)
extern void Sequence_StableSortSequencedObjs_mC3780B8F109A9114A2D5CE3C2605903E282E1FAB (void);
// 0x000000D3 System.Boolean DG.Tweening.Sequence::IsAnyCallbackSet(DG.Tweening.Sequence)
extern void Sequence_IsAnyCallbackSet_m98D9A7B3915C4C54A385E0F39646C3DF9D62A600 (void);
// 0x000000D4 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOAspect(UnityEngine.Camera,System.Single,System.Single)
extern void ShortcutExtensions_DOAspect_mE9ECE416D6C7FD3BDE1DB73DFBCAA738589062E2 (void);
// 0x000000D5 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.ShortcutExtensions::DOColor(UnityEngine.Camera,UnityEngine.Color,System.Single)
extern void ShortcutExtensions_DOColor_mC438262691549AA19473BFC4777A39E4DF995E03 (void);
// 0x000000D6 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOFarClipPlane(UnityEngine.Camera,System.Single,System.Single)
extern void ShortcutExtensions_DOFarClipPlane_mB243E0B3DDF685289216FB198FBAA773C7876C58 (void);
// 0x000000D7 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOFieldOfView(UnityEngine.Camera,System.Single,System.Single)
extern void ShortcutExtensions_DOFieldOfView_mDF8F791F7D4672A51988CE4F3434A589F0F7F62D (void);
// 0x000000D8 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DONearClipPlane(UnityEngine.Camera,System.Single,System.Single)
extern void ShortcutExtensions_DONearClipPlane_mBD3B11717DBA7B9FDF01EC45DABF8E841B400B3D (void);
// 0x000000D9 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOOrthoSize(UnityEngine.Camera,System.Single,System.Single)
extern void ShortcutExtensions_DOOrthoSize_mE96037CC51B44CB1B04CF436779EAF60BF6C0CD3 (void);
// 0x000000DA DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions> DG.Tweening.ShortcutExtensions::DOPixelRect(UnityEngine.Camera,UnityEngine.Rect,System.Single)
extern void ShortcutExtensions_DOPixelRect_m30CEF5D00DEFB324BB8514B95CF0328929274598 (void);
// 0x000000DB DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions> DG.Tweening.ShortcutExtensions::DORect(UnityEngine.Camera,UnityEngine.Rect,System.Single)
extern void ShortcutExtensions_DORect_m21D8649612CDF6196C7D4C591382743112FEAFD3 (void);
// 0x000000DC DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakePosition(UnityEngine.Camera,System.Single,System.Single,System.Int32,System.Single,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakePosition_m45CB5C2107BCB248F0EF021A8C81DC84655F2827 (void);
// 0x000000DD DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakePosition(UnityEngine.Camera,System.Single,UnityEngine.Vector3,System.Int32,System.Single,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakePosition_m7110C83FC1DC1963A245BFD178CE3351AF588541 (void);
// 0x000000DE DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakeRotation(UnityEngine.Camera,System.Single,System.Single,System.Int32,System.Single,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakeRotation_m7E0F68137308B2DCCE102DD9136C5018934B3AF6 (void);
// 0x000000DF DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakeRotation(UnityEngine.Camera,System.Single,UnityEngine.Vector3,System.Int32,System.Single,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakeRotation_m52BE42BA066C7A74BADCF9B91B0C6D60C419FB6E (void);
// 0x000000E0 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.ShortcutExtensions::DOColor(UnityEngine.Light,UnityEngine.Color,System.Single)
extern void ShortcutExtensions_DOColor_mAC2C2C38A8C15064D70E6B39F5735A09DDA0D581 (void);
// 0x000000E1 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOIntensity(UnityEngine.Light,System.Single,System.Single)
extern void ShortcutExtensions_DOIntensity_m27397BA48763F123CFEE2F08D1B68C536BD1AE12 (void);
// 0x000000E2 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOShadowStrength(UnityEngine.Light,System.Single,System.Single)
extern void ShortcutExtensions_DOShadowStrength_m5C68624DB62ED7BBB84F03131DAC97B815041123 (void);
// 0x000000E3 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOColor(UnityEngine.LineRenderer,DG.Tweening.Color2,DG.Tweening.Color2,System.Single)
extern void ShortcutExtensions_DOColor_m047D7B3D5AAF424172290E105A6B991020516A56 (void);
// 0x000000E4 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.ShortcutExtensions::DOColor(UnityEngine.Material,UnityEngine.Color,System.Single)
extern void ShortcutExtensions_DOColor_m7770E3969D58563343B129139B857669312EFAA9 (void);
// 0x000000E5 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.ShortcutExtensions::DOColor(UnityEngine.Material,UnityEngine.Color,System.String,System.Single)
extern void ShortcutExtensions_DOColor_mF41D0D0338A1C71122B56F08353CAFA9931B5183 (void);
// 0x000000E6 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.ShortcutExtensions::DOColor(UnityEngine.Material,UnityEngine.Color,System.Int32,System.Single)
extern void ShortcutExtensions_DOColor_m902471CFC01B9E318F432233AD11C00BED289EF8 (void);
// 0x000000E7 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.ShortcutExtensions::DOFade(UnityEngine.Material,System.Single,System.Single)
extern void ShortcutExtensions_DOFade_m1C499FE6483845A6BBE9C4EB80D11062C9859FC8 (void);
// 0x000000E8 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.ShortcutExtensions::DOFade(UnityEngine.Material,System.Single,System.String,System.Single)
extern void ShortcutExtensions_DOFade_mF89476AE4BB169F0F4E829D105A639D03F864163 (void);
// 0x000000E9 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.ShortcutExtensions::DOFade(UnityEngine.Material,System.Single,System.Int32,System.Single)
extern void ShortcutExtensions_DOFade_m2F02D24BBE5D0167CC8C852D21E9E5153332B56F (void);
// 0x000000EA DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOFloat(UnityEngine.Material,System.Single,System.String,System.Single)
extern void ShortcutExtensions_DOFloat_mC88ECC5840584E487DAD7A47724F277158EB3A7F (void);
// 0x000000EB DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOFloat(UnityEngine.Material,System.Single,System.Int32,System.Single)
extern void ShortcutExtensions_DOFloat_m69CDCA35D0819C7B9A582C15D19E946153E080C0 (void);
// 0x000000EC DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOOffset(UnityEngine.Material,UnityEngine.Vector2,System.Single)
extern void ShortcutExtensions_DOOffset_m7B96FE908DE6175518AFD3EA61BFF765F23E59CF (void);
// 0x000000ED DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOOffset(UnityEngine.Material,UnityEngine.Vector2,System.String,System.Single)
extern void ShortcutExtensions_DOOffset_mB64259517DC2B7BC622A6573D950AE495EB7DD4E (void);
// 0x000000EE DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOTiling(UnityEngine.Material,UnityEngine.Vector2,System.Single)
extern void ShortcutExtensions_DOTiling_m112A47C17BF7518DE0A12E98B2C22EDBFE70EA43 (void);
// 0x000000EF DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOTiling(UnityEngine.Material,UnityEngine.Vector2,System.String,System.Single)
extern void ShortcutExtensions_DOTiling_m44582100DA4D7F4B116777F5D285885846BF806C (void);
// 0x000000F0 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOVector(UnityEngine.Material,UnityEngine.Vector4,System.String,System.Single)
extern void ShortcutExtensions_DOVector_mC26736E716689ECAC62785114485C14A623A3AE9 (void);
// 0x000000F1 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOVector(UnityEngine.Material,UnityEngine.Vector4,System.Int32,System.Single)
extern void ShortcutExtensions_DOVector_m00FD44EC8D9045D280EAFBDA475E351754C8D19C (void);
// 0x000000F2 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOResize(UnityEngine.TrailRenderer,System.Single,System.Single,System.Single)
extern void ShortcutExtensions_DOResize_m6DE50633682D47B2ACC43DFE6B5B463B764F4C15 (void);
// 0x000000F3 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOTime(UnityEngine.TrailRenderer,System.Single,System.Single)
extern void ShortcutExtensions_DOTime_m00953893B1B5A1C6F21849BF5E107DE30ACB2DC4 (void);
// 0x000000F4 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOMove(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Boolean)
extern void ShortcutExtensions_DOMove_m82274FDC0216A91A1FAF16844805D06BF9A287FF (void);
// 0x000000F5 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOMoveX(UnityEngine.Transform,System.Single,System.Single,System.Boolean)
extern void ShortcutExtensions_DOMoveX_m1173E2DE6886AEE7BDB63E1479CD102A0F734543 (void);
// 0x000000F6 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOMoveY(UnityEngine.Transform,System.Single,System.Single,System.Boolean)
extern void ShortcutExtensions_DOMoveY_m7986D5CC9DCD45AED6BC48F87C306CCED2B6BE62 (void);
// 0x000000F7 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOMoveZ(UnityEngine.Transform,System.Single,System.Single,System.Boolean)
extern void ShortcutExtensions_DOMoveZ_m6ECBC915613E53BB64DEC10900A8A9E290B24844 (void);
// 0x000000F8 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOLocalMove(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Boolean)
extern void ShortcutExtensions_DOLocalMove_m22F3EB581DADB5A3FC59B69F7F6F05A86F8E8348 (void);
// 0x000000F9 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOLocalMoveX(UnityEngine.Transform,System.Single,System.Single,System.Boolean)
extern void ShortcutExtensions_DOLocalMoveX_m3411FBE47AB4960B865E352F672F94B788F712D5 (void);
// 0x000000FA DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOLocalMoveY(UnityEngine.Transform,System.Single,System.Single,System.Boolean)
extern void ShortcutExtensions_DOLocalMoveY_mA4BDBF3ACA5B305B59551FBF9813D5BF35487CD5 (void);
// 0x000000FB DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOLocalMoveZ(UnityEngine.Transform,System.Single,System.Single,System.Boolean)
extern void ShortcutExtensions_DOLocalMoveZ_mF9B4A4F9168E6A7BDA6EF9271E82706198E52BD3 (void);
// 0x000000FC DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions> DG.Tweening.ShortcutExtensions::DORotate(UnityEngine.Transform,UnityEngine.Vector3,System.Single,DG.Tweening.RotateMode)
extern void ShortcutExtensions_DORotate_mA2804C1A3E4780383111262752CC7056BBC7D470 (void);
// 0x000000FD DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Quaternion,DG.Tweening.Plugins.Options.NoOptions> DG.Tweening.ShortcutExtensions::DORotateQuaternion(UnityEngine.Transform,UnityEngine.Quaternion,System.Single)
extern void ShortcutExtensions_DORotateQuaternion_m36B2BE20F4F4E39AA82234D0503D01A67D901021 (void);
// 0x000000FE DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions> DG.Tweening.ShortcutExtensions::DOLocalRotate(UnityEngine.Transform,UnityEngine.Vector3,System.Single,DG.Tweening.RotateMode)
extern void ShortcutExtensions_DOLocalRotate_m6EB8F37963023C6B157C60013B98D2B612816DA4 (void);
// 0x000000FF DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Quaternion,DG.Tweening.Plugins.Options.NoOptions> DG.Tweening.ShortcutExtensions::DOLocalRotateQuaternion(UnityEngine.Transform,UnityEngine.Quaternion,System.Single)
extern void ShortcutExtensions_DOLocalRotateQuaternion_mD0927EF813E9A8B6786FCB7B5A510C6585E1CA30 (void);
// 0x00000100 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOScale(UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void ShortcutExtensions_DOScale_mF7AC6EA0FD71B399776D758AD57B94F18A47F580 (void);
// 0x00000101 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOScale(UnityEngine.Transform,System.Single,System.Single)
extern void ShortcutExtensions_DOScale_m5935113B55474CC0551EF8A8EA3CFA82371D5E99 (void);
// 0x00000102 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOScaleX(UnityEngine.Transform,System.Single,System.Single)
extern void ShortcutExtensions_DOScaleX_m88DC7010A9399FA9EE1430AF3A466D87E3921879 (void);
// 0x00000103 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOScaleY(UnityEngine.Transform,System.Single,System.Single)
extern void ShortcutExtensions_DOScaleY_m0DBDA35C6ADF0A2A0A9FA5323EF4B4F3DA9DC1B5 (void);
// 0x00000104 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.ShortcutExtensions::DOScaleZ(UnityEngine.Transform,System.Single,System.Single)
extern void ShortcutExtensions_DOScaleZ_m922D963D40194D815D0CE1184486A3D4829504B9 (void);
// 0x00000105 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOLookAt(UnityEngine.Transform,UnityEngine.Vector3,System.Single,DG.Tweening.AxisConstraint,System.Nullable`1<UnityEngine.Vector3>)
extern void ShortcutExtensions_DOLookAt_m7B2760200371481892C58C78878C4EF546EC6763 (void);
// 0x00000106 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DODynamicLookAt(UnityEngine.Transform,UnityEngine.Vector3,System.Single,DG.Tweening.AxisConstraint,System.Nullable`1<UnityEngine.Vector3>)
extern void ShortcutExtensions_DODynamicLookAt_mCFAA30AC7C2EA6F9AC28F3828F2ACA22499791D1 (void);
// 0x00000107 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::LookAt(UnityEngine.Transform,UnityEngine.Vector3,System.Single,DG.Tweening.AxisConstraint,System.Nullable`1<UnityEngine.Vector3>,System.Boolean)
extern void ShortcutExtensions_LookAt_mB87549F2630F836B9FAC37BA2D29A405EF03B515 (void);
// 0x00000108 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOPunchPosition(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Int32,System.Single,System.Boolean)
extern void ShortcutExtensions_DOPunchPosition_mD022015ABB94942EE909F7F8E0F3660D52FA3D9E (void);
// 0x00000109 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOPunchScale(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Int32,System.Single)
extern void ShortcutExtensions_DOPunchScale_mD7D825D1761F0264BC1D00027B79330844400B9A (void);
// 0x0000010A DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOPunchRotation(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Int32,System.Single)
extern void ShortcutExtensions_DOPunchRotation_mDC55C1F23E2C17A4E9D4BF5BB787BB1DE98D7AC4 (void);
// 0x0000010B DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakePosition(UnityEngine.Transform,System.Single,System.Single,System.Int32,System.Single,System.Boolean,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakePosition_m6B9660D299172E1E37B7C1573C5263A37C48739F (void);
// 0x0000010C DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakePosition(UnityEngine.Transform,System.Single,UnityEngine.Vector3,System.Int32,System.Single,System.Boolean,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakePosition_m13612BE4565D5755BF5F4AD92827925CDEBD821C (void);
// 0x0000010D DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakeRotation(UnityEngine.Transform,System.Single,System.Single,System.Int32,System.Single,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakeRotation_m5E79D157D34BD0FC61A3E9564716975C0B992293 (void);
// 0x0000010E DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakeRotation(UnityEngine.Transform,System.Single,UnityEngine.Vector3,System.Int32,System.Single,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakeRotation_m6292F37F5BAC8EA3EAA8BD3B5507E65E5D9CE729 (void);
// 0x0000010F DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakeScale(UnityEngine.Transform,System.Single,System.Single,System.Int32,System.Single,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakeScale_mEE72C2F72BD8B6AAA2BEA8BDC20E46E9494A655A (void);
// 0x00000110 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOShakeScale(UnityEngine.Transform,System.Single,UnityEngine.Vector3,System.Int32,System.Single,System.Boolean,DG.Tweening.ShakeRandomnessMode)
extern void ShortcutExtensions_DOShakeScale_m89D5B0734EA7D724637547C2A14DA5865A1A77DF (void);
// 0x00000111 DG.Tweening.Sequence DG.Tweening.ShortcutExtensions::DOJump(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Int32,System.Single,System.Boolean)
extern void ShortcutExtensions_DOJump_m918C2D5466D8F6D332AE98BFB88E0871B060DA36 (void);
// 0x00000112 DG.Tweening.Sequence DG.Tweening.ShortcutExtensions::DOLocalJump(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Int32,System.Single,System.Boolean)
extern void ShortcutExtensions_DOLocalJump_m4F8B01A9D2BAD5B26E9B1E8A8212DE8BED54F557 (void);
// 0x00000113 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.ShortcutExtensions::DOPath(UnityEngine.Transform,UnityEngine.Vector3[],System.Single,DG.Tweening.PathType,DG.Tweening.PathMode,System.Int32,System.Nullable`1<UnityEngine.Color>)
extern void ShortcutExtensions_DOPath_mB31C78FE35F62166F934D044D3B0B4BAC6304D4B (void);
// 0x00000114 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.ShortcutExtensions::DOLocalPath(UnityEngine.Transform,UnityEngine.Vector3[],System.Single,DG.Tweening.PathType,DG.Tweening.PathMode,System.Int32,System.Nullable`1<UnityEngine.Color>)
extern void ShortcutExtensions_DOLocalPath_m00D123733C52DD578549A9F31151194C0CA079BB (void);
// 0x00000115 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.ShortcutExtensions::DOPath(UnityEngine.Transform,DG.Tweening.Plugins.Core.PathCore.Path,System.Single,DG.Tweening.PathMode)
extern void ShortcutExtensions_DOPath_m3E70D921DDA265292CF467212AC676371F110691 (void);
// 0x00000116 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.ShortcutExtensions::DOLocalPath(UnityEngine.Transform,DG.Tweening.Plugins.Core.PathCore.Path,System.Single,DG.Tweening.PathMode)
extern void ShortcutExtensions_DOLocalPath_m4F4C77B2C481DDCB0FDBCE8B3C4442D897F1B2DA (void);
// 0x00000117 DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions> DG.Tweening.ShortcutExtensions::DOTimeScale(DG.Tweening.Tween,System.Single,System.Single)
extern void ShortcutExtensions_DOTimeScale_m5E0756CBAFCFBBE6F19E544200E57863C4BA29C7 (void);
// 0x00000118 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendableColor(UnityEngine.Light,UnityEngine.Color,System.Single)
extern void ShortcutExtensions_DOBlendableColor_m6D881864FAADBEFC395EF3676BC6840B6181003C (void);
// 0x00000119 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendableColor(UnityEngine.Material,UnityEngine.Color,System.Single)
extern void ShortcutExtensions_DOBlendableColor_mB4B51382219C2371D8122BDF90CADC0796C39B56 (void);
// 0x0000011A DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendableColor(UnityEngine.Material,UnityEngine.Color,System.String,System.Single)
extern void ShortcutExtensions_DOBlendableColor_m6F9B2C98A1BDD89B0B416E73DA685EDDE569C318 (void);
// 0x0000011B DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendableColor(UnityEngine.Material,UnityEngine.Color,System.Int32,System.Single)
extern void ShortcutExtensions_DOBlendableColor_m3894C723770667A4E3412C45BB41E8E788119B5F (void);
// 0x0000011C DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendableMoveBy(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Boolean)
extern void ShortcutExtensions_DOBlendableMoveBy_mE8B5A140CB7E51980FDC15DD76366D1A23AECB8D (void);
// 0x0000011D DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendableLocalMoveBy(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Boolean)
extern void ShortcutExtensions_DOBlendableLocalMoveBy_mB7638D6C3CC5106CD711AFE8053685B6E2C30BBF (void);
// 0x0000011E DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendableRotateBy(UnityEngine.Transform,UnityEngine.Vector3,System.Single,DG.Tweening.RotateMode)
extern void ShortcutExtensions_DOBlendableRotateBy_mB7E8F46D372D33702D92542702FC055B6E5F9C89 (void);
// 0x0000011F DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendableLocalRotateBy(UnityEngine.Transform,UnityEngine.Vector3,System.Single,DG.Tweening.RotateMode)
extern void ShortcutExtensions_DOBlendableLocalRotateBy_mCB395A6DCD07BCCEAF501FD486955A43E4B295B2 (void);
// 0x00000120 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendablePunchRotation(UnityEngine.Transform,UnityEngine.Vector3,System.Single,System.Int32,System.Single)
extern void ShortcutExtensions_DOBlendablePunchRotation_m7AF8A8051402033D49416E5A6106AE1E69A3FB49 (void);
// 0x00000121 DG.Tweening.Tweener DG.Tweening.ShortcutExtensions::DOBlendableScaleBy(UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void ShortcutExtensions_DOBlendableScaleBy_m7BADCC6C4398F420D288E0E3B508609E5B612886 (void);
// 0x00000122 System.Int32 DG.Tweening.ShortcutExtensions::DOComplete(UnityEngine.Component,System.Boolean)
extern void ShortcutExtensions_DOComplete_mC76F1E5F1C3FC93A195ED13839A42D63A8A4007C (void);
// 0x00000123 System.Int32 DG.Tweening.ShortcutExtensions::DOComplete(UnityEngine.Material,System.Boolean)
extern void ShortcutExtensions_DOComplete_m1F8696120349F77CBAA082F0B4B9E87A570489F4 (void);
// 0x00000124 System.Int32 DG.Tweening.ShortcutExtensions::DOKill(UnityEngine.Component,System.Boolean)
extern void ShortcutExtensions_DOKill_m3F197E779AB6CA95FF3C4C2DD547B4B493E42D46 (void);
// 0x00000125 System.Int32 DG.Tweening.ShortcutExtensions::DOKill(UnityEngine.Material,System.Boolean)
extern void ShortcutExtensions_DOKill_mA88D7174BB15882E9F2FC0C07A78DD096DD7B02D (void);
// 0x00000126 System.Int32 DG.Tweening.ShortcutExtensions::DOFlip(UnityEngine.Component)
extern void ShortcutExtensions_DOFlip_m0F160622F017B8EEA110E6868461ED7288904CAD (void);
// 0x00000127 System.Int32 DG.Tweening.ShortcutExtensions::DOFlip(UnityEngine.Material)
extern void ShortcutExtensions_DOFlip_m6C70971874D6C538D9ACF953CA9126FFF91983C5 (void);
// 0x00000128 System.Int32 DG.Tweening.ShortcutExtensions::DOGoto(UnityEngine.Component,System.Single,System.Boolean)
extern void ShortcutExtensions_DOGoto_m969AFE4BADC4BB821EFD9B05A786E6B780064DB0 (void);
// 0x00000129 System.Int32 DG.Tweening.ShortcutExtensions::DOGoto(UnityEngine.Material,System.Single,System.Boolean)
extern void ShortcutExtensions_DOGoto_m22C5343949FBF39C5E6E2209D7C8FD220C2F773F (void);
// 0x0000012A System.Int32 DG.Tweening.ShortcutExtensions::DOPause(UnityEngine.Component)
extern void ShortcutExtensions_DOPause_mB6E749E6F49864EF31F821A09F84CFCB2766A6D0 (void);
// 0x0000012B System.Int32 DG.Tweening.ShortcutExtensions::DOPause(UnityEngine.Material)
extern void ShortcutExtensions_DOPause_mFA584CF337399AAA4B080D2468EDC87C65243923 (void);
// 0x0000012C System.Int32 DG.Tweening.ShortcutExtensions::DOPlay(UnityEngine.Component)
extern void ShortcutExtensions_DOPlay_m33F4EE8E48C2610513626994E58C12A3CC720C2E (void);
// 0x0000012D System.Int32 DG.Tweening.ShortcutExtensions::DOPlay(UnityEngine.Material)
extern void ShortcutExtensions_DOPlay_m2402C7171E5E43CCF50DFDB758A6D2307A125185 (void);
// 0x0000012E System.Int32 DG.Tweening.ShortcutExtensions::DOPlayBackwards(UnityEngine.Component)
extern void ShortcutExtensions_DOPlayBackwards_m176213A53F407C711C1A6BE509DA9B934DEB4C89 (void);
// 0x0000012F System.Int32 DG.Tweening.ShortcutExtensions::DOPlayBackwards(UnityEngine.Material)
extern void ShortcutExtensions_DOPlayBackwards_mCBF64C71205FBA34EA570628FCEEB3F8EBDE8987 (void);
// 0x00000130 System.Int32 DG.Tweening.ShortcutExtensions::DOPlayForward(UnityEngine.Component)
extern void ShortcutExtensions_DOPlayForward_m79A9002106CACC71D6E18EB731AB700713E5E2A1 (void);
// 0x00000131 System.Int32 DG.Tweening.ShortcutExtensions::DOPlayForward(UnityEngine.Material)
extern void ShortcutExtensions_DOPlayForward_m5DAE0042EE85D4C7543F3F9CAAB874167F4A72EB (void);
// 0x00000132 System.Int32 DG.Tweening.ShortcutExtensions::DORestart(UnityEngine.Component,System.Boolean)
extern void ShortcutExtensions_DORestart_m3CA008E63D7607E04507BC8977568501417EF06E (void);
// 0x00000133 System.Int32 DG.Tweening.ShortcutExtensions::DORestart(UnityEngine.Material,System.Boolean)
extern void ShortcutExtensions_DORestart_m68D5B5B82C4C0FA7DB4447E63EF5E7B17D59707A (void);
// 0x00000134 System.Int32 DG.Tweening.ShortcutExtensions::DORewind(UnityEngine.Component,System.Boolean)
extern void ShortcutExtensions_DORewind_mE913AA9DCE96F481292455CA7132B1ED602DF710 (void);
// 0x00000135 System.Int32 DG.Tweening.ShortcutExtensions::DORewind(UnityEngine.Material,System.Boolean)
extern void ShortcutExtensions_DORewind_m7FB526E680AF81409FDDEA2AEEACD75A771A4D79 (void);
// 0x00000136 System.Int32 DG.Tweening.ShortcutExtensions::DOSmoothRewind(UnityEngine.Component)
extern void ShortcutExtensions_DOSmoothRewind_m52F2C7CAC6BF0DABC637EC72EB3C5F7B6FDE7C9B (void);
// 0x00000137 System.Int32 DG.Tweening.ShortcutExtensions::DOSmoothRewind(UnityEngine.Material)
extern void ShortcutExtensions_DOSmoothRewind_m5DF3E075EF5A1DB7BF94C4B83909845AE2E5BE1A (void);
// 0x00000138 System.Int32 DG.Tweening.ShortcutExtensions::DOTogglePause(UnityEngine.Component)
extern void ShortcutExtensions_DOTogglePause_m9D9CDCF160BA5E96A99AC46113A87A7CB376F144 (void);
// 0x00000139 System.Int32 DG.Tweening.ShortcutExtensions::DOTogglePause(UnityEngine.Material)
extern void ShortcutExtensions_DOTogglePause_mE620BA810033BAC9654C95A57C9BADD65EC48F63 (void);
// 0x0000013A System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass0_0::.ctor()
extern void U3CU3Ec__DisplayClass0_0__ctor_m14F8614CA85AED6EEB9F83CD5240ECB824FD30BC (void);
// 0x0000013B System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass0_0::<DOAspect>b__0()
extern void U3CU3Ec__DisplayClass0_0_U3CDOAspectU3Eb__0_m3B8BE2D1BEEAEDD0102C3DC4A32B80C07DD8264F (void);
// 0x0000013C System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass0_0::<DOAspect>b__1(System.Single)
extern void U3CU3Ec__DisplayClass0_0_U3CDOAspectU3Eb__1_mDDD2F090D812F2AF5DC451DF365B91E91410B0B0 (void);
// 0x0000013D System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass10_0::.ctor()
extern void U3CU3Ec__DisplayClass10_0__ctor_mDBD1C6FF072B315E22B6BEDD89A6754F63C9C214 (void);
// 0x0000013E UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass10_0::<DOShakeRotation>b__0()
extern void U3CU3Ec__DisplayClass10_0_U3CDOShakeRotationU3Eb__0_m6EE0D66C6A2F40EA31F9F76273517E62A68D18E6 (void);
// 0x0000013F System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass10_0::<DOShakeRotation>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass10_0_U3CDOShakeRotationU3Eb__1_mF2555B38D1D48BE9BC0D51C9254F5D1D224BAA3E (void);
// 0x00000140 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass11_0::.ctor()
extern void U3CU3Ec__DisplayClass11_0__ctor_m5721180AF4A066848626CE3084B7C816916F4C86 (void);
// 0x00000141 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass11_0::<DOShakeRotation>b__0()
extern void U3CU3Ec__DisplayClass11_0_U3CDOShakeRotationU3Eb__0_mC15954C72678B2B67CE4C4015E67B9855AA6894A (void);
// 0x00000142 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass11_0::<DOShakeRotation>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass11_0_U3CDOShakeRotationU3Eb__1_mDF144244EEE86AA5942A63587B459FE87394D717 (void);
// 0x00000143 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass12_0::.ctor()
extern void U3CU3Ec__DisplayClass12_0__ctor_m77B82950D8ED2AE3169C0193888A86E0D538A5CB (void);
// 0x00000144 UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass12_0::<DOColor>b__0()
extern void U3CU3Ec__DisplayClass12_0_U3CDOColorU3Eb__0_m32DE3BA95C1F0C32BE0552EE5599FFBB0FE97DF1 (void);
// 0x00000145 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass12_0::<DOColor>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass12_0_U3CDOColorU3Eb__1_m4D05D90FBB17A5455CC66D17A83CF1656A87755B (void);
// 0x00000146 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass13_0::.ctor()
extern void U3CU3Ec__DisplayClass13_0__ctor_m06708EDA2D1D9444D4BBD595985B8EFFD0595710 (void);
// 0x00000147 System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass13_0::<DOIntensity>b__0()
extern void U3CU3Ec__DisplayClass13_0_U3CDOIntensityU3Eb__0_mCDD8C584608D56E4644AEEFEF0993C89615D2ADA (void);
// 0x00000148 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass13_0::<DOIntensity>b__1(System.Single)
extern void U3CU3Ec__DisplayClass13_0_U3CDOIntensityU3Eb__1_m91CCE11F8FA50E2CE4CCAF30A2CBD50FF6F6AEF8 (void);
// 0x00000149 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass14_0::.ctor()
extern void U3CU3Ec__DisplayClass14_0__ctor_m5FB455D73785B75245469F31A7392D0144FCDF21 (void);
// 0x0000014A System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass14_0::<DOShadowStrength>b__0()
extern void U3CU3Ec__DisplayClass14_0_U3CDOShadowStrengthU3Eb__0_m2DC657B64395CE80DD215B383A03539A0CEEF380 (void);
// 0x0000014B System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass14_0::<DOShadowStrength>b__1(System.Single)
extern void U3CU3Ec__DisplayClass14_0_U3CDOShadowStrengthU3Eb__1_mC8073F85B752DECD9A03AA3B72A083098E0D62AB (void);
// 0x0000014C System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass15_0::.ctor()
extern void U3CU3Ec__DisplayClass15_0__ctor_m9578242E75063C730CD83AA00A13DE917D78112D (void);
// 0x0000014D DG.Tweening.Color2 DG.Tweening.ShortcutExtensions/<>c__DisplayClass15_0::<DOColor>b__0()
extern void U3CU3Ec__DisplayClass15_0_U3CDOColorU3Eb__0_mB56C6DC371C3B3D7C76896EB0980DB19812055AE (void);
// 0x0000014E System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass15_0::<DOColor>b__1(DG.Tweening.Color2)
extern void U3CU3Ec__DisplayClass15_0_U3CDOColorU3Eb__1_m4A0DD6168BAD515186450A98443517EB737BFC83 (void);
// 0x0000014F System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass16_0::.ctor()
extern void U3CU3Ec__DisplayClass16_0__ctor_m2B3D033F265B069E406A5330FE153EDADECEBC28 (void);
// 0x00000150 UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass16_0::<DOColor>b__0()
extern void U3CU3Ec__DisplayClass16_0_U3CDOColorU3Eb__0_m68313755D418CC0E079ECB927D1DA4C137927F73 (void);
// 0x00000151 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass16_0::<DOColor>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass16_0_U3CDOColorU3Eb__1_mBCABAA139578080574B0C7AFBD7938C6E49961EF (void);
// 0x00000152 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass17_0::.ctor()
extern void U3CU3Ec__DisplayClass17_0__ctor_mE986B184C7B7F578C4EFDDCDC57D33970F613668 (void);
// 0x00000153 UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass17_0::<DOColor>b__0()
extern void U3CU3Ec__DisplayClass17_0_U3CDOColorU3Eb__0_mF06E3103940FEA26AE34A10EB7737BEF9322200D (void);
// 0x00000154 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass17_0::<DOColor>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass17_0_U3CDOColorU3Eb__1_mD8C4BBDABA25C6A116793F58B6EED5DF8EDF4145 (void);
// 0x00000155 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass18_0::.ctor()
extern void U3CU3Ec__DisplayClass18_0__ctor_mF6B6673A10CC377B356E27C342F3CEDE4F015655 (void);
// 0x00000156 UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass18_0::<DOColor>b__0()
extern void U3CU3Ec__DisplayClass18_0_U3CDOColorU3Eb__0_mC6E544B7FA223A789381F3327AB3A206BFEDBDFC (void);
// 0x00000157 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass18_0::<DOColor>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass18_0_U3CDOColorU3Eb__1_m9BF452CD36004FAC17150C6F6716921B4B1389B9 (void);
// 0x00000158 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass19_0::.ctor()
extern void U3CU3Ec__DisplayClass19_0__ctor_m6A019E5099794BFD3EC9819B9C19AC696B218865 (void);
// 0x00000159 UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass19_0::<DOFade>b__0()
extern void U3CU3Ec__DisplayClass19_0_U3CDOFadeU3Eb__0_m50D60171D21D0F9B87A60CA102A9CE3DAE504842 (void);
// 0x0000015A System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass19_0::<DOFade>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass19_0_U3CDOFadeU3Eb__1_m5203CDD55EDCD16C106CF2230BEB634973FA87C1 (void);
// 0x0000015B System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass1_0::.ctor()
extern void U3CU3Ec__DisplayClass1_0__ctor_mF7AE84F589865AFA97F3F0613C42A63F7ADF9FD2 (void);
// 0x0000015C UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass1_0::<DOColor>b__0()
extern void U3CU3Ec__DisplayClass1_0_U3CDOColorU3Eb__0_mB50622EF9AB0EA0CF6318319A488B134B1E441EA (void);
// 0x0000015D System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass1_0::<DOColor>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass1_0_U3CDOColorU3Eb__1_mC208F6B646E6706D73391F28237DE9FB3E01C022 (void);
// 0x0000015E System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass20_0::.ctor()
extern void U3CU3Ec__DisplayClass20_0__ctor_m46D2A9D57813D152D095E17B82ADE4E928A4F340 (void);
// 0x0000015F UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass20_0::<DOFade>b__0()
extern void U3CU3Ec__DisplayClass20_0_U3CDOFadeU3Eb__0_m65265FF6B2A568D71DB04F4DEDB17A56740B973B (void);
// 0x00000160 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass20_0::<DOFade>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass20_0_U3CDOFadeU3Eb__1_m352986405F427FBF9D1A7638E8CE454B2040D20A (void);
// 0x00000161 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass21_0::.ctor()
extern void U3CU3Ec__DisplayClass21_0__ctor_m20E016BF125EB91B4F5E6055F2A639D3F38E01D1 (void);
// 0x00000162 UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass21_0::<DOFade>b__0()
extern void U3CU3Ec__DisplayClass21_0_U3CDOFadeU3Eb__0_m2683CDC969E8BD8C678DEC58DD5FD63F57F63141 (void);
// 0x00000163 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass21_0::<DOFade>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass21_0_U3CDOFadeU3Eb__1_mCFC1424EB14ADA721DACA63749D848761345659C (void);
// 0x00000164 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass22_0::.ctor()
extern void U3CU3Ec__DisplayClass22_0__ctor_mE9442DCF87C8F6389A6A0A61D1105BE83045DF26 (void);
// 0x00000165 System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass22_0::<DOFloat>b__0()
extern void U3CU3Ec__DisplayClass22_0_U3CDOFloatU3Eb__0_mAD602079E9F2B4853A31B8BD4EFFFB344F5C5809 (void);
// 0x00000166 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass22_0::<DOFloat>b__1(System.Single)
extern void U3CU3Ec__DisplayClass22_0_U3CDOFloatU3Eb__1_m0589A7EA48C46D08E55A89EBABF17FCB2429C0CB (void);
// 0x00000167 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass23_0::.ctor()
extern void U3CU3Ec__DisplayClass23_0__ctor_m3D03408645A4AF14B1AC909311CBB96DE3CFEDFF (void);
// 0x00000168 System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass23_0::<DOFloat>b__0()
extern void U3CU3Ec__DisplayClass23_0_U3CDOFloatU3Eb__0_m7C5DE11C01617221026A8257B9273241699B4F61 (void);
// 0x00000169 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass23_0::<DOFloat>b__1(System.Single)
extern void U3CU3Ec__DisplayClass23_0_U3CDOFloatU3Eb__1_mFF0E5B82DCD030309B2FB7AE1D59E8C83D8563AC (void);
// 0x0000016A System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass24_0::.ctor()
extern void U3CU3Ec__DisplayClass24_0__ctor_m3D84B12A9479235642E67A8B23057D53538240EF (void);
// 0x0000016B UnityEngine.Vector2 DG.Tweening.ShortcutExtensions/<>c__DisplayClass24_0::<DOOffset>b__0()
extern void U3CU3Ec__DisplayClass24_0_U3CDOOffsetU3Eb__0_m615CB1C7DF301BF290C15825CF686F172317AA1A (void);
// 0x0000016C System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass24_0::<DOOffset>b__1(UnityEngine.Vector2)
extern void U3CU3Ec__DisplayClass24_0_U3CDOOffsetU3Eb__1_m71F196546178F686FC6EEB0AE494120CBE907F60 (void);
// 0x0000016D System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass25_0::.ctor()
extern void U3CU3Ec__DisplayClass25_0__ctor_m6DC224CBD0A329D0855A1DF42D0F02498015F32B (void);
// 0x0000016E UnityEngine.Vector2 DG.Tweening.ShortcutExtensions/<>c__DisplayClass25_0::<DOOffset>b__0()
extern void U3CU3Ec__DisplayClass25_0_U3CDOOffsetU3Eb__0_m4BB3231BDB040EEE41ACD5FFDE49808AA0A48F68 (void);
// 0x0000016F System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass25_0::<DOOffset>b__1(UnityEngine.Vector2)
extern void U3CU3Ec__DisplayClass25_0_U3CDOOffsetU3Eb__1_mC32BCBA2A6744289CBB4E905AD7AC46CDB11FC84 (void);
// 0x00000170 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass26_0::.ctor()
extern void U3CU3Ec__DisplayClass26_0__ctor_mD06477925B075A1C5F0C20FF4265B246680922BC (void);
// 0x00000171 UnityEngine.Vector2 DG.Tweening.ShortcutExtensions/<>c__DisplayClass26_0::<DOTiling>b__0()
extern void U3CU3Ec__DisplayClass26_0_U3CDOTilingU3Eb__0_m21A9418CA352D5B40AFA7432D49E946CD5FF2839 (void);
// 0x00000172 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass26_0::<DOTiling>b__1(UnityEngine.Vector2)
extern void U3CU3Ec__DisplayClass26_0_U3CDOTilingU3Eb__1_m5FEE43CB4561CFF3BC4333EE3CC6B5AE7FE57DB7 (void);
// 0x00000173 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass27_0::.ctor()
extern void U3CU3Ec__DisplayClass27_0__ctor_m57322655082D93A26CC7BE6652797818B8EAE4BD (void);
// 0x00000174 UnityEngine.Vector2 DG.Tweening.ShortcutExtensions/<>c__DisplayClass27_0::<DOTiling>b__0()
extern void U3CU3Ec__DisplayClass27_0_U3CDOTilingU3Eb__0_m96DF397511A1C88992F2DD301DDF91FE3DA710A7 (void);
// 0x00000175 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass27_0::<DOTiling>b__1(UnityEngine.Vector2)
extern void U3CU3Ec__DisplayClass27_0_U3CDOTilingU3Eb__1_m88831A946F576A9B977CB52AE2006EFD2F3A3801 (void);
// 0x00000176 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass28_0::.ctor()
extern void U3CU3Ec__DisplayClass28_0__ctor_m5AC250F09B06A879670E49F35453952EB61B4CFB (void);
// 0x00000177 UnityEngine.Vector4 DG.Tweening.ShortcutExtensions/<>c__DisplayClass28_0::<DOVector>b__0()
extern void U3CU3Ec__DisplayClass28_0_U3CDOVectorU3Eb__0_m1D25A90B7D94566FE07109C6E35DCD65AC71B8C2 (void);
// 0x00000178 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass28_0::<DOVector>b__1(UnityEngine.Vector4)
extern void U3CU3Ec__DisplayClass28_0_U3CDOVectorU3Eb__1_m3973332F5965D272EB19152E4857A3EE9918AC3C (void);
// 0x00000179 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass29_0::.ctor()
extern void U3CU3Ec__DisplayClass29_0__ctor_m0220C8C3A1421AC6125FF06C18B858862B9ECE30 (void);
// 0x0000017A UnityEngine.Vector4 DG.Tweening.ShortcutExtensions/<>c__DisplayClass29_0::<DOVector>b__0()
extern void U3CU3Ec__DisplayClass29_0_U3CDOVectorU3Eb__0_m91EAA9F1BE517B3A9EC3F713A114384AEBA5B2D6 (void);
// 0x0000017B System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass29_0::<DOVector>b__1(UnityEngine.Vector4)
extern void U3CU3Ec__DisplayClass29_0_U3CDOVectorU3Eb__1_mB8F5C30F9E03F2CE1B2648F6CFD9771BC4A3E393 (void);
// 0x0000017C System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass2_0::.ctor()
extern void U3CU3Ec__DisplayClass2_0__ctor_m77A78F37F4F30D6C82425B3B94EBD31CC0F4D0B3 (void);
// 0x0000017D System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass2_0::<DOFarClipPlane>b__0()
extern void U3CU3Ec__DisplayClass2_0_U3CDOFarClipPlaneU3Eb__0_m2B7E640D986C4DE7127ED311FDD0E981646CE3BF (void);
// 0x0000017E System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass2_0::<DOFarClipPlane>b__1(System.Single)
extern void U3CU3Ec__DisplayClass2_0_U3CDOFarClipPlaneU3Eb__1_m20E5E80C138AF18AF89190EE60552446A1CCE24E (void);
// 0x0000017F System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass30_0::.ctor()
extern void U3CU3Ec__DisplayClass30_0__ctor_mEF82474D90EFC7E8C04D471F27CBD3BDD6BE93AA (void);
// 0x00000180 UnityEngine.Vector2 DG.Tweening.ShortcutExtensions/<>c__DisplayClass30_0::<DOResize>b__0()
extern void U3CU3Ec__DisplayClass30_0_U3CDOResizeU3Eb__0_m6E12D44BD65A871DE203ACD9000AA2993F76C558 (void);
// 0x00000181 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass30_0::<DOResize>b__1(UnityEngine.Vector2)
extern void U3CU3Ec__DisplayClass30_0_U3CDOResizeU3Eb__1_m49CAE0AA6E1D2AADEE7FFC4E2B47D7E85D75ECA1 (void);
// 0x00000182 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass31_0::.ctor()
extern void U3CU3Ec__DisplayClass31_0__ctor_m35DF34BF4DA2381F29755301658A0BB747FA96EB (void);
// 0x00000183 System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass31_0::<DOTime>b__0()
extern void U3CU3Ec__DisplayClass31_0_U3CDOTimeU3Eb__0_m0FD8E6015BEBDE03A9ABAED5983392B2FCB1F4DA (void);
// 0x00000184 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass31_0::<DOTime>b__1(System.Single)
extern void U3CU3Ec__DisplayClass31_0_U3CDOTimeU3Eb__1_m4620873F08672F52FDF5B1FEF817BB5A8D9DEEB2 (void);
// 0x00000185 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass32_0::.ctor()
extern void U3CU3Ec__DisplayClass32_0__ctor_mF99E40BAC9D70A41F9D49D720B24F55F5B514161 (void);
// 0x00000186 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass32_0::<DOMove>b__0()
extern void U3CU3Ec__DisplayClass32_0_U3CDOMoveU3Eb__0_m15E1AAFD0E46945E290D6BE466818E3ED7F7F6E3 (void);
// 0x00000187 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass32_0::<DOMove>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass32_0_U3CDOMoveU3Eb__1_m580B3AFDB23D638C3BA6A8EE6DE4B6420CE8FE0A (void);
// 0x00000188 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass33_0::.ctor()
extern void U3CU3Ec__DisplayClass33_0__ctor_mC307857DB264A833A859C337847ABE7CFE02ABEA (void);
// 0x00000189 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass33_0::<DOMoveX>b__0()
extern void U3CU3Ec__DisplayClass33_0_U3CDOMoveXU3Eb__0_m672EE198EEFD3F11643380B5B4AC8A8BDF6DC8AB (void);
// 0x0000018A System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass33_0::<DOMoveX>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass33_0_U3CDOMoveXU3Eb__1_mF2D746B379D982432C60E81D729F17841522B9EC (void);
// 0x0000018B System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass34_0::.ctor()
extern void U3CU3Ec__DisplayClass34_0__ctor_m9A0DE73F61CAF5080A3B3AEEF584CC91173CDE84 (void);
// 0x0000018C UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass34_0::<DOMoveY>b__0()
extern void U3CU3Ec__DisplayClass34_0_U3CDOMoveYU3Eb__0_mEA5D9AE6240AB3C10DF0754378C02A20C7CB6FA6 (void);
// 0x0000018D System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass34_0::<DOMoveY>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass34_0_U3CDOMoveYU3Eb__1_mE520F974C708A818D0E3B1120A6D61003CE03BB4 (void);
// 0x0000018E System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass35_0::.ctor()
extern void U3CU3Ec__DisplayClass35_0__ctor_mE23897DD201880C93010CF2ED112E4D41184E089 (void);
// 0x0000018F UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass35_0::<DOMoveZ>b__0()
extern void U3CU3Ec__DisplayClass35_0_U3CDOMoveZU3Eb__0_mF31E73B5B0A03025464121D9A883B4E2BFF1936F (void);
// 0x00000190 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass35_0::<DOMoveZ>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass35_0_U3CDOMoveZU3Eb__1_mFC1B457E5544CB48C65CDA2DFA66021E862DBC00 (void);
// 0x00000191 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass36_0::.ctor()
extern void U3CU3Ec__DisplayClass36_0__ctor_mAE58EFC3DD1DD7C1F36380102ECD20D2AA00F1CE (void);
// 0x00000192 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass36_0::<DOLocalMove>b__0()
extern void U3CU3Ec__DisplayClass36_0_U3CDOLocalMoveU3Eb__0_m6AF2104AC0D94DF07EC176F21E354FE77D112485 (void);
// 0x00000193 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass36_0::<DOLocalMove>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass36_0_U3CDOLocalMoveU3Eb__1_m92CAF0D1D04465B4298655176590064880FA0DAC (void);
// 0x00000194 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass37_0::.ctor()
extern void U3CU3Ec__DisplayClass37_0__ctor_mBAC6F62D6E9F705DC745BBD054888DF225AA5424 (void);
// 0x00000195 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass37_0::<DOLocalMoveX>b__0()
extern void U3CU3Ec__DisplayClass37_0_U3CDOLocalMoveXU3Eb__0_mAC2B0F4A96659A604324E423FCE9BA452E814AE7 (void);
// 0x00000196 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass37_0::<DOLocalMoveX>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass37_0_U3CDOLocalMoveXU3Eb__1_m1AEC67CD3D0D9CCDD71D2E279DF41F6D1B4E9C2F (void);
// 0x00000197 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass38_0::.ctor()
extern void U3CU3Ec__DisplayClass38_0__ctor_m6F9BF3A11CB51B09C745E748C09390267086B87F (void);
// 0x00000198 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass38_0::<DOLocalMoveY>b__0()
extern void U3CU3Ec__DisplayClass38_0_U3CDOLocalMoveYU3Eb__0_m2B2E81D5F0161C957DF092462992DDCF6392456A (void);
// 0x00000199 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass38_0::<DOLocalMoveY>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass38_0_U3CDOLocalMoveYU3Eb__1_m8349857541D29FF7CC3330277AEF5BE80DE9C2E3 (void);
// 0x0000019A System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass39_0::.ctor()
extern void U3CU3Ec__DisplayClass39_0__ctor_m29A7A06708DC5A483C9B9BF0C06C692B4261093A (void);
// 0x0000019B UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass39_0::<DOLocalMoveZ>b__0()
extern void U3CU3Ec__DisplayClass39_0_U3CDOLocalMoveZU3Eb__0_m26C942D482D50282BAF7E2F3AD96941F03356DD9 (void);
// 0x0000019C System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass39_0::<DOLocalMoveZ>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass39_0_U3CDOLocalMoveZU3Eb__1_mD37D2AF63E4348522DA92AE358F170830D3D1BF8 (void);
// 0x0000019D System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass3_0::.ctor()
extern void U3CU3Ec__DisplayClass3_0__ctor_mD3DB006B7B8A1A194339F0ECC23CA1E04787EFD9 (void);
// 0x0000019E System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass3_0::<DOFieldOfView>b__0()
extern void U3CU3Ec__DisplayClass3_0_U3CDOFieldOfViewU3Eb__0_m15FBEF870514F3B7A0E107807AA4CC626C0A5B91 (void);
// 0x0000019F System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass3_0::<DOFieldOfView>b__1(System.Single)
extern void U3CU3Ec__DisplayClass3_0_U3CDOFieldOfViewU3Eb__1_mE0B6B23B688947A0F131A1CC31CE19D153052990 (void);
// 0x000001A0 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass40_0::.ctor()
extern void U3CU3Ec__DisplayClass40_0__ctor_m8E6EAA117C9611630A2AF2B34959429DA441AF03 (void);
// 0x000001A1 UnityEngine.Quaternion DG.Tweening.ShortcutExtensions/<>c__DisplayClass40_0::<DORotate>b__0()
extern void U3CU3Ec__DisplayClass40_0_U3CDORotateU3Eb__0_m89100800A2C8745BE1323653608E893652183B7E (void);
// 0x000001A2 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass40_0::<DORotate>b__1(UnityEngine.Quaternion)
extern void U3CU3Ec__DisplayClass40_0_U3CDORotateU3Eb__1_mE57D43D5CBC331CCB313FE97DF304962B4B61272 (void);
// 0x000001A3 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass41_0::.ctor()
extern void U3CU3Ec__DisplayClass41_0__ctor_m7211D7D9CFE52BDAC307D7597FDE8827E2EE72BF (void);
// 0x000001A4 UnityEngine.Quaternion DG.Tweening.ShortcutExtensions/<>c__DisplayClass41_0::<DORotateQuaternion>b__0()
extern void U3CU3Ec__DisplayClass41_0_U3CDORotateQuaternionU3Eb__0_m11D48D2D5C30864291E106B06B3325AFE50FD6FE (void);
// 0x000001A5 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass41_0::<DORotateQuaternion>b__1(UnityEngine.Quaternion)
extern void U3CU3Ec__DisplayClass41_0_U3CDORotateQuaternionU3Eb__1_m4656937AA9BF166347E1732EFFF7CFC00AFE99C6 (void);
// 0x000001A6 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass42_0::.ctor()
extern void U3CU3Ec__DisplayClass42_0__ctor_m894ECDDCD8FA39381D104A78723ED0D3B0FEFA24 (void);
// 0x000001A7 UnityEngine.Quaternion DG.Tweening.ShortcutExtensions/<>c__DisplayClass42_0::<DOLocalRotate>b__0()
extern void U3CU3Ec__DisplayClass42_0_U3CDOLocalRotateU3Eb__0_m60162FBB56ACF20CFD036154E08DB13B16C3CE47 (void);
// 0x000001A8 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass42_0::<DOLocalRotate>b__1(UnityEngine.Quaternion)
extern void U3CU3Ec__DisplayClass42_0_U3CDOLocalRotateU3Eb__1_m8DBE8031D1E7627BE19794A3F97744BA72D358D0 (void);
// 0x000001A9 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass43_0::.ctor()
extern void U3CU3Ec__DisplayClass43_0__ctor_mA87CB75673A8966542861C9F91A3AFCCC9C1A5B2 (void);
// 0x000001AA UnityEngine.Quaternion DG.Tweening.ShortcutExtensions/<>c__DisplayClass43_0::<DOLocalRotateQuaternion>b__0()
extern void U3CU3Ec__DisplayClass43_0_U3CDOLocalRotateQuaternionU3Eb__0_mE8288A54BEBE4DBA9C4B67329F1F706079522125 (void);
// 0x000001AB System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass43_0::<DOLocalRotateQuaternion>b__1(UnityEngine.Quaternion)
extern void U3CU3Ec__DisplayClass43_0_U3CDOLocalRotateQuaternionU3Eb__1_m3C8277B8FB3C947800D0A4559F751C052C164B37 (void);
// 0x000001AC System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass44_0::.ctor()
extern void U3CU3Ec__DisplayClass44_0__ctor_mC24979AC46FE287403167265ABFD5C08E7C62D33 (void);
// 0x000001AD UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass44_0::<DOScale>b__0()
extern void U3CU3Ec__DisplayClass44_0_U3CDOScaleU3Eb__0_m0A1B1E2996430F9455E9FF81C312223B3C46CE35 (void);
// 0x000001AE System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass44_0::<DOScale>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass44_0_U3CDOScaleU3Eb__1_m95D31585339C2303A33F37C20E75697B13FC0FF4 (void);
// 0x000001AF System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass45_0::.ctor()
extern void U3CU3Ec__DisplayClass45_0__ctor_mFE0E60DCA44E3246F0DB335E19E0AF2E402534AB (void);
// 0x000001B0 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass45_0::<DOScale>b__0()
extern void U3CU3Ec__DisplayClass45_0_U3CDOScaleU3Eb__0_mA019F344C52AAF2983264C13E2927C86587FF328 (void);
// 0x000001B1 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass45_0::<DOScale>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass45_0_U3CDOScaleU3Eb__1_mFEB696A329BC4E973789918A21DAD4D410DC0C7C (void);
// 0x000001B2 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass46_0::.ctor()
extern void U3CU3Ec__DisplayClass46_0__ctor_m9121E5D8A8711B02BE3270A41620C9BAC279F3D3 (void);
// 0x000001B3 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass46_0::<DOScaleX>b__0()
extern void U3CU3Ec__DisplayClass46_0_U3CDOScaleXU3Eb__0_mAC5538D08B0057A16B011DDBC19D439ED4F6B39F (void);
// 0x000001B4 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass46_0::<DOScaleX>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass46_0_U3CDOScaleXU3Eb__1_mFA80DCF42E0225F9C4E0B5CA43E5669F1358E619 (void);
// 0x000001B5 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass47_0::.ctor()
extern void U3CU3Ec__DisplayClass47_0__ctor_mFF8096FA13023A99D7F23913A1E04040C4C3A6B4 (void);
// 0x000001B6 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass47_0::<DOScaleY>b__0()
extern void U3CU3Ec__DisplayClass47_0_U3CDOScaleYU3Eb__0_m19690B99AF76F16C69662374F92BE4DB91603CF7 (void);
// 0x000001B7 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass47_0::<DOScaleY>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass47_0_U3CDOScaleYU3Eb__1_m4E713488A1664A212A158C0406179AFEE46B9E66 (void);
// 0x000001B8 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass48_0::.ctor()
extern void U3CU3Ec__DisplayClass48_0__ctor_m9A5E1D3195B205418263C7243DF6A4B0ECEE41FE (void);
// 0x000001B9 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass48_0::<DOScaleZ>b__0()
extern void U3CU3Ec__DisplayClass48_0_U3CDOScaleZU3Eb__0_mBA1EEFF4583ACD3BD9E60BCAA07DAFACA474454D (void);
// 0x000001BA System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass48_0::<DOScaleZ>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass48_0_U3CDOScaleZU3Eb__1_m209AF9BAFB6DB10D66CEA60482CF36979896DA58 (void);
// 0x000001BB System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass4_0::.ctor()
extern void U3CU3Ec__DisplayClass4_0__ctor_m77E54345DFA70ADC4D7EC7ABF871C8A4AD64B233 (void);
// 0x000001BC System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass4_0::<DONearClipPlane>b__0()
extern void U3CU3Ec__DisplayClass4_0_U3CDONearClipPlaneU3Eb__0_m873F66541037293275E7E80CE866ECC57078F716 (void);
// 0x000001BD System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass4_0::<DONearClipPlane>b__1(System.Single)
extern void U3CU3Ec__DisplayClass4_0_U3CDONearClipPlaneU3Eb__1_m0600DE0E9328530D86EC4FCB0412C549EC89BE7A (void);
// 0x000001BE System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass51_0::.ctor()
extern void U3CU3Ec__DisplayClass51_0__ctor_m6993C5B08436C61812CD70E98322A42665AC82C9 (void);
// 0x000001BF UnityEngine.Quaternion DG.Tweening.ShortcutExtensions/<>c__DisplayClass51_0::<LookAt>b__0()
extern void U3CU3Ec__DisplayClass51_0_U3CLookAtU3Eb__0_mFFFB688997750C6AC2E360D18E6520A7CBD3593A (void);
// 0x000001C0 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass51_0::<LookAt>b__1(UnityEngine.Quaternion)
extern void U3CU3Ec__DisplayClass51_0_U3CLookAtU3Eb__1_mFA385489E528D88DC72474BBB4D6ECE26A8D0915 (void);
// 0x000001C1 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass52_0::.ctor()
extern void U3CU3Ec__DisplayClass52_0__ctor_mF34A0E0114DC164E1A106453AA3A5B83EA8ACB6B (void);
// 0x000001C2 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass52_0::<DOPunchPosition>b__0()
extern void U3CU3Ec__DisplayClass52_0_U3CDOPunchPositionU3Eb__0_m81C33B214128A250A39E1906E632239592E04F6B (void);
// 0x000001C3 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass52_0::<DOPunchPosition>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass52_0_U3CDOPunchPositionU3Eb__1_m024A958172481CA5FA792FEB41467797B3C9B6EA (void);
// 0x000001C4 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass53_0::.ctor()
extern void U3CU3Ec__DisplayClass53_0__ctor_m22CC6EB24492FBDB93D8B16E626B49D8FDDB1DEB (void);
// 0x000001C5 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass53_0::<DOPunchScale>b__0()
extern void U3CU3Ec__DisplayClass53_0_U3CDOPunchScaleU3Eb__0_mBE325AAB304A26B2715A6395CDD180A6C8A3600C (void);
// 0x000001C6 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass53_0::<DOPunchScale>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass53_0_U3CDOPunchScaleU3Eb__1_m792A395062F71C906D9048E0D283B66B2230BFFF (void);
// 0x000001C7 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass54_0::.ctor()
extern void U3CU3Ec__DisplayClass54_0__ctor_m8487623AB289AE5A29AAE68FBD16094EBED4619A (void);
// 0x000001C8 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass54_0::<DOPunchRotation>b__0()
extern void U3CU3Ec__DisplayClass54_0_U3CDOPunchRotationU3Eb__0_mBB19DB5560F41C19F8B73FCF54924309F34BBA57 (void);
// 0x000001C9 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass54_0::<DOPunchRotation>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass54_0_U3CDOPunchRotationU3Eb__1_m5371FF76477A13C88F79E83F00CD9D66F12E528C (void);
// 0x000001CA System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass55_0::.ctor()
extern void U3CU3Ec__DisplayClass55_0__ctor_m97A8CADF7B9EEE52B623FF51986470FE315B8F2B (void);
// 0x000001CB UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass55_0::<DOShakePosition>b__0()
extern void U3CU3Ec__DisplayClass55_0_U3CDOShakePositionU3Eb__0_mE10A40D355957ADCEFA12B43BE0EBE540DE94FF9 (void);
// 0x000001CC System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass55_0::<DOShakePosition>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass55_0_U3CDOShakePositionU3Eb__1_m7092F461DE9ADE4FADC6EAF78FCED8244D01B129 (void);
// 0x000001CD System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass56_0::.ctor()
extern void U3CU3Ec__DisplayClass56_0__ctor_mE3F409583C85BFFAD329D6657AE57326CEA380B8 (void);
// 0x000001CE UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass56_0::<DOShakePosition>b__0()
extern void U3CU3Ec__DisplayClass56_0_U3CDOShakePositionU3Eb__0_mF2B79D1D2A52FEE77DBEF0D3752814719E7424EB (void);
// 0x000001CF System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass56_0::<DOShakePosition>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass56_0_U3CDOShakePositionU3Eb__1_m5EA33E40BBAB50F4E27DAC7CDD695724E7C44354 (void);
// 0x000001D0 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass57_0::.ctor()
extern void U3CU3Ec__DisplayClass57_0__ctor_mF231F02D3A9995FC73737A6B2D5A9B607F22F03A (void);
// 0x000001D1 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass57_0::<DOShakeRotation>b__0()
extern void U3CU3Ec__DisplayClass57_0_U3CDOShakeRotationU3Eb__0_m194541436AE87A8B7EFA3A39A6FC71C52DA24564 (void);
// 0x000001D2 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass57_0::<DOShakeRotation>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass57_0_U3CDOShakeRotationU3Eb__1_m196FE3822A094C80CB3C6090549C7EC66D1BA11B (void);
// 0x000001D3 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass58_0::.ctor()
extern void U3CU3Ec__DisplayClass58_0__ctor_m53D84AC3C9E88A1B5850B1D7B2CF4A782504B6CB (void);
// 0x000001D4 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass58_0::<DOShakeRotation>b__0()
extern void U3CU3Ec__DisplayClass58_0_U3CDOShakeRotationU3Eb__0_m2FD3E368BC52F4035C332895913857CCACF526DF (void);
// 0x000001D5 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass58_0::<DOShakeRotation>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass58_0_U3CDOShakeRotationU3Eb__1_mD40137CBF22DEAB514CF358112190DBB4E6EBC86 (void);
// 0x000001D6 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass59_0::.ctor()
extern void U3CU3Ec__DisplayClass59_0__ctor_mFB9FDA8361D047ECEAA4AE0A80216FAC13FB3367 (void);
// 0x000001D7 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass59_0::<DOShakeScale>b__0()
extern void U3CU3Ec__DisplayClass59_0_U3CDOShakeScaleU3Eb__0_mC43F13106D5AFEDD6F71D7D4BC7A245FB53FB2ED (void);
// 0x000001D8 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass59_0::<DOShakeScale>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass59_0_U3CDOShakeScaleU3Eb__1_m8BC5E5A93F052CCDEFDA7A96927D471FB36AD4F4 (void);
// 0x000001D9 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass5_0::.ctor()
extern void U3CU3Ec__DisplayClass5_0__ctor_m7783B6BCA09BDF4543ADF7F62889ABBA94DB3A3A (void);
// 0x000001DA System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass5_0::<DOOrthoSize>b__0()
extern void U3CU3Ec__DisplayClass5_0_U3CDOOrthoSizeU3Eb__0_m034E070A3B9354C6D1D5DE8A1B26FCF53208F73D (void);
// 0x000001DB System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass5_0::<DOOrthoSize>b__1(System.Single)
extern void U3CU3Ec__DisplayClass5_0_U3CDOOrthoSizeU3Eb__1_m2AC152D033DE5ACFE3232261EF59650B917174AE (void);
// 0x000001DC System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass60_0::.ctor()
extern void U3CU3Ec__DisplayClass60_0__ctor_mBD17ABB100CA70BA08DE77CD4E7EFD2AB391F9E7 (void);
// 0x000001DD UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass60_0::<DOShakeScale>b__0()
extern void U3CU3Ec__DisplayClass60_0_U3CDOShakeScaleU3Eb__0_m8563BAF05E9A2C089BB4D13E78A8B16B3D88E36C (void);
// 0x000001DE System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass60_0::<DOShakeScale>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass60_0_U3CDOShakeScaleU3Eb__1_mC249A37B407D2424DF1B0008589DAE10769FAE32 (void);
// 0x000001DF System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass61_0::.ctor()
extern void U3CU3Ec__DisplayClass61_0__ctor_mF0DCB007CD3DEB4B3862304576FE084BCD3EEC88 (void);
// 0x000001E0 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass61_0::<DOJump>b__0()
extern void U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__0_mBCA8B1ACA7CD1328FF640C0032E06EF9EB12892E (void);
// 0x000001E1 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass61_0::<DOJump>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__1_m65426AE26A828CEB2A9F34622F437EB966DFF7F9 (void);
// 0x000001E2 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass61_0::<DOJump>b__2()
extern void U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__2_m6F01E6D9A017E490E275EE3E6F2ED6865041625F (void);
// 0x000001E3 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass61_0::<DOJump>b__3()
extern void U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__3_m6EEE85C49B210817225EFABD979308B70E3D3DAB (void);
// 0x000001E4 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass61_0::<DOJump>b__4(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__4_m086B019BAE346B800762DCD8FB8A3CD7FB25847A (void);
// 0x000001E5 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass61_0::<DOJump>b__5()
extern void U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__5_m95E816FF3C25EA02814904FF4133E1BBC58D09D8 (void);
// 0x000001E6 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass61_0::<DOJump>b__6(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__6_mE44249A4ECAC427275C4FE5770DD3A0AE053186F (void);
// 0x000001E7 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass61_0::<DOJump>b__7()
extern void U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__7_mD6F5856616519F9170A14259EBE6CBA7AB625C4A (void);
// 0x000001E8 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass62_0::.ctor()
extern void U3CU3Ec__DisplayClass62_0__ctor_m721B008BA9D9EDDA666EA690FDA87621599EFA9A (void);
// 0x000001E9 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass62_0::<DOLocalJump>b__0()
extern void U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__0_m20C855D3078EC01E8392F76CF463C5C0CF6C9491 (void);
// 0x000001EA System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass62_0::<DOLocalJump>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__1_mC866579560B3053A7C64BAAA82FA71BBBB7DCDEC (void);
// 0x000001EB System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass62_0::<DOLocalJump>b__2()
extern void U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__2_mC2A0D20A6E2AC20AF1A2AA66A02F988027155BAA (void);
// 0x000001EC UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass62_0::<DOLocalJump>b__3()
extern void U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__3_m6DCD34A55C87B354D1FB34D195338AF2F2CB2EDE (void);
// 0x000001ED System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass62_0::<DOLocalJump>b__4(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__4_mCB64533DDBD812D943FC3134C4F3296000BCEA36 (void);
// 0x000001EE UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass62_0::<DOLocalJump>b__5()
extern void U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__5_m60C754E97231121E8E9D81E5E4E3DB2DA2A39B22 (void);
// 0x000001EF System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass62_0::<DOLocalJump>b__6(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__6_m419506042A2BDECDDDD27B322891EAF108C20642 (void);
// 0x000001F0 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass62_0::<DOLocalJump>b__7()
extern void U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__7_mD0E4918DCCCC6C8B36424E882E9214E7B675CE18 (void);
// 0x000001F1 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass63_0::.ctor()
extern void U3CU3Ec__DisplayClass63_0__ctor_mB96BCCBA4CD18893BF3FE33921D1A1349C579F9A (void);
// 0x000001F2 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass63_0::<DOPath>b__0()
extern void U3CU3Ec__DisplayClass63_0_U3CDOPathU3Eb__0_m8D3440E5FE89819FD8DCDAFCFE7FC1AAFCEB92DE (void);
// 0x000001F3 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass63_0::<DOPath>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass63_0_U3CDOPathU3Eb__1_m87BE9FA5B9FA36647B1AB9EAC06C498131DA3077 (void);
// 0x000001F4 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass64_0::.ctor()
extern void U3CU3Ec__DisplayClass64_0__ctor_m797499323817E936BD2292B270BFC32D447AD78B (void);
// 0x000001F5 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass64_0::<DOLocalPath>b__0()
extern void U3CU3Ec__DisplayClass64_0_U3CDOLocalPathU3Eb__0_mDE31EA6776321007A3438FC8F6A329C3F1DDCC88 (void);
// 0x000001F6 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass64_0::<DOLocalPath>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass64_0_U3CDOLocalPathU3Eb__1_mD3A0960DF5B0665A23903CAA811D3FF0703B2D10 (void);
// 0x000001F7 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass65_0::.ctor()
extern void U3CU3Ec__DisplayClass65_0__ctor_m099D2648CB7647055F29AA9353217918A0A26C52 (void);
// 0x000001F8 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass65_0::<DOPath>b__0()
extern void U3CU3Ec__DisplayClass65_0_U3CDOPathU3Eb__0_m116A470BA28E49FB3E1429D5661C0920AE8D06FC (void);
// 0x000001F9 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass65_0::<DOPath>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass65_0_U3CDOPathU3Eb__1_m936610184D3C1E8F0105886D4AF3BE35438B9EB0 (void);
// 0x000001FA System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass66_0::.ctor()
extern void U3CU3Ec__DisplayClass66_0__ctor_m830CCD8E00AFFE9A909C331B429BF8E61EA32E1D (void);
// 0x000001FB UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass66_0::<DOLocalPath>b__0()
extern void U3CU3Ec__DisplayClass66_0_U3CDOLocalPathU3Eb__0_m5B791E9992D1668527B86298721253FC7BAC54E5 (void);
// 0x000001FC System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass66_0::<DOLocalPath>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass66_0_U3CDOLocalPathU3Eb__1_m1180D44A1229B978D6DC4519B895FD07506D0F9A (void);
// 0x000001FD System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass67_0::.ctor()
extern void U3CU3Ec__DisplayClass67_0__ctor_mC6B7195B1F7134B7B48316FBDBD8E0B276A8B8C1 (void);
// 0x000001FE System.Single DG.Tweening.ShortcutExtensions/<>c__DisplayClass67_0::<DOTimeScale>b__0()
extern void U3CU3Ec__DisplayClass67_0_U3CDOTimeScaleU3Eb__0_mBCD23BC1C3F1F4901406334A1D97E0FE6B01E85D (void);
// 0x000001FF System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass67_0::<DOTimeScale>b__1(System.Single)
extern void U3CU3Ec__DisplayClass67_0_U3CDOTimeScaleU3Eb__1_mBA9D6C0032563A2910CC5D911E73796D0366D85F (void);
// 0x00000200 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass68_0::.ctor()
extern void U3CU3Ec__DisplayClass68_0__ctor_mD5D28BC610D7B5B492B9FBFA29FD8C5436D91678 (void);
// 0x00000201 UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass68_0::<DOBlendableColor>b__0()
extern void U3CU3Ec__DisplayClass68_0_U3CDOBlendableColorU3Eb__0_m6555CECA55A46E1A39D7B69058610E99BBB58332 (void);
// 0x00000202 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass68_0::<DOBlendableColor>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass68_0_U3CDOBlendableColorU3Eb__1_mC9E004DA8FBA7AD74F2963BD88A280A3B9A76995 (void);
// 0x00000203 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass69_0::.ctor()
extern void U3CU3Ec__DisplayClass69_0__ctor_m336ACFBD10BE165468D57CCA35DE58DA5EA10CDF (void);
// 0x00000204 UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass69_0::<DOBlendableColor>b__0()
extern void U3CU3Ec__DisplayClass69_0_U3CDOBlendableColorU3Eb__0_mC9DCA4181D48AA4BE1FB6993E74A305ACAAF82A5 (void);
// 0x00000205 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass69_0::<DOBlendableColor>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass69_0_U3CDOBlendableColorU3Eb__1_m02AF9B2CE735A0DC43899353BE7721AAF90B9C7F (void);
// 0x00000206 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass6_0::.ctor()
extern void U3CU3Ec__DisplayClass6_0__ctor_m3EF7021A9BC9E02A6D31DAECC153759EF98F1745 (void);
// 0x00000207 UnityEngine.Rect DG.Tweening.ShortcutExtensions/<>c__DisplayClass6_0::<DOPixelRect>b__0()
extern void U3CU3Ec__DisplayClass6_0_U3CDOPixelRectU3Eb__0_m056478689B03F8E3C264EF28603426A02853E288 (void);
// 0x00000208 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass6_0::<DOPixelRect>b__1(UnityEngine.Rect)
extern void U3CU3Ec__DisplayClass6_0_U3CDOPixelRectU3Eb__1_m5FD55DBC4B5BD7120C6D09D6155A91E15FAE6BE0 (void);
// 0x00000209 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass70_0::.ctor()
extern void U3CU3Ec__DisplayClass70_0__ctor_mBD371290CDFD8769612574B932E398F4E1788CFB (void);
// 0x0000020A UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass70_0::<DOBlendableColor>b__0()
extern void U3CU3Ec__DisplayClass70_0_U3CDOBlendableColorU3Eb__0_m231E5EBC1ACD38F97DEFC1A473505618AB21FFE3 (void);
// 0x0000020B System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass70_0::<DOBlendableColor>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass70_0_U3CDOBlendableColorU3Eb__1_mB72CD2B2475C5415579A7FF83EA76EE554307B82 (void);
// 0x0000020C System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass71_0::.ctor()
extern void U3CU3Ec__DisplayClass71_0__ctor_m81538C42748C81D4E0F0D8894692851055750B90 (void);
// 0x0000020D UnityEngine.Color DG.Tweening.ShortcutExtensions/<>c__DisplayClass71_0::<DOBlendableColor>b__0()
extern void U3CU3Ec__DisplayClass71_0_U3CDOBlendableColorU3Eb__0_m931F78F97E359A760009DF99D57A80F4FF6FC8C1 (void);
// 0x0000020E System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass71_0::<DOBlendableColor>b__1(UnityEngine.Color)
extern void U3CU3Ec__DisplayClass71_0_U3CDOBlendableColorU3Eb__1_m4DAED7A1E30DB4DF1528B4E22F23F356B556359D (void);
// 0x0000020F System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass72_0::.ctor()
extern void U3CU3Ec__DisplayClass72_0__ctor_m1D7F378D80D7D3576BD72FCC807B325894ED0915 (void);
// 0x00000210 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass72_0::<DOBlendableMoveBy>b__0()
extern void U3CU3Ec__DisplayClass72_0_U3CDOBlendableMoveByU3Eb__0_mD1D39ED6CF0DCA3C9EFDEE04FA17C9CB848B3861 (void);
// 0x00000211 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass72_0::<DOBlendableMoveBy>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass72_0_U3CDOBlendableMoveByU3Eb__1_m69692A8D35EA77D48059CC93EBD49BF21DA590C1 (void);
// 0x00000212 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass73_0::.ctor()
extern void U3CU3Ec__DisplayClass73_0__ctor_m6649AB477E03767C1021955E5ABBE7DECC87C3F2 (void);
// 0x00000213 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass73_0::<DOBlendableLocalMoveBy>b__0()
extern void U3CU3Ec__DisplayClass73_0_U3CDOBlendableLocalMoveByU3Eb__0_m1AC8C73A45605BB87EB1846A614ECE6B8F038918 (void);
// 0x00000214 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass73_0::<DOBlendableLocalMoveBy>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass73_0_U3CDOBlendableLocalMoveByU3Eb__1_m9789CF263903DA2070FE21F6E11095BC2CE5D511 (void);
// 0x00000215 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass74_0::.ctor()
extern void U3CU3Ec__DisplayClass74_0__ctor_m99F7AC61317AA00B2F6B13D3851F93635F755689 (void);
// 0x00000216 UnityEngine.Quaternion DG.Tweening.ShortcutExtensions/<>c__DisplayClass74_0::<DOBlendableRotateBy>b__0()
extern void U3CU3Ec__DisplayClass74_0_U3CDOBlendableRotateByU3Eb__0_mE40A0FC0BBCBC29D75C78B6FE25EDD674A7356F6 (void);
// 0x00000217 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass74_0::<DOBlendableRotateBy>b__1(UnityEngine.Quaternion)
extern void U3CU3Ec__DisplayClass74_0_U3CDOBlendableRotateByU3Eb__1_m3805AB400A575DF0161E0C31CF64A2B9A791E83E (void);
// 0x00000218 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass75_0::.ctor()
extern void U3CU3Ec__DisplayClass75_0__ctor_m80AB38B540DBE1AC01FFD7811E4DD3160393336E (void);
// 0x00000219 UnityEngine.Quaternion DG.Tweening.ShortcutExtensions/<>c__DisplayClass75_0::<DOBlendableLocalRotateBy>b__0()
extern void U3CU3Ec__DisplayClass75_0_U3CDOBlendableLocalRotateByU3Eb__0_m7881708F320253DCE3753A16A4CD7FE2C7B10C8A (void);
// 0x0000021A System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass75_0::<DOBlendableLocalRotateBy>b__1(UnityEngine.Quaternion)
extern void U3CU3Ec__DisplayClass75_0_U3CDOBlendableLocalRotateByU3Eb__1_mA7F97873E40347E60AD46B7820CCCE0AE53FA5CA (void);
// 0x0000021B System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass76_0::.ctor()
extern void U3CU3Ec__DisplayClass76_0__ctor_m22A4DFBA23997056B27645B80F8984E145C70DCB (void);
// 0x0000021C UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass76_0::<DOBlendablePunchRotation>b__0()
extern void U3CU3Ec__DisplayClass76_0_U3CDOBlendablePunchRotationU3Eb__0_m40B5F65FF6749A3C2AB19513960836BB3A476919 (void);
// 0x0000021D System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass76_0::<DOBlendablePunchRotation>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass76_0_U3CDOBlendablePunchRotationU3Eb__1_m953315BF07BBFAC9F0C752A06114F448CE67FAE6 (void);
// 0x0000021E System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass77_0::.ctor()
extern void U3CU3Ec__DisplayClass77_0__ctor_mBA0040576F8B3B6452D8527B1B42064443690136 (void);
// 0x0000021F UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass77_0::<DOBlendableScaleBy>b__0()
extern void U3CU3Ec__DisplayClass77_0_U3CDOBlendableScaleByU3Eb__0_m17EADF977475A1EEF768FE70AC3D97EF590DD189 (void);
// 0x00000220 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass77_0::<DOBlendableScaleBy>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass77_0_U3CDOBlendableScaleByU3Eb__1_m30828F5AE0730C435901F5646137B19E1EB29140 (void);
// 0x00000221 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass7_0::.ctor()
extern void U3CU3Ec__DisplayClass7_0__ctor_m99FED5DE1D5FCE5A033A41690EEE45C7B02436FA (void);
// 0x00000222 UnityEngine.Rect DG.Tweening.ShortcutExtensions/<>c__DisplayClass7_0::<DORect>b__0()
extern void U3CU3Ec__DisplayClass7_0_U3CDORectU3Eb__0_m323992D6C4F67E9BCDAC970E9132420617355BEC (void);
// 0x00000223 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass7_0::<DORect>b__1(UnityEngine.Rect)
extern void U3CU3Ec__DisplayClass7_0_U3CDORectU3Eb__1_m326FD99ABCA6F25C8F9BC3438FFA62CC05A6813E (void);
// 0x00000224 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass8_0::.ctor()
extern void U3CU3Ec__DisplayClass8_0__ctor_mCB1B4229E202920D84CC031B99C3E747B73471C9 (void);
// 0x00000225 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass8_0::<DOShakePosition>b__0()
extern void U3CU3Ec__DisplayClass8_0_U3CDOShakePositionU3Eb__0_mF42E5FF0D71942D542EE5BBFC86A1F6520BAED6A (void);
// 0x00000226 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass8_0::<DOShakePosition>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass8_0_U3CDOShakePositionU3Eb__1_m48B9C988EB41A14206B8C9EE4ADF8C8A2F1B634A (void);
// 0x00000227 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass9_0::.ctor()
extern void U3CU3Ec__DisplayClass9_0__ctor_m69682F27FB68AF9E238ECAB74B1CB15F7FD25543 (void);
// 0x00000228 UnityEngine.Vector3 DG.Tweening.ShortcutExtensions/<>c__DisplayClass9_0::<DOShakePosition>b__0()
extern void U3CU3Ec__DisplayClass9_0_U3CDOShakePositionU3Eb__0_m904DA7104A37C0D2A2453C411C3E389B4F0D75DD (void);
// 0x00000229 System.Void DG.Tweening.ShortcutExtensions/<>c__DisplayClass9_0::<DOShakePosition>b__1(UnityEngine.Vector3)
extern void U3CU3Ec__DisplayClass9_0_U3CDOShakePositionU3Eb__1_m9888C9196873E5D534AFDBAC95A7532022BA9CB8 (void);
// 0x0000022A System.Void DG.Tweening.TweenParams::.ctor()
extern void TweenParams__ctor_m5F3F28BA299D184563D1AF7B8B19DBF186915F2E (void);
// 0x0000022B DG.Tweening.TweenParams DG.Tweening.TweenParams::Clear()
extern void TweenParams_Clear_mEBB99A92738E51EB5018540C95844F230DD3A13F (void);
// 0x0000022C DG.Tweening.TweenParams DG.Tweening.TweenParams::SetAutoKill(System.Boolean)
extern void TweenParams_SetAutoKill_mA0D39BA65BDFFC88C132C208730B417A863ADDCC (void);
// 0x0000022D DG.Tweening.TweenParams DG.Tweening.TweenParams::SetId(System.Object)
extern void TweenParams_SetId_m173A81D2C2429A35976E6F0588EE4082D13D8481 (void);
// 0x0000022E DG.Tweening.TweenParams DG.Tweening.TweenParams::SetId(System.String)
extern void TweenParams_SetId_m753850D312FF6D2E254CDAE73D402ABBD3251EAC (void);
// 0x0000022F DG.Tweening.TweenParams DG.Tweening.TweenParams::SetId(System.Int32)
extern void TweenParams_SetId_m87F9EFA8D2446BCEC1149317F2F7B02678FC3935 (void);
// 0x00000230 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetTarget(System.Object)
extern void TweenParams_SetTarget_m4AE8DB5FB9DAE4F911BA9405B81EA6CE6C3B6FB8 (void);
// 0x00000231 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetLoops(System.Int32,System.Nullable`1<DG.Tweening.LoopType>)
extern void TweenParams_SetLoops_m8191038E50515B7171130F5A63B58D49A4AE751F (void);
// 0x00000232 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetEase(DG.Tweening.Ease,System.Nullable`1<System.Single>,System.Nullable`1<System.Single>)
extern void TweenParams_SetEase_m595B5405EAB2EF60C8B08934202922724323F568 (void);
// 0x00000233 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetEase(UnityEngine.AnimationCurve)
extern void TweenParams_SetEase_m5919B6168471BD922DA020F3DF62D1759B42EEA3 (void);
// 0x00000234 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetEase(DG.Tweening.EaseFunction)
extern void TweenParams_SetEase_mE1CCC1AC51D40BD5AF0AEE467DA96AF395CD8999 (void);
// 0x00000235 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetRecyclable(System.Boolean)
extern void TweenParams_SetRecyclable_m46EC952EE565A3E13C711916ABEFCC36F42FEA6C (void);
// 0x00000236 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetUpdate(System.Boolean)
extern void TweenParams_SetUpdate_m1DE5A39760E44F974A13150B9D22A43BA538BE85 (void);
// 0x00000237 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetUpdate(DG.Tweening.UpdateType,System.Boolean)
extern void TweenParams_SetUpdate_m05ECFCEA35FE949F3E78EFAAEB3295E44691E5D2 (void);
// 0x00000238 DG.Tweening.TweenParams DG.Tweening.TweenParams::OnStart(DG.Tweening.TweenCallback)
extern void TweenParams_OnStart_m50048410DF94AEEDF3549DB9904080E56C572030 (void);
// 0x00000239 DG.Tweening.TweenParams DG.Tweening.TweenParams::OnPlay(DG.Tweening.TweenCallback)
extern void TweenParams_OnPlay_mFC826F785D143068EE0FA0E37128B639A63B43BA (void);
// 0x0000023A DG.Tweening.TweenParams DG.Tweening.TweenParams::OnRewind(DG.Tweening.TweenCallback)
extern void TweenParams_OnRewind_m5A37A1096AFF82082F244F8716FB5E87155F60CF (void);
// 0x0000023B DG.Tweening.TweenParams DG.Tweening.TweenParams::OnUpdate(DG.Tweening.TweenCallback)
extern void TweenParams_OnUpdate_m95CD680B0CA15FFD9098DF88DE81310355DACCE8 (void);
// 0x0000023C DG.Tweening.TweenParams DG.Tweening.TweenParams::OnStepComplete(DG.Tweening.TweenCallback)
extern void TweenParams_OnStepComplete_m2BB5EA797AFB686F45AADDBBD3A986B7C511476C (void);
// 0x0000023D DG.Tweening.TweenParams DG.Tweening.TweenParams::OnComplete(DG.Tweening.TweenCallback)
extern void TweenParams_OnComplete_mE829825D5F74322D13F2B6C4EBF59A8FF2A06191 (void);
// 0x0000023E DG.Tweening.TweenParams DG.Tweening.TweenParams::OnKill(DG.Tweening.TweenCallback)
extern void TweenParams_OnKill_m1853F941D8B794CF6262EC0FEA608FFF7D4E8445 (void);
// 0x0000023F DG.Tweening.TweenParams DG.Tweening.TweenParams::OnWaypointChange(DG.Tweening.TweenCallback`1<System.Int32>)
extern void TweenParams_OnWaypointChange_m24647E70B50CB31A9D37DF7AA2DD42AAAED34492 (void);
// 0x00000240 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetDelay(System.Single)
extern void TweenParams_SetDelay_m542E64992644B47D6EE4592F4B9E0F2B43ED6988 (void);
// 0x00000241 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetRelative(System.Boolean)
extern void TweenParams_SetRelative_mA5490896AD402F9563BAC58D14F66A66633C3B68 (void);
// 0x00000242 DG.Tweening.TweenParams DG.Tweening.TweenParams::SetSpeedBased(System.Boolean)
extern void TweenParams_SetSpeedBased_m7984A5F846AC411A530E76ACE13A29C2F49BFA10 (void);
// 0x00000243 System.Void DG.Tweening.TweenParams::.cctor()
extern void TweenParams__cctor_m489645841D9F80D199F4F1E3891CFEEB31B45045 (void);
// 0x00000244 T DG.Tweening.TweenSettingsExtensions::SetAutoKill(T)
// 0x00000245 T DG.Tweening.TweenSettingsExtensions::SetAutoKill(T,System.Boolean)
// 0x00000246 T DG.Tweening.TweenSettingsExtensions::SetId(T,System.Object)
// 0x00000247 T DG.Tweening.TweenSettingsExtensions::SetId(T,System.String)
// 0x00000248 T DG.Tweening.TweenSettingsExtensions::SetId(T,System.Int32)
// 0x00000249 T DG.Tweening.TweenSettingsExtensions::SetLink(T,UnityEngine.GameObject)
// 0x0000024A T DG.Tweening.TweenSettingsExtensions::SetLink(T,UnityEngine.GameObject,DG.Tweening.LinkBehaviour)
// 0x0000024B T DG.Tweening.TweenSettingsExtensions::SetTarget(T,System.Object)
// 0x0000024C T DG.Tweening.TweenSettingsExtensions::SetLoops(T,System.Int32)
// 0x0000024D T DG.Tweening.TweenSettingsExtensions::SetLoops(T,System.Int32,DG.Tweening.LoopType)
// 0x0000024E T DG.Tweening.TweenSettingsExtensions::SetEase(T,DG.Tweening.Ease)
// 0x0000024F T DG.Tweening.TweenSettingsExtensions::SetEase(T,DG.Tweening.Ease,System.Single)
// 0x00000250 T DG.Tweening.TweenSettingsExtensions::SetEase(T,DG.Tweening.Ease,System.Single,System.Single)
// 0x00000251 T DG.Tweening.TweenSettingsExtensions::SetEase(T,UnityEngine.AnimationCurve)
// 0x00000252 T DG.Tweening.TweenSettingsExtensions::SetEase(T,DG.Tweening.EaseFunction)
// 0x00000253 T DG.Tweening.TweenSettingsExtensions::SetRecyclable(T)
// 0x00000254 T DG.Tweening.TweenSettingsExtensions::SetRecyclable(T,System.Boolean)
// 0x00000255 T DG.Tweening.TweenSettingsExtensions::SetUpdate(T,System.Boolean)
// 0x00000256 T DG.Tweening.TweenSettingsExtensions::SetUpdate(T,DG.Tweening.UpdateType)
// 0x00000257 T DG.Tweening.TweenSettingsExtensions::SetUpdate(T,DG.Tweening.UpdateType,System.Boolean)
// 0x00000258 T DG.Tweening.TweenSettingsExtensions::SetInverted(T)
// 0x00000259 T DG.Tweening.TweenSettingsExtensions::SetInverted(T,System.Boolean)
// 0x0000025A T DG.Tweening.TweenSettingsExtensions::OnStart(T,DG.Tweening.TweenCallback)
// 0x0000025B T DG.Tweening.TweenSettingsExtensions::OnPlay(T,DG.Tweening.TweenCallback)
// 0x0000025C T DG.Tweening.TweenSettingsExtensions::OnPause(T,DG.Tweening.TweenCallback)
// 0x0000025D T DG.Tweening.TweenSettingsExtensions::OnRewind(T,DG.Tweening.TweenCallback)
// 0x0000025E T DG.Tweening.TweenSettingsExtensions::OnUpdate(T,DG.Tweening.TweenCallback)
// 0x0000025F T DG.Tweening.TweenSettingsExtensions::OnStepComplete(T,DG.Tweening.TweenCallback)
// 0x00000260 T DG.Tweening.TweenSettingsExtensions::OnComplete(T,DG.Tweening.TweenCallback)
// 0x00000261 T DG.Tweening.TweenSettingsExtensions::OnKill(T,DG.Tweening.TweenCallback)
// 0x00000262 T DG.Tweening.TweenSettingsExtensions::OnWaypointChange(T,DG.Tweening.TweenCallback`1<System.Int32>)
// 0x00000263 T DG.Tweening.TweenSettingsExtensions::SetAs(T,DG.Tweening.Tween)
// 0x00000264 T DG.Tweening.TweenSettingsExtensions::SetAs(T,DG.Tweening.TweenParams)
// 0x00000265 DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::Append(DG.Tweening.Sequence,DG.Tweening.Tween)
extern void TweenSettingsExtensions_Append_mAFF074C9F7DE2BC8FF383C7729B13E732345F28F (void);
// 0x00000266 DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::Prepend(DG.Tweening.Sequence,DG.Tweening.Tween)
extern void TweenSettingsExtensions_Prepend_m241B44F371F4C324511EEE213AE5866CFC75E3BE (void);
// 0x00000267 DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::Join(DG.Tweening.Sequence,DG.Tweening.Tween)
extern void TweenSettingsExtensions_Join_mBA1D659EE0310BBE1F42148057403E7C5EEDB777 (void);
// 0x00000268 DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::Insert(DG.Tweening.Sequence,System.Single,DG.Tweening.Tween)
extern void TweenSettingsExtensions_Insert_m69E4EA33E374050B17000847C172BC6F93C71928 (void);
// 0x00000269 DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::AppendInterval(DG.Tweening.Sequence,System.Single)
extern void TweenSettingsExtensions_AppendInterval_m36B7A337E62568050B2A3220C9140D06CD50CD82 (void);
// 0x0000026A DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::PrependInterval(DG.Tweening.Sequence,System.Single)
extern void TweenSettingsExtensions_PrependInterval_mCC5525CBFA2E20938D4D095DF5F78720C23228E0 (void);
// 0x0000026B DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::AppendCallback(DG.Tweening.Sequence,DG.Tweening.TweenCallback)
extern void TweenSettingsExtensions_AppendCallback_m050F66C6988DBDC56B2DA4A52A25228F0B073804 (void);
// 0x0000026C DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::PrependCallback(DG.Tweening.Sequence,DG.Tweening.TweenCallback)
extern void TweenSettingsExtensions_PrependCallback_mC34DCD244F93C4B32D9E3751F0D9DBA504F42BC5 (void);
// 0x0000026D DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::JoinCallback(DG.Tweening.Sequence,DG.Tweening.TweenCallback)
extern void TweenSettingsExtensions_JoinCallback_m2A3763A75F4E1FB11FED3DF028BC0F3C2B8CBB64 (void);
// 0x0000026E DG.Tweening.Sequence DG.Tweening.TweenSettingsExtensions::InsertCallback(DG.Tweening.Sequence,System.Single,DG.Tweening.TweenCallback)
extern void TweenSettingsExtensions_InsertCallback_mA93A3B878FA88987C572D25C52FBA283D86E8CF9 (void);
// 0x0000026F System.Boolean DG.Tweening.TweenSettingsExtensions::ValidateAddToSequence(DG.Tweening.Sequence,DG.Tweening.Tween,System.Boolean)
extern void TweenSettingsExtensions_ValidateAddToSequence_m6420279478426B0BF7E90A9BE5A4CB70C60C7F7E (void);
// 0x00000270 T DG.Tweening.TweenSettingsExtensions::From(T)
// 0x00000271 T DG.Tweening.TweenSettingsExtensions::From(T,System.Boolean)
// 0x00000272 T DG.Tweening.TweenSettingsExtensions::From(T,System.Boolean,System.Boolean)
// 0x00000273 DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.TweenSettingsExtensions::From(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>,T2,System.Boolean,System.Boolean)
// 0x00000274 DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions> DG.Tweening.TweenSettingsExtensions::From(DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions>,System.Single,System.Boolean,System.Boolean)
extern void TweenSettingsExtensions_From_m497E1389C0F3F6B8E1FC52522FD23AC386621BDC (void);
// 0x00000275 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions> DG.Tweening.TweenSettingsExtensions::From(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions>,System.Single,System.Boolean,System.Boolean)
extern void TweenSettingsExtensions_From_m81CAED85AE1EE7446C7CB6B58C08DF4107E2F74B (void);
// 0x00000276 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions> DG.Tweening.TweenSettingsExtensions::From(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions>,System.Single,System.Boolean,System.Boolean)
extern void TweenSettingsExtensions_From_mC35C4711D2293C36E778485B229605E0490F4AD6 (void);
// 0x00000277 T DG.Tweening.TweenSettingsExtensions::SetDelay(T,System.Single)
// 0x00000278 T DG.Tweening.TweenSettingsExtensions::SetDelay(T,System.Single,System.Boolean)
// 0x00000279 T DG.Tweening.TweenSettingsExtensions::SetRelative(T)
// 0x0000027A T DG.Tweening.TweenSettingsExtensions::SetRelative(T,System.Boolean)
// 0x0000027B T DG.Tweening.TweenSettingsExtensions::SetSpeedBased(T)
// 0x0000027C T DG.Tweening.TweenSettingsExtensions::SetSpeedBased(T,System.Boolean)
// 0x0000027D DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions>,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m94B0ECDB7445CABBF2814531E137F51582AE5425 (void);
// 0x0000027E DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions>,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m470EC93A8B43B25894F7143B876B117AFF2B000E (void);
// 0x0000027F DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions>,DG.Tweening.AxisConstraint,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_mFD076C0377973484D8607E062753FE55478F0D0C (void);
// 0x00000280 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions>,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m6FA72AD20A82D69FDB0189B31D81B5A78653FF50 (void);
// 0x00000281 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions>,DG.Tweening.AxisConstraint,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700 (void);
// 0x00000282 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions>,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m0F46F26543CFFDC14BC8E2656E8AE15D9CB56E22 (void);
// 0x00000283 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions>,DG.Tweening.AxisConstraint,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m0C0AD0BEADFEB3D7B611204AFC7104E90FDBB716 (void);
// 0x00000284 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions>,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m5201429E1EBC789C83A30D912C019E2F88B6EE85 (void);
// 0x00000285 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions>,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_mDBFD729D4FEE37F7A828AD98BFC532A802617F08 (void);
// 0x00000286 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions>,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m28C06D4AD8A8667B6E28205BAE7DC962E6BDAB9B (void);
// 0x00000287 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<System.String,System.String,DG.Tweening.Plugins.Options.StringOptions>,System.Boolean,DG.Tweening.ScrambleMode,System.String)
extern void TweenSettingsExtensions_SetOptions_m571339B54CA3A4BD935C07D719EE40E4FAE2C9D2 (void);
// 0x00000288 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m3AF4D0166C4D467CCEFCB9B3116EA69D200C73E2 (void);
// 0x00000289 DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>,DG.Tweening.AxisConstraint,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_mDBB5ACE45F2ED717358105B955DFE7EC6EA9F408 (void);
// 0x0000028A DG.Tweening.Tweener DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions>,System.Single,System.Boolean,System.Boolean)
extern void TweenSettingsExtensions_SetOptions_m6D1AF94283335AC29B4DD9FBACA274940C96C444 (void);
// 0x0000028B DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,DG.Tweening.AxisConstraint,DG.Tweening.AxisConstraint)
extern void TweenSettingsExtensions_SetOptions_mCAAE66B84802D2D369854C3741F4D1BBCD097EEF (void);
// 0x0000028C DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.TweenSettingsExtensions::SetOptions(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,System.Boolean,DG.Tweening.AxisConstraint,DG.Tweening.AxisConstraint)
extern void TweenSettingsExtensions_SetOptions_m5E6F9A1145201D9D34824DE50132581D3DDC8DCE (void);
// 0x0000028D DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.TweenSettingsExtensions::SetLookAt(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,UnityEngine.Vector3,System.Nullable`1<UnityEngine.Vector3>,System.Nullable`1<UnityEngine.Vector3>)
extern void TweenSettingsExtensions_SetLookAt_m92E5AE38463525D9672DF0E98D6980096DBC2814 (void);
// 0x0000028E DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.TweenSettingsExtensions::SetLookAt(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,UnityEngine.Vector3,System.Boolean)
extern void TweenSettingsExtensions_SetLookAt_mEE755B6D883B6702C1ACF6C98E56E7646CB38ACF (void);
// 0x0000028F DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.TweenSettingsExtensions::SetLookAt(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,UnityEngine.Transform,System.Nullable`1<UnityEngine.Vector3>,System.Nullable`1<UnityEngine.Vector3>)
extern void TweenSettingsExtensions_SetLookAt_m2D965125CC14890614DFBFF2A5E762852F9F1AC2 (void);
// 0x00000290 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.TweenSettingsExtensions::SetLookAt(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,UnityEngine.Transform,System.Boolean)
extern void TweenSettingsExtensions_SetLookAt_mEEEE2B9F0794E5B8E5D3C3B9D601CE5F61445533 (void);
// 0x00000291 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.TweenSettingsExtensions::SetLookAt(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,System.Single,System.Nullable`1<UnityEngine.Vector3>,System.Nullable`1<UnityEngine.Vector3>)
extern void TweenSettingsExtensions_SetLookAt_mECF406EE42F980E66C745916D2BBDF63F7E4C0D6 (void);
// 0x00000292 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.TweenSettingsExtensions::SetLookAt(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,System.Single,System.Boolean)
extern void TweenSettingsExtensions_SetLookAt_m1AF2A3C9FA9942AA89C52A62743E480FDA9A4C12 (void);
// 0x00000293 DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.TweenSettingsExtensions::SetLookAt(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,DG.Tweening.Plugins.Options.OrientType,UnityEngine.Vector3,UnityEngine.Transform,System.Single,System.Nullable`1<UnityEngine.Vector3>,System.Nullable`1<UnityEngine.Vector3>,System.Boolean)
extern void TweenSettingsExtensions_SetLookAt_mF70A1BDA41F0C7CD95ABCD9E0DA9216E7F467553 (void);
// 0x00000294 System.Void DG.Tweening.TweenSettingsExtensions::SetPathForwardDirection(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,System.Nullable`1<UnityEngine.Vector3>,System.Nullable`1<UnityEngine.Vector3>)
extern void TweenSettingsExtensions_SetPathForwardDirection_mF2A4AB7983A3BBDA380EA993C205C6D8AFE0B64C (void);
// 0x00000295 System.Boolean DG.Tweening.Tween::get_isRelative()
extern void Tween_get_isRelative_mC31C34D21C3953F9AA7F25C0429BEBE45D2DBAE2 (void);
// 0x00000296 System.Void DG.Tweening.Tween::set_isRelative(System.Boolean)
extern void Tween_set_isRelative_m881085052780C20122B970FA26766E551DA3B8EB (void);
// 0x00000297 System.Boolean DG.Tweening.Tween::get_active()
extern void Tween_get_active_mD4253DD1A64623E342282E139081B787935A3E5E (void);
// 0x00000298 System.Void DG.Tweening.Tween::set_active(System.Boolean)
extern void Tween_set_active_m7E2D493098F2406830BAE9201422B8A1E7ADE2C7 (void);
// 0x00000299 System.Single DG.Tweening.Tween::get_fullPosition()
extern void Tween_get_fullPosition_m45E7CC4BFD8E07EB345918F956F0B3AE241C39FA (void);
// 0x0000029A System.Void DG.Tweening.Tween::set_fullPosition(System.Single)
extern void Tween_set_fullPosition_m903F28B26102F66CA13DB472121140925FA6495C (void);
// 0x0000029B System.Boolean DG.Tweening.Tween::get_hasLoops()
extern void Tween_get_hasLoops_m95F8C10D71229834EB4B6225C7F2E8F692B71FFC (void);
// 0x0000029C System.Boolean DG.Tweening.Tween::get_playedOnce()
extern void Tween_get_playedOnce_mDA42B6964058549DB8BBC9217DBBB2F0EB67A335 (void);
// 0x0000029D System.Void DG.Tweening.Tween::set_playedOnce(System.Boolean)
extern void Tween_set_playedOnce_mC95D34B48FDF13A9C3B8451B2794A9FDA537019F (void);
// 0x0000029E System.Single DG.Tweening.Tween::get_position()
extern void Tween_get_position_mF8A2FF9C0DA291DEC595AC8C00E2E096A009B5A8 (void);
// 0x0000029F System.Void DG.Tweening.Tween::set_position(System.Single)
extern void Tween_set_position_mFA8507C0C9F2E513D037EB506CDC444AC993F4B2 (void);
// 0x000002A0 System.Void DG.Tweening.Tween::Reset()
extern void Tween_Reset_m7E3A4C092BDB502A8B12E5DBB461602400A31C8D (void);
// 0x000002A1 System.Boolean DG.Tweening.Tween::Validate()
// 0x000002A2 System.Single DG.Tweening.Tween::UpdateDelay(System.Single)
extern void Tween_UpdateDelay_m3BCCB4073A5EEDBD7DCE43C250D8BB79D255AE6B (void);
// 0x000002A3 System.Boolean DG.Tweening.Tween::Startup()
// 0x000002A4 System.Boolean DG.Tweening.Tween::ApplyTween(System.Single,System.Int32,System.Int32,System.Boolean,DG.Tweening.Core.Enums.UpdateMode,DG.Tweening.Core.Enums.UpdateNotice)
// 0x000002A5 System.Boolean DG.Tweening.Tween::DoGoto(DG.Tweening.Tween,System.Single,System.Int32,DG.Tweening.Core.Enums.UpdateMode)
extern void Tween_DoGoto_m1A61731CB2E2D27D1E08ADD844E575A5DAD8939C (void);
// 0x000002A6 System.Boolean DG.Tweening.Tween::OnTweenCallback(DG.Tweening.TweenCallback,DG.Tweening.Tween)
extern void Tween_OnTweenCallback_mAF944138F2F0D8BFF50EC9B5EC1C24ADD7623FB8 (void);
// 0x000002A7 System.Boolean DG.Tweening.Tween::OnTweenCallback(DG.Tweening.TweenCallback`1<T>,DG.Tweening.Tween,T)
// 0x000002A8 System.Void DG.Tweening.Tween::.ctor()
extern void Tween__ctor_m92AEA714BCF3EAFB7FAE3E6714A03B8F2CB45D17 (void);
// 0x000002A9 System.Void DG.Tweening.Tweener::.ctor()
extern void Tweener__ctor_m04B7FAE8742229AF46C846C73F08E0F12A943F26 (void);
// 0x000002AA DG.Tweening.Tweener DG.Tweening.Tweener::ChangeStartValue(System.Object,System.Single)
// 0x000002AB DG.Tweening.Tweener DG.Tweening.Tweener::ChangeEndValue(System.Object,System.Single,System.Boolean)
// 0x000002AC DG.Tweening.Tweener DG.Tweening.Tweener::ChangeEndValue(System.Object,System.Boolean)
// 0x000002AD DG.Tweening.Tweener DG.Tweening.Tweener::ChangeValues(System.Object,System.Object,System.Single)
// 0x000002AE DG.Tweening.Tweener DG.Tweening.Tweener::SetFrom(System.Boolean)
// 0x000002AF System.Boolean DG.Tweening.Tweener::Setup(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>,DG.Tweening.Core.DOGetter`1<T1>,DG.Tweening.Core.DOSetter`1<T1>,T2,System.Single,DG.Tweening.Plugins.Core.ABSTweenPlugin`3<T1,T2,TPlugOptions>)
// 0x000002B0 System.Single DG.Tweening.Tweener::DoUpdateDelay(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>,System.Single)
// 0x000002B1 System.Boolean DG.Tweening.Tweener::DoStartup(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>)
// 0x000002B2 DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Tweener::DoChangeStartValue(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>,T2,System.Single)
// 0x000002B3 DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Tweener::DoChangeEndValue(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>,T2,System.Single,System.Boolean)
// 0x000002B4 DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Tweener::DoChangeValues(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>,T2,T2,System.Single)
// 0x000002B5 System.Boolean DG.Tweening.Tweener::DOStartupSpecials(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>)
// 0x000002B6 System.Void DG.Tweening.Tweener::DOStartupDurationBased(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>)
// 0x000002B7 System.Void DG.Tweening.Plugins.CircleOptions::Reset()
extern void CircleOptions_Reset_m6AFD52048392CA538A62BD5A05F3CC96DCF8ECBF (void);
// 0x000002B8 System.Void DG.Tweening.Plugins.CircleOptions::Initialize(UnityEngine.Vector2,UnityEngine.Vector2)
extern void CircleOptions_Initialize_m**************************************** (void);
// 0x000002B9 System.Void DG.Tweening.Plugins.CirclePlugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions>)
extern void CirclePlugin_Reset_m**************************************** (void);
// 0x000002BA System.Void DG.Tweening.Plugins.CirclePlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions>,System.Boolean)
extern void CirclePlugin_SetFrom_m**************************************** (void);
// 0x000002BB System.Void DG.Tweening.Plugins.CirclePlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions>,UnityEngine.Vector2,System.Boolean,System.Boolean)
extern void CirclePlugin_SetFrom_m**************************************** (void);
// 0x000002BC DG.Tweening.Plugins.Core.ABSTweenPlugin`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions> DG.Tweening.Plugins.CirclePlugin::Get()
extern void CirclePlugin_Get_m**************************************** (void);
// 0x000002BD UnityEngine.Vector2 DG.Tweening.Plugins.CirclePlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions>,UnityEngine.Vector2)
extern void CirclePlugin_ConvertToStartValue_m**************************************** (void);
// 0x000002BE System.Void DG.Tweening.Plugins.CirclePlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions>)
extern void CirclePlugin_SetRelativeEndValue_m**************************************** (void);
// 0x000002BF System.Void DG.Tweening.Plugins.CirclePlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.CircleOptions>)
extern void CirclePlugin_SetChangeValue_m**************************************** (void);
// 0x000002C0 System.Single DG.Tweening.Plugins.CirclePlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.CircleOptions,System.Single,UnityEngine.Vector2)
extern void CirclePlugin_GetSpeedBasedDuration_m**************************************** (void);
// 0x000002C1 System.Void DG.Tweening.Plugins.CirclePlugin::EvaluateAndApply(DG.Tweening.Plugins.CircleOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Vector2>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector2>,System.Single,UnityEngine.Vector2,UnityEngine.Vector2,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void CirclePlugin_EvaluateAndApply_m**************************************** (void);
// 0x000002C2 UnityEngine.Vector2 DG.Tweening.Plugins.CirclePlugin::GetPositionOnCircle(DG.Tweening.Plugins.CircleOptions,System.Single)
extern void CirclePlugin_GetPositionOnCircle_m**************************************** (void);
// 0x000002C3 System.Void DG.Tweening.Plugins.CirclePlugin::.ctor()
extern void CirclePlugin__ctor_m**************************************** (void);
// 0x000002C4 System.Void DG.Tweening.Plugins.Color2Plugin::Reset(DG.Tweening.Core.TweenerCore`3<DG.Tweening.Color2,DG.Tweening.Color2,DG.Tweening.Plugins.Options.ColorOptions>)
extern void Color2Plugin_Reset_m4D1585F6639130F1C1D63606F8627B5AF78A54A0 (void);
// 0x000002C5 System.Void DG.Tweening.Plugins.Color2Plugin::SetFrom(DG.Tweening.Core.TweenerCore`3<DG.Tweening.Color2,DG.Tweening.Color2,DG.Tweening.Plugins.Options.ColorOptions>,System.Boolean)
extern void Color2Plugin_SetFrom_mC3F20E4EDA65C6449C25BACD6DDB6C1199952155 (void);
// 0x000002C6 System.Void DG.Tweening.Plugins.Color2Plugin::SetFrom(DG.Tweening.Core.TweenerCore`3<DG.Tweening.Color2,DG.Tweening.Color2,DG.Tweening.Plugins.Options.ColorOptions>,DG.Tweening.Color2,System.Boolean,System.Boolean)
extern void Color2Plugin_SetFrom_m121840A073B33E14337DD4B87B44A7CFB1E34CA5 (void);
// 0x000002C7 DG.Tweening.Color2 DG.Tweening.Plugins.Color2Plugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<DG.Tweening.Color2,DG.Tweening.Color2,DG.Tweening.Plugins.Options.ColorOptions>,DG.Tweening.Color2)
extern void Color2Plugin_ConvertToStartValue_m9F85F95CB7AA6AFCAE9F5F20AB785B89048FBDC2 (void);
// 0x000002C8 System.Void DG.Tweening.Plugins.Color2Plugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<DG.Tweening.Color2,DG.Tweening.Color2,DG.Tweening.Plugins.Options.ColorOptions>)
extern void Color2Plugin_SetRelativeEndValue_m18D1D370D75DDEF9636CBE1AA964BD11D98A9490 (void);
// 0x000002C9 System.Void DG.Tweening.Plugins.Color2Plugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<DG.Tweening.Color2,DG.Tweening.Color2,DG.Tweening.Plugins.Options.ColorOptions>)
extern void Color2Plugin_SetChangeValue_m72A9F3272BC54989B589E62DC802685AB4BCD641 (void);
// 0x000002CA System.Single DG.Tweening.Plugins.Color2Plugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.ColorOptions,System.Single,DG.Tweening.Color2)
extern void Color2Plugin_GetSpeedBasedDuration_mBACAE2EDEDED990D2E2B4078D9F6255DC52C8EA2 (void);
// 0x000002CB System.Void DG.Tweening.Plugins.Color2Plugin::EvaluateAndApply(DG.Tweening.Plugins.Options.ColorOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<DG.Tweening.Color2>,DG.Tweening.Core.DOSetter`1<DG.Tweening.Color2>,System.Single,DG.Tweening.Color2,DG.Tweening.Color2,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void Color2Plugin_EvaluateAndApply_m1B7E14629EC95B176DBBE14BAE19367A1BA2B728 (void);
// 0x000002CC System.Void DG.Tweening.Plugins.Color2Plugin::.ctor()
extern void Color2Plugin__ctor_mBCE84E5A0ECEAF61FFA54965D1341A4C24E3FA46 (void);
// 0x000002CD System.Void DG.Tweening.Plugins.DoublePlugin::Reset(DG.Tweening.Core.TweenerCore`3<System.Double,System.Double,DG.Tweening.Plugins.Options.NoOptions>)
extern void DoublePlugin_Reset_mC48E0D04271DED61DE1422AC67CBFB42DD39D065 (void);
// 0x000002CE System.Void DG.Tweening.Plugins.DoublePlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.Double,System.Double,DG.Tweening.Plugins.Options.NoOptions>,System.Boolean)
extern void DoublePlugin_SetFrom_m45DF6B4F897082911AB5E64942EB89FCBC61267A (void);
// 0x000002CF System.Void DG.Tweening.Plugins.DoublePlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.Double,System.Double,DG.Tweening.Plugins.Options.NoOptions>,System.Double,System.Boolean,System.Boolean)
extern void DoublePlugin_SetFrom_m628EE9B9815FEE0E0F3EFA7186A20FEC544C04C6 (void);
// 0x000002D0 System.Double DG.Tweening.Plugins.DoublePlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<System.Double,System.Double,DG.Tweening.Plugins.Options.NoOptions>,System.Double)
extern void DoublePlugin_ConvertToStartValue_m3BA8C6742F81078B49EDAE5BE1B277BD3BDD6768 (void);
// 0x000002D1 System.Void DG.Tweening.Plugins.DoublePlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<System.Double,System.Double,DG.Tweening.Plugins.Options.NoOptions>)
extern void DoublePlugin_SetRelativeEndValue_m16BB892BED6D8E6DA25C367941CE14D1B5117F88 (void);
// 0x000002D2 System.Void DG.Tweening.Plugins.DoublePlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<System.Double,System.Double,DG.Tweening.Plugins.Options.NoOptions>)
extern void DoublePlugin_SetChangeValue_m2F806A26CE2DB009ADF69F33C05A136E5F1455D6 (void);
// 0x000002D3 System.Single DG.Tweening.Plugins.DoublePlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.NoOptions,System.Single,System.Double)
extern void DoublePlugin_GetSpeedBasedDuration_m0661238953316795039FB9C7D05DBD4591710C33 (void);
// 0x000002D4 System.Void DG.Tweening.Plugins.DoublePlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.NoOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<System.Double>,DG.Tweening.Core.DOSetter`1<System.Double>,System.Single,System.Double,System.Double,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void DoublePlugin_EvaluateAndApply_mA1D9B93334F3D3FA3C878C6A21231EC32FA8A38A (void);
// 0x000002D5 System.Void DG.Tweening.Plugins.DoublePlugin::.ctor()
extern void DoublePlugin__ctor_mBC919A2E3792A7F49C9B42888306B7D328FAA373 (void);
// 0x000002D6 System.Void DG.Tweening.Plugins.LongPlugin::Reset(DG.Tweening.Core.TweenerCore`3<System.Int64,System.Int64,DG.Tweening.Plugins.Options.NoOptions>)
extern void LongPlugin_Reset_m37478A613E00FF0F05D3A58BEF11D649BFED5AC2 (void);
// 0x000002D7 System.Void DG.Tweening.Plugins.LongPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.Int64,System.Int64,DG.Tweening.Plugins.Options.NoOptions>,System.Boolean)
extern void LongPlugin_SetFrom_m51A61DCC7C9CC9B0B32921B7A0B4A776A9441455 (void);
// 0x000002D8 System.Void DG.Tweening.Plugins.LongPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.Int64,System.Int64,DG.Tweening.Plugins.Options.NoOptions>,System.Int64,System.Boolean,System.Boolean)
extern void LongPlugin_SetFrom_m9D93F349D8673F54EB16E54FBD92E3551E706B7F (void);
// 0x000002D9 System.Int64 DG.Tweening.Plugins.LongPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<System.Int64,System.Int64,DG.Tweening.Plugins.Options.NoOptions>,System.Int64)
extern void LongPlugin_ConvertToStartValue_mE222FBCEE94E48591988FEE4CEC005EFA70A1209 (void);
// 0x000002DA System.Void DG.Tweening.Plugins.LongPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<System.Int64,System.Int64,DG.Tweening.Plugins.Options.NoOptions>)
extern void LongPlugin_SetRelativeEndValue_mF1C87E177CCEA2C17A5090CA9B1201F093BE1C7B (void);
// 0x000002DB System.Void DG.Tweening.Plugins.LongPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<System.Int64,System.Int64,DG.Tweening.Plugins.Options.NoOptions>)
extern void LongPlugin_SetChangeValue_mB10261C79DC621100E22EEB1DACA566C9C7AC3F4 (void);
// 0x000002DC System.Single DG.Tweening.Plugins.LongPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.NoOptions,System.Single,System.Int64)
extern void LongPlugin_GetSpeedBasedDuration_mDC5560F8BC27B8917E870B1D3A2923E5E0C277C4 (void);
// 0x000002DD System.Void DG.Tweening.Plugins.LongPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.NoOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<System.Int64>,DG.Tweening.Core.DOSetter`1<System.Int64>,System.Single,System.Int64,System.Int64,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void LongPlugin_EvaluateAndApply_mE4002CBF4187D69E1934AE0F49F3B314110AB9EB (void);
// 0x000002DE System.Void DG.Tweening.Plugins.LongPlugin::.ctor()
extern void LongPlugin__ctor_m2AD9AB566ED8B00E5944E0338ED86569A3E6A143 (void);
// 0x000002DF System.Void DG.Tweening.Plugins.UlongPlugin::Reset(DG.Tweening.Core.TweenerCore`3<System.UInt64,System.UInt64,DG.Tweening.Plugins.Options.NoOptions>)
extern void UlongPlugin_Reset_mA7D583EA46AE862A4A279127535806B67AC93863 (void);
// 0x000002E0 System.Void DG.Tweening.Plugins.UlongPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.UInt64,System.UInt64,DG.Tweening.Plugins.Options.NoOptions>,System.Boolean)
extern void UlongPlugin_SetFrom_mB55DCE5245B000AF510915EDF942F8C91DF2E8ED (void);
// 0x000002E1 System.Void DG.Tweening.Plugins.UlongPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.UInt64,System.UInt64,DG.Tweening.Plugins.Options.NoOptions>,System.UInt64,System.Boolean,System.Boolean)
extern void UlongPlugin_SetFrom_m2426CC1748DC034F8D5C066D6900BB6E746F635A (void);
// 0x000002E2 System.UInt64 DG.Tweening.Plugins.UlongPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<System.UInt64,System.UInt64,DG.Tweening.Plugins.Options.NoOptions>,System.UInt64)
extern void UlongPlugin_ConvertToStartValue_m3F94E8568572F1E2BD3E03F043E625C2B356870D (void);
// 0x000002E3 System.Void DG.Tweening.Plugins.UlongPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<System.UInt64,System.UInt64,DG.Tweening.Plugins.Options.NoOptions>)
extern void UlongPlugin_SetRelativeEndValue_m6CE5E8F05C004378BB3CC4414FDA18B2F31C8C73 (void);
// 0x000002E4 System.Void DG.Tweening.Plugins.UlongPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<System.UInt64,System.UInt64,DG.Tweening.Plugins.Options.NoOptions>)
extern void UlongPlugin_SetChangeValue_mD9BAA6099E44D738055E34640E4C33A4E0986C89 (void);
// 0x000002E5 System.Single DG.Tweening.Plugins.UlongPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.NoOptions,System.Single,System.UInt64)
extern void UlongPlugin_GetSpeedBasedDuration_mFA983E09C14C54961FE6DE1D3D3CCCE6DFC306B2 (void);
// 0x000002E6 System.Void DG.Tweening.Plugins.UlongPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.NoOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<System.UInt64>,DG.Tweening.Core.DOSetter`1<System.UInt64>,System.Single,System.UInt64,System.UInt64,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void UlongPlugin_EvaluateAndApply_m6830EB9544F8402C6DA835C5C7F8FC11658AC9B3 (void);
// 0x000002E7 System.Void DG.Tweening.Plugins.UlongPlugin::.ctor()
extern void UlongPlugin__ctor_m03E4C0827FFAC8322DEAC2D784708416A15DBD56 (void);
// 0x000002E8 System.Void DG.Tweening.Plugins.Vector3ArrayPlugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>)
extern void Vector3ArrayPlugin_Reset_mA352ED531258DEAD2F621144C41DB9258D0B9571 (void);
// 0x000002E9 System.Void DG.Tweening.Plugins.Vector3ArrayPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>,System.Boolean)
extern void Vector3ArrayPlugin_SetFrom_m69DD6E6C7E4B79129DAEAF6B7D42272560894D62 (void);
// 0x000002EA System.Void DG.Tweening.Plugins.Vector3ArrayPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>,UnityEngine.Vector3[],System.Boolean,System.Boolean)
extern void Vector3ArrayPlugin_SetFrom_m17B0796B5408764AE682C1C458879E500D43825D (void);
// 0x000002EB UnityEngine.Vector3[] DG.Tweening.Plugins.Vector3ArrayPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>,UnityEngine.Vector3)
extern void Vector3ArrayPlugin_ConvertToStartValue_mE150B601DA8164B66376565D966458967B80849D (void);
// 0x000002EC System.Void DG.Tweening.Plugins.Vector3ArrayPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>)
extern void Vector3ArrayPlugin_SetRelativeEndValue_m301BAFA7B81A646DD9E0ACFE44A4E03DD3DA8762 (void);
// 0x000002ED System.Void DG.Tweening.Plugins.Vector3ArrayPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>)
extern void Vector3ArrayPlugin_SetChangeValue_m6CCE20B567B1B0A291C3F99001D73DAAE4000173 (void);
// 0x000002EE System.Single DG.Tweening.Plugins.Vector3ArrayPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.Vector3ArrayOptions,System.Single,UnityEngine.Vector3[])
extern void Vector3ArrayPlugin_GetSpeedBasedDuration_m3DE9F80A2BB6E51D8306D5FD0B7394D299B09B3E (void);
// 0x000002EF System.Void DG.Tweening.Plugins.Vector3ArrayPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.Vector3ArrayOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,System.Single,UnityEngine.Vector3[],UnityEngine.Vector3[],System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void Vector3ArrayPlugin_EvaluateAndApply_mB6F3D9AF1FA6921223C3BDFFC17B62B6FDD5BD9E (void);
// 0x000002F0 System.Void DG.Tweening.Plugins.Vector3ArrayPlugin::.ctor()
extern void Vector3ArrayPlugin__ctor_m7E64B2C0B6FFD43FDEA1634185763064A0511EA3 (void);
// 0x000002F1 System.Void DG.Tweening.Plugins.PathPlugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>)
extern void PathPlugin_Reset_mF8A7585640B692E1337D76DC9E193C9EED0F7AC2 (void);
// 0x000002F2 System.Void DG.Tweening.Plugins.PathPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,System.Boolean)
extern void PathPlugin_SetFrom_mF6B10813AD9588EAA43C524A1A53886ECEBC45D4 (void);
// 0x000002F3 System.Void DG.Tweening.Plugins.PathPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,DG.Tweening.Plugins.Core.PathCore.Path,System.Boolean,System.Boolean)
extern void PathPlugin_SetFrom_m58C1CFEBE65EAE3674C5B939D3948D00DAF97ACD (void);
// 0x000002F4 DG.Tweening.Plugins.Core.ABSTweenPlugin`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions> DG.Tweening.Plugins.PathPlugin::Get()
extern void PathPlugin_Get_m997FB98C4FEB9E5E74B71034B36E2A40D0153BC6 (void);
// 0x000002F5 DG.Tweening.Plugins.Core.PathCore.Path DG.Tweening.Plugins.PathPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>,UnityEngine.Vector3)
extern void PathPlugin_ConvertToStartValue_m2DD9E300B9FF4CA1DCD746E8E16548AE29CA86A2 (void);
// 0x000002F6 System.Void DG.Tweening.Plugins.PathPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>)
extern void PathPlugin_SetRelativeEndValue_m41C96CE4555F5DA30851CB54577E1997D954F579 (void);
// 0x000002F7 System.Void DG.Tweening.Plugins.PathPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions>)
extern void PathPlugin_SetChangeValue_m8C85C4B7E85276B4D0885D390DE430E3DD349CBE (void);
// 0x000002F8 System.Single DG.Tweening.Plugins.PathPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.PathOptions,System.Single,DG.Tweening.Plugins.Core.PathCore.Path)
extern void PathPlugin_GetSpeedBasedDuration_mCEA473E67BBD3A8BE7356A87B2F69FB13FF59B3B (void);
// 0x000002F9 System.Void DG.Tweening.Plugins.PathPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.PathOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,System.Single,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Core.PathCore.Path,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void PathPlugin_EvaluateAndApply_mB1C894CDC1DC3E9B3D8AFD7704C8F08208E0C465 (void);
// 0x000002FA System.Void DG.Tweening.Plugins.PathPlugin::SetOrientation(DG.Tweening.Plugins.Options.PathOptions,DG.Tweening.Tween,DG.Tweening.Plugins.Core.PathCore.Path,System.Single,UnityEngine.Vector3,DG.Tweening.Core.Enums.UpdateNotice)
extern void PathPlugin_SetOrientation_m3F5BCB1FCBB546654FEB270D7B568F1667E9977B (void);
// 0x000002FB UnityEngine.Vector3 DG.Tweening.Plugins.PathPlugin::DivideVectorByVector(UnityEngine.Vector3,UnityEngine.Vector3)
extern void PathPlugin_DivideVectorByVector_m276361EB5587FDDEB9D985E6DA35C1E903B7F7B8 (void);
// 0x000002FC UnityEngine.Vector3 DG.Tweening.Plugins.PathPlugin::MultiplyVectorByVector(UnityEngine.Vector3,UnityEngine.Vector3)
extern void PathPlugin_MultiplyVectorByVector_m43E9793D9F35E401FC9766892AD1D4B69CD88829 (void);
// 0x000002FD System.Void DG.Tweening.Plugins.PathPlugin::.ctor()
extern void PathPlugin__ctor_mD8811EA5B57B17FC56CD3E926FF69CB41C5FC183 (void);
// 0x000002FE System.Void DG.Tweening.Plugins.ColorPlugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions>)
extern void ColorPlugin_Reset_mD5C17D1107C847229AE3AF5513CB3FA5194961F4 (void);
// 0x000002FF System.Void DG.Tweening.Plugins.ColorPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions>,System.Boolean)
extern void ColorPlugin_SetFrom_m479A8C5CD0FCA868D41ED7A29A7214B511640CBD (void);
// 0x00000300 System.Void DG.Tweening.Plugins.ColorPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions>,UnityEngine.Color,System.Boolean,System.Boolean)
extern void ColorPlugin_SetFrom_mAADAA832F76978A40ACA0EFF96202E7348425594 (void);
// 0x00000301 UnityEngine.Color DG.Tweening.Plugins.ColorPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions>,UnityEngine.Color)
extern void ColorPlugin_ConvertToStartValue_mCFE988E065959F8C5898AAA3B209ABAFCD8AF3A8 (void);
// 0x00000302 System.Void DG.Tweening.Plugins.ColorPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions>)
extern void ColorPlugin_SetRelativeEndValue_m66E263246FC9F63F50A4C5AFFC3C5E0D70B59E84 (void);
// 0x00000303 System.Void DG.Tweening.Plugins.ColorPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Color,UnityEngine.Color,DG.Tweening.Plugins.Options.ColorOptions>)
extern void ColorPlugin_SetChangeValue_m185BDA16E9FFD1AE83152A1C8B4C55F13E4B74E7 (void);
// 0x00000304 System.Single DG.Tweening.Plugins.ColorPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.ColorOptions,System.Single,UnityEngine.Color)
extern void ColorPlugin_GetSpeedBasedDuration_mABC5DCBD1B8D595243C93FA40FCDED9E25469AA0 (void);
// 0x00000305 System.Void DG.Tweening.Plugins.ColorPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.ColorOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Color>,DG.Tweening.Core.DOSetter`1<UnityEngine.Color>,System.Single,UnityEngine.Color,UnityEngine.Color,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void ColorPlugin_EvaluateAndApply_m190AB3B2127491FD54359B4F3350728BA6DC501D (void);
// 0x00000306 System.Void DG.Tweening.Plugins.ColorPlugin::.ctor()
extern void ColorPlugin__ctor_m633C70643C227166D0DE895DCD198751ADB832B8 (void);
// 0x00000307 System.Void DG.Tweening.Plugins.IntPlugin::Reset(DG.Tweening.Core.TweenerCore`3<System.Int32,System.Int32,DG.Tweening.Plugins.Options.NoOptions>)
extern void IntPlugin_Reset_mB5E54408080BA058CE3141E70024CF7BBD4124A1 (void);
// 0x00000308 System.Void DG.Tweening.Plugins.IntPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.Int32,System.Int32,DG.Tweening.Plugins.Options.NoOptions>,System.Boolean)
extern void IntPlugin_SetFrom_mD5482FE1E980BDD2A15F9EF5A3DBA53C84CC2C30 (void);
// 0x00000309 System.Void DG.Tweening.Plugins.IntPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.Int32,System.Int32,DG.Tweening.Plugins.Options.NoOptions>,System.Int32,System.Boolean,System.Boolean)
extern void IntPlugin_SetFrom_mD097AC17A5388FDF1F19F78CC62E9E961B45B311 (void);
// 0x0000030A System.Int32 DG.Tweening.Plugins.IntPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<System.Int32,System.Int32,DG.Tweening.Plugins.Options.NoOptions>,System.Int32)
extern void IntPlugin_ConvertToStartValue_m18C3236122A0CA21CCC67F1E04178CA298FDD21A (void);
// 0x0000030B System.Void DG.Tweening.Plugins.IntPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<System.Int32,System.Int32,DG.Tweening.Plugins.Options.NoOptions>)
extern void IntPlugin_SetRelativeEndValue_m2998B97675D44F79988F48E53F764D589E40A76C (void);
// 0x0000030C System.Void DG.Tweening.Plugins.IntPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<System.Int32,System.Int32,DG.Tweening.Plugins.Options.NoOptions>)
extern void IntPlugin_SetChangeValue_m0546A7F4D7D2E054EEBE0CD36AAD672E92F74C43 (void);
// 0x0000030D System.Single DG.Tweening.Plugins.IntPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.NoOptions,System.Single,System.Int32)
extern void IntPlugin_GetSpeedBasedDuration_mDD8E0299B88828750EA43F995838A8A14F052CB7 (void);
// 0x0000030E System.Void DG.Tweening.Plugins.IntPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.NoOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<System.Int32>,DG.Tweening.Core.DOSetter`1<System.Int32>,System.Single,System.Int32,System.Int32,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void IntPlugin_EvaluateAndApply_m10614E91896AD00781AC48833B45456C1B782D41 (void);
// 0x0000030F System.Void DG.Tweening.Plugins.IntPlugin::.ctor()
extern void IntPlugin__ctor_mDD5E0E9B685B85810B0E99FD3A724390EE99C913 (void);
// 0x00000310 System.Void DG.Tweening.Plugins.QuaternionPlugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions>)
extern void QuaternionPlugin_Reset_mF9544485D1461B1A60EC89CEAD333B5ABE4B3D7B (void);
// 0x00000311 System.Void DG.Tweening.Plugins.QuaternionPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions>,System.Boolean)
extern void QuaternionPlugin_SetFrom_m06BACD08FF14D0BD3229DD0520C80C70C80E7F1C (void);
// 0x00000312 System.Void DG.Tweening.Plugins.QuaternionPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions>,UnityEngine.Vector3,System.Boolean,System.Boolean)
extern void QuaternionPlugin_SetFrom_mDDD414437498EAD85880C442C0F717AF338DAB5A (void);
// 0x00000313 UnityEngine.Vector3 DG.Tweening.Plugins.QuaternionPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions>,UnityEngine.Quaternion)
extern void QuaternionPlugin_ConvertToStartValue_m6D2E6DD4E47A7B6377A89DBBF830864048B739D5 (void);
// 0x00000314 System.Void DG.Tweening.Plugins.QuaternionPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions>)
extern void QuaternionPlugin_SetRelativeEndValue_m5363B1A3F417A3EFA102252017BBABDEC0EB11D4 (void);
// 0x00000315 System.Void DG.Tweening.Plugins.QuaternionPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions>)
extern void QuaternionPlugin_SetChangeValue_m40DCF9B299943301BBBAF466B57F8604C4475CD2 (void);
// 0x00000316 System.Single DG.Tweening.Plugins.QuaternionPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.QuaternionOptions,System.Single,UnityEngine.Vector3)
extern void QuaternionPlugin_GetSpeedBasedDuration_m8BE69247A323044EABCF6D824250F3B9BEA9D9FC (void);
// 0x00000317 System.Void DG.Tweening.Plugins.QuaternionPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.QuaternionOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Quaternion>,DG.Tweening.Core.DOSetter`1<UnityEngine.Quaternion>,System.Single,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void QuaternionPlugin_EvaluateAndApply_m43B55A6171F6647EF62F262D07E51CEDB724AC02 (void);
// 0x00000318 UnityEngine.Vector3 DG.Tweening.Plugins.QuaternionPlugin::GetEulerValForCalculations(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions>,UnityEngine.Vector3,UnityEngine.Vector3)
extern void QuaternionPlugin_GetEulerValForCalculations_mD6F45618229BFA08AC7DA4148EBBA31B909DD41D (void);
// 0x00000319 UnityEngine.Vector3 DG.Tweening.Plugins.QuaternionPlugin::FlipEulerAngles(UnityEngine.Vector3)
extern void QuaternionPlugin_FlipEulerAngles_mB5A2A2890526B45D639D67C0D82A7290EFF4808F (void);
// 0x0000031A System.Void DG.Tweening.Plugins.QuaternionPlugin::.ctor()
extern void QuaternionPlugin__ctor_m300B7DE9FF8C992D2A5A7E069AFD7A2054D9960C (void);
// 0x0000031B System.Void DG.Tweening.Plugins.RectOffsetPlugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.RectOffset,UnityEngine.RectOffset,DG.Tweening.Plugins.Options.NoOptions>)
extern void RectOffsetPlugin_Reset_m94F0DAB51730C6879FDF35EC1B1063950C16DD93 (void);
// 0x0000031C System.Void DG.Tweening.Plugins.RectOffsetPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.RectOffset,UnityEngine.RectOffset,DG.Tweening.Plugins.Options.NoOptions>,System.Boolean)
extern void RectOffsetPlugin_SetFrom_m450A669373A57D51711CA2F4BDDA5F47F6A8A3A9 (void);
// 0x0000031D System.Void DG.Tweening.Plugins.RectOffsetPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.RectOffset,UnityEngine.RectOffset,DG.Tweening.Plugins.Options.NoOptions>,UnityEngine.RectOffset,System.Boolean,System.Boolean)
extern void RectOffsetPlugin_SetFrom_mA2D46286DA1C8A8321B60B37178AF10B1DCAC2A8 (void);
// 0x0000031E UnityEngine.RectOffset DG.Tweening.Plugins.RectOffsetPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.RectOffset,UnityEngine.RectOffset,DG.Tweening.Plugins.Options.NoOptions>,UnityEngine.RectOffset)
extern void RectOffsetPlugin_ConvertToStartValue_m0FD425737084A6587013AF55BAE8E4A4DF478025 (void);
// 0x0000031F System.Void DG.Tweening.Plugins.RectOffsetPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.RectOffset,UnityEngine.RectOffset,DG.Tweening.Plugins.Options.NoOptions>)
extern void RectOffsetPlugin_SetRelativeEndValue_mBD08440BECDFC30E1C2DF9019ED71925C23EAF35 (void);
// 0x00000320 System.Void DG.Tweening.Plugins.RectOffsetPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.RectOffset,UnityEngine.RectOffset,DG.Tweening.Plugins.Options.NoOptions>)
extern void RectOffsetPlugin_SetChangeValue_m56A91F6ABD143A79390BA34C4218B4A7F79D9D61 (void);
// 0x00000321 System.Single DG.Tweening.Plugins.RectOffsetPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.NoOptions,System.Single,UnityEngine.RectOffset)
extern void RectOffsetPlugin_GetSpeedBasedDuration_m0A00284A81996026211253E284C1FDDAE195E504 (void);
// 0x00000322 System.Void DG.Tweening.Plugins.RectOffsetPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.NoOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.RectOffset>,DG.Tweening.Core.DOSetter`1<UnityEngine.RectOffset>,System.Single,UnityEngine.RectOffset,UnityEngine.RectOffset,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void RectOffsetPlugin_EvaluateAndApply_mB4F799834E657E60C3EE2C9CD385847D2C5233D7 (void);
// 0x00000323 System.Void DG.Tweening.Plugins.RectOffsetPlugin::.ctor()
extern void RectOffsetPlugin__ctor_m889C8F4862C868D55130CED884544F46AA4A4066 (void);
// 0x00000324 System.Void DG.Tweening.Plugins.RectOffsetPlugin::.cctor()
extern void RectOffsetPlugin__cctor_mDEE0140B33EF6EA1F0DC496427FBD4F505427071 (void);
// 0x00000325 System.Void DG.Tweening.Plugins.RectPlugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions>)
extern void RectPlugin_Reset_m608F606EA7062DD500E16CF96D44D1A803AC1CC0 (void);
// 0x00000326 System.Void DG.Tweening.Plugins.RectPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions>,System.Boolean)
extern void RectPlugin_SetFrom_m364E3B14FB559ECA15C19CF930EBB07D3A619D71 (void);
// 0x00000327 System.Void DG.Tweening.Plugins.RectPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions>,UnityEngine.Rect,System.Boolean,System.Boolean)
extern void RectPlugin_SetFrom_mFDB4209FD9AB4F0EA55D7A23E2A036EAA55C2711 (void);
// 0x00000328 UnityEngine.Rect DG.Tweening.Plugins.RectPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions>,UnityEngine.Rect)
extern void RectPlugin_ConvertToStartValue_m3584F27A3B1467B5184137FC28302E391EB1C828 (void);
// 0x00000329 System.Void DG.Tweening.Plugins.RectPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions>)
extern void RectPlugin_SetRelativeEndValue_mA2683C712701D3C1978D2648A07C0567BCF11518 (void);
// 0x0000032A System.Void DG.Tweening.Plugins.RectPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Rect,UnityEngine.Rect,DG.Tweening.Plugins.Options.RectOptions>)
extern void RectPlugin_SetChangeValue_m51E32400665D3D389B296EABAF8A9F31BD654466 (void);
// 0x0000032B System.Single DG.Tweening.Plugins.RectPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.RectOptions,System.Single,UnityEngine.Rect)
extern void RectPlugin_GetSpeedBasedDuration_m6898F84A4B2C11EA25628AF6C81261F6FD965D42 (void);
// 0x0000032C System.Void DG.Tweening.Plugins.RectPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.RectOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Rect>,DG.Tweening.Core.DOSetter`1<UnityEngine.Rect>,System.Single,UnityEngine.Rect,UnityEngine.Rect,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void RectPlugin_EvaluateAndApply_m8A244A6FFE476677C065055C9A1CE56F35A82894 (void);
// 0x0000032D System.Void DG.Tweening.Plugins.RectPlugin::.ctor()
extern void RectPlugin__ctor_mC8C57164273B43CA3968BD9892AF8FAFF86431ED (void);
// 0x0000032E System.Void DG.Tweening.Plugins.UintPlugin::Reset(DG.Tweening.Core.TweenerCore`3<System.UInt32,System.UInt32,DG.Tweening.Plugins.Options.UintOptions>)
extern void UintPlugin_Reset_m70C443EF07DEF1E40EE7775F3273819C457718A3 (void);
// 0x0000032F System.Void DG.Tweening.Plugins.UintPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.UInt32,System.UInt32,DG.Tweening.Plugins.Options.UintOptions>,System.Boolean)
extern void UintPlugin_SetFrom_mD132C598273DD59BCE7D4F749BCB2CDBBDB8E4FC (void);
// 0x00000330 System.Void DG.Tweening.Plugins.UintPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.UInt32,System.UInt32,DG.Tweening.Plugins.Options.UintOptions>,System.UInt32,System.Boolean,System.Boolean)
extern void UintPlugin_SetFrom_m86C9E8677D2055A6DB0B30905ADFF50DBAD4E905 (void);
// 0x00000331 System.UInt32 DG.Tweening.Plugins.UintPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<System.UInt32,System.UInt32,DG.Tweening.Plugins.Options.UintOptions>,System.UInt32)
extern void UintPlugin_ConvertToStartValue_m3DEA6264D97583AC1E26E44D58496406ADDF5711 (void);
// 0x00000332 System.Void DG.Tweening.Plugins.UintPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<System.UInt32,System.UInt32,DG.Tweening.Plugins.Options.UintOptions>)
extern void UintPlugin_SetRelativeEndValue_m3239D0B60543DE9E7A58EEAFCAEC224B81C95C91 (void);
// 0x00000333 System.Void DG.Tweening.Plugins.UintPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<System.UInt32,System.UInt32,DG.Tweening.Plugins.Options.UintOptions>)
extern void UintPlugin_SetChangeValue_m5F1E259311413FE8EC8857E3FAAFB165AF7066E5 (void);
// 0x00000334 System.Single DG.Tweening.Plugins.UintPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.UintOptions,System.Single,System.UInt32)
extern void UintPlugin_GetSpeedBasedDuration_mAF144DF48A8B58A24DE487C81E932181FD8C329F (void);
// 0x00000335 System.Void DG.Tweening.Plugins.UintPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.UintOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<System.UInt32>,DG.Tweening.Core.DOSetter`1<System.UInt32>,System.Single,System.UInt32,System.UInt32,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void UintPlugin_EvaluateAndApply_m1F772D5D22A9704A5E6B9D5ED0C320F77DAF2776 (void);
// 0x00000336 System.Void DG.Tweening.Plugins.UintPlugin::.ctor()
extern void UintPlugin__ctor_mB34F86253C685C80872915B2976E4546D1D8E897 (void);
// 0x00000337 System.Void DG.Tweening.Plugins.Vector2Plugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions>)
extern void Vector2Plugin_Reset_mC73661350E837DB36291164C3C728BA866B087AC (void);
// 0x00000338 System.Void DG.Tweening.Plugins.Vector2Plugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions>,System.Boolean)
extern void Vector2Plugin_SetFrom_mABACF87EB7C31E680DEB311AD713B3EFF80C94C3 (void);
// 0x00000339 System.Void DG.Tweening.Plugins.Vector2Plugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions>,UnityEngine.Vector2,System.Boolean,System.Boolean)
extern void Vector2Plugin_SetFrom_m298EB269C4EAD37DF4B7C54814FD90D954850672 (void);
// 0x0000033A UnityEngine.Vector2 DG.Tweening.Plugins.Vector2Plugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions>,UnityEngine.Vector2)
extern void Vector2Plugin_ConvertToStartValue_m77193485E58C5FA705208BAD82CEFD51A5CD0A55 (void);
// 0x0000033B System.Void DG.Tweening.Plugins.Vector2Plugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions>)
extern void Vector2Plugin_SetRelativeEndValue_m4669281FAC1BC5858C42AE3F74F570F8FDC8B511 (void);
// 0x0000033C System.Void DG.Tweening.Plugins.Vector2Plugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector2,UnityEngine.Vector2,DG.Tweening.Plugins.Options.VectorOptions>)
extern void Vector2Plugin_SetChangeValue_m8EF0B36B68AA1D37982E0A781448A02363BF0505 (void);
// 0x0000033D System.Single DG.Tweening.Plugins.Vector2Plugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.VectorOptions,System.Single,UnityEngine.Vector2)
extern void Vector2Plugin_GetSpeedBasedDuration_m8F03E4891B90E95E147600C8652258F403DBD9D8 (void);
// 0x0000033E System.Void DG.Tweening.Plugins.Vector2Plugin::EvaluateAndApply(DG.Tweening.Plugins.Options.VectorOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Vector2>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector2>,System.Single,UnityEngine.Vector2,UnityEngine.Vector2,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void Vector2Plugin_EvaluateAndApply_m5E9A974C9840D7FFB7AA8D2A75551317A187BD85 (void);
// 0x0000033F System.Void DG.Tweening.Plugins.Vector2Plugin::.ctor()
extern void Vector2Plugin__ctor_m2023C1B763C66A6B5DADA4EA95AABC590996058C (void);
// 0x00000340 System.Void DG.Tweening.Plugins.Vector4Plugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions>)
extern void Vector4Plugin_Reset_m77B2CBFC27C6F6F64FA0BB07FBBD72A7B7C9F360 (void);
// 0x00000341 System.Void DG.Tweening.Plugins.Vector4Plugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions>,System.Boolean)
extern void Vector4Plugin_SetFrom_m8B96E0FCBA5D14E5E5D6D8C37EE36704D270A264 (void);
// 0x00000342 System.Void DG.Tweening.Plugins.Vector4Plugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions>,UnityEngine.Vector4,System.Boolean,System.Boolean)
extern void Vector4Plugin_SetFrom_mD3513AE4A93D72127D3B987CF14C9C861F961D7C (void);
// 0x00000343 UnityEngine.Vector4 DG.Tweening.Plugins.Vector4Plugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions>,UnityEngine.Vector4)
extern void Vector4Plugin_ConvertToStartValue_m1D126F93BDF693BD05F2A4E3379CA87B979FD512 (void);
// 0x00000344 System.Void DG.Tweening.Plugins.Vector4Plugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions>)
extern void Vector4Plugin_SetRelativeEndValue_m69588D0E049A70F6B0ACABD34DCBDF8E19CC5652 (void);
// 0x00000345 System.Void DG.Tweening.Plugins.Vector4Plugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector4,UnityEngine.Vector4,DG.Tweening.Plugins.Options.VectorOptions>)
extern void Vector4Plugin_SetChangeValue_m69D1C0143C4D88BF84ABF66533A0E815D26F6FBE (void);
// 0x00000346 System.Single DG.Tweening.Plugins.Vector4Plugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.VectorOptions,System.Single,UnityEngine.Vector4)
extern void Vector4Plugin_GetSpeedBasedDuration_mBA1AF59D5B2CB2A44BFED869AAAD474902E7122C (void);
// 0x00000347 System.Void DG.Tweening.Plugins.Vector4Plugin::EvaluateAndApply(DG.Tweening.Plugins.Options.VectorOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Vector4>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector4>,System.Single,UnityEngine.Vector4,UnityEngine.Vector4,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void Vector4Plugin_EvaluateAndApply_mF75883427661BC86C345394F7415F8177E432611 (void);
// 0x00000348 System.Void DG.Tweening.Plugins.Vector4Plugin::.ctor()
extern void Vector4Plugin__ctor_mF9E2DC11518FE5B03FE1BE2B6C930318307C2002 (void);
// 0x00000349 System.Void DG.Tweening.Plugins.StringPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.String,System.String,DG.Tweening.Plugins.Options.StringOptions>,System.Boolean)
extern void StringPlugin_SetFrom_mA6E09A5AB048B4210846D451F5D83821518CA7A3 (void);
// 0x0000034A System.Void DG.Tweening.Plugins.StringPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.String,System.String,DG.Tweening.Plugins.Options.StringOptions>,System.String,System.Boolean,System.Boolean)
extern void StringPlugin_SetFrom_m7F541B13D8C91B1ED197C70AD49AA94A480E6FD5 (void);
// 0x0000034B System.Void DG.Tweening.Plugins.StringPlugin::Reset(DG.Tweening.Core.TweenerCore`3<System.String,System.String,DG.Tweening.Plugins.Options.StringOptions>)
extern void StringPlugin_Reset_mCE185934F14CBC66803FFE6326EF09B5D8B3C0D8 (void);
// 0x0000034C System.String DG.Tweening.Plugins.StringPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<System.String,System.String,DG.Tweening.Plugins.Options.StringOptions>,System.String)
extern void StringPlugin_ConvertToStartValue_m34D7B1A6492C9FBCA91B13E86865E1C450E231A6 (void);
// 0x0000034D System.Void DG.Tweening.Plugins.StringPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<System.String,System.String,DG.Tweening.Plugins.Options.StringOptions>)
extern void StringPlugin_SetRelativeEndValue_m281AAEA3815035C5354255DDEA8AE9AEBAF7B81E (void);
// 0x0000034E System.Void DG.Tweening.Plugins.StringPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<System.String,System.String,DG.Tweening.Plugins.Options.StringOptions>)
extern void StringPlugin_SetChangeValue_m154A8DEFEC01D8C683858C11602A2CB62123841B (void);
// 0x0000034F System.Single DG.Tweening.Plugins.StringPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.StringOptions,System.Single,System.String)
extern void StringPlugin_GetSpeedBasedDuration_mA2F5245600035283DBF76137C655DC84268B0B4D (void);
// 0x00000350 System.Void DG.Tweening.Plugins.StringPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.StringOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<System.String>,DG.Tweening.Core.DOSetter`1<System.String>,System.Single,System.String,System.String,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void StringPlugin_EvaluateAndApply_m840D7E6F40F85B6AB5DBC825829141A7B41D42F3 (void);
// 0x00000351 System.Text.StringBuilder DG.Tweening.Plugins.StringPlugin::Append(System.String,System.Int32,System.Int32,System.Boolean)
extern void StringPlugin_Append_m393FFA9AFB5B28A407DEE6BEA599F096A2AD4BD1 (void);
// 0x00000352 System.Char[] DG.Tweening.Plugins.StringPlugin::ScrambledCharsToUse(DG.Tweening.Plugins.Options.StringOptions)
extern void StringPlugin_ScrambledCharsToUse_mB94A339E51635D7F97F27274382BEF24D40A85FB (void);
// 0x00000353 System.Void DG.Tweening.Plugins.StringPlugin::.ctor()
extern void StringPlugin__ctor_m431ECBCEC5363E152AA20286544F3444FC0BD70A (void);
// 0x00000354 System.Void DG.Tweening.Plugins.StringPlugin::.cctor()
extern void StringPlugin__cctor_m43D24AE51E83690FDDDF7DA16CF9BCD78E38672C (void);
// 0x00000355 System.Void DG.Tweening.Plugins.StringPluginExtensions::.cctor()
extern void StringPluginExtensions__cctor_m0EF08249B07EA4E0C6FADEC570B9B33E308FEDC7 (void);
// 0x00000356 System.Void DG.Tweening.Plugins.StringPluginExtensions::ScrambleChars(System.Char[])
extern void StringPluginExtensions_ScrambleChars_mEF2DC4717926EAEECA971FD887352673AC8683F2 (void);
// 0x00000357 System.Text.StringBuilder DG.Tweening.Plugins.StringPluginExtensions::AppendScrambledChars(System.Text.StringBuilder,System.Int32,System.Char[])
extern void StringPluginExtensions_AppendScrambledChars_mB1EC8925920C497377D1B1E8958C0B13BF88AF3F (void);
// 0x00000358 System.Void DG.Tweening.Plugins.FloatPlugin::Reset(DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions>)
extern void FloatPlugin_Reset_mDCE52CE11F99836C0653864F7F58BD29B65439C4 (void);
// 0x00000359 System.Void DG.Tweening.Plugins.FloatPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions>,System.Boolean)
extern void FloatPlugin_SetFrom_m232CA49201386D4A445C495174456277698E3356 (void);
// 0x0000035A System.Void DG.Tweening.Plugins.FloatPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions>,System.Single,System.Boolean,System.Boolean)
extern void FloatPlugin_SetFrom_mCED12A8DE946A659A94D436A3E4DABCD3071B679 (void);
// 0x0000035B System.Single DG.Tweening.Plugins.FloatPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions>,System.Single)
extern void FloatPlugin_ConvertToStartValue_m0158D70929F3E0AAD5ACB0085AC8E55729019B6E (void);
// 0x0000035C System.Void DG.Tweening.Plugins.FloatPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions>)
extern void FloatPlugin_SetRelativeEndValue_m62E86FC1881D233AF898B585417FCC59AB8FC9E4 (void);
// 0x0000035D System.Void DG.Tweening.Plugins.FloatPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<System.Single,System.Single,DG.Tweening.Plugins.Options.FloatOptions>)
extern void FloatPlugin_SetChangeValue_mBDCF4EFF8AC92B8BF2CBFBC4A9B88474C976DE50 (void);
// 0x0000035E System.Single DG.Tweening.Plugins.FloatPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.FloatOptions,System.Single,System.Single)
extern void FloatPlugin_GetSpeedBasedDuration_m069F5CAC863F1D352534798BA23B9CD53ADA76D0 (void);
// 0x0000035F System.Void DG.Tweening.Plugins.FloatPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.FloatOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<System.Single>,DG.Tweening.Core.DOSetter`1<System.Single>,System.Single,System.Single,System.Single,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void FloatPlugin_EvaluateAndApply_m9266A96307CA39D509331D9253E5114195B7F869 (void);
// 0x00000360 System.Void DG.Tweening.Plugins.FloatPlugin::.ctor()
extern void FloatPlugin__ctor_mDDE3C38DE5AF13E77CCAFC0F11823A547D4723C7 (void);
// 0x00000361 System.Void DG.Tweening.Plugins.Vector3Plugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions>)
extern void Vector3Plugin_Reset_m3917A46D223807D40CA67E69C3F45FA2E00AA338 (void);
// 0x00000362 System.Void DG.Tweening.Plugins.Vector3Plugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions>,System.Boolean)
extern void Vector3Plugin_SetFrom_m675F63D9FD954A1B29876A6E2C5E445476D54046 (void);
// 0x00000363 System.Void DG.Tweening.Plugins.Vector3Plugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions>,UnityEngine.Vector3,System.Boolean,System.Boolean)
extern void Vector3Plugin_SetFrom_mB473F1F765B584CCEB7F120C02E59C03D255DB50 (void);
// 0x00000364 UnityEngine.Vector3 DG.Tweening.Plugins.Vector3Plugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions>,UnityEngine.Vector3)
extern void Vector3Plugin_ConvertToStartValue_m42E52ED0EAAE4671EA20E8261F495A1657BBFBC8 (void);
// 0x00000365 System.Void DG.Tweening.Plugins.Vector3Plugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions>)
extern void Vector3Plugin_SetRelativeEndValue_m2C1B8ECD82B9174C83EB7E1E4FBE121F54209249 (void);
// 0x00000366 System.Void DG.Tweening.Plugins.Vector3Plugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions>)
extern void Vector3Plugin_SetChangeValue_m08F27B7FB6DE60F46C78163F088FA5E41DCFA98C (void);
// 0x00000367 System.Single DG.Tweening.Plugins.Vector3Plugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.VectorOptions,System.Single,UnityEngine.Vector3)
extern void Vector3Plugin_GetSpeedBasedDuration_m415AFD7A8572E5DC5A8BF1601705B12967BC5317 (void);
// 0x00000368 System.Void DG.Tweening.Plugins.Vector3Plugin::EvaluateAndApply(DG.Tweening.Plugins.Options.VectorOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,System.Single,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void Vector3Plugin_EvaluateAndApply_m3E6600D619A76F183A9B4E67B2E1310F49D91A79 (void);
// 0x00000369 System.Void DG.Tweening.Plugins.Vector3Plugin::.ctor()
extern void Vector3Plugin__ctor_m83B9306A6E1F7F6A4ED2F534D0614235705C8AEC (void);
// 0x0000036A System.Void DG.Tweening.Plugins.Options.IPlugOptions::Reset()
// 0x0000036B System.Void DG.Tweening.Plugins.Options.PathOptions::Reset()
extern void PathOptions_Reset_mCF088F48CAA5F7D294ACE99D8D12CD07F4BEA060 (void);
// 0x0000036C System.Void DG.Tweening.Plugins.Options.QuaternionOptions::Reset()
extern void QuaternionOptions_Reset_mFC1BCF4AC4D766AFE3BF6E93A16C5CB2146C2B9E (void);
// 0x0000036D System.Void DG.Tweening.Plugins.Options.UintOptions::Reset()
extern void UintOptions_Reset_m3A17AAA2C83433C4B0246CFFDB26210823E19EFC (void);
// 0x0000036E System.Void DG.Tweening.Plugins.Options.Vector3ArrayOptions::Reset()
extern void Vector3ArrayOptions_Reset_m5FBD5DB30FA9B5B3ECFF1AB327B53C95179E9589 (void);
// 0x0000036F System.Void DG.Tweening.Plugins.Options.NoOptions::Reset()
extern void NoOptions_Reset_m4C770E8106D2D141A2C9E1347A9EDF05CD4F0B9E (void);
// 0x00000370 System.Void DG.Tweening.Plugins.Options.ColorOptions::Reset()
extern void ColorOptions_Reset_m61D38CFBD63AA3DB49D4ABAC7460191E9EA425A4 (void);
// 0x00000371 System.Void DG.Tweening.Plugins.Options.FloatOptions::Reset()
extern void FloatOptions_Reset_m7796B4F4CD7ACD23C0D11F4191A6DEC735E515DE (void);
// 0x00000372 System.Void DG.Tweening.Plugins.Options.RectOptions::Reset()
extern void RectOptions_Reset_m06D669A680AF4A4BE9C113D5748F2727C9AF0300 (void);
// 0x00000373 System.Void DG.Tweening.Plugins.Options.StringOptions::Reset()
extern void StringOptions_Reset_mC44E99B51F61681EE22B9D7FB9D0F4698A568397 (void);
// 0x00000374 System.Void DG.Tweening.Plugins.Options.VectorOptions::Reset()
extern void VectorOptions_Reset_mD2EB7C2BEE4AD4B30E6552CD5CF925B06B1EDEFE (void);
// 0x00000375 System.Boolean DG.Tweening.Plugins.Core.SpecialPluginsUtils::SetLookAt(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Vector3,DG.Tweening.Plugins.Options.QuaternionOptions>)
extern void SpecialPluginsUtils_SetLookAt_m032BE1B008809A24C0B4BD53A9689F7DD70F7FCF (void);
// 0x00000376 System.Boolean DG.Tweening.Plugins.Core.SpecialPluginsUtils::SetPunch(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>)
extern void SpecialPluginsUtils_SetPunch_m65C9920310ABC6FC6C483A6CEC088F3A360EE3CA (void);
// 0x00000377 System.Boolean DG.Tweening.Plugins.Core.SpecialPluginsUtils::SetShake(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>)
extern void SpecialPluginsUtils_SetShake_m6F23CBF51E66BEAB3791161223E67374E976FF20 (void);
// 0x00000378 System.Boolean DG.Tweening.Plugins.Core.SpecialPluginsUtils::SetCameraShakePosition(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3[],DG.Tweening.Plugins.Options.Vector3ArrayOptions>)
extern void SpecialPluginsUtils_SetCameraShakePosition_m49B0FC03FA80CF51647589CCCB44BA1487B037A3 (void);
// 0x00000379 DG.Tweening.Core.DOGetter`1<T1> DG.Tweening.Plugins.Core.IPlugSetter`4::Getter()
// 0x0000037A DG.Tweening.Core.DOSetter`1<T1> DG.Tweening.Plugins.Core.IPlugSetter`4::Setter()
// 0x0000037B T2 DG.Tweening.Plugins.Core.IPlugSetter`4::EndValue()
// 0x0000037C TPlugOptions DG.Tweening.Plugins.Core.IPlugSetter`4::GetOptions()
// 0x0000037D System.Void DG.Tweening.Plugins.Core.ABSTweenPlugin`3::Reset(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>)
// 0x0000037E System.Void DG.Tweening.Plugins.Core.ABSTweenPlugin`3::SetFrom(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>,System.Boolean)
// 0x0000037F System.Void DG.Tweening.Plugins.Core.ABSTweenPlugin`3::SetFrom(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>,T2,System.Boolean,System.Boolean)
// 0x00000380 T2 DG.Tweening.Plugins.Core.ABSTweenPlugin`3::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>,T1)
// 0x00000381 System.Void DG.Tweening.Plugins.Core.ABSTweenPlugin`3::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>)
// 0x00000382 System.Void DG.Tweening.Plugins.Core.ABSTweenPlugin`3::SetChangeValue(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>)
// 0x00000383 System.Single DG.Tweening.Plugins.Core.ABSTweenPlugin`3::GetSpeedBasedDuration(TPlugOptions,System.Single,T2)
// 0x00000384 System.Void DG.Tweening.Plugins.Core.ABSTweenPlugin`3::EvaluateAndApply(TPlugOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<T1>,DG.Tweening.Core.DOSetter`1<T1>,System.Single,T2,T2,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
// 0x00000385 System.Void DG.Tweening.Plugins.Core.ABSTweenPlugin`3::.ctor()
// 0x00000386 DG.Tweening.Plugins.Core.ABSTweenPlugin`3<T1,T2,TPlugOptions> DG.Tweening.Plugins.Core.PluginsManager::GetDefaultPlugin()
// 0x00000387 DG.Tweening.Plugins.Core.ABSTweenPlugin`3<T1,T2,TPlugOptions> DG.Tweening.Plugins.Core.PluginsManager::GetCustomPlugin()
// 0x00000388 System.Void DG.Tweening.Plugins.Core.PluginsManager::PurgeAll()
extern void PluginsManager_PurgeAll_m9EDAE828FEBAFA93291F199449C5AE6648FB949D (void);
// 0x00000389 System.Int32 DG.Tweening.Plugins.Core.PathCore.CubicBezierDecoder::get_minInputWaypoints()
extern void CubicBezierDecoder_get_minInputWaypoints_m7B83624FDC0792C7D99C66E4293B78C3CAD551DF (void);
// 0x0000038A System.Void DG.Tweening.Plugins.Core.PathCore.CubicBezierDecoder::FinalizePath(DG.Tweening.Plugins.Core.PathCore.Path,UnityEngine.Vector3[],System.Boolean)
extern void CubicBezierDecoder_FinalizePath_mE98400AC5E2006EC8BF639774C71A37C3E47E03C (void);
// 0x0000038B UnityEngine.Vector3 DG.Tweening.Plugins.Core.PathCore.CubicBezierDecoder::GetPoint(System.Single,UnityEngine.Vector3[],DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Core.PathCore.ControlPoint[])
extern void CubicBezierDecoder_GetPoint_m1FA6505950448A4973E190441F2762203F2C0C71 (void);
// 0x0000038C System.Void DG.Tweening.Plugins.Core.PathCore.CubicBezierDecoder::SetTimeToLengthTables(DG.Tweening.Plugins.Core.PathCore.Path,System.Int32)
extern void CubicBezierDecoder_SetTimeToLengthTables_m8DE088CFF73E9F1E1A90A428D307B856FC92094C (void);
// 0x0000038D System.Void DG.Tweening.Plugins.Core.PathCore.CubicBezierDecoder::SetWaypointsLengths(DG.Tweening.Plugins.Core.PathCore.Path,System.Int32)
extern void CubicBezierDecoder_SetWaypointsLengths_mC384E54972E37D9157D8799E6E502BB0093CBC40 (void);
// 0x0000038E System.Void DG.Tweening.Plugins.Core.PathCore.CubicBezierDecoder::.ctor()
extern void CubicBezierDecoder__ctor_mEF0D332E62BCB16F6DA71192746F7A43A9B3B461 (void);
// 0x0000038F System.Void DG.Tweening.Plugins.Core.PathCore.CubicBezierDecoder::.cctor()
extern void CubicBezierDecoder__cctor_mF060F45215DCDEF59F446852B0B71F2E578B015D (void);
// 0x00000390 System.Void DG.Tweening.Plugins.Core.PathCore.ControlPoint::.ctor(UnityEngine.Vector3,UnityEngine.Vector3)
extern void ControlPoint__ctor_m61705A2A5C9615DC8E052515CB9B1F62A4600668 (void);
// 0x00000391 DG.Tweening.Plugins.Core.PathCore.ControlPoint DG.Tweening.Plugins.Core.PathCore.ControlPoint::op_Addition(DG.Tweening.Plugins.Core.PathCore.ControlPoint,UnityEngine.Vector3)
extern void ControlPoint_op_Addition_m273B684A11735A299C1886F36F41C7C6CB27E49D (void);
// 0x00000392 System.String DG.Tweening.Plugins.Core.PathCore.ControlPoint::ToString()
extern void ControlPoint_ToString_m160B7B0BCC744DE7EF312A520CE814561A24C236 (void);
// 0x00000393 System.Void DG.Tweening.Plugins.Core.PathCore.ABSPathDecoder::FinalizePath(DG.Tweening.Plugins.Core.PathCore.Path,UnityEngine.Vector3[],System.Boolean)
// 0x00000394 UnityEngine.Vector3 DG.Tweening.Plugins.Core.PathCore.ABSPathDecoder::GetPoint(System.Single,UnityEngine.Vector3[],DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Core.PathCore.ControlPoint[])
// 0x00000395 System.Int32 DG.Tweening.Plugins.Core.PathCore.ABSPathDecoder::get_minInputWaypoints()
// 0x00000396 System.Void DG.Tweening.Plugins.Core.PathCore.ABSPathDecoder::.ctor()
extern void ABSPathDecoder__ctor_m9000360B01DBC18CF8310F47FFEC61E0FC0E4C96 (void);
// 0x00000397 System.Int32 DG.Tweening.Plugins.Core.PathCore.CatmullRomDecoder::get_minInputWaypoints()
extern void CatmullRomDecoder_get_minInputWaypoints_m153ED0D4FC6DD92AE23C25BAD56C028CD94EFAA0 (void);
// 0x00000398 System.Void DG.Tweening.Plugins.Core.PathCore.CatmullRomDecoder::FinalizePath(DG.Tweening.Plugins.Core.PathCore.Path,UnityEngine.Vector3[],System.Boolean)
extern void CatmullRomDecoder_FinalizePath_m9BF4FC78055D6A3E593E05BD4B05A496215A4D4E (void);
// 0x00000399 UnityEngine.Vector3 DG.Tweening.Plugins.Core.PathCore.CatmullRomDecoder::GetPoint(System.Single,UnityEngine.Vector3[],DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Core.PathCore.ControlPoint[])
extern void CatmullRomDecoder_GetPoint_mCDAE4F84C87110712EA863C289A715BC1E232835 (void);
// 0x0000039A System.Void DG.Tweening.Plugins.Core.PathCore.CatmullRomDecoder::SetTimeToLengthTables(DG.Tweening.Plugins.Core.PathCore.Path,System.Int32)
extern void CatmullRomDecoder_SetTimeToLengthTables_m0E06C670A5FBDCA57207AB97B869ED8191D3ABED (void);
// 0x0000039B System.Void DG.Tweening.Plugins.Core.PathCore.CatmullRomDecoder::SetWaypointsLengths(DG.Tweening.Plugins.Core.PathCore.Path,System.Int32)
extern void CatmullRomDecoder_SetWaypointsLengths_mFB5DE54C8D41124A153BA13614F79F49BEC3ACC1 (void);
// 0x0000039C System.Void DG.Tweening.Plugins.Core.PathCore.CatmullRomDecoder::.ctor()
extern void CatmullRomDecoder__ctor_m40C3B62E2B6753C8C604B192AE38CB0E00751B63 (void);
// 0x0000039D System.Void DG.Tweening.Plugins.Core.PathCore.CatmullRomDecoder::.cctor()
extern void CatmullRomDecoder__cctor_m171A41BE4CB68DC3602E24837E2F5E067FB150D0 (void);
// 0x0000039E System.Int32 DG.Tweening.Plugins.Core.PathCore.LinearDecoder::get_minInputWaypoints()
extern void LinearDecoder_get_minInputWaypoints_m281051707CDDE5604573AD93F1080AE15B74B507 (void);
// 0x0000039F System.Void DG.Tweening.Plugins.Core.PathCore.LinearDecoder::FinalizePath(DG.Tweening.Plugins.Core.PathCore.Path,UnityEngine.Vector3[],System.Boolean)
extern void LinearDecoder_FinalizePath_m8A38C47B480F0AFC98FDC223622982AED28DAF65 (void);
// 0x000003A0 UnityEngine.Vector3 DG.Tweening.Plugins.Core.PathCore.LinearDecoder::GetPoint(System.Single,UnityEngine.Vector3[],DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Core.PathCore.ControlPoint[])
extern void LinearDecoder_GetPoint_m923A45C4AECF4B832BACDFE7D338611E3014172E (void);
// 0x000003A1 System.Void DG.Tweening.Plugins.Core.PathCore.LinearDecoder::SetTimeToLengthTables(DG.Tweening.Plugins.Core.PathCore.Path,System.Int32)
extern void LinearDecoder_SetTimeToLengthTables_mB336D6CE0306247E6C80F9B472E86F744520550B (void);
// 0x000003A2 System.Void DG.Tweening.Plugins.Core.PathCore.LinearDecoder::SetWaypointsLengths(DG.Tweening.Plugins.Core.PathCore.Path,System.Int32)
extern void LinearDecoder_SetWaypointsLengths_mCBF22C43656162E062D89990E7E091A40A9E3417 (void);
// 0x000003A3 System.Void DG.Tweening.Plugins.Core.PathCore.LinearDecoder::.ctor()
extern void LinearDecoder__ctor_mD23879EA491AC8A5105D0FC69F6D16BDA0998CA5 (void);
// 0x000003A4 System.Int32 DG.Tweening.Plugins.Core.PathCore.Path::get_minInputWaypoints()
extern void Path_get_minInputWaypoints_m95720C5307FB3D3A9F6F750389FE589302340693 (void);
// 0x000003A5 System.Void DG.Tweening.Plugins.Core.PathCore.Path::.ctor(DG.Tweening.PathType,UnityEngine.Vector3[],System.Int32,System.Nullable`1<UnityEngine.Color>)
extern void Path__ctor_mE17CD95D405E8FF0440A6631D97C8876074B4824 (void);
// 0x000003A6 System.Void DG.Tweening.Plugins.Core.PathCore.Path::.ctor()
extern void Path__ctor_mE316C2800B03412006B86883F27ECC5CC08CA3AB (void);
// 0x000003A7 System.Void DG.Tweening.Plugins.Core.PathCore.Path::FinalizePath(System.Boolean,DG.Tweening.AxisConstraint,UnityEngine.Vector3)
extern void Path_FinalizePath_m2D9A9AE99327DBCDF8EDBAE55E149ED1F4BD3BB7 (void);
// 0x000003A8 UnityEngine.Vector3 DG.Tweening.Plugins.Core.PathCore.Path::GetPoint(System.Single,System.Boolean)
extern void Path_GetPoint_m6D04BA28C0F375D3030DD2714E2295A2A656AFE1 (void);
// 0x000003A9 System.Single DG.Tweening.Plugins.Core.PathCore.Path::ConvertToConstantPathPerc(System.Single)
extern void Path_ConvertToConstantPathPerc_m946321C867B331E56BFBF358F57391804611B570 (void);
// 0x000003AA System.Int32 DG.Tweening.Plugins.Core.PathCore.Path::GetWaypointIndexFromPerc(System.Single,System.Boolean)
extern void Path_GetWaypointIndexFromPerc_m3C41176271872EA1D2B5AE1C18EFAAE729CB2C00 (void);
// 0x000003AB UnityEngine.Vector3[] DG.Tweening.Plugins.Core.PathCore.Path::GetDrawPoints(DG.Tweening.Plugins.Core.PathCore.Path,System.Int32)
extern void Path_GetDrawPoints_mB91D933EBB4F22E74F19B72611C147089E0D9157 (void);
// 0x000003AC System.Void DG.Tweening.Plugins.Core.PathCore.Path::RefreshNonLinearDrawWps(DG.Tweening.Plugins.Core.PathCore.Path)
extern void Path_RefreshNonLinearDrawWps_m80E683DA112432AAE39D0E8C3F2C8B41CBB60285 (void);
// 0x000003AD System.Void DG.Tweening.Plugins.Core.PathCore.Path::Destroy()
extern void Path_Destroy_mB5139AE354F434F76149B4672ACE7B835FBE029C (void);
// 0x000003AE DG.Tweening.Plugins.Core.PathCore.Path DG.Tweening.Plugins.Core.PathCore.Path::CloneIncremental(System.Int32)
extern void Path_CloneIncremental_m02F0B70C4F51FBAEA81DB888E37FBA2E4962D428 (void);
// 0x000003AF System.Void DG.Tweening.Plugins.Core.PathCore.Path::AssignWaypoints(UnityEngine.Vector3[],System.Boolean)
extern void Path_AssignWaypoints_mF34F707A39321C2912B6C7E4D23DAEEEEF61D2F8 (void);
// 0x000003B0 System.Void DG.Tweening.Plugins.Core.PathCore.Path::AssignDecoder(DG.Tweening.PathType)
extern void Path_AssignDecoder_mD3A97C75986B01E822BF9726461B65DE77BC0B98 (void);
// 0x000003B1 System.Void DG.Tweening.Plugins.Core.PathCore.Path::Draw()
extern void Path_Draw_mAE9CA81D5ED87ECC9C1213EFCA01639CF0AE2B32 (void);
// 0x000003B2 System.Void DG.Tweening.Plugins.Core.PathCore.Path::Draw(DG.Tweening.Plugins.Core.PathCore.Path)
extern void Path_Draw_m1D276328F6B71518A310A8D1CCB4B29B5455E33C (void);
// 0x000003B3 UnityEngine.Vector3 DG.Tweening.Plugins.Core.PathCore.Path::ConvertToDrawPoint(UnityEngine.Vector3,DG.Tweening.Plugins.Options.PathOptions)
extern void Path_ConvertToDrawPoint_mA82DE42189D5973E0C7D917C099490B9A2A796E6 (void);
// 0x000003B4 DG.Tweening.CustomPlugins.PureQuaternionPlugin DG.Tweening.CustomPlugins.PureQuaternionPlugin::Plug()
extern void PureQuaternionPlugin_Plug_m9E68B44A606149BDC72B0A34B213F4D69D69B057 (void);
// 0x000003B5 System.Void DG.Tweening.CustomPlugins.PureQuaternionPlugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Quaternion,DG.Tweening.Plugins.Options.NoOptions>)
extern void PureQuaternionPlugin_Reset_mB5AFC237DF652F5063FD9AB3DF61022E67A0CC59 (void);
// 0x000003B6 System.Void DG.Tweening.CustomPlugins.PureQuaternionPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Quaternion,DG.Tweening.Plugins.Options.NoOptions>,System.Boolean)
extern void PureQuaternionPlugin_SetFrom_mFF76A1E62A2E98213522B7CDF98943A46A3923B6 (void);
// 0x000003B7 System.Void DG.Tweening.CustomPlugins.PureQuaternionPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Quaternion,DG.Tweening.Plugins.Options.NoOptions>,UnityEngine.Quaternion,System.Boolean,System.Boolean)
extern void PureQuaternionPlugin_SetFrom_m36CC738145B37615D47FFB8C9A5977D5DBA47691 (void);
// 0x000003B8 UnityEngine.Quaternion DG.Tweening.CustomPlugins.PureQuaternionPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Quaternion,DG.Tweening.Plugins.Options.NoOptions>,UnityEngine.Quaternion)
extern void PureQuaternionPlugin_ConvertToStartValue_mA9949A5D3656481E2BDF410B5EE57F9BC14C9025 (void);
// 0x000003B9 System.Void DG.Tweening.CustomPlugins.PureQuaternionPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Quaternion,DG.Tweening.Plugins.Options.NoOptions>)
extern void PureQuaternionPlugin_SetRelativeEndValue_m0395FF9730B5E046CCABEFB12C33926176F3F04F (void);
// 0x000003BA System.Void DG.Tweening.CustomPlugins.PureQuaternionPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Quaternion,UnityEngine.Quaternion,DG.Tweening.Plugins.Options.NoOptions>)
extern void PureQuaternionPlugin_SetChangeValue_m51824249B9B13071AB74B6EB35171469AF86C448 (void);
// 0x000003BB System.Single DG.Tweening.CustomPlugins.PureQuaternionPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.Options.NoOptions,System.Single,UnityEngine.Quaternion)
extern void PureQuaternionPlugin_GetSpeedBasedDuration_mD2607B3BA5EEC0012E01D0A2E7631DAECC980DEF (void);
// 0x000003BC System.Void DG.Tweening.CustomPlugins.PureQuaternionPlugin::EvaluateAndApply(DG.Tweening.Plugins.Options.NoOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Quaternion>,DG.Tweening.Core.DOSetter`1<UnityEngine.Quaternion>,System.Single,UnityEngine.Quaternion,UnityEngine.Quaternion,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void PureQuaternionPlugin_EvaluateAndApply_m93205FF7464FD7AA204CBC01BE7052DB75B479BA (void);
// 0x000003BD System.Void DG.Tweening.CustomPlugins.PureQuaternionPlugin::.ctor()
extern void PureQuaternionPlugin__ctor_mA8C3C2835915524E8267F4F6DA7A63DA6AE6A7EE (void);
// 0x000003BE System.Void DG.Tweening.Core.ABSSequentiable::.ctor()
extern void ABSSequentiable__ctor_m70B4D2A525C71C87049ED3177A821B264D682A7B (void);
// 0x000003BF System.Void DG.Tweening.Core.DOGetter`1::.ctor(System.Object,System.IntPtr)
// 0x000003C0 T DG.Tweening.Core.DOGetter`1::Invoke()
// 0x000003C1 System.IAsyncResult DG.Tweening.Core.DOGetter`1::BeginInvoke(System.AsyncCallback,System.Object)
// 0x000003C2 T DG.Tweening.Core.DOGetter`1::EndInvoke(System.IAsyncResult)
// 0x000003C3 System.Void DG.Tweening.Core.DOSetter`1::.ctor(System.Object,System.IntPtr)
// 0x000003C4 System.Void DG.Tweening.Core.DOSetter`1::Invoke(T)
// 0x000003C5 System.IAsyncResult DG.Tweening.Core.DOSetter`1::BeginInvoke(T,System.AsyncCallback,System.Object)
// 0x000003C6 System.Void DG.Tweening.Core.DOSetter`1::EndInvoke(System.IAsyncResult)
// 0x000003C7 System.Int32 DG.Tweening.Core.Debugger::get_logPriority()
extern void Debugger_get_logPriority_m7AC0D46AE0BA9AC3ED1ED50AD20734033D16D0AC (void);
// 0x000003C8 System.Void DG.Tweening.Core.Debugger::Log(System.Object)
extern void Debugger_Log_mED54C2BED376B5F8631F5B23490BE1608675E456 (void);
// 0x000003C9 System.Void DG.Tweening.Core.Debugger::LogWarning(System.Object,DG.Tweening.Tween)
extern void Debugger_LogWarning_mB528DCD3175EB2D670A62A6507A656F8DE76D06E (void);
// 0x000003CA System.Void DG.Tweening.Core.Debugger::LogError(System.Object,DG.Tweening.Tween)
extern void Debugger_LogError_mBAD7F720F13F12B84A27F564F83F00B66EEE6B9E (void);
// 0x000003CB System.Void DG.Tweening.Core.Debugger::LogSafeModeCapturedError(System.Object,DG.Tweening.Tween)
extern void Debugger_LogSafeModeCapturedError_m0EE4B4F14B9243B4A9BD757853CAB77B5A888ED6 (void);
// 0x000003CC System.Void DG.Tweening.Core.Debugger::LogReport(System.Object)
extern void Debugger_LogReport_m2A1E8B46BC302E45B799171C5C51BFA4DD07693C (void);
// 0x000003CD System.Void DG.Tweening.Core.Debugger::LogSafeModeReport(System.Object)
extern void Debugger_LogSafeModeReport_m2B8F77FCC5EAA0AD7F936AF62EFBC4FDE5D19E71 (void);
// 0x000003CE System.Void DG.Tweening.Core.Debugger::LogInvalidTween(DG.Tweening.Tween)
extern void Debugger_LogInvalidTween_mEC44C60EC29E4EE401EFE473B01A209D79F3A83F (void);
// 0x000003CF System.Void DG.Tweening.Core.Debugger::LogNestedTween(DG.Tweening.Tween)
extern void Debugger_LogNestedTween_m409C30CE26DFF284388E93FDD37738CF2116F128 (void);
// 0x000003D0 System.Void DG.Tweening.Core.Debugger::LogNullTween(DG.Tweening.Tween)
extern void Debugger_LogNullTween_m9C6A433A68F2C3DFCDFF127434135F8BBC116740 (void);
// 0x000003D1 System.Void DG.Tweening.Core.Debugger::LogNonPathTween(DG.Tweening.Tween)
extern void Debugger_LogNonPathTween_m49C992074A1EA55D708A26531D3FE7078A66405E (void);
// 0x000003D2 System.Void DG.Tweening.Core.Debugger::LogMissingMaterialProperty(System.String)
extern void Debugger_LogMissingMaterialProperty_m49458D73DDB5BBBFB7AE84BA0A1C638A6A5B27C7 (void);
// 0x000003D3 System.Void DG.Tweening.Core.Debugger::LogMissingMaterialProperty(System.Int32)
extern void Debugger_LogMissingMaterialProperty_m8847B17D16E01FB04A460D0984CAD3FEEF9AB854 (void);
// 0x000003D4 System.Void DG.Tweening.Core.Debugger::LogRemoveActiveTweenError(System.String,DG.Tweening.Tween)
extern void Debugger_LogRemoveActiveTweenError_m30A8AF0E0595F76045BF409584252064AE8460CD (void);
// 0x000003D5 System.Void DG.Tweening.Core.Debugger::LogAddActiveTweenError(System.String,DG.Tweening.Tween)
extern void Debugger_LogAddActiveTweenError_mAD5420AA037EB3CAAF612D228AF6055A0D80200E (void);
// 0x000003D6 System.Void DG.Tweening.Core.Debugger::SetLogPriority(DG.Tweening.LogBehaviour)
extern void Debugger_SetLogPriority_m14859DD9F9DD27E2D875BF50939F31D0F7043D6E (void);
// 0x000003D7 System.Boolean DG.Tweening.Core.Debugger::ShouldLogSafeModeCapturedError()
extern void Debugger_ShouldLogSafeModeCapturedError_m5EE800D2A5FD98C95C119D9AF68035095A123C47 (void);
// 0x000003D8 System.String DG.Tweening.Core.Debugger::GetDebugDataMessage(DG.Tweening.Tween)
extern void Debugger_GetDebugDataMessage_m08353947E413DB5642A38FE8A746F79F6F880C4B (void);
// 0x000003D9 System.Void DG.Tweening.Core.Debugger::AddDebugDataToMessage(System.String&,DG.Tweening.Tween)
extern void Debugger_AddDebugDataToMessage_mDA43D632BCA0B5487160E204FBCA074DA97AC509 (void);
// 0x000003DA System.Void DG.Tweening.Core.Debugger/Sequence::LogAddToNullSequence()
extern void Sequence_LogAddToNullSequence_m9D28C74E58E7BFD59961AEAAA7C84E35C33F09E9 (void);
// 0x000003DB System.Void DG.Tweening.Core.Debugger/Sequence::LogAddToInactiveSequence()
extern void Sequence_LogAddToInactiveSequence_m91917D80E18F30B5AA21BE2ECD5B1716ABB802E1 (void);
// 0x000003DC System.Void DG.Tweening.Core.Debugger/Sequence::LogAddToLockedSequence()
extern void Sequence_LogAddToLockedSequence_m0ACADB5C3D96FDF32DD765792D5ED9A9D11795A2 (void);
// 0x000003DD System.Void DG.Tweening.Core.Debugger/Sequence::LogAddNullTween()
extern void Sequence_LogAddNullTween_m0A4624D91F7056D1A511BE858284E2C57E6757B2 (void);
// 0x000003DE System.Void DG.Tweening.Core.Debugger/Sequence::LogAddInactiveTween(DG.Tweening.Tween)
extern void Sequence_LogAddInactiveTween_m14520D9DA312BAE265A62A4E8DAB68A7FE48AEF9 (void);
// 0x000003DF System.Void DG.Tweening.Core.Debugger/Sequence::LogAddAlreadySequencedTween(DG.Tweening.Tween)
extern void Sequence_LogAddAlreadySequencedTween_mEEF676C9EB96FDDBD4D590787DA4FA923B2EC87C (void);
// 0x000003E0 System.Void DG.Tweening.Core.DOTweenComponent::Awake()
extern void DOTweenComponent_Awake_m2B8731C5F756551F8CF374ABA9BBA5D97C36A956 (void);
// 0x000003E1 System.Void DG.Tweening.Core.DOTweenComponent::Start()
extern void DOTweenComponent_Start_m14BD516448C0D8B487AA199C84AFA5984EE38E71 (void);
// 0x000003E2 System.Void DG.Tweening.Core.DOTweenComponent::Update()
extern void DOTweenComponent_Update_m1EB628942CC4EA34F24E9AA2326532D8AD5B0541 (void);
// 0x000003E3 System.Void DG.Tweening.Core.DOTweenComponent::LateUpdate()
extern void DOTweenComponent_LateUpdate_mB523F2163A4033594262DB217EDC62C6806DBAF8 (void);
// 0x000003E4 System.Void DG.Tweening.Core.DOTweenComponent::FixedUpdate()
extern void DOTweenComponent_FixedUpdate_m5D4B2412E608B1F490333A9A5680074A5C02A7C0 (void);
// 0x000003E5 System.Void DG.Tweening.Core.DOTweenComponent::OnDrawGizmos()
extern void DOTweenComponent_OnDrawGizmos_m28A4D3B2A76DC3FE70A1BBAE4D5E993624526FCE (void);
// 0x000003E6 System.Void DG.Tweening.Core.DOTweenComponent::OnDestroy()
extern void DOTweenComponent_OnDestroy_m813D82282FC9C21C3DF589E5A954FA11FAFEA3BA (void);
// 0x000003E7 System.Void DG.Tweening.Core.DOTweenComponent::OnApplicationPause(System.Boolean)
extern void DOTweenComponent_OnApplicationPause_m93370BF7B8AA301B841B0BBCC61E9579AD3CDDF7 (void);
// 0x000003E8 System.Void DG.Tweening.Core.DOTweenComponent::OnApplicationQuit()
extern void DOTweenComponent_OnApplicationQuit_mCF7BB912F912C5B91A6D6A6141DE8E085D8C3E7F (void);
// 0x000003E9 DG.Tweening.IDOTweenInit DG.Tweening.Core.DOTweenComponent::SetCapacity(System.Int32,System.Int32)
extern void DOTweenComponent_SetCapacity_m3B4936853427792607B2BC9A022300724C6275B8 (void);
// 0x000003EA System.Collections.IEnumerator DG.Tweening.Core.DOTweenComponent::WaitForCompletion(DG.Tweening.Tween)
extern void DOTweenComponent_WaitForCompletion_m1F26E152C4B17EF6797B49AD8070C40B872D615C (void);
// 0x000003EB System.Collections.IEnumerator DG.Tweening.Core.DOTweenComponent::WaitForRewind(DG.Tweening.Tween)
extern void DOTweenComponent_WaitForRewind_m7719D9F5E014FC7403F70045B4E732D40428CB27 (void);
// 0x000003EC System.Collections.IEnumerator DG.Tweening.Core.DOTweenComponent::WaitForKill(DG.Tweening.Tween)
extern void DOTweenComponent_WaitForKill_m53130F016C0B24C5345C66972D287236F374AC37 (void);
// 0x000003ED System.Collections.IEnumerator DG.Tweening.Core.DOTweenComponent::WaitForElapsedLoops(DG.Tweening.Tween,System.Int32)
extern void DOTweenComponent_WaitForElapsedLoops_mE9D67BF81141C1D92F85ECD20FFC51E2E8777774 (void);
// 0x000003EE System.Collections.IEnumerator DG.Tweening.Core.DOTweenComponent::WaitForPosition(DG.Tweening.Tween,System.Single)
extern void DOTweenComponent_WaitForPosition_m0F07976F5331F98F88289288ACE220D0F708FE84 (void);
// 0x000003EF System.Collections.IEnumerator DG.Tweening.Core.DOTweenComponent::WaitForStart(DG.Tweening.Tween)
extern void DOTweenComponent_WaitForStart_m91F59DF680F874160B5ED20691215591B75A4B65 (void);
// 0x000003F0 System.Void DG.Tweening.Core.DOTweenComponent::Create()
extern void DOTweenComponent_Create_m2F098BE99694BEFEF39E22C3D8294ED04E9A11D1 (void);
// 0x000003F1 System.Void DG.Tweening.Core.DOTweenComponent::DestroyInstance()
extern void DOTweenComponent_DestroyInstance_mA0743B89EF2340212A90DA6C777A021BB346723E (void);
// 0x000003F2 System.Void DG.Tweening.Core.DOTweenComponent::.ctor()
extern void DOTweenComponent__ctor_mA7CF37E0C0B8644ED78CCB9DD9A0386349236501 (void);
// 0x000003F3 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForCompletion>d__17::.ctor(System.Int32)
extern void U3CWaitForCompletionU3Ed__17__ctor_m9B9BD6B6B1C86B245E628C7BBD96C48A4FEC90A7 (void);
// 0x000003F4 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForCompletion>d__17::System.IDisposable.Dispose()
extern void U3CWaitForCompletionU3Ed__17_System_IDisposable_Dispose_m6E7622FD584C46143CE48AE8D901B82D6B81E2C2 (void);
// 0x000003F5 System.Boolean DG.Tweening.Core.DOTweenComponent/<WaitForCompletion>d__17::MoveNext()
extern void U3CWaitForCompletionU3Ed__17_MoveNext_m2F1960A98359AC8B9E035986BD45BD5405679C4B (void);
// 0x000003F6 System.Object DG.Tweening.Core.DOTweenComponent/<WaitForCompletion>d__17::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForCompletionU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC05B77AA530F9675D5FDDCA1A4733BC27E3C2439 (void);
// 0x000003F7 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForCompletion>d__17::System.Collections.IEnumerator.Reset()
extern void U3CWaitForCompletionU3Ed__17_System_Collections_IEnumerator_Reset_m7BFAF4BCB2EB8824994930872AA1F4C9F937F8D6 (void);
// 0x000003F8 System.Object DG.Tweening.Core.DOTweenComponent/<WaitForCompletion>d__17::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForCompletionU3Ed__17_System_Collections_IEnumerator_get_Current_m9BD50CD51DBAC1EF925608B2C7F90B8514730A93 (void);
// 0x000003F9 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForElapsedLoops>d__20::.ctor(System.Int32)
extern void U3CWaitForElapsedLoopsU3Ed__20__ctor_m0E883FEA0DB5A2F545307245677A55F01475573D (void);
// 0x000003FA System.Void DG.Tweening.Core.DOTweenComponent/<WaitForElapsedLoops>d__20::System.IDisposable.Dispose()
extern void U3CWaitForElapsedLoopsU3Ed__20_System_IDisposable_Dispose_mB245A3DDFCDA84977DC07B90F241B2EDAECABF12 (void);
// 0x000003FB System.Boolean DG.Tweening.Core.DOTweenComponent/<WaitForElapsedLoops>d__20::MoveNext()
extern void U3CWaitForElapsedLoopsU3Ed__20_MoveNext_m66D5E529747FA0233925AD4E0019ECE0A1566281 (void);
// 0x000003FC System.Object DG.Tweening.Core.DOTweenComponent/<WaitForElapsedLoops>d__20::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForElapsedLoopsU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3F1FFC50C883BF778D0BAB6CD66026A11C271745 (void);
// 0x000003FD System.Void DG.Tweening.Core.DOTweenComponent/<WaitForElapsedLoops>d__20::System.Collections.IEnumerator.Reset()
extern void U3CWaitForElapsedLoopsU3Ed__20_System_Collections_IEnumerator_Reset_m3AEF8C8FF47ED0146C773C81FF6E95551AC5656B (void);
// 0x000003FE System.Object DG.Tweening.Core.DOTweenComponent/<WaitForElapsedLoops>d__20::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForElapsedLoopsU3Ed__20_System_Collections_IEnumerator_get_Current_m168026DFCA711B3CCBB9FE3A9DC9C5AF591E7644 (void);
// 0x000003FF System.Void DG.Tweening.Core.DOTweenComponent/<WaitForKill>d__19::.ctor(System.Int32)
extern void U3CWaitForKillU3Ed__19__ctor_mAD5A32E6A99FF369B4E92459748D6DBE55DB3352 (void);
// 0x00000400 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForKill>d__19::System.IDisposable.Dispose()
extern void U3CWaitForKillU3Ed__19_System_IDisposable_Dispose_m09429E49D6D5A1F3A4ADD0AAFBD069F31B0A327E (void);
// 0x00000401 System.Boolean DG.Tweening.Core.DOTweenComponent/<WaitForKill>d__19::MoveNext()
extern void U3CWaitForKillU3Ed__19_MoveNext_m73B95ED60A8D89F7128C3127B0E32E262C03D4CE (void);
// 0x00000402 System.Object DG.Tweening.Core.DOTweenComponent/<WaitForKill>d__19::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForKillU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1F3DB7C082A0CBAE4F421246790D84510B33F5BC (void);
// 0x00000403 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForKill>d__19::System.Collections.IEnumerator.Reset()
extern void U3CWaitForKillU3Ed__19_System_Collections_IEnumerator_Reset_m6F137BD0A99B05D514028F733A890657CB13F3C5 (void);
// 0x00000404 System.Object DG.Tweening.Core.DOTweenComponent/<WaitForKill>d__19::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForKillU3Ed__19_System_Collections_IEnumerator_get_Current_mC7DAE3183F86C69AF2D4D0E342672FE0D9C05063 (void);
// 0x00000405 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForPosition>d__21::.ctor(System.Int32)
extern void U3CWaitForPositionU3Ed__21__ctor_m00B910EE847D915D805BE8C2D4B22C563F046EDE (void);
// 0x00000406 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForPosition>d__21::System.IDisposable.Dispose()
extern void U3CWaitForPositionU3Ed__21_System_IDisposable_Dispose_mAEEE1414E694730A874AF5B7F0165A1CB6BA2C01 (void);
// 0x00000407 System.Boolean DG.Tweening.Core.DOTweenComponent/<WaitForPosition>d__21::MoveNext()
extern void U3CWaitForPositionU3Ed__21_MoveNext_m757E20ED1DF079EEABC7CE8C48234E22FA0301F9 (void);
// 0x00000408 System.Object DG.Tweening.Core.DOTweenComponent/<WaitForPosition>d__21::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForPositionU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2A0D03647872D4D91D71A0D5C6173C64177A0B9E (void);
// 0x00000409 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForPosition>d__21::System.Collections.IEnumerator.Reset()
extern void U3CWaitForPositionU3Ed__21_System_Collections_IEnumerator_Reset_mEA4BB9475FFEBB94C16F9A06B75CDC46B32A34B1 (void);
// 0x0000040A System.Object DG.Tweening.Core.DOTweenComponent/<WaitForPosition>d__21::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForPositionU3Ed__21_System_Collections_IEnumerator_get_Current_m81559CB25BBA4D58F7D3DDEB7BA928991066A120 (void);
// 0x0000040B System.Void DG.Tweening.Core.DOTweenComponent/<WaitForRewind>d__18::.ctor(System.Int32)
extern void U3CWaitForRewindU3Ed__18__ctor_mC655F5292F44006BDA6120321B57CD4707238199 (void);
// 0x0000040C System.Void DG.Tweening.Core.DOTweenComponent/<WaitForRewind>d__18::System.IDisposable.Dispose()
extern void U3CWaitForRewindU3Ed__18_System_IDisposable_Dispose_mE0758E2A6C4D0B18D5C4F0DB89311E29DF5BD801 (void);
// 0x0000040D System.Boolean DG.Tweening.Core.DOTweenComponent/<WaitForRewind>d__18::MoveNext()
extern void U3CWaitForRewindU3Ed__18_MoveNext_m569E1D17475CE2D1DD9B6E4EA6AA8482D903F220 (void);
// 0x0000040E System.Object DG.Tweening.Core.DOTweenComponent/<WaitForRewind>d__18::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForRewindU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7E0F9DC591C57BA1AB0145C09C455E9B347401B3 (void);
// 0x0000040F System.Void DG.Tweening.Core.DOTweenComponent/<WaitForRewind>d__18::System.Collections.IEnumerator.Reset()
extern void U3CWaitForRewindU3Ed__18_System_Collections_IEnumerator_Reset_mE4C432CFFEFE442C513474BF671398BB10409C21 (void);
// 0x00000410 System.Object DG.Tweening.Core.DOTweenComponent/<WaitForRewind>d__18::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForRewindU3Ed__18_System_Collections_IEnumerator_get_Current_m6CE4043738217E1AD69C9B6B68BEFA4A78381893 (void);
// 0x00000411 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForStart>d__22::.ctor(System.Int32)
extern void U3CWaitForStartU3Ed__22__ctor_mB0D561103C778281B449CB6461946393D663BB85 (void);
// 0x00000412 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForStart>d__22::System.IDisposable.Dispose()
extern void U3CWaitForStartU3Ed__22_System_IDisposable_Dispose_m0DC72886B4B6493E87B5F923E7EBDFDF95C7C67A (void);
// 0x00000413 System.Boolean DG.Tweening.Core.DOTweenComponent/<WaitForStart>d__22::MoveNext()
extern void U3CWaitForStartU3Ed__22_MoveNext_mAC382D1D241D54067037E5075ED30789C2EE45AD (void);
// 0x00000414 System.Object DG.Tweening.Core.DOTweenComponent/<WaitForStart>d__22::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForStartU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8C6F2B13B215B38084A5BD79B9A57D1D2E0746B7 (void);
// 0x00000415 System.Void DG.Tweening.Core.DOTweenComponent/<WaitForStart>d__22::System.Collections.IEnumerator.Reset()
extern void U3CWaitForStartU3Ed__22_System_Collections_IEnumerator_Reset_m4DC2F72D4A6F3B962EE5CB28AFA1E454A5C2E11E (void);
// 0x00000416 System.Object DG.Tweening.Core.DOTweenComponent/<WaitForStart>d__22::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForStartU3Ed__22_System_Collections_IEnumerator_get_Current_m16EF6B19F0927A778093A6157BF2D6CEE5625804 (void);
// 0x00000417 System.Void DG.Tweening.Core.DOTweenSettings::.ctor()
extern void DOTweenSettings__ctor_m887E5989C5E07DD06A56C7BB9651D513DBE8E04A (void);
// 0x00000418 System.Void DG.Tweening.Core.DOTweenSettings/SafeModeOptions::.ctor()
extern void SafeModeOptions__ctor_m30119D0CFA2C471B7AA0F2AFBE5AE8130D4AADA7 (void);
// 0x00000419 System.Void DG.Tweening.Core.DOTweenSettings/ModulesSetup::.ctor()
extern void ModulesSetup__ctor_mFC121E34A299D3C21E06DDCDA8392FF3EC4CDB28 (void);
// 0x0000041A T DG.Tweening.Core.Extensions::SetSpecialStartupMode(T,DG.Tweening.Core.Enums.SpecialStartupMode)
// 0x0000041B DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Core.Extensions::Blendable(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>)
// 0x0000041C DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Core.Extensions::NoFrom(DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions>)
// 0x0000041D System.Void DG.Tweening.Core.DOTweenExternalCommand::add_SetOrientationOnPath(System.Action`4<DG.Tweening.Plugins.Options.PathOptions,DG.Tweening.Tween,UnityEngine.Quaternion,UnityEngine.Transform>)
extern void DOTweenExternalCommand_add_SetOrientationOnPath_mBD8B37B1978CBE4534F9B8868C734F5C0A2B2804 (void);
// 0x0000041E System.Void DG.Tweening.Core.DOTweenExternalCommand::remove_SetOrientationOnPath(System.Action`4<DG.Tweening.Plugins.Options.PathOptions,DG.Tweening.Tween,UnityEngine.Quaternion,UnityEngine.Transform>)
extern void DOTweenExternalCommand_remove_SetOrientationOnPath_m552309BE5B7623397429DDAC02D5C6FDB0C0FB44 (void);
// 0x0000041F System.Void DG.Tweening.Core.DOTweenExternalCommand::Dispatch_SetOrientationOnPath(DG.Tweening.Plugins.Options.PathOptions,DG.Tweening.Tween,UnityEngine.Quaternion,UnityEngine.Transform)
extern void DOTweenExternalCommand_Dispatch_SetOrientationOnPath_m66381EB3F8DF2CB2F8D0FF0462C40E2AEA21EE3C (void);
// 0x00000420 System.Int32 DG.Tweening.Core.SafeModeReport::get_totMissingTargetOrFieldErrors()
extern void SafeModeReport_get_totMissingTargetOrFieldErrors_mFC456B568815DFA27E1C10970DC3926B210BFFE5 (void);
// 0x00000421 System.Void DG.Tweening.Core.SafeModeReport::set_totMissingTargetOrFieldErrors(System.Int32)
extern void SafeModeReport_set_totMissingTargetOrFieldErrors_m6D3CEE836FE7F2CFFA794FED0E551DDD0ADF7AA9 (void);
// 0x00000422 System.Int32 DG.Tweening.Core.SafeModeReport::get_totCallbackErrors()
extern void SafeModeReport_get_totCallbackErrors_m31FC475B782E0CCD82744DB4A5079F28D3973133 (void);
// 0x00000423 System.Void DG.Tweening.Core.SafeModeReport::set_totCallbackErrors(System.Int32)
extern void SafeModeReport_set_totCallbackErrors_m4215E716C771AE5BBDEE51AA0520815AF1690D98 (void);
// 0x00000424 System.Int32 DG.Tweening.Core.SafeModeReport::get_totStartupErrors()
extern void SafeModeReport_get_totStartupErrors_m5ED6052FB179745BA7C225BC301811A253B15790 (void);
// 0x00000425 System.Void DG.Tweening.Core.SafeModeReport::set_totStartupErrors(System.Int32)
extern void SafeModeReport_set_totStartupErrors_m293BB3C69C6A944F04276A9DFAB2EDCAE91C9082 (void);
// 0x00000426 System.Int32 DG.Tweening.Core.SafeModeReport::get_totUnsetErrors()
extern void SafeModeReport_get_totUnsetErrors_m136EA51EF49FF199E3939A17AD7575C0E9785840 (void);
// 0x00000427 System.Void DG.Tweening.Core.SafeModeReport::set_totUnsetErrors(System.Int32)
extern void SafeModeReport_set_totUnsetErrors_m9232EA974F7A04A1D816F7B49B268D9F2F3E837C (void);
// 0x00000428 System.Void DG.Tweening.Core.SafeModeReport::Add(DG.Tweening.Core.SafeModeReport/SafeModeReportType)
extern void SafeModeReport_Add_mC993434DFE20DDF1E44389DACD34085F31E6ED20 (void);
// 0x00000429 System.Int32 DG.Tweening.Core.SafeModeReport::GetTotErrors()
extern void SafeModeReport_GetTotErrors_mE0AC77EDDFA876A6332177C51DA53E4A94F5ED70 (void);
// 0x0000042A System.Void DG.Tweening.Core.SequenceCallback::.ctor(System.Single,DG.Tweening.TweenCallback)
extern void SequenceCallback__ctor_m0013E5DAA8ACE7366348DF78CED5E181DF620BF5 (void);
// 0x0000042B System.Void DG.Tweening.Core.TweenLink::.ctor(UnityEngine.GameObject,DG.Tweening.LinkBehaviour)
extern void TweenLink__ctor_mBAF95830E1F98BEF3183462386B086ED0B4C6828 (void);
// 0x0000042C System.Void DG.Tweening.Core.TweenManager::.cctor()
extern void TweenManager__cctor_m32BF1B0E0166CC1C1832F3980654478B2C445F07 (void);
// 0x0000042D DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Core.TweenManager::GetTweener()
// 0x0000042E DG.Tweening.Sequence DG.Tweening.Core.TweenManager::GetSequence()
extern void TweenManager_GetSequence_m64E8F2E01C9BDE2A4006C4265C84614F1DF92CB2 (void);
// 0x0000042F System.Void DG.Tweening.Core.TweenManager::SetUpdateType(DG.Tweening.Tween,DG.Tweening.UpdateType,System.Boolean)
extern void TweenManager_SetUpdateType_m4C1E97DD675C70ABA66E53D283EDABB6337E44B4 (void);
// 0x00000430 System.Void DG.Tweening.Core.TweenManager::AddActiveTweenToSequence(DG.Tweening.Tween)
extern void TweenManager_AddActiveTweenToSequence_m2D83B988481444A51AA9330D5AB9E63BF1FD3B18 (void);
// 0x00000431 System.Int32 DG.Tweening.Core.TweenManager::DespawnAll()
extern void TweenManager_DespawnAll_mBBA75EE14B48B5F3802ECE79F8C05D318DD40EDC (void);
// 0x00000432 System.Void DG.Tweening.Core.TweenManager::Despawn(DG.Tweening.Tween,System.Boolean)
extern void TweenManager_Despawn_mF56A8F1D2B480230F68F0149F6122D0BBAA129FC (void);
// 0x00000433 System.Void DG.Tweening.Core.TweenManager::PurgeAll(System.Boolean)
extern void TweenManager_PurgeAll_m73DD4E5679DCEFA742B176F67493E245603A685D (void);
// 0x00000434 System.Void DG.Tweening.Core.TweenManager::PurgePools()
extern void TweenManager_PurgePools_m00794B3E7D39DF9078C9524DA48A8E9C500F9C05 (void);
// 0x00000435 System.Void DG.Tweening.Core.TweenManager::AddTweenLink(DG.Tweening.Tween,DG.Tweening.Core.TweenLink)
extern void TweenManager_AddTweenLink_mF49C6E8A327858C52795CF0F2FA665C1A82A840C (void);
// 0x00000436 System.Void DG.Tweening.Core.TweenManager::RemoveTweenLink(DG.Tweening.Tween)
extern void TweenManager_RemoveTweenLink_mA00A9ACA1D2E241FF5E4C63D88686194CB0D092E (void);
// 0x00000437 System.Void DG.Tweening.Core.TweenManager::ResetCapacities()
extern void TweenManager_ResetCapacities_m6B74BE623F2ADFCDD966F76EDBF2B2D5DD7898B8 (void);
// 0x00000438 System.Void DG.Tweening.Core.TweenManager::SetCapacities(System.Int32,System.Int32)
extern void TweenManager_SetCapacities_m1A1D0CA467C77D297C7CD9AB105DB5609112175F (void);
// 0x00000439 System.Int32 DG.Tweening.Core.TweenManager::Validate()
extern void TweenManager_Validate_m78792D55B7C684959FBF548DD7D4FE4473140164 (void);
// 0x0000043A System.Void DG.Tweening.Core.TweenManager::Update(DG.Tweening.UpdateType,System.Single,System.Single)
extern void TweenManager_Update_mB27C9AE8D928418163CE32FD6EF1C82F9A9F63C7 (void);
// 0x0000043B System.Boolean DG.Tweening.Core.TweenManager::Update(DG.Tweening.Tween,System.Single,System.Single,System.Boolean)
extern void TweenManager_Update_mC74314D555CD02AD5E70C57AEDE614E5B7FAA928 (void);
// 0x0000043C System.Int32 DG.Tweening.Core.TweenManager::FilteredOperation(DG.Tweening.Core.Enums.OperationType,DG.Tweening.Core.Enums.FilterType,System.Object,System.Boolean,System.Single,System.Object,System.Object[])
extern void TweenManager_FilteredOperation_mEF0C87B677542616CB4324153651CA06F548BE39 (void);
// 0x0000043D System.Boolean DG.Tweening.Core.TweenManager::Complete(DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.Enums.UpdateMode)
extern void TweenManager_Complete_mCE26F442C189C358288018529BD88F0F1D99C390 (void);
// 0x0000043E System.Boolean DG.Tweening.Core.TweenManager::Flip(DG.Tweening.Tween)
extern void TweenManager_Flip_mAE0BD66D990CE593A474271E4D48B994451AF5B7 (void);
// 0x0000043F System.Void DG.Tweening.Core.TweenManager::ForceInit(DG.Tweening.Tween,System.Boolean)
extern void TweenManager_ForceInit_m490AB9D6D7F427844FBC663194066EF146853737 (void);
// 0x00000440 System.Boolean DG.Tweening.Core.TweenManager::Goto(DG.Tweening.Tween,System.Single,System.Boolean,DG.Tweening.Core.Enums.UpdateMode)
extern void TweenManager_Goto_m04F0FF9EC0394154CCFE8552D797374147D31CDC (void);
// 0x00000441 System.Boolean DG.Tweening.Core.TweenManager::Pause(DG.Tweening.Tween)
extern void TweenManager_Pause_mF53E744566930DFD9A6642B63E872D12F23B4269 (void);
// 0x00000442 System.Boolean DG.Tweening.Core.TweenManager::Play(DG.Tweening.Tween)
extern void TweenManager_Play_m16548C8C4203A88870C6E54E44F03CDA11763DA2 (void);
// 0x00000443 System.Boolean DG.Tweening.Core.TweenManager::PlayBackwards(DG.Tweening.Tween)
extern void TweenManager_PlayBackwards_mCC2A5578B578BEE7005204D8D7A546957BE605E9 (void);
// 0x00000444 System.Boolean DG.Tweening.Core.TweenManager::PlayForward(DG.Tweening.Tween)
extern void TweenManager_PlayForward_mD5568593F49E0036FA185E22CC1B24A22B35DEF2 (void);
// 0x00000445 System.Boolean DG.Tweening.Core.TweenManager::Restart(DG.Tweening.Tween,System.Boolean,System.Single)
extern void TweenManager_Restart_mB3E06722F56E0371559BC8DA43D5D09D8D391A7C (void);
// 0x00000446 System.Boolean DG.Tweening.Core.TweenManager::Rewind(DG.Tweening.Tween,System.Boolean)
extern void TweenManager_Rewind_mF610181130C4218E0F462966D54EE193E8171A7F (void);
// 0x00000447 System.Boolean DG.Tweening.Core.TweenManager::SmoothRewind(DG.Tweening.Tween)
extern void TweenManager_SmoothRewind_m3C5DE57B8A3DDE3EBBA223EC0AA2CDFD9E567E82 (void);
// 0x00000448 System.Boolean DG.Tweening.Core.TweenManager::TogglePause(DG.Tweening.Tween)
extern void TweenManager_TogglePause_m9352B05E74FE75A5CC99DF8E0AB7B03D020B9C49 (void);
// 0x00000449 System.Int32 DG.Tweening.Core.TweenManager::TotalPooledTweens()
extern void TweenManager_TotalPooledTweens_m945DAF1DACA5D98A100FC3A9DDF46FA131E0E843 (void);
// 0x0000044A System.Int32 DG.Tweening.Core.TweenManager::TotalPlayingTweens()
extern void TweenManager_TotalPlayingTweens_mCF20DFCFC526CE2C1D4F689BAA49FBC36C013A44 (void);
// 0x0000044B System.Int32 DG.Tweening.Core.TweenManager::TotalTweensById(System.Object,System.Boolean)
extern void TweenManager_TotalTweensById_mB2121BF73CEBFC4800F5D82484E6437A644BD1AB (void);
// 0x0000044C System.Collections.Generic.List`1<DG.Tweening.Tween> DG.Tweening.Core.TweenManager::GetActiveTweens(System.Boolean,System.Collections.Generic.List`1<DG.Tweening.Tween>)
extern void TweenManager_GetActiveTweens_m4C669787FB938BA2B578095A31AEDAD387A92607 (void);
// 0x0000044D System.Collections.Generic.List`1<DG.Tweening.Tween> DG.Tweening.Core.TweenManager::GetTweensById(System.Object,System.Boolean,System.Collections.Generic.List`1<DG.Tweening.Tween>)
extern void TweenManager_GetTweensById_mA095AA370EEF92919F7F7946250702BC38802B69 (void);
// 0x0000044E System.Int32 DG.Tweening.Core.TweenManager::DoGetTweensById(System.Object,System.Boolean,System.Boolean,System.Collections.Generic.List`1<DG.Tweening.Tween>)
extern void TweenManager_DoGetTweensById_m60609672BA165F4B190DA84F6B0958A04525FBFC (void);
// 0x0000044F System.Collections.Generic.List`1<DG.Tweening.Tween> DG.Tweening.Core.TweenManager::GetTweensByTarget(System.Object,System.Boolean,System.Collections.Generic.List`1<DG.Tweening.Tween>)
extern void TweenManager_GetTweensByTarget_mB7E24BC65ECF8FAC509628A3CA842745D7B77BCD (void);
// 0x00000450 System.Void DG.Tweening.Core.TweenManager::MarkForKilling(DG.Tweening.Tween,System.Boolean)
extern void TweenManager_MarkForKilling_m3D50AC8F9D12E17BF9D7E0CE6F6235C0A558403E (void);
// 0x00000451 System.Void DG.Tweening.Core.TweenManager::EvaluateTweenLink(DG.Tweening.Tween)
extern void TweenManager_EvaluateTweenLink_mE5358C53588B9201A77D284C6EA08C13090F5398 (void);
// 0x00000452 System.Void DG.Tweening.Core.TweenManager::AddActiveTween(DG.Tweening.Tween)
extern void TweenManager_AddActiveTween_mDE4BACC61DA83AB6BD50B1AA57CB16F40272CD05 (void);
// 0x00000453 System.Void DG.Tweening.Core.TweenManager::ReorganizeActiveTweens()
extern void TweenManager_ReorganizeActiveTweens_mB40CEDBB24B90540B86274D77D7795EAE1330EA3 (void);
// 0x00000454 System.Void DG.Tweening.Core.TweenManager::DespawnActiveTweens(System.Collections.Generic.List`1<DG.Tweening.Tween>)
extern void TweenManager_DespawnActiveTweens_m61D3A0AA83721EB57E1E3DF0C9C75F9F71632AC3 (void);
// 0x00000455 System.Void DG.Tweening.Core.TweenManager::RemoveActiveTween(DG.Tweening.Tween)
extern void TweenManager_RemoveActiveTween_m666B3E84DC9E1CD8F8E8F79A44EBFE16CA03AE49 (void);
// 0x00000456 System.Void DG.Tweening.Core.TweenManager::ClearTweenArray(DG.Tweening.Tween[])
extern void TweenManager_ClearTweenArray_mA86AEF4A53B4DCEF3319D39B9769AECE4E0093E8 (void);
// 0x00000457 System.Void DG.Tweening.Core.TweenManager::IncreaseCapacities(DG.Tweening.Core.TweenManager/CapacityIncreaseMode)
extern void TweenManager_IncreaseCapacities_m5316415F2F95212320B0745CAF123B139A8CDA62 (void);
// 0x00000458 System.Void DG.Tweening.Core.TweenManager::ManageOnRewindCallbackWhenAlreadyRewinded(DG.Tweening.Tween,System.Boolean)
extern void TweenManager_ManageOnRewindCallbackWhenAlreadyRewinded_m1054CA72AB6AC784DD9A50AE4F89EE2D4DC38480 (void);
// 0x00000459 UnityEngine.Vector3 DG.Tweening.Core.DOTweenUtils::Vector3FromAngle(System.Single,System.Single)
extern void DOTweenUtils_Vector3FromAngle_m8CEB04AC772D36B9F06F5C3A2B6EED469E300E63 (void);
// 0x0000045A System.Single DG.Tweening.Core.DOTweenUtils::Angle2D(UnityEngine.Vector3,UnityEngine.Vector3)
extern void DOTweenUtils_Angle2D_m7261977166A00A58FAC3A27EB2F36CAA3689996B (void);
// 0x0000045B UnityEngine.Vector3 DG.Tweening.Core.DOTweenUtils::RotateAroundPivot(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void DOTweenUtils_RotateAroundPivot_m12A75F16D4ADF0F1BC9760092FC8C6A0A6982896 (void);
// 0x0000045C UnityEngine.Vector2 DG.Tweening.Core.DOTweenUtils::GetPointOnCircle(UnityEngine.Vector2,System.Single,System.Single)
extern void DOTweenUtils_GetPointOnCircle_m**************************************** (void);
// 0x0000045D System.Boolean DG.Tweening.Core.DOTweenUtils::Vector3AreApproximatelyEqual(UnityEngine.Vector3,UnityEngine.Vector3)
extern void DOTweenUtils_Vector3AreApproximatelyEqual_m10059FD0B451D4D005575F6A92A304DD2514A0A3 (void);
// 0x0000045E System.Type DG.Tweening.Core.DOTweenUtils::GetLooseScriptType(System.String)
extern void DOTweenUtils_GetLooseScriptType_m0E6B311478C18FB4A2DC3268573C05656627C95E (void);
// 0x0000045F System.Void DG.Tweening.Core.DOTweenUtils::.cctor()
extern void DOTweenUtils__cctor_m4A00E4531C84F391F5F8BBA5EDC7B3C4CAA52A1D (void);
// 0x00000460 System.Void DG.Tweening.Core.TweenerCore`3::.ctor()
// 0x00000461 DG.Tweening.Tweener DG.Tweening.Core.TweenerCore`3::ChangeStartValue(System.Object,System.Single)
// 0x00000462 DG.Tweening.Tweener DG.Tweening.Core.TweenerCore`3::ChangeEndValue(System.Object,System.Boolean)
// 0x00000463 DG.Tweening.Tweener DG.Tweening.Core.TweenerCore`3::ChangeEndValue(System.Object,System.Single,System.Boolean)
// 0x00000464 DG.Tweening.Tweener DG.Tweening.Core.TweenerCore`3::ChangeValues(System.Object,System.Object,System.Single)
// 0x00000465 DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Core.TweenerCore`3::ChangeStartValue(T2,System.Single)
// 0x00000466 DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Core.TweenerCore`3::ChangeEndValue(T2,System.Boolean)
// 0x00000467 DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Core.TweenerCore`3::ChangeEndValue(T2,System.Single,System.Boolean)
// 0x00000468 DG.Tweening.Core.TweenerCore`3<T1,T2,TPlugOptions> DG.Tweening.Core.TweenerCore`3::ChangeValues(T2,T2,System.Single)
// 0x00000469 DG.Tweening.Tweener DG.Tweening.Core.TweenerCore`3::SetFrom(System.Boolean)
// 0x0000046A DG.Tweening.Tweener DG.Tweening.Core.TweenerCore`3::SetFrom(T2,System.Boolean,System.Boolean)
// 0x0000046B System.Void DG.Tweening.Core.TweenerCore`3::Reset()
// 0x0000046C System.Boolean DG.Tweening.Core.TweenerCore`3::Validate()
// 0x0000046D System.Boolean DG.Tweening.Core.TweenerCore`3::ValidateChangeValueType(System.Type,System.Boolean&)
// 0x0000046E System.Single DG.Tweening.Core.TweenerCore`3::UpdateDelay(System.Single)
// 0x0000046F System.Boolean DG.Tweening.Core.TweenerCore`3::Startup()
// 0x00000470 System.Boolean DG.Tweening.Core.TweenerCore`3::ApplyTween(System.Single,System.Int32,System.Int32,System.Boolean,DG.Tweening.Core.Enums.UpdateMode,DG.Tweening.Core.Enums.UpdateNotice)
// 0x00000471 System.Single DG.Tweening.Core.Easing.Bounce::EaseIn(System.Single,System.Single,System.Single,System.Single)
extern void Bounce_EaseIn_m1253ADF94B39EF139C56EEE7AD5EC2F5E0C29769 (void);
// 0x00000472 System.Single DG.Tweening.Core.Easing.Bounce::EaseOut(System.Single,System.Single,System.Single,System.Single)
extern void Bounce_EaseOut_mEF5499252352724ADC55887B541B5053DACFC27F (void);
// 0x00000473 System.Single DG.Tweening.Core.Easing.Bounce::EaseInOut(System.Single,System.Single,System.Single,System.Single)
extern void Bounce_EaseInOut_m6D386BE3A485A50DE77E480D40008D01ADDE79F0 (void);
// 0x00000474 System.Single DG.Tweening.Core.Easing.EaseManager::Evaluate(DG.Tweening.Tween,System.Single,System.Single,System.Single,System.Single)
extern void EaseManager_Evaluate_mD683DD74534996BFA45C075DCC2927E089C4E26E (void);
// 0x00000475 System.Single DG.Tweening.Core.Easing.EaseManager::Evaluate(DG.Tweening.Ease,DG.Tweening.EaseFunction,System.Single,System.Single,System.Single,System.Single)
extern void EaseManager_Evaluate_m26A532BC322B246C5CE9D45ABC16384F58F8389A (void);
// 0x00000476 DG.Tweening.EaseFunction DG.Tweening.Core.Easing.EaseManager::ToEaseFunction(DG.Tweening.Ease)
extern void EaseManager_ToEaseFunction_m2A02F664C835CDD47ED6725D6D2D40980E8F090F (void);
// 0x00000477 System.Boolean DG.Tweening.Core.Easing.EaseManager::IsFlashEase(DG.Tweening.Ease)
extern void EaseManager_IsFlashEase_mB7D47A96B8C663F7FA56AD8D10B9586C76204FF6 (void);
// 0x00000478 System.Void DG.Tweening.Core.Easing.EaseManager/<>c::.cctor()
extern void U3CU3Ec__cctor_m0EDCB89FD689F09CA60721AC58164F4A6F8D5829 (void);
// 0x00000479 System.Void DG.Tweening.Core.Easing.EaseManager/<>c::.ctor()
extern void U3CU3Ec__ctor_m493D21EFED4AA24CF19C03307F647E3509C676D7 (void);
// 0x0000047A System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_0(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_0_mA591CD137EB5B6D02B6971FBC63B661AC250EC75 (void);
// 0x0000047B System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_1(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_1_m57D1C907CE498E49D3BB97C93F17DF219D768D39 (void);
// 0x0000047C System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_2(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_2_m21BE7DFB1D3B3FB48430C5F8808A2BB23B1D2B4D (void);
// 0x0000047D System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_3(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_3_mB965A8E5686FF467D23FE3E5EF3564ABA36C56D2 (void);
// 0x0000047E System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_4(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_4_m638274DD909D0E6DF66C0A0D7C0F40E8312029CB (void);
// 0x0000047F System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_5(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_5_mFF2D7DC4AA25EE6E7044305C57FA8816631D80CF (void);
// 0x00000480 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_6(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_6_m4A06F8BB2D4B4F436E14D46CA73C41D4560A4AC6 (void);
// 0x00000481 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_7(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_7_m0FA0259C9E4CF4FB47077B12BA6F6D9ECC6107FC (void);
// 0x00000482 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_8(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_8_m5E29AABC5E842BD7E4DBADFC4AE227D489BBC4D1 (void);
// 0x00000483 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_9(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_9_m36315113AE76BC13E5D5744624A50F7C70D71A7C (void);
// 0x00000484 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_10(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_10_m67931A9DE04B64D996D4D75887E68EB788B0A06F (void);
// 0x00000485 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_11(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_11_m20CABBCE05D3787A592230CD7297001E7F35A596 (void);
// 0x00000486 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_12(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_12_m508362096DB00AE0E73BF7579CB6BC45DF2D9687 (void);
// 0x00000487 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_13(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_13_mC0AE923467BF23F592DC3A8B0E2CF016C5629789 (void);
// 0x00000488 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_14(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_14_mBA8044322F5D001ABE2EE659D719182A4B466305 (void);
// 0x00000489 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_15(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_15_m143B41512ED2E1512E2D7B2C7697A7583AAC79F0 (void);
// 0x0000048A System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_16(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_16_mC78B573955B2410B75554D0DC28AC93AB94C10C6 (void);
// 0x0000048B System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_17(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_17_m20C33BC6179A293D6EA71D95849E5DAD91A166DB (void);
// 0x0000048C System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_18(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_18_m03DC93147BECBFF659B4A535F5B570DAE38C9D51 (void);
// 0x0000048D System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_19(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_19_mB7E0AB1B73C108145A8F25D53E8BFDDF9C7EED1F (void);
// 0x0000048E System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_20(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_20_m6CF35FA9B64182DABCE68C45B7F4D1EC12625F1A (void);
// 0x0000048F System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_21(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_21_mEFB97FC4EF2F2013C16CEB5C595C4AE50A952D17 (void);
// 0x00000490 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_22(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_22_m3AB7A98254DE9433AAB67A5E5514EAD314473E53 (void);
// 0x00000491 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_23(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_23_m0B6D784AC89C1E97C747D139D1B1ED1ED17D9FBE (void);
// 0x00000492 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_24(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_24_mE1A867F687DDC6784BB3F07B6155B944E372B676 (void);
// 0x00000493 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_25(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_25_m4311927391BC4689C23C4879F2A3A480DBCFA83A (void);
// 0x00000494 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_26(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_26_m0D8C4E72A899A604290EC23AFB7FEBB610D1DFC9 (void);
// 0x00000495 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_27(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_27_m0B5C0A9408EF2C04CB8485D882A5CFC1E4AE6405 (void);
// 0x00000496 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_28(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_28_mDBE83F3AD26C7FCA54203A989CE269D45059337C (void);
// 0x00000497 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_29(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_29_mFBCD6FCE05841BC79641430A90A2977771EE7EBD (void);
// 0x00000498 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_30(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_30_m380700CA1A01498516F89AE80941E0CCBEAD913D (void);
// 0x00000499 System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_31(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_31_m66D73E4257B74EED5B9A2D69C031F1B90130E510 (void);
// 0x0000049A System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_32(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_32_m204E047E92BBE9BEB4FDEACB8663D533CA934CD7 (void);
// 0x0000049B System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_33(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_33_m56E998A9C20DBF9308DDF064FCCA0A0E99B07A0D (void);
// 0x0000049C System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_34(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_34_m2998C252D5D9DB9E120CFF0A224648C9E356A1A6 (void);
// 0x0000049D System.Single DG.Tweening.Core.Easing.EaseManager/<>c::<ToEaseFunction>b__4_35(System.Single,System.Single,System.Single,System.Single)
extern void U3CU3Ec_U3CToEaseFunctionU3Eb__4_35_m83FA33A1ECD1CC5CFB64C678728D5E1D6862C382 (void);
// 0x0000049E System.Void DG.Tweening.Core.Easing.EaseCurve::.ctor(UnityEngine.AnimationCurve)
extern void EaseCurve__ctor_m29BD0E232922C8CE2E4AC877F79EF6096C34EDFB (void);
// 0x0000049F System.Single DG.Tweening.Core.Easing.EaseCurve::Evaluate(System.Single,System.Single,System.Single,System.Single)
extern void EaseCurve_Evaluate_m036A88A768920A29FA6C7EA6E78646F679C9DFEE (void);
// 0x000004A0 System.Single DG.Tweening.Core.Easing.Flash::Ease(System.Single,System.Single,System.Single,System.Single)
extern void Flash_Ease_mA28C135D4B118A9A4469FEFFDEC3329226E6A096 (void);
// 0x000004A1 System.Single DG.Tweening.Core.Easing.Flash::EaseIn(System.Single,System.Single,System.Single,System.Single)
extern void Flash_EaseIn_m7A2DCE17466DCF086004A0147F534851240EADFA (void);
// 0x000004A2 System.Single DG.Tweening.Core.Easing.Flash::EaseOut(System.Single,System.Single,System.Single,System.Single)
extern void Flash_EaseOut_m306F24AC2A2EF38682E1C0AB8834FAC036658955 (void);
// 0x000004A3 System.Single DG.Tweening.Core.Easing.Flash::EaseInOut(System.Single,System.Single,System.Single,System.Single)
extern void Flash_EaseInOut_m71C9C9CD9B50F446B635F0ABB97D1AFA7F52F4A9 (void);
// 0x000004A4 System.Single DG.Tweening.Core.Easing.Flash::WeightedEase(System.Single,System.Single,System.Int32,System.Single,System.Single,System.Single)
extern void Flash_WeightedEase_mF9EC6A43BAEE75E4D2E93FCA21E099B0FA8CCA35 (void);
static Il2CppMethodPointer s_methodPointers[1188] = 
{
	Color2__ctor_m0D4E285146629A09FA473F8FDE3D4A4F3889D58E,
	Color2_op_Addition_m4BD6D878284D56DB00BF838BFF135155E70D6C1A,
	Color2_op_Subtraction_m78CDC06AF474D662931568BFA73CD8C477BE2D99,
	Color2_op_Multiply_m1AE5DF5597AA4375991E023FA04AABAAB64870C0,
	TweenCallback__ctor_m68CC9304423CBDE43001F9B1413B5DAAF70DB621,
	TweenCallback_Invoke_mE4105043678D7086C7740B0D7B7589B734C14E1F,
	TweenCallback_BeginInvoke_mC486E8964C6EF9E60782772AE59BC3B0A3217C30,
	TweenCallback_EndInvoke_m3C721FF86D9072053EA9BC5604B1B3D820329123,
	NULL,
	NULL,
	NULL,
	NULL,
	EaseFunction__ctor_mD630BE102357BB21BD878DF5E98F90BFE785A0F8,
	EaseFunction_Invoke_mC30ABF785F84A8769541950EDC3C2CB0B8F6FB8D,
	EaseFunction_BeginInvoke_mFAB698BD64F636C31FCE27A231156FC2A9499DBC,
	EaseFunction_EndInvoke_m7F85E0D3DAFBC71192CA9340D5B29C2FA4F32165,
	CubicBezier_GetPointOnSegment_m696FD077A89C79276C401503AFF1DFB199CDBCEA,
	CubicBezier_GetSegmentPointCloud_m7BD76991275FE643853C194A3E3CCC3D1D32AB14,
	CubicBezier_GetSegmentPointCloud_mB42A75DDB4383485731CFF005E10D1F9A51C1D9E,
	DOTween_get_logBehaviour_m50FAF61152D634B61FFA7D3B04F7C2A10E6E9B97,
	DOTween_set_logBehaviour_mD910C3B966CBE72E0ED43B7EA96BA06331090D6A,
	DOTween_get_debugStoreTargetId_m96F1367AD4955D84B2284A01EDD146DCE64B3A75,
	DOTween_set_debugStoreTargetId_m9A024F9090E856AC801B9DD5AECE4B8CAFBD96A5,
	DOTween_get_isQuitting_m5511C54A110FA836D9B16B76ED0A390F1C5A7990,
	DOTween_set_isQuitting_m1F583636E361122DB50E89CC8CBDFA081DE1E28A,
	DOTween_Init_mAD6E37B9B311DFFBCCACAB726DA36D40A7DB8C23,
	DOTween_AutoInit_mF7B0D31019E4A0D0212902AC43E359A1BD763C29,
	DOTween_Init_m31648CA12FD2195F125B2B4773B7BF8DAFA11080,
	DOTween_SetTweensCapacity_m0A87694AEF8A3F716EC1340496DCDE1A99E838EA,
	DOTween_Clear_m6CFE7E673765E730176BF919B55B1D7C1A923075,
	DOTween_Clear_m6585EFB74CE7D3B89776A06190F810BFFC31A32A,
	DOTween_ClearCachedTweens_mDECBBF6297745D2D6B6EE820A9FC13A99B7714B1,
	DOTween_Validate_mCB9E29EEDB6CD44AB6D8CD527C7BA028B421C321,
	DOTween_ManualUpdate_mF5092C3F5871F46377151A1117F4D341E9DCC00C,
	DOTween_To_m9C9EBC0FB6CF94364DD4FF85C476D8EE0A7FF4B1,
	DOTween_To_mEE7384557D7EB80C79A8D60638B4EC5315518AAA,
	DOTween_To_mE00A5CA8947AD59966A2CFE3810F1FEEC83E157B,
	DOTween_To_mA2EB91A5D1B226DADC20E24A11AE2BA91087AEE0,
	DOTween_To_m0A0AA071215B37E2ACF8070183723654980F8AE3,
	DOTween_To_mCF3CE02B6A8876B87B3487AF8C7A538FB67DD5A0,
	DOTween_To_mA4E61D06204BD01537C08EEB9ED148C18ABC75ED,
	DOTween_To_mAF71D4F17109689870406A4565636216CCFE7680,
	DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8,
	DOTween_To_mF9194C82EBAF83FF928C8F54ED13441A4AB39141,
	DOTween_To_m7A731ADF3CCD5C4439F8710B2CD16BC6CEB051D0,
	DOTween_To_mA1EBEAF49DD682B626CB60B4C53B7069F7933339,
	DOTween_To_mA77855459ADB369B17DB84A390E88A1CC27868F4,
	DOTween_To_mA55CD0AEE530AAB079E27FEEBB2623DD813477B6,
	NULL,
	DOTween_ToAxis_mB32AF2179F60E4D7B59B182A82C376801256C01D,
	DOTween_ToAlpha_m93AB0EDCF2956A0DD806D9537799F2727CA5E7C1,
	DOTween_To_mD96259B51203D6105DAE33CE4E4CDF89023A5BB5,
	DOTween_Punch_mD470A46B3BEC0E312D0438C14C0980E853CA4D32,
	DOTween_Shake_m75BBA8491D91B639689EFFD42491E61556D9FF0B,
	DOTween_Shake_m6C1929582C5F952ED98444CD22A37F54DD0A4345,
	DOTween_Shake_m5FD4EEB34398EB5F3C65443313CEC66788EFDFF2,
	DOTween_ToArray_mEC8B5DBDFCBC6DE60994B6E6DD0CCDDA43F001AB,
	DOTween_To_mE583287C4050FF3368FD9B043A7D60D78296CBBE,
	DOTween_Sequence_mC3AE53A08EC5DFD7DB9AE18A2FD11226A6611F89,
	DOTween_Sequence_mE0F7F846966AA538367AF365C620B0464A6D48E4,
	DOTween_CompleteAll_mC914401379CE84151E8C91A5DE6C4038154D0E7E,
	DOTween_Complete_m12E2987F7D42218DCCE051E2FDB803E75FD8BA91,
	DOTween_CompleteAndReturnKilledTot_m9A22E74F449F88E7333C1117511D5F3912D30536,
	DOTween_CompleteAndReturnKilledTot_m77F801701C03DB55B442098F543A09F2C5600452,
	DOTween_CompleteAndReturnKilledTot_mA7D31092FECF318CB6209CF9E855460D15DD81C9,
	DOTween_CompleteAndReturnKilledTotExceptFor_m8AFEA53C85E3BAC276F66975737E9D3990495CCC,
	DOTween_FlipAll_m51A32BD6788A2EF321AAC2EB49D60BFBF6EFAF49,
	DOTween_Flip_mCE1C7CBA31A981D035B52150957C3A01B7F2A978,
	DOTween_GotoAll_m49A8DD69B67F5E62428E616559183F545F84B3D2,
	DOTween_Goto_m19ABF14E2B1ACAFFA17E8AB48AEE2207B6FFA6C9,
	DOTween_KillAll_mA9D826F93A89E061EABF30308378391E7163AEB0,
	DOTween_KillAll_m83C060D22440D6C7A8CB7AD018F038E363B27C2A,
	DOTween_Kill_mAB4C96CE1F1BCF25E5347AE0FC295D064EA53FB2,
	DOTween_Kill_m53573B76803A245F23AFAC2AB22995FDA4A9235A,
	DOTween_PauseAll_mBEAEB6365AEA3EB8755BE21D8A45D0ABFC8F1694,
	DOTween_Pause_m498BECFBBC8FBD76425B8AE1F38E2ECC9AE296D4,
	DOTween_PlayAll_m5510B90EBBCA172D2D1EC8FFE1CCF44E34B9E9C0,
	DOTween_Play_m466F46F9DF6585E17C595438BA15147319540DC4,
	DOTween_Play_mEFD3A1E3CC218D3916032325F2E119C2004D9473,
	DOTween_PlayBackwardsAll_mF6926DA985F2523FFC77DC111A657CE1C6FCC7C4,
	DOTween_PlayBackwards_m3709E26071CCE5CB368584477DF2FC9A8B9D052B,
	DOTween_PlayBackwards_mC4FB110A49C220C2B5FE768989D133F0F8721FA7,
	DOTween_PlayForwardAll_mCF7A53806203F40BF65D18C029617319111C88E1,
	DOTween_PlayForward_m4F27092024989DEB74466D9D9C370D5FA6621DCF,
	DOTween_PlayForward_mC1FF85BCF9DC773A3E4FA3DC12377D1F9E1BADD4,
	DOTween_RestartAll_m68B018164D3C980A8B88D4501AB353C9A3BE621E,
	DOTween_Restart_mEE2F85FC8741BBCE7C1E76C143B3DCE5B9C78DEF,
	DOTween_Restart_m89E03C91814831E13EA36751C4A8471CF41FA249,
	DOTween_RewindAll_m7F9B893866CDBDB4C9D416E6DEA1DC21622101E3,
	DOTween_Rewind_m5C4020E9007FAAF719C1BE01CE440EBB39193619,
	DOTween_SmoothRewindAll_mD4DA180BE6BDD18A40F14422E2CC19A56BFC0192,
	DOTween_SmoothRewind_m36B53DA282A0BC7948453A6725939D60A33D5C22,
	DOTween_TogglePauseAll_m107930535CBAFB21956D555DCE987CFC19969455,
	DOTween_TogglePause_m8CE7CA00FE30F3C926F34362E2DE67A161536598,
	DOTween_IsTweening_mD96EA6DEB69E80EE2948D2C561009E8DA9F9B820,
	DOTween_TotalActiveTweens_mEFE6677CD80989E356252940A3CE46B710C6CC41,
	DOTween_TotalActiveTweeners_m14F743B11BECF70A64B57CE0631C5EA1021EA5CE,
	DOTween_TotalActiveSequences_mA5F412A7630E24EAAB30B3B275A97CD96F669B1D,
	DOTween_TotalPlayingTweens_m2F68C3282E316EBF83F3D3AE5AE59DFE1040E23F,
	DOTween_TotalTweensById_m2DCD5FD3DF61F905001D1BC4B15D3C5ABF9A9511,
	DOTween_PlayingTweens_mA44E34F2DED68B5EA5E8861A150FCC4085DACE90,
	DOTween_PausedTweens_mBF94B78D500712E0478E2AF3F66FC25462FA96D5,
	DOTween_TweensById_m80980F3CDF268395EA51EEE8B012BB49A45E2988,
	DOTween_TweensByTarget_m1F6174B35FA2489CBDFAFF39A71187EC02908A72,
	DOTween_InitCheck_mA3F71F5F48DEF60104F960552849E293CDDEBA7E,
	NULL,
	DOTween__ctor_m21C0AF17F063BE0B93E6E82D2C9F1AC9573CC719,
	DOTween__cctor_m278575843F5C4300324E26C3AC8D91B665F2F155,
	U3CU3Ec__DisplayClass67_0__ctor_m7DA2265F9A2C0614A5BCCE53DAF356F6E21B5717,
	U3CU3Ec__DisplayClass67_0_U3CToU3Eb__0_m40D6775B16A95A4AC26C3B159301101DE5B4FC43,
	U3CU3Ec__DisplayClass67_0_U3CToU3Eb__1_m96DBB5AD703E30C584801821DDF8C08C64138FD7,
	DOVirtual_Float_mA492E430AE72B6C1D8C71077C9BE7F82E98ED185,
	DOVirtual_Int_m5835CD7A7EC7E4954BA30641CEA4B31FE4437EDD,
	DOVirtual_Vector2_m96EE942293ECC9D9013DD8F7D94D427B0C2388CB,
	DOVirtual_Vector3_mD1BD751546A5A4CB1AA8AF3744997FD7A9DCBEC4,
	DOVirtual_Color_m1C5D178D63A7510AD9D451262A0F07FD4B42EDEB,
	DOVirtual_EasedValue_m40F83FCD3705E8DD33558C84B3F7067D91F3B9DF,
	DOVirtual_EasedValue_m7A69BDAB0B236BBBF70E29013D1AB3E38D968CBC,
	DOVirtual_EasedValue_mC076ABFEB3AE48B9AC2F2F5894F6283DC20CA05B,
	DOVirtual_EasedValue_m321C2569673A65C8404E336A38BE551761233A56,
	DOVirtual_EasedValue_mB81B2D5233C9CE32914D5B4D7438CD23007DD030,
	DOVirtual_EasedValue_m76425A9DFA6F1E09795F814DB96DA9653B4A5B95,
	DOVirtual_EasedValue_m244D9AD0AF382417C3C881AD80DD58944218D9BC,
	DOVirtual_EasedValue_mB2DF1EE529D5619FBF0FBD266150866D8490F39B,
	DOVirtual_DelayedCall_m14018BD265A6BB37019E19FB5076D693C1B60112,
	U3CU3Ec__DisplayClass0_0__ctor_m49CA857D87CCE7F4D5FC0D7367EAED41314E8119,
	U3CU3Ec__DisplayClass0_0_U3CFloatU3Eb__0_mEAED8273E9F7281F3F23328DB9C0608546C82739,
	U3CU3Ec__DisplayClass0_0_U3CFloatU3Eb__1_mBFA0955B5DA8573DBA53BF87F177ABB1AC42BCB9,
	U3CU3Ec__DisplayClass0_0_U3CFloatU3Eb__2_m07C62CFF2717E629D136D622D3CB1399C82A38F2,
	U3CU3Ec__DisplayClass1_0__ctor_m86626EAF02B0A51DA6AE0C66BC88A5A8B4131FA5,
	U3CU3Ec__DisplayClass1_0_U3CIntU3Eb__0_mEF9AC3D0EA5F7B83064890D9A9AE99D022006262,
	U3CU3Ec__DisplayClass1_0_U3CIntU3Eb__1_m3FED03119653529A0BBAD4DD316EBC0C72FFA630,
	U3CU3Ec__DisplayClass1_0_U3CIntU3Eb__2_m78CD6CFF5518C7B5545D5AB204909B3B56F593B0,
	U3CU3Ec__DisplayClass2_0__ctor_m6B443DD65DDFEA0AE484C48168FCB77DD0757FBB,
	U3CU3Ec__DisplayClass2_0_U3CVector2U3Eb__0_m0548111E0011A18D69A174AEE645E0A6FDA16C48,
	U3CU3Ec__DisplayClass2_0_U3CVector2U3Eb__1_mC5538B1F3805376CF610DC5E51085CAA2B784EA0,
	U3CU3Ec__DisplayClass2_0_U3CVector2U3Eb__2_m445DB7290517C829BA806FA806E41D63BD9A9F10,
	U3CU3Ec__DisplayClass3_0__ctor_mADD596F50E712B09718BBBC46C8473B263C7D345,
	U3CU3Ec__DisplayClass3_0_U3CVector3U3Eb__0_mC03535DBBD1C002113422C01CDCEB101FB33FAD7,
	U3CU3Ec__DisplayClass3_0_U3CVector3U3Eb__1_m6331F9D5916248A66ECC0FF36DC1AC38A782A533,
	U3CU3Ec__DisplayClass3_0_U3CVector3U3Eb__2_m844A8C367F62820E0F4D3CC5F83BA9D53F0A3C20,
	U3CU3Ec__DisplayClass4_0__ctor_mFBE34FE98D006ED51F6C71A4840E439F17FB7565,
	U3CU3Ec__DisplayClass4_0_U3CColorU3Eb__0_m5FBF415030A8478EF8DDABA735B599CE3413F28E,
	U3CU3Ec__DisplayClass4_0_U3CColorU3Eb__1_m1B5C8EF17DF1A096A1C6AD1B5B4BE6A190C0CE40,
	U3CU3Ec__DisplayClass4_0_U3CColorU3Eb__2_m27CEC300AA827EF4CB0E62A4BD9F782A7485FD6A,
	EaseFactory_StopMotion_m4DC70923409B87F488623BD9BB3B3E509EAED212,
	EaseFactory_StopMotion_m6DD31A920F64A97DC82C3C75E0ABDA5E5F079F3F,
	EaseFactory_StopMotion_m0CAF496284105D5C4AA42AF91BA176CCEC0F7C0B,
	EaseFactory__ctor_mC5C91AF007C9B1B3B21F3B283F31B69731A283E0,
	U3CU3Ec__DisplayClass2_0__ctor_mB1FA14DE4DB023CCD4E2B70E49EAAB2604E223A8,
	U3CU3Ec__DisplayClass2_0_U3CStopMotionU3Eb__0_mB5A46C351CA279129DEA3054E00DF2DBC9C9EF58,
	NULL,
	TweenExtensions_Complete_m27D13C838F7DCBFE14632132B60338F42B3ACB73,
	TweenExtensions_Complete_m5B347FC17642C404A25E17D231836B2BE39E0632,
	NULL,
	TweenExtensions_Flip_m7931C804A4B933A372DFAF3A1804A56ED959A443,
	TweenExtensions_ForceInit_mBE21B6CF98B217D09E814F598CCF6659D7660420,
	TweenExtensions_Goto_m0256CB1AA2FAB6786021BF3322D737AE4D37B2FC,
	TweenExtensions_GotoWithCallbacks_m8E3ED556DB6F69ED916D03C2D523B8DD2F21BDA4,
	TweenExtensions_DoGoto_mE805A8F7E5146DE10428CEFBBD34AEC5814EC002,
	TweenExtensions_Kill_mFFDE4DE45D8A91C357ABFF2F8FA95D412924F466,
	TweenExtensions_ManualUpdate_m31E78BA6C0D5181085D9F5A82D1AC237E1DA48ED,
	NULL,
	NULL,
	TweenExtensions_PlayBackwards_mF585684B8D5082A2A895C628F372FDABF690E0FB,
	TweenExtensions_PlayForward_mE9D0A1BCADDDFD7429F97CF8AE9BE9D1E81F6439,
	TweenExtensions_Restart_m4080AE8184C33AFCB12BFBB850424D13836E6214,
	TweenExtensions_Rewind_m0ECE9F671C1A1BE35270FD24F9AC81DC5645DAF1,
	TweenExtensions_SmoothRewind_mA6788BEC5688F797A270A20E94F9FC150111936A,
	TweenExtensions_TogglePause_m6AA5BB6139190D6F7A5B1516802D650DBC813322,
	TweenExtensions_GotoWaypoint_m5408B35F8A7546DDF5FFCAC1247921B6EC4176D4,
	TweenExtensions_WaitForCompletion_m481B1D9B59FBFDB1040E57ABFEE5551EAF568A14,
	TweenExtensions_WaitForRewind_mA97F5969BBA948A3452A593469F5B6177F9992D8,
	TweenExtensions_WaitForKill_mCD155C79A9FEC8B28D3E2DD06135D906A3664E84,
	TweenExtensions_WaitForElapsedLoops_m357B73AB3CE93158AB511B150DC5D6BA9B3356A5,
	TweenExtensions_WaitForPosition_mF6787D2CD4AC874CCB7F07DBF04B7AF17A93417A,
	TweenExtensions_WaitForStart_mBD8031FD2F5FFEA975582085DEDDEA12F8FE8F19,
	TweenExtensions_CompletedLoops_m5A7B5AEE691F491182E5FD7009C21E3BBC90CA8B,
	TweenExtensions_Delay_m64B89ACB80BB7813CA424659E612C7969A6AFA2F,
	TweenExtensions_ElapsedDelay_m07C1B01E56C62D2614D2707969D6E3A464DF95D7,
	TweenExtensions_Duration_mFF4115983798994675D0431ADD2D333958714222,
	TweenExtensions_Elapsed_m62BF350941E0EEBF4B5282BC1523E52CC1E6E24D,
	TweenExtensions_ElapsedPercentage_m2B88D6261A10FE69DA344E2EDF1D72F0DC7C4FA8,
	TweenExtensions_ElapsedDirectionalPercentage_m86020EAE1A0CA49FDE72191B7F45FEE990B9CFD5,
	TweenExtensions_IsActive_m7CB8E490D86B9E14B6B4B4004F1D199790397743,
	TweenExtensions_IsBackwards_mF43EE5EB9C7A38949F1B1511E93D64FA2D7CA816,
	TweenExtensions_IsLoopingOrExecutingBackwards_mF449DF8CB80AAF2C255A3F4C361622E4E5139AE1,
	TweenExtensions_IsComplete_mBB619B64C19A85AB4EEEBB3D37D3E720A9E0AF4C,
	TweenExtensions_IsInitialized_m8C2B24B55147A6773849EB32D4D00E128CAB211B,
	TweenExtensions_IsPlaying_m32EF28DEB59B931FA4607BAC3BED0DE275A1D843,
	TweenExtensions_Loops_m3EE2123D586F927EBFDF26FD4F9936A194B0C8C3,
	TweenExtensions_PathGetPoint_m99A277314FB3A2FE9283DC873010447CDAA36842,
	TweenExtensions_PathGetDrawPoints_mFA9C28626C8FEB8E5A2793835713E763F8FDAA05,
	TweenExtensions_PathLength_m754255B76B1362A6F4EE6CC8F0D7E6E6D6108AB0,
	Sequence__ctor_mFFB83C470D70B8512E2B393A0C07D90FEC2CBC84,
	Sequence_DoPrepend_mD160A1A92AB3AA419088A3DDA26497C6648CAC76,
	Sequence_DoInsert_mD948F47640F2159358EE6B18952EB77AEE6610F4,
	Sequence_DoAppendInterval_m0C3E54F3B28A78293C67DD1AFFBDB27A665E59BE,
	Sequence_DoPrependInterval_m5349CCACB49068164F7EEDE05FB705AA7458D248,
	Sequence_DoInsertCallback_m54445936FAD5F03DA3B631C195FC393C1B80F0B2,
	Sequence_UpdateDelay_m1036F784D7C13B47D60C95A67DB53C207FC16C25,
	Sequence_Reset_m006B0E92244A1C149A954C5183E9826DC5C828AE,
	Sequence_Validate_m608AF33CC00272DAAD250F44DC2D974EF9931AFC,
	Sequence_Startup_m76F5EC2C0703BDAFA28DA67BB0AD4B97BE0A9D4E,
	Sequence_ApplyTween_mC85811FDB77E639F3665931DD46CCC17626354A7,
	Sequence_Setup_mDCF62E1E0C88A3090CBDD2D79A544EF03150202A,
	Sequence_DoStartup_mEC96B51F5254BE451DD5CBFED2EFC13FE463F7F0,
	Sequence_DoApplyTween_m06E4746BDB1F214259ADC078EDD812BBBFCFC54D,
	Sequence_ApplyInternalCycle_m2B145923EEC8BE7893BB8F17B217F26318AC8B94,
	Sequence_StableSortSequencedObjs_mC3780B8F109A9114A2D5CE3C2605903E282E1FAB,
	Sequence_IsAnyCallbackSet_m98D9A7B3915C4C54A385E0F39646C3DF9D62A600,
	ShortcutExtensions_DOAspect_mE9ECE416D6C7FD3BDE1DB73DFBCAA738589062E2,
	ShortcutExtensions_DOColor_mC438262691549AA19473BFC4777A39E4DF995E03,
	ShortcutExtensions_DOFarClipPlane_mB243E0B3DDF685289216FB198FBAA773C7876C58,
	ShortcutExtensions_DOFieldOfView_mDF8F791F7D4672A51988CE4F3434A589F0F7F62D,
	ShortcutExtensions_DONearClipPlane_mBD3B11717DBA7B9FDF01EC45DABF8E841B400B3D,
	ShortcutExtensions_DOOrthoSize_mE96037CC51B44CB1B04CF436779EAF60BF6C0CD3,
	ShortcutExtensions_DOPixelRect_m30CEF5D00DEFB324BB8514B95CF0328929274598,
	ShortcutExtensions_DORect_m21D8649612CDF6196C7D4C591382743112FEAFD3,
	ShortcutExtensions_DOShakePosition_m45CB5C2107BCB248F0EF021A8C81DC84655F2827,
	ShortcutExtensions_DOShakePosition_m7110C83FC1DC1963A245BFD178CE3351AF588541,
	ShortcutExtensions_DOShakeRotation_m7E0F68137308B2DCCE102DD9136C5018934B3AF6,
	ShortcutExtensions_DOShakeRotation_m52BE42BA066C7A74BADCF9B91B0C6D60C419FB6E,
	ShortcutExtensions_DOColor_mAC2C2C38A8C15064D70E6B39F5735A09DDA0D581,
	ShortcutExtensions_DOIntensity_m27397BA48763F123CFEE2F08D1B68C536BD1AE12,
	ShortcutExtensions_DOShadowStrength_m5C68624DB62ED7BBB84F03131DAC97B815041123,
	ShortcutExtensions_DOColor_m047D7B3D5AAF424172290E105A6B991020516A56,
	ShortcutExtensions_DOColor_m7770E3969D58563343B129139B857669312EFAA9,
	ShortcutExtensions_DOColor_mF41D0D0338A1C71122B56F08353CAFA9931B5183,
	ShortcutExtensions_DOColor_m902471CFC01B9E318F432233AD11C00BED289EF8,
	ShortcutExtensions_DOFade_m1C499FE6483845A6BBE9C4EB80D11062C9859FC8,
	ShortcutExtensions_DOFade_mF89476AE4BB169F0F4E829D105A639D03F864163,
	ShortcutExtensions_DOFade_m2F02D24BBE5D0167CC8C852D21E9E5153332B56F,
	ShortcutExtensions_DOFloat_mC88ECC5840584E487DAD7A47724F277158EB3A7F,
	ShortcutExtensions_DOFloat_m69CDCA35D0819C7B9A582C15D19E946153E080C0,
	ShortcutExtensions_DOOffset_m7B96FE908DE6175518AFD3EA61BFF765F23E59CF,
	ShortcutExtensions_DOOffset_mB64259517DC2B7BC622A6573D950AE495EB7DD4E,
	ShortcutExtensions_DOTiling_m112A47C17BF7518DE0A12E98B2C22EDBFE70EA43,
	ShortcutExtensions_DOTiling_m44582100DA4D7F4B116777F5D285885846BF806C,
	ShortcutExtensions_DOVector_mC26736E716689ECAC62785114485C14A623A3AE9,
	ShortcutExtensions_DOVector_m00FD44EC8D9045D280EAFBDA475E351754C8D19C,
	ShortcutExtensions_DOResize_m6DE50633682D47B2ACC43DFE6B5B463B764F4C15,
	ShortcutExtensions_DOTime_m00953893B1B5A1C6F21849BF5E107DE30ACB2DC4,
	ShortcutExtensions_DOMove_m82274FDC0216A91A1FAF16844805D06BF9A287FF,
	ShortcutExtensions_DOMoveX_m1173E2DE6886AEE7BDB63E1479CD102A0F734543,
	ShortcutExtensions_DOMoveY_m7986D5CC9DCD45AED6BC48F87C306CCED2B6BE62,
	ShortcutExtensions_DOMoveZ_m6ECBC915613E53BB64DEC10900A8A9E290B24844,
	ShortcutExtensions_DOLocalMove_m22F3EB581DADB5A3FC59B69F7F6F05A86F8E8348,
	ShortcutExtensions_DOLocalMoveX_m3411FBE47AB4960B865E352F672F94B788F712D5,
	ShortcutExtensions_DOLocalMoveY_mA4BDBF3ACA5B305B59551FBF9813D5BF35487CD5,
	ShortcutExtensions_DOLocalMoveZ_mF9B4A4F9168E6A7BDA6EF9271E82706198E52BD3,
	ShortcutExtensions_DORotate_mA2804C1A3E4780383111262752CC7056BBC7D470,
	ShortcutExtensions_DORotateQuaternion_m36B2BE20F4F4E39AA82234D0503D01A67D901021,
	ShortcutExtensions_DOLocalRotate_m6EB8F37963023C6B157C60013B98D2B612816DA4,
	ShortcutExtensions_DOLocalRotateQuaternion_mD0927EF813E9A8B6786FCB7B5A510C6585E1CA30,
	ShortcutExtensions_DOScale_mF7AC6EA0FD71B399776D758AD57B94F18A47F580,
	ShortcutExtensions_DOScale_m5935113B55474CC0551EF8A8EA3CFA82371D5E99,
	ShortcutExtensions_DOScaleX_m88DC7010A9399FA9EE1430AF3A466D87E3921879,
	ShortcutExtensions_DOScaleY_m0DBDA35C6ADF0A2A0A9FA5323EF4B4F3DA9DC1B5,
	ShortcutExtensions_DOScaleZ_m922D963D40194D815D0CE1184486A3D4829504B9,
	ShortcutExtensions_DOLookAt_m7B2760200371481892C58C78878C4EF546EC6763,
	ShortcutExtensions_DODynamicLookAt_mCFAA30AC7C2EA6F9AC28F3828F2ACA22499791D1,
	ShortcutExtensions_LookAt_mB87549F2630F836B9FAC37BA2D29A405EF03B515,
	ShortcutExtensions_DOPunchPosition_mD022015ABB94942EE909F7F8E0F3660D52FA3D9E,
	ShortcutExtensions_DOPunchScale_mD7D825D1761F0264BC1D00027B79330844400B9A,
	ShortcutExtensions_DOPunchRotation_mDC55C1F23E2C17A4E9D4BF5BB787BB1DE98D7AC4,
	ShortcutExtensions_DOShakePosition_m6B9660D299172E1E37B7C1573C5263A37C48739F,
	ShortcutExtensions_DOShakePosition_m13612BE4565D5755BF5F4AD92827925CDEBD821C,
	ShortcutExtensions_DOShakeRotation_m5E79D157D34BD0FC61A3E9564716975C0B992293,
	ShortcutExtensions_DOShakeRotation_m6292F37F5BAC8EA3EAA8BD3B5507E65E5D9CE729,
	ShortcutExtensions_DOShakeScale_mEE72C2F72BD8B6AAA2BEA8BDC20E46E9494A655A,
	ShortcutExtensions_DOShakeScale_m89D5B0734EA7D724637547C2A14DA5865A1A77DF,
	ShortcutExtensions_DOJump_m918C2D5466D8F6D332AE98BFB88E0871B060DA36,
	ShortcutExtensions_DOLocalJump_m4F8B01A9D2BAD5B26E9B1E8A8212DE8BED54F557,
	ShortcutExtensions_DOPath_mB31C78FE35F62166F934D044D3B0B4BAC6304D4B,
	ShortcutExtensions_DOLocalPath_m00D123733C52DD578549A9F31151194C0CA079BB,
	ShortcutExtensions_DOPath_m3E70D921DDA265292CF467212AC676371F110691,
	ShortcutExtensions_DOLocalPath_m4F4C77B2C481DDCB0FDBCE8B3C4442D897F1B2DA,
	ShortcutExtensions_DOTimeScale_m5E0756CBAFCFBBE6F19E544200E57863C4BA29C7,
	ShortcutExtensions_DOBlendableColor_m6D881864FAADBEFC395EF3676BC6840B6181003C,
	ShortcutExtensions_DOBlendableColor_mB4B51382219C2371D8122BDF90CADC0796C39B56,
	ShortcutExtensions_DOBlendableColor_m6F9B2C98A1BDD89B0B416E73DA685EDDE569C318,
	ShortcutExtensions_DOBlendableColor_m3894C723770667A4E3412C45BB41E8E788119B5F,
	ShortcutExtensions_DOBlendableMoveBy_mE8B5A140CB7E51980FDC15DD76366D1A23AECB8D,
	ShortcutExtensions_DOBlendableLocalMoveBy_mB7638D6C3CC5106CD711AFE8053685B6E2C30BBF,
	ShortcutExtensions_DOBlendableRotateBy_mB7E8F46D372D33702D92542702FC055B6E5F9C89,
	ShortcutExtensions_DOBlendableLocalRotateBy_mCB395A6DCD07BCCEAF501FD486955A43E4B295B2,
	ShortcutExtensions_DOBlendablePunchRotation_m7AF8A8051402033D49416E5A6106AE1E69A3FB49,
	ShortcutExtensions_DOBlendableScaleBy_m7BADCC6C4398F420D288E0E3B508609E5B612886,
	ShortcutExtensions_DOComplete_mC76F1E5F1C3FC93A195ED13839A42D63A8A4007C,
	ShortcutExtensions_DOComplete_m1F8696120349F77CBAA082F0B4B9E87A570489F4,
	ShortcutExtensions_DOKill_m3F197E779AB6CA95FF3C4C2DD547B4B493E42D46,
	ShortcutExtensions_DOKill_mA88D7174BB15882E9F2FC0C07A78DD096DD7B02D,
	ShortcutExtensions_DOFlip_m0F160622F017B8EEA110E6868461ED7288904CAD,
	ShortcutExtensions_DOFlip_m6C70971874D6C538D9ACF953CA9126FFF91983C5,
	ShortcutExtensions_DOGoto_m969AFE4BADC4BB821EFD9B05A786E6B780064DB0,
	ShortcutExtensions_DOGoto_m22C5343949FBF39C5E6E2209D7C8FD220C2F773F,
	ShortcutExtensions_DOPause_mB6E749E6F49864EF31F821A09F84CFCB2766A6D0,
	ShortcutExtensions_DOPause_mFA584CF337399AAA4B080D2468EDC87C65243923,
	ShortcutExtensions_DOPlay_m33F4EE8E48C2610513626994E58C12A3CC720C2E,
	ShortcutExtensions_DOPlay_m2402C7171E5E43CCF50DFDB758A6D2307A125185,
	ShortcutExtensions_DOPlayBackwards_m176213A53F407C711C1A6BE509DA9B934DEB4C89,
	ShortcutExtensions_DOPlayBackwards_mCBF64C71205FBA34EA570628FCEEB3F8EBDE8987,
	ShortcutExtensions_DOPlayForward_m79A9002106CACC71D6E18EB731AB700713E5E2A1,
	ShortcutExtensions_DOPlayForward_m5DAE0042EE85D4C7543F3F9CAAB874167F4A72EB,
	ShortcutExtensions_DORestart_m3CA008E63D7607E04507BC8977568501417EF06E,
	ShortcutExtensions_DORestart_m68D5B5B82C4C0FA7DB4447E63EF5E7B17D59707A,
	ShortcutExtensions_DORewind_mE913AA9DCE96F481292455CA7132B1ED602DF710,
	ShortcutExtensions_DORewind_m7FB526E680AF81409FDDEA2AEEACD75A771A4D79,
	ShortcutExtensions_DOSmoothRewind_m52F2C7CAC6BF0DABC637EC72EB3C5F7B6FDE7C9B,
	ShortcutExtensions_DOSmoothRewind_m5DF3E075EF5A1DB7BF94C4B83909845AE2E5BE1A,
	ShortcutExtensions_DOTogglePause_m9D9CDCF160BA5E96A99AC46113A87A7CB376F144,
	ShortcutExtensions_DOTogglePause_mE620BA810033BAC9654C95A57C9BADD65EC48F63,
	U3CU3Ec__DisplayClass0_0__ctor_m14F8614CA85AED6EEB9F83CD5240ECB824FD30BC,
	U3CU3Ec__DisplayClass0_0_U3CDOAspectU3Eb__0_m3B8BE2D1BEEAEDD0102C3DC4A32B80C07DD8264F,
	U3CU3Ec__DisplayClass0_0_U3CDOAspectU3Eb__1_mDDD2F090D812F2AF5DC451DF365B91E91410B0B0,
	U3CU3Ec__DisplayClass10_0__ctor_mDBD1C6FF072B315E22B6BEDD89A6754F63C9C214,
	U3CU3Ec__DisplayClass10_0_U3CDOShakeRotationU3Eb__0_m6EE0D66C6A2F40EA31F9F76273517E62A68D18E6,
	U3CU3Ec__DisplayClass10_0_U3CDOShakeRotationU3Eb__1_mF2555B38D1D48BE9BC0D51C9254F5D1D224BAA3E,
	U3CU3Ec__DisplayClass11_0__ctor_m5721180AF4A066848626CE3084B7C816916F4C86,
	U3CU3Ec__DisplayClass11_0_U3CDOShakeRotationU3Eb__0_mC15954C72678B2B67CE4C4015E67B9855AA6894A,
	U3CU3Ec__DisplayClass11_0_U3CDOShakeRotationU3Eb__1_mDF144244EEE86AA5942A63587B459FE87394D717,
	U3CU3Ec__DisplayClass12_0__ctor_m77B82950D8ED2AE3169C0193888A86E0D538A5CB,
	U3CU3Ec__DisplayClass12_0_U3CDOColorU3Eb__0_m32DE3BA95C1F0C32BE0552EE5599FFBB0FE97DF1,
	U3CU3Ec__DisplayClass12_0_U3CDOColorU3Eb__1_m4D05D90FBB17A5455CC66D17A83CF1656A87755B,
	U3CU3Ec__DisplayClass13_0__ctor_m06708EDA2D1D9444D4BBD595985B8EFFD0595710,
	U3CU3Ec__DisplayClass13_0_U3CDOIntensityU3Eb__0_mCDD8C584608D56E4644AEEFEF0993C89615D2ADA,
	U3CU3Ec__DisplayClass13_0_U3CDOIntensityU3Eb__1_m91CCE11F8FA50E2CE4CCAF30A2CBD50FF6F6AEF8,
	U3CU3Ec__DisplayClass14_0__ctor_m5FB455D73785B75245469F31A7392D0144FCDF21,
	U3CU3Ec__DisplayClass14_0_U3CDOShadowStrengthU3Eb__0_m2DC657B64395CE80DD215B383A03539A0CEEF380,
	U3CU3Ec__DisplayClass14_0_U3CDOShadowStrengthU3Eb__1_mC8073F85B752DECD9A03AA3B72A083098E0D62AB,
	U3CU3Ec__DisplayClass15_0__ctor_m9578242E75063C730CD83AA00A13DE917D78112D,
	U3CU3Ec__DisplayClass15_0_U3CDOColorU3Eb__0_mB56C6DC371C3B3D7C76896EB0980DB19812055AE,
	U3CU3Ec__DisplayClass15_0_U3CDOColorU3Eb__1_m4A0DD6168BAD515186450A98443517EB737BFC83,
	U3CU3Ec__DisplayClass16_0__ctor_m2B3D033F265B069E406A5330FE153EDADECEBC28,
	U3CU3Ec__DisplayClass16_0_U3CDOColorU3Eb__0_m68313755D418CC0E079ECB927D1DA4C137927F73,
	U3CU3Ec__DisplayClass16_0_U3CDOColorU3Eb__1_mBCABAA139578080574B0C7AFBD7938C6E49961EF,
	U3CU3Ec__DisplayClass17_0__ctor_mE986B184C7B7F578C4EFDDCDC57D33970F613668,
	U3CU3Ec__DisplayClass17_0_U3CDOColorU3Eb__0_mF06E3103940FEA26AE34A10EB7737BEF9322200D,
	U3CU3Ec__DisplayClass17_0_U3CDOColorU3Eb__1_mD8C4BBDABA25C6A116793F58B6EED5DF8EDF4145,
	U3CU3Ec__DisplayClass18_0__ctor_mF6B6673A10CC377B356E27C342F3CEDE4F015655,
	U3CU3Ec__DisplayClass18_0_U3CDOColorU3Eb__0_mC6E544B7FA223A789381F3327AB3A206BFEDBDFC,
	U3CU3Ec__DisplayClass18_0_U3CDOColorU3Eb__1_m9BF452CD36004FAC17150C6F6716921B4B1389B9,
	U3CU3Ec__DisplayClass19_0__ctor_m6A019E5099794BFD3EC9819B9C19AC696B218865,
	U3CU3Ec__DisplayClass19_0_U3CDOFadeU3Eb__0_m50D60171D21D0F9B87A60CA102A9CE3DAE504842,
	U3CU3Ec__DisplayClass19_0_U3CDOFadeU3Eb__1_m5203CDD55EDCD16C106CF2230BEB634973FA87C1,
	U3CU3Ec__DisplayClass1_0__ctor_mF7AE84F589865AFA97F3F0613C42A63F7ADF9FD2,
	U3CU3Ec__DisplayClass1_0_U3CDOColorU3Eb__0_mB50622EF9AB0EA0CF6318319A488B134B1E441EA,
	U3CU3Ec__DisplayClass1_0_U3CDOColorU3Eb__1_mC208F6B646E6706D73391F28237DE9FB3E01C022,
	U3CU3Ec__DisplayClass20_0__ctor_m46D2A9D57813D152D095E17B82ADE4E928A4F340,
	U3CU3Ec__DisplayClass20_0_U3CDOFadeU3Eb__0_m65265FF6B2A568D71DB04F4DEDB17A56740B973B,
	U3CU3Ec__DisplayClass20_0_U3CDOFadeU3Eb__1_m352986405F427FBF9D1A7638E8CE454B2040D20A,
	U3CU3Ec__DisplayClass21_0__ctor_m20E016BF125EB91B4F5E6055F2A639D3F38E01D1,
	U3CU3Ec__DisplayClass21_0_U3CDOFadeU3Eb__0_m2683CDC969E8BD8C678DEC58DD5FD63F57F63141,
	U3CU3Ec__DisplayClass21_0_U3CDOFadeU3Eb__1_mCFC1424EB14ADA721DACA63749D848761345659C,
	U3CU3Ec__DisplayClass22_0__ctor_mE9442DCF87C8F6389A6A0A61D1105BE83045DF26,
	U3CU3Ec__DisplayClass22_0_U3CDOFloatU3Eb__0_mAD602079E9F2B4853A31B8BD4EFFFB344F5C5809,
	U3CU3Ec__DisplayClass22_0_U3CDOFloatU3Eb__1_m0589A7EA48C46D08E55A89EBABF17FCB2429C0CB,
	U3CU3Ec__DisplayClass23_0__ctor_m3D03408645A4AF14B1AC909311CBB96DE3CFEDFF,
	U3CU3Ec__DisplayClass23_0_U3CDOFloatU3Eb__0_m7C5DE11C01617221026A8257B9273241699B4F61,
	U3CU3Ec__DisplayClass23_0_U3CDOFloatU3Eb__1_mFF0E5B82DCD030309B2FB7AE1D59E8C83D8563AC,
	U3CU3Ec__DisplayClass24_0__ctor_m3D84B12A9479235642E67A8B23057D53538240EF,
	U3CU3Ec__DisplayClass24_0_U3CDOOffsetU3Eb__0_m615CB1C7DF301BF290C15825CF686F172317AA1A,
	U3CU3Ec__DisplayClass24_0_U3CDOOffsetU3Eb__1_m71F196546178F686FC6EEB0AE494120CBE907F60,
	U3CU3Ec__DisplayClass25_0__ctor_m6DC224CBD0A329D0855A1DF42D0F02498015F32B,
	U3CU3Ec__DisplayClass25_0_U3CDOOffsetU3Eb__0_m4BB3231BDB040EEE41ACD5FFDE49808AA0A48F68,
	U3CU3Ec__DisplayClass25_0_U3CDOOffsetU3Eb__1_mC32BCBA2A6744289CBB4E905AD7AC46CDB11FC84,
	U3CU3Ec__DisplayClass26_0__ctor_mD06477925B075A1C5F0C20FF4265B246680922BC,
	U3CU3Ec__DisplayClass26_0_U3CDOTilingU3Eb__0_m21A9418CA352D5B40AFA7432D49E946CD5FF2839,
	U3CU3Ec__DisplayClass26_0_U3CDOTilingU3Eb__1_m5FEE43CB4561CFF3BC4333EE3CC6B5AE7FE57DB7,
	U3CU3Ec__DisplayClass27_0__ctor_m57322655082D93A26CC7BE6652797818B8EAE4BD,
	U3CU3Ec__DisplayClass27_0_U3CDOTilingU3Eb__0_m96DF397511A1C88992F2DD301DDF91FE3DA710A7,
	U3CU3Ec__DisplayClass27_0_U3CDOTilingU3Eb__1_m88831A946F576A9B977CB52AE2006EFD2F3A3801,
	U3CU3Ec__DisplayClass28_0__ctor_m5AC250F09B06A879670E49F35453952EB61B4CFB,
	U3CU3Ec__DisplayClass28_0_U3CDOVectorU3Eb__0_m1D25A90B7D94566FE07109C6E35DCD65AC71B8C2,
	U3CU3Ec__DisplayClass28_0_U3CDOVectorU3Eb__1_m3973332F5965D272EB19152E4857A3EE9918AC3C,
	U3CU3Ec__DisplayClass29_0__ctor_m0220C8C3A1421AC6125FF06C18B858862B9ECE30,
	U3CU3Ec__DisplayClass29_0_U3CDOVectorU3Eb__0_m91EAA9F1BE517B3A9EC3F713A114384AEBA5B2D6,
	U3CU3Ec__DisplayClass29_0_U3CDOVectorU3Eb__1_mB8F5C30F9E03F2CE1B2648F6CFD9771BC4A3E393,
	U3CU3Ec__DisplayClass2_0__ctor_m77A78F37F4F30D6C82425B3B94EBD31CC0F4D0B3,
	U3CU3Ec__DisplayClass2_0_U3CDOFarClipPlaneU3Eb__0_m2B7E640D986C4DE7127ED311FDD0E981646CE3BF,
	U3CU3Ec__DisplayClass2_0_U3CDOFarClipPlaneU3Eb__1_m20E5E80C138AF18AF89190EE60552446A1CCE24E,
	U3CU3Ec__DisplayClass30_0__ctor_mEF82474D90EFC7E8C04D471F27CBD3BDD6BE93AA,
	U3CU3Ec__DisplayClass30_0_U3CDOResizeU3Eb__0_m6E12D44BD65A871DE203ACD9000AA2993F76C558,
	U3CU3Ec__DisplayClass30_0_U3CDOResizeU3Eb__1_m49CAE0AA6E1D2AADEE7FFC4E2B47D7E85D75ECA1,
	U3CU3Ec__DisplayClass31_0__ctor_m35DF34BF4DA2381F29755301658A0BB747FA96EB,
	U3CU3Ec__DisplayClass31_0_U3CDOTimeU3Eb__0_m0FD8E6015BEBDE03A9ABAED5983392B2FCB1F4DA,
	U3CU3Ec__DisplayClass31_0_U3CDOTimeU3Eb__1_m4620873F08672F52FDF5B1FEF817BB5A8D9DEEB2,
	U3CU3Ec__DisplayClass32_0__ctor_mF99E40BAC9D70A41F9D49D720B24F55F5B514161,
	U3CU3Ec__DisplayClass32_0_U3CDOMoveU3Eb__0_m15E1AAFD0E46945E290D6BE466818E3ED7F7F6E3,
	U3CU3Ec__DisplayClass32_0_U3CDOMoveU3Eb__1_m580B3AFDB23D638C3BA6A8EE6DE4B6420CE8FE0A,
	U3CU3Ec__DisplayClass33_0__ctor_mC307857DB264A833A859C337847ABE7CFE02ABEA,
	U3CU3Ec__DisplayClass33_0_U3CDOMoveXU3Eb__0_m672EE198EEFD3F11643380B5B4AC8A8BDF6DC8AB,
	U3CU3Ec__DisplayClass33_0_U3CDOMoveXU3Eb__1_mF2D746B379D982432C60E81D729F17841522B9EC,
	U3CU3Ec__DisplayClass34_0__ctor_m9A0DE73F61CAF5080A3B3AEEF584CC91173CDE84,
	U3CU3Ec__DisplayClass34_0_U3CDOMoveYU3Eb__0_mEA5D9AE6240AB3C10DF0754378C02A20C7CB6FA6,
	U3CU3Ec__DisplayClass34_0_U3CDOMoveYU3Eb__1_mE520F974C708A818D0E3B1120A6D61003CE03BB4,
	U3CU3Ec__DisplayClass35_0__ctor_mE23897DD201880C93010CF2ED112E4D41184E089,
	U3CU3Ec__DisplayClass35_0_U3CDOMoveZU3Eb__0_mF31E73B5B0A03025464121D9A883B4E2BFF1936F,
	U3CU3Ec__DisplayClass35_0_U3CDOMoveZU3Eb__1_mFC1B457E5544CB48C65CDA2DFA66021E862DBC00,
	U3CU3Ec__DisplayClass36_0__ctor_mAE58EFC3DD1DD7C1F36380102ECD20D2AA00F1CE,
	U3CU3Ec__DisplayClass36_0_U3CDOLocalMoveU3Eb__0_m6AF2104AC0D94DF07EC176F21E354FE77D112485,
	U3CU3Ec__DisplayClass36_0_U3CDOLocalMoveU3Eb__1_m92CAF0D1D04465B4298655176590064880FA0DAC,
	U3CU3Ec__DisplayClass37_0__ctor_mBAC6F62D6E9F705DC745BBD054888DF225AA5424,
	U3CU3Ec__DisplayClass37_0_U3CDOLocalMoveXU3Eb__0_mAC2B0F4A96659A604324E423FCE9BA452E814AE7,
	U3CU3Ec__DisplayClass37_0_U3CDOLocalMoveXU3Eb__1_m1AEC67CD3D0D9CCDD71D2E279DF41F6D1B4E9C2F,
	U3CU3Ec__DisplayClass38_0__ctor_m6F9BF3A11CB51B09C745E748C09390267086B87F,
	U3CU3Ec__DisplayClass38_0_U3CDOLocalMoveYU3Eb__0_m2B2E81D5F0161C957DF092462992DDCF6392456A,
	U3CU3Ec__DisplayClass38_0_U3CDOLocalMoveYU3Eb__1_m8349857541D29FF7CC3330277AEF5BE80DE9C2E3,
	U3CU3Ec__DisplayClass39_0__ctor_m29A7A06708DC5A483C9B9BF0C06C692B4261093A,
	U3CU3Ec__DisplayClass39_0_U3CDOLocalMoveZU3Eb__0_m26C942D482D50282BAF7E2F3AD96941F03356DD9,
	U3CU3Ec__DisplayClass39_0_U3CDOLocalMoveZU3Eb__1_mD37D2AF63E4348522DA92AE358F170830D3D1BF8,
	U3CU3Ec__DisplayClass3_0__ctor_mD3DB006B7B8A1A194339F0ECC23CA1E04787EFD9,
	U3CU3Ec__DisplayClass3_0_U3CDOFieldOfViewU3Eb__0_m15FBEF870514F3B7A0E107807AA4CC626C0A5B91,
	U3CU3Ec__DisplayClass3_0_U3CDOFieldOfViewU3Eb__1_mE0B6B23B688947A0F131A1CC31CE19D153052990,
	U3CU3Ec__DisplayClass40_0__ctor_m8E6EAA117C9611630A2AF2B34959429DA441AF03,
	U3CU3Ec__DisplayClass40_0_U3CDORotateU3Eb__0_m89100800A2C8745BE1323653608E893652183B7E,
	U3CU3Ec__DisplayClass40_0_U3CDORotateU3Eb__1_mE57D43D5CBC331CCB313FE97DF304962B4B61272,
	U3CU3Ec__DisplayClass41_0__ctor_m7211D7D9CFE52BDAC307D7597FDE8827E2EE72BF,
	U3CU3Ec__DisplayClass41_0_U3CDORotateQuaternionU3Eb__0_m11D48D2D5C30864291E106B06B3325AFE50FD6FE,
	U3CU3Ec__DisplayClass41_0_U3CDORotateQuaternionU3Eb__1_m4656937AA9BF166347E1732EFFF7CFC00AFE99C6,
	U3CU3Ec__DisplayClass42_0__ctor_m894ECDDCD8FA39381D104A78723ED0D3B0FEFA24,
	U3CU3Ec__DisplayClass42_0_U3CDOLocalRotateU3Eb__0_m60162FBB56ACF20CFD036154E08DB13B16C3CE47,
	U3CU3Ec__DisplayClass42_0_U3CDOLocalRotateU3Eb__1_m8DBE8031D1E7627BE19794A3F97744BA72D358D0,
	U3CU3Ec__DisplayClass43_0__ctor_mA87CB75673A8966542861C9F91A3AFCCC9C1A5B2,
	U3CU3Ec__DisplayClass43_0_U3CDOLocalRotateQuaternionU3Eb__0_mE8288A54BEBE4DBA9C4B67329F1F706079522125,
	U3CU3Ec__DisplayClass43_0_U3CDOLocalRotateQuaternionU3Eb__1_m3C8277B8FB3C947800D0A4559F751C052C164B37,
	U3CU3Ec__DisplayClass44_0__ctor_mC24979AC46FE287403167265ABFD5C08E7C62D33,
	U3CU3Ec__DisplayClass44_0_U3CDOScaleU3Eb__0_m0A1B1E2996430F9455E9FF81C312223B3C46CE35,
	U3CU3Ec__DisplayClass44_0_U3CDOScaleU3Eb__1_m95D31585339C2303A33F37C20E75697B13FC0FF4,
	U3CU3Ec__DisplayClass45_0__ctor_mFE0E60DCA44E3246F0DB335E19E0AF2E402534AB,
	U3CU3Ec__DisplayClass45_0_U3CDOScaleU3Eb__0_mA019F344C52AAF2983264C13E2927C86587FF328,
	U3CU3Ec__DisplayClass45_0_U3CDOScaleU3Eb__1_mFEB696A329BC4E973789918A21DAD4D410DC0C7C,
	U3CU3Ec__DisplayClass46_0__ctor_m9121E5D8A8711B02BE3270A41620C9BAC279F3D3,
	U3CU3Ec__DisplayClass46_0_U3CDOScaleXU3Eb__0_mAC5538D08B0057A16B011DDBC19D439ED4F6B39F,
	U3CU3Ec__DisplayClass46_0_U3CDOScaleXU3Eb__1_mFA80DCF42E0225F9C4E0B5CA43E5669F1358E619,
	U3CU3Ec__DisplayClass47_0__ctor_mFF8096FA13023A99D7F23913A1E04040C4C3A6B4,
	U3CU3Ec__DisplayClass47_0_U3CDOScaleYU3Eb__0_m19690B99AF76F16C69662374F92BE4DB91603CF7,
	U3CU3Ec__DisplayClass47_0_U3CDOScaleYU3Eb__1_m4E713488A1664A212A158C0406179AFEE46B9E66,
	U3CU3Ec__DisplayClass48_0__ctor_m9A5E1D3195B205418263C7243DF6A4B0ECEE41FE,
	U3CU3Ec__DisplayClass48_0_U3CDOScaleZU3Eb__0_mBA1EEFF4583ACD3BD9E60BCAA07DAFACA474454D,
	U3CU3Ec__DisplayClass48_0_U3CDOScaleZU3Eb__1_m209AF9BAFB6DB10D66CEA60482CF36979896DA58,
	U3CU3Ec__DisplayClass4_0__ctor_m77E54345DFA70ADC4D7EC7ABF871C8A4AD64B233,
	U3CU3Ec__DisplayClass4_0_U3CDONearClipPlaneU3Eb__0_m873F66541037293275E7E80CE866ECC57078F716,
	U3CU3Ec__DisplayClass4_0_U3CDONearClipPlaneU3Eb__1_m0600DE0E9328530D86EC4FCB0412C549EC89BE7A,
	U3CU3Ec__DisplayClass51_0__ctor_m6993C5B08436C61812CD70E98322A42665AC82C9,
	U3CU3Ec__DisplayClass51_0_U3CLookAtU3Eb__0_mFFFB688997750C6AC2E360D18E6520A7CBD3593A,
	U3CU3Ec__DisplayClass51_0_U3CLookAtU3Eb__1_mFA385489E528D88DC72474BBB4D6ECE26A8D0915,
	U3CU3Ec__DisplayClass52_0__ctor_mF34A0E0114DC164E1A106453AA3A5B83EA8ACB6B,
	U3CU3Ec__DisplayClass52_0_U3CDOPunchPositionU3Eb__0_m81C33B214128A250A39E1906E632239592E04F6B,
	U3CU3Ec__DisplayClass52_0_U3CDOPunchPositionU3Eb__1_m024A958172481CA5FA792FEB41467797B3C9B6EA,
	U3CU3Ec__DisplayClass53_0__ctor_m22CC6EB24492FBDB93D8B16E626B49D8FDDB1DEB,
	U3CU3Ec__DisplayClass53_0_U3CDOPunchScaleU3Eb__0_mBE325AAB304A26B2715A6395CDD180A6C8A3600C,
	U3CU3Ec__DisplayClass53_0_U3CDOPunchScaleU3Eb__1_m792A395062F71C906D9048E0D283B66B2230BFFF,
	U3CU3Ec__DisplayClass54_0__ctor_m8487623AB289AE5A29AAE68FBD16094EBED4619A,
	U3CU3Ec__DisplayClass54_0_U3CDOPunchRotationU3Eb__0_mBB19DB5560F41C19F8B73FCF54924309F34BBA57,
	U3CU3Ec__DisplayClass54_0_U3CDOPunchRotationU3Eb__1_m5371FF76477A13C88F79E83F00CD9D66F12E528C,
	U3CU3Ec__DisplayClass55_0__ctor_m97A8CADF7B9EEE52B623FF51986470FE315B8F2B,
	U3CU3Ec__DisplayClass55_0_U3CDOShakePositionU3Eb__0_mE10A40D355957ADCEFA12B43BE0EBE540DE94FF9,
	U3CU3Ec__DisplayClass55_0_U3CDOShakePositionU3Eb__1_m7092F461DE9ADE4FADC6EAF78FCED8244D01B129,
	U3CU3Ec__DisplayClass56_0__ctor_mE3F409583C85BFFAD329D6657AE57326CEA380B8,
	U3CU3Ec__DisplayClass56_0_U3CDOShakePositionU3Eb__0_mF2B79D1D2A52FEE77DBEF0D3752814719E7424EB,
	U3CU3Ec__DisplayClass56_0_U3CDOShakePositionU3Eb__1_m5EA33E40BBAB50F4E27DAC7CDD695724E7C44354,
	U3CU3Ec__DisplayClass57_0__ctor_mF231F02D3A9995FC73737A6B2D5A9B607F22F03A,
	U3CU3Ec__DisplayClass57_0_U3CDOShakeRotationU3Eb__0_m194541436AE87A8B7EFA3A39A6FC71C52DA24564,
	U3CU3Ec__DisplayClass57_0_U3CDOShakeRotationU3Eb__1_m196FE3822A094C80CB3C6090549C7EC66D1BA11B,
	U3CU3Ec__DisplayClass58_0__ctor_m53D84AC3C9E88A1B5850B1D7B2CF4A782504B6CB,
	U3CU3Ec__DisplayClass58_0_U3CDOShakeRotationU3Eb__0_m2FD3E368BC52F4035C332895913857CCACF526DF,
	U3CU3Ec__DisplayClass58_0_U3CDOShakeRotationU3Eb__1_mD40137CBF22DEAB514CF358112190DBB4E6EBC86,
	U3CU3Ec__DisplayClass59_0__ctor_mFB9FDA8361D047ECEAA4AE0A80216FAC13FB3367,
	U3CU3Ec__DisplayClass59_0_U3CDOShakeScaleU3Eb__0_mC43F13106D5AFEDD6F71D7D4BC7A245FB53FB2ED,
	U3CU3Ec__DisplayClass59_0_U3CDOShakeScaleU3Eb__1_m8BC5E5A93F052CCDEFDA7A96927D471FB36AD4F4,
	U3CU3Ec__DisplayClass5_0__ctor_m7783B6BCA09BDF4543ADF7F62889ABBA94DB3A3A,
	U3CU3Ec__DisplayClass5_0_U3CDOOrthoSizeU3Eb__0_m034E070A3B9354C6D1D5DE8A1B26FCF53208F73D,
	U3CU3Ec__DisplayClass5_0_U3CDOOrthoSizeU3Eb__1_m2AC152D033DE5ACFE3232261EF59650B917174AE,
	U3CU3Ec__DisplayClass60_0__ctor_mBD17ABB100CA70BA08DE77CD4E7EFD2AB391F9E7,
	U3CU3Ec__DisplayClass60_0_U3CDOShakeScaleU3Eb__0_m8563BAF05E9A2C089BB4D13E78A8B16B3D88E36C,
	U3CU3Ec__DisplayClass60_0_U3CDOShakeScaleU3Eb__1_mC249A37B407D2424DF1B0008589DAE10769FAE32,
	U3CU3Ec__DisplayClass61_0__ctor_mF0DCB007CD3DEB4B3862304576FE084BCD3EEC88,
	U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__0_mBCA8B1ACA7CD1328FF640C0032E06EF9EB12892E,
	U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__1_m65426AE26A828CEB2A9F34622F437EB966DFF7F9,
	U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__2_m6F01E6D9A017E490E275EE3E6F2ED6865041625F,
	U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__3_m6EEE85C49B210817225EFABD979308B70E3D3DAB,
	U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__4_m086B019BAE346B800762DCD8FB8A3CD7FB25847A,
	U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__5_m95E816FF3C25EA02814904FF4133E1BBC58D09D8,
	U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__6_mE44249A4ECAC427275C4FE5770DD3A0AE053186F,
	U3CU3Ec__DisplayClass61_0_U3CDOJumpU3Eb__7_mD6F5856616519F9170A14259EBE6CBA7AB625C4A,
	U3CU3Ec__DisplayClass62_0__ctor_m721B008BA9D9EDDA666EA690FDA87621599EFA9A,
	U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__0_m20C855D3078EC01E8392F76CF463C5C0CF6C9491,
	U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__1_mC866579560B3053A7C64BAAA82FA71BBBB7DCDEC,
	U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__2_mC2A0D20A6E2AC20AF1A2AA66A02F988027155BAA,
	U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__3_m6DCD34A55C87B354D1FB34D195338AF2F2CB2EDE,
	U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__4_mCB64533DDBD812D943FC3134C4F3296000BCEA36,
	U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__5_m60C754E97231121E8E9D81E5E4E3DB2DA2A39B22,
	U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__6_m419506042A2BDECDDDD27B322891EAF108C20642,
	U3CU3Ec__DisplayClass62_0_U3CDOLocalJumpU3Eb__7_mD0E4918DCCCC6C8B36424E882E9214E7B675CE18,
	U3CU3Ec__DisplayClass63_0__ctor_mB96BCCBA4CD18893BF3FE33921D1A1349C579F9A,
	U3CU3Ec__DisplayClass63_0_U3CDOPathU3Eb__0_m8D3440E5FE89819FD8DCDAFCFE7FC1AAFCEB92DE,
	U3CU3Ec__DisplayClass63_0_U3CDOPathU3Eb__1_m87BE9FA5B9FA36647B1AB9EAC06C498131DA3077,
	U3CU3Ec__DisplayClass64_0__ctor_m797499323817E936BD2292B270BFC32D447AD78B,
	U3CU3Ec__DisplayClass64_0_U3CDOLocalPathU3Eb__0_mDE31EA6776321007A3438FC8F6A329C3F1DDCC88,
	U3CU3Ec__DisplayClass64_0_U3CDOLocalPathU3Eb__1_mD3A0960DF5B0665A23903CAA811D3FF0703B2D10,
	U3CU3Ec__DisplayClass65_0__ctor_m099D2648CB7647055F29AA9353217918A0A26C52,
	U3CU3Ec__DisplayClass65_0_U3CDOPathU3Eb__0_m116A470BA28E49FB3E1429D5661C0920AE8D06FC,
	U3CU3Ec__DisplayClass65_0_U3CDOPathU3Eb__1_m936610184D3C1E8F0105886D4AF3BE35438B9EB0,
	U3CU3Ec__DisplayClass66_0__ctor_m830CCD8E00AFFE9A909C331B429BF8E61EA32E1D,
	U3CU3Ec__DisplayClass66_0_U3CDOLocalPathU3Eb__0_m5B791E9992D1668527B86298721253FC7BAC54E5,
	U3CU3Ec__DisplayClass66_0_U3CDOLocalPathU3Eb__1_m1180D44A1229B978D6DC4519B895FD07506D0F9A,
	U3CU3Ec__DisplayClass67_0__ctor_mC6B7195B1F7134B7B48316FBDBD8E0B276A8B8C1,
	U3CU3Ec__DisplayClass67_0_U3CDOTimeScaleU3Eb__0_mBCD23BC1C3F1F4901406334A1D97E0FE6B01E85D,
	U3CU3Ec__DisplayClass67_0_U3CDOTimeScaleU3Eb__1_mBA9D6C0032563A2910CC5D911E73796D0366D85F,
	U3CU3Ec__DisplayClass68_0__ctor_mD5D28BC610D7B5B492B9FBFA29FD8C5436D91678,
	U3CU3Ec__DisplayClass68_0_U3CDOBlendableColorU3Eb__0_m6555CECA55A46E1A39D7B69058610E99BBB58332,
	U3CU3Ec__DisplayClass68_0_U3CDOBlendableColorU3Eb__1_mC9E004DA8FBA7AD74F2963BD88A280A3B9A76995,
	U3CU3Ec__DisplayClass69_0__ctor_m336ACFBD10BE165468D57CCA35DE58DA5EA10CDF,
	U3CU3Ec__DisplayClass69_0_U3CDOBlendableColorU3Eb__0_mC9DCA4181D48AA4BE1FB6993E74A305ACAAF82A5,
	U3CU3Ec__DisplayClass69_0_U3CDOBlendableColorU3Eb__1_m02AF9B2CE735A0DC43899353BE7721AAF90B9C7F,
	U3CU3Ec__DisplayClass6_0__ctor_m3EF7021A9BC9E02A6D31DAECC153759EF98F1745,
	U3CU3Ec__DisplayClass6_0_U3CDOPixelRectU3Eb__0_m056478689B03F8E3C264EF28603426A02853E288,
	U3CU3Ec__DisplayClass6_0_U3CDOPixelRectU3Eb__1_m5FD55DBC4B5BD7120C6D09D6155A91E15FAE6BE0,
	U3CU3Ec__DisplayClass70_0__ctor_mBD371290CDFD8769612574B932E398F4E1788CFB,
	U3CU3Ec__DisplayClass70_0_U3CDOBlendableColorU3Eb__0_m231E5EBC1ACD38F97DEFC1A473505618AB21FFE3,
	U3CU3Ec__DisplayClass70_0_U3CDOBlendableColorU3Eb__1_mB72CD2B2475C5415579A7FF83EA76EE554307B82,
	U3CU3Ec__DisplayClass71_0__ctor_m81538C42748C81D4E0F0D8894692851055750B90,
	U3CU3Ec__DisplayClass71_0_U3CDOBlendableColorU3Eb__0_m931F78F97E359A760009DF99D57A80F4FF6FC8C1,
	U3CU3Ec__DisplayClass71_0_U3CDOBlendableColorU3Eb__1_m4DAED7A1E30DB4DF1528B4E22F23F356B556359D,
	U3CU3Ec__DisplayClass72_0__ctor_m1D7F378D80D7D3576BD72FCC807B325894ED0915,
	U3CU3Ec__DisplayClass72_0_U3CDOBlendableMoveByU3Eb__0_mD1D39ED6CF0DCA3C9EFDEE04FA17C9CB848B3861,
	U3CU3Ec__DisplayClass72_0_U3CDOBlendableMoveByU3Eb__1_m69692A8D35EA77D48059CC93EBD49BF21DA590C1,
	U3CU3Ec__DisplayClass73_0__ctor_m6649AB477E03767C1021955E5ABBE7DECC87C3F2,
	U3CU3Ec__DisplayClass73_0_U3CDOBlendableLocalMoveByU3Eb__0_m1AC8C73A45605BB87EB1846A614ECE6B8F038918,
	U3CU3Ec__DisplayClass73_0_U3CDOBlendableLocalMoveByU3Eb__1_m9789CF263903DA2070FE21F6E11095BC2CE5D511,
	U3CU3Ec__DisplayClass74_0__ctor_m99F7AC61317AA00B2F6B13D3851F93635F755689,
	U3CU3Ec__DisplayClass74_0_U3CDOBlendableRotateByU3Eb__0_mE40A0FC0BBCBC29D75C78B6FE25EDD674A7356F6,
	U3CU3Ec__DisplayClass74_0_U3CDOBlendableRotateByU3Eb__1_m3805AB400A575DF0161E0C31CF64A2B9A791E83E,
	U3CU3Ec__DisplayClass75_0__ctor_m80AB38B540DBE1AC01FFD7811E4DD3160393336E,
	U3CU3Ec__DisplayClass75_0_U3CDOBlendableLocalRotateByU3Eb__0_m7881708F320253DCE3753A16A4CD7FE2C7B10C8A,
	U3CU3Ec__DisplayClass75_0_U3CDOBlendableLocalRotateByU3Eb__1_mA7F97873E40347E60AD46B7820CCCE0AE53FA5CA,
	U3CU3Ec__DisplayClass76_0__ctor_m22A4DFBA23997056B27645B80F8984E145C70DCB,
	U3CU3Ec__DisplayClass76_0_U3CDOBlendablePunchRotationU3Eb__0_m40B5F65FF6749A3C2AB19513960836BB3A476919,
	U3CU3Ec__DisplayClass76_0_U3CDOBlendablePunchRotationU3Eb__1_m953315BF07BBFAC9F0C752A06114F448CE67FAE6,
	U3CU3Ec__DisplayClass77_0__ctor_mBA0040576F8B3B6452D8527B1B42064443690136,
	U3CU3Ec__DisplayClass77_0_U3CDOBlendableScaleByU3Eb__0_m17EADF977475A1EEF768FE70AC3D97EF590DD189,
	U3CU3Ec__DisplayClass77_0_U3CDOBlendableScaleByU3Eb__1_m30828F5AE0730C435901F5646137B19E1EB29140,
	U3CU3Ec__DisplayClass7_0__ctor_m99FED5DE1D5FCE5A033A41690EEE45C7B02436FA,
	U3CU3Ec__DisplayClass7_0_U3CDORectU3Eb__0_m323992D6C4F67E9BCDAC970E9132420617355BEC,
	U3CU3Ec__DisplayClass7_0_U3CDORectU3Eb__1_m326FD99ABCA6F25C8F9BC3438FFA62CC05A6813E,
	U3CU3Ec__DisplayClass8_0__ctor_mCB1B4229E202920D84CC031B99C3E747B73471C9,
	U3CU3Ec__DisplayClass8_0_U3CDOShakePositionU3Eb__0_mF42E5FF0D71942D542EE5BBFC86A1F6520BAED6A,
	U3CU3Ec__DisplayClass8_0_U3CDOShakePositionU3Eb__1_m48B9C988EB41A14206B8C9EE4ADF8C8A2F1B634A,
	U3CU3Ec__DisplayClass9_0__ctor_m69682F27FB68AF9E238ECAB74B1CB15F7FD25543,
	U3CU3Ec__DisplayClass9_0_U3CDOShakePositionU3Eb__0_m904DA7104A37C0D2A2453C411C3E389B4F0D75DD,
	U3CU3Ec__DisplayClass9_0_U3CDOShakePositionU3Eb__1_m9888C9196873E5D534AFDBAC95A7532022BA9CB8,
	TweenParams__ctor_m5F3F28BA299D184563D1AF7B8B19DBF186915F2E,
	TweenParams_Clear_mEBB99A92738E51EB5018540C95844F230DD3A13F,
	TweenParams_SetAutoKill_mA0D39BA65BDFFC88C132C208730B417A863ADDCC,
	TweenParams_SetId_m173A81D2C2429A35976E6F0588EE4082D13D8481,
	TweenParams_SetId_m753850D312FF6D2E254CDAE73D402ABBD3251EAC,
	TweenParams_SetId_m87F9EFA8D2446BCEC1149317F2F7B02678FC3935,
	TweenParams_SetTarget_m4AE8DB5FB9DAE4F911BA9405B81EA6CE6C3B6FB8,
	TweenParams_SetLoops_m8191038E50515B7171130F5A63B58D49A4AE751F,
	TweenParams_SetEase_m595B5405EAB2EF60C8B08934202922724323F568,
	TweenParams_SetEase_m5919B6168471BD922DA020F3DF62D1759B42EEA3,
	TweenParams_SetEase_mE1CCC1AC51D40BD5AF0AEE467DA96AF395CD8999,
	TweenParams_SetRecyclable_m46EC952EE565A3E13C711916ABEFCC36F42FEA6C,
	TweenParams_SetUpdate_m1DE5A39760E44F974A13150B9D22A43BA538BE85,
	TweenParams_SetUpdate_m05ECFCEA35FE949F3E78EFAAEB3295E44691E5D2,
	TweenParams_OnStart_m50048410DF94AEEDF3549DB9904080E56C572030,
	TweenParams_OnPlay_mFC826F785D143068EE0FA0E37128B639A63B43BA,
	TweenParams_OnRewind_m5A37A1096AFF82082F244F8716FB5E87155F60CF,
	TweenParams_OnUpdate_m95CD680B0CA15FFD9098DF88DE81310355DACCE8,
	TweenParams_OnStepComplete_m2BB5EA797AFB686F45AADDBBD3A986B7C511476C,
	TweenParams_OnComplete_mE829825D5F74322D13F2B6C4EBF59A8FF2A06191,
	TweenParams_OnKill_m1853F941D8B794CF6262EC0FEA608FFF7D4E8445,
	TweenParams_OnWaypointChange_m24647E70B50CB31A9D37DF7AA2DD42AAAED34492,
	TweenParams_SetDelay_m542E64992644B47D6EE4592F4B9E0F2B43ED6988,
	TweenParams_SetRelative_mA5490896AD402F9563BAC58D14F66A66633C3B68,
	TweenParams_SetSpeedBased_m7984A5F846AC411A530E76ACE13A29C2F49BFA10,
	TweenParams__cctor_m489645841D9F80D199F4F1E3891CFEEB31B45045,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TweenSettingsExtensions_Append_mAFF074C9F7DE2BC8FF383C7729B13E732345F28F,
	TweenSettingsExtensions_Prepend_m241B44F371F4C324511EEE213AE5866CFC75E3BE,
	TweenSettingsExtensions_Join_mBA1D659EE0310BBE1F42148057403E7C5EEDB777,
	TweenSettingsExtensions_Insert_m69E4EA33E374050B17000847C172BC6F93C71928,
	TweenSettingsExtensions_AppendInterval_m36B7A337E62568050B2A3220C9140D06CD50CD82,
	TweenSettingsExtensions_PrependInterval_mCC5525CBFA2E20938D4D095DF5F78720C23228E0,
	TweenSettingsExtensions_AppendCallback_m050F66C6988DBDC56B2DA4A52A25228F0B073804,
	TweenSettingsExtensions_PrependCallback_mC34DCD244F93C4B32D9E3751F0D9DBA504F42BC5,
	TweenSettingsExtensions_JoinCallback_m2A3763A75F4E1FB11FED3DF028BC0F3C2B8CBB64,
	TweenSettingsExtensions_InsertCallback_mA93A3B878FA88987C572D25C52FBA283D86E8CF9,
	TweenSettingsExtensions_ValidateAddToSequence_m6420279478426B0BF7E90A9BE5A4CB70C60C7F7E,
	NULL,
	NULL,
	NULL,
	NULL,
	TweenSettingsExtensions_From_m497E1389C0F3F6B8E1FC52522FD23AC386621BDC,
	TweenSettingsExtensions_From_m81CAED85AE1EE7446C7CB6B58C08DF4107E2F74B,
	TweenSettingsExtensions_From_mC35C4711D2293C36E778485B229605E0490F4AD6,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TweenSettingsExtensions_SetOptions_m94B0ECDB7445CABBF2814531E137F51582AE5425,
	TweenSettingsExtensions_SetOptions_m470EC93A8B43B25894F7143B876B117AFF2B000E,
	TweenSettingsExtensions_SetOptions_mFD076C0377973484D8607E062753FE55478F0D0C,
	TweenSettingsExtensions_SetOptions_m6FA72AD20A82D69FDB0189B31D81B5A78653FF50,
	TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700,
	TweenSettingsExtensions_SetOptions_m0F46F26543CFFDC14BC8E2656E8AE15D9CB56E22,
	TweenSettingsExtensions_SetOptions_m0C0AD0BEADFEB3D7B611204AFC7104E90FDBB716,
	TweenSettingsExtensions_SetOptions_m5201429E1EBC789C83A30D912C019E2F88B6EE85,
	TweenSettingsExtensions_SetOptions_mDBFD729D4FEE37F7A828AD98BFC532A802617F08,
	TweenSettingsExtensions_SetOptions_m28C06D4AD8A8667B6E28205BAE7DC962E6BDAB9B,
	TweenSettingsExtensions_SetOptions_m571339B54CA3A4BD935C07D719EE40E4FAE2C9D2,
	TweenSettingsExtensions_SetOptions_m3AF4D0166C4D467CCEFCB9B3116EA69D200C73E2,
	TweenSettingsExtensions_SetOptions_mDBB5ACE45F2ED717358105B955DFE7EC6EA9F408,
	TweenSettingsExtensions_SetOptions_m6D1AF94283335AC29B4DD9FBACA274940C96C444,
	TweenSettingsExtensions_SetOptions_mCAAE66B84802D2D369854C3741F4D1BBCD097EEF,
	TweenSettingsExtensions_SetOptions_m5E6F9A1145201D9D34824DE50132581D3DDC8DCE,
	TweenSettingsExtensions_SetLookAt_m92E5AE38463525D9672DF0E98D6980096DBC2814,
	TweenSettingsExtensions_SetLookAt_mEE755B6D883B6702C1ACF6C98E56E7646CB38ACF,
	TweenSettingsExtensions_SetLookAt_m2D965125CC14890614DFBFF2A5E762852F9F1AC2,
	TweenSettingsExtensions_SetLookAt_mEEEE2B9F0794E5B8E5D3C3B9D601CE5F61445533,
	TweenSettingsExtensions_SetLookAt_mECF406EE42F980E66C745916D2BBDF63F7E4C0D6,
	TweenSettingsExtensions_SetLookAt_m1AF2A3C9FA9942AA89C52A62743E480FDA9A4C12,
	TweenSettingsExtensions_SetLookAt_mF70A1BDA41F0C7CD95ABCD9E0DA9216E7F467553,
	TweenSettingsExtensions_SetPathForwardDirection_mF2A4AB7983A3BBDA380EA993C205C6D8AFE0B64C,
	Tween_get_isRelative_mC31C34D21C3953F9AA7F25C0429BEBE45D2DBAE2,
	Tween_set_isRelative_m881085052780C20122B970FA26766E551DA3B8EB,
	Tween_get_active_mD4253DD1A64623E342282E139081B787935A3E5E,
	Tween_set_active_m7E2D493098F2406830BAE9201422B8A1E7ADE2C7,
	Tween_get_fullPosition_m45E7CC4BFD8E07EB345918F956F0B3AE241C39FA,
	Tween_set_fullPosition_m903F28B26102F66CA13DB472121140925FA6495C,
	Tween_get_hasLoops_m95F8C10D71229834EB4B6225C7F2E8F692B71FFC,
	Tween_get_playedOnce_mDA42B6964058549DB8BBC9217DBBB2F0EB67A335,
	Tween_set_playedOnce_mC95D34B48FDF13A9C3B8451B2794A9FDA537019F,
	Tween_get_position_mF8A2FF9C0DA291DEC595AC8C00E2E096A009B5A8,
	Tween_set_position_mFA8507C0C9F2E513D037EB506CDC444AC993F4B2,
	Tween_Reset_m7E3A4C092BDB502A8B12E5DBB461602400A31C8D,
	NULL,
	Tween_UpdateDelay_m3BCCB4073A5EEDBD7DCE43C250D8BB79D255AE6B,
	NULL,
	NULL,
	Tween_DoGoto_m1A61731CB2E2D27D1E08ADD844E575A5DAD8939C,
	Tween_OnTweenCallback_mAF944138F2F0D8BFF50EC9B5EC1C24ADD7623FB8,
	NULL,
	Tween__ctor_m92AEA714BCF3EAFB7FAE3E6714A03B8F2CB45D17,
	Tweener__ctor_m04B7FAE8742229AF46C846C73F08E0F12A943F26,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CircleOptions_Reset_m6AFD52048392CA538A62BD5A05F3CC96DCF8ECBF,
	CircleOptions_Initialize_m****************************************,
	CirclePlugin_Reset_m****************************************,
	CirclePlugin_SetFrom_m****************************************,
	CirclePlugin_SetFrom_m****************************************,
	CirclePlugin_Get_m****************************************,
	CirclePlugin_ConvertToStartValue_m****************************************,
	CirclePlugin_SetRelativeEndValue_m****************************************,
	CirclePlugin_SetChangeValue_m****************************************,
	CirclePlugin_GetSpeedBasedDuration_m****************************************,
	CirclePlugin_EvaluateAndApply_m****************************************,
	CirclePlugin_GetPositionOnCircle_m****************************************,
	CirclePlugin__ctor_m****************************************,
	Color2Plugin_Reset_m4D1585F6639130F1C1D63606F8627B5AF78A54A0,
	Color2Plugin_SetFrom_mC3F20E4EDA65C6449C25BACD6DDB6C1199952155,
	Color2Plugin_SetFrom_m121840A073B33E14337DD4B87B44A7CFB1E34CA5,
	Color2Plugin_ConvertToStartValue_m9F85F95CB7AA6AFCAE9F5F20AB785B89048FBDC2,
	Color2Plugin_SetRelativeEndValue_m18D1D370D75DDEF9636CBE1AA964BD11D98A9490,
	Color2Plugin_SetChangeValue_m72A9F3272BC54989B589E62DC802685AB4BCD641,
	Color2Plugin_GetSpeedBasedDuration_mBACAE2EDEDED990D2E2B4078D9F6255DC52C8EA2,
	Color2Plugin_EvaluateAndApply_m1B7E14629EC95B176DBBE14BAE19367A1BA2B728,
	Color2Plugin__ctor_mBCE84E5A0ECEAF61FFA54965D1341A4C24E3FA46,
	DoublePlugin_Reset_mC48E0D04271DED61DE1422AC67CBFB42DD39D065,
	DoublePlugin_SetFrom_m45DF6B4F897082911AB5E64942EB89FCBC61267A,
	DoublePlugin_SetFrom_m628EE9B9815FEE0E0F3EFA7186A20FEC544C04C6,
	DoublePlugin_ConvertToStartValue_m3BA8C6742F81078B49EDAE5BE1B277BD3BDD6768,
	DoublePlugin_SetRelativeEndValue_m16BB892BED6D8E6DA25C367941CE14D1B5117F88,
	DoublePlugin_SetChangeValue_m2F806A26CE2DB009ADF69F33C05A136E5F1455D6,
	DoublePlugin_GetSpeedBasedDuration_m0661238953316795039FB9C7D05DBD4591710C33,
	DoublePlugin_EvaluateAndApply_mA1D9B93334F3D3FA3C878C6A21231EC32FA8A38A,
	DoublePlugin__ctor_mBC919A2E3792A7F49C9B42888306B7D328FAA373,
	LongPlugin_Reset_m37478A613E00FF0F05D3A58BEF11D649BFED5AC2,
	LongPlugin_SetFrom_m51A61DCC7C9CC9B0B32921B7A0B4A776A9441455,
	LongPlugin_SetFrom_m9D93F349D8673F54EB16E54FBD92E3551E706B7F,
	LongPlugin_ConvertToStartValue_mE222FBCEE94E48591988FEE4CEC005EFA70A1209,
	LongPlugin_SetRelativeEndValue_mF1C87E177CCEA2C17A5090CA9B1201F093BE1C7B,
	LongPlugin_SetChangeValue_mB10261C79DC621100E22EEB1DACA566C9C7AC3F4,
	LongPlugin_GetSpeedBasedDuration_mDC5560F8BC27B8917E870B1D3A2923E5E0C277C4,
	LongPlugin_EvaluateAndApply_mE4002CBF4187D69E1934AE0F49F3B314110AB9EB,
	LongPlugin__ctor_m2AD9AB566ED8B00E5944E0338ED86569A3E6A143,
	UlongPlugin_Reset_mA7D583EA46AE862A4A279127535806B67AC93863,
	UlongPlugin_SetFrom_mB55DCE5245B000AF510915EDF942F8C91DF2E8ED,
	UlongPlugin_SetFrom_m2426CC1748DC034F8D5C066D6900BB6E746F635A,
	UlongPlugin_ConvertToStartValue_m3F94E8568572F1E2BD3E03F043E625C2B356870D,
	UlongPlugin_SetRelativeEndValue_m6CE5E8F05C004378BB3CC4414FDA18B2F31C8C73,
	UlongPlugin_SetChangeValue_mD9BAA6099E44D738055E34640E4C33A4E0986C89,
	UlongPlugin_GetSpeedBasedDuration_mFA983E09C14C54961FE6DE1D3D3CCCE6DFC306B2,
	UlongPlugin_EvaluateAndApply_m6830EB9544F8402C6DA835C5C7F8FC11658AC9B3,
	UlongPlugin__ctor_m03E4C0827FFAC8322DEAC2D784708416A15DBD56,
	Vector3ArrayPlugin_Reset_mA352ED531258DEAD2F621144C41DB9258D0B9571,
	Vector3ArrayPlugin_SetFrom_m69DD6E6C7E4B79129DAEAF6B7D42272560894D62,
	Vector3ArrayPlugin_SetFrom_m17B0796B5408764AE682C1C458879E500D43825D,
	Vector3ArrayPlugin_ConvertToStartValue_mE150B601DA8164B66376565D966458967B80849D,
	Vector3ArrayPlugin_SetRelativeEndValue_m301BAFA7B81A646DD9E0ACFE44A4E03DD3DA8762,
	Vector3ArrayPlugin_SetChangeValue_m6CCE20B567B1B0A291C3F99001D73DAAE4000173,
	Vector3ArrayPlugin_GetSpeedBasedDuration_m3DE9F80A2BB6E51D8306D5FD0B7394D299B09B3E,
	Vector3ArrayPlugin_EvaluateAndApply_mB6F3D9AF1FA6921223C3BDFFC17B62B6FDD5BD9E,
	Vector3ArrayPlugin__ctor_m7E64B2C0B6FFD43FDEA1634185763064A0511EA3,
	PathPlugin_Reset_mF8A7585640B692E1337D76DC9E193C9EED0F7AC2,
	PathPlugin_SetFrom_mF6B10813AD9588EAA43C524A1A53886ECEBC45D4,
	PathPlugin_SetFrom_m58C1CFEBE65EAE3674C5B939D3948D00DAF97ACD,
	PathPlugin_Get_m997FB98C4FEB9E5E74B71034B36E2A40D0153BC6,
	PathPlugin_ConvertToStartValue_m2DD9E300B9FF4CA1DCD746E8E16548AE29CA86A2,
	PathPlugin_SetRelativeEndValue_m41C96CE4555F5DA30851CB54577E1997D954F579,
	PathPlugin_SetChangeValue_m8C85C4B7E85276B4D0885D390DE430E3DD349CBE,
	PathPlugin_GetSpeedBasedDuration_mCEA473E67BBD3A8BE7356A87B2F69FB13FF59B3B,
	PathPlugin_EvaluateAndApply_mB1C894CDC1DC3E9B3D8AFD7704C8F08208E0C465,
	PathPlugin_SetOrientation_m3F5BCB1FCBB546654FEB270D7B568F1667E9977B,
	PathPlugin_DivideVectorByVector_m276361EB5587FDDEB9D985E6DA35C1E903B7F7B8,
	PathPlugin_MultiplyVectorByVector_m43E9793D9F35E401FC9766892AD1D4B69CD88829,
	PathPlugin__ctor_mD8811EA5B57B17FC56CD3E926FF69CB41C5FC183,
	ColorPlugin_Reset_mD5C17D1107C847229AE3AF5513CB3FA5194961F4,
	ColorPlugin_SetFrom_m479A8C5CD0FCA868D41ED7A29A7214B511640CBD,
	ColorPlugin_SetFrom_mAADAA832F76978A40ACA0EFF96202E7348425594,
	ColorPlugin_ConvertToStartValue_mCFE988E065959F8C5898AAA3B209ABAFCD8AF3A8,
	ColorPlugin_SetRelativeEndValue_m66E263246FC9F63F50A4C5AFFC3C5E0D70B59E84,
	ColorPlugin_SetChangeValue_m185BDA16E9FFD1AE83152A1C8B4C55F13E4B74E7,
	ColorPlugin_GetSpeedBasedDuration_mABC5DCBD1B8D595243C93FA40FCDED9E25469AA0,
	ColorPlugin_EvaluateAndApply_m190AB3B2127491FD54359B4F3350728BA6DC501D,
	ColorPlugin__ctor_m633C70643C227166D0DE895DCD198751ADB832B8,
	IntPlugin_Reset_mB5E54408080BA058CE3141E70024CF7BBD4124A1,
	IntPlugin_SetFrom_mD5482FE1E980BDD2A15F9EF5A3DBA53C84CC2C30,
	IntPlugin_SetFrom_mD097AC17A5388FDF1F19F78CC62E9E961B45B311,
	IntPlugin_ConvertToStartValue_m18C3236122A0CA21CCC67F1E04178CA298FDD21A,
	IntPlugin_SetRelativeEndValue_m2998B97675D44F79988F48E53F764D589E40A76C,
	IntPlugin_SetChangeValue_m0546A7F4D7D2E054EEBE0CD36AAD672E92F74C43,
	IntPlugin_GetSpeedBasedDuration_mDD8E0299B88828750EA43F995838A8A14F052CB7,
	IntPlugin_EvaluateAndApply_m10614E91896AD00781AC48833B45456C1B782D41,
	IntPlugin__ctor_mDD5E0E9B685B85810B0E99FD3A724390EE99C913,
	QuaternionPlugin_Reset_mF9544485D1461B1A60EC89CEAD333B5ABE4B3D7B,
	QuaternionPlugin_SetFrom_m06BACD08FF14D0BD3229DD0520C80C70C80E7F1C,
	QuaternionPlugin_SetFrom_mDDD414437498EAD85880C442C0F717AF338DAB5A,
	QuaternionPlugin_ConvertToStartValue_m6D2E6DD4E47A7B6377A89DBBF830864048B739D5,
	QuaternionPlugin_SetRelativeEndValue_m5363B1A3F417A3EFA102252017BBABDEC0EB11D4,
	QuaternionPlugin_SetChangeValue_m40DCF9B299943301BBBAF466B57F8604C4475CD2,
	QuaternionPlugin_GetSpeedBasedDuration_m8BE69247A323044EABCF6D824250F3B9BEA9D9FC,
	QuaternionPlugin_EvaluateAndApply_m43B55A6171F6647EF62F262D07E51CEDB724AC02,
	QuaternionPlugin_GetEulerValForCalculations_mD6F45618229BFA08AC7DA4148EBBA31B909DD41D,
	QuaternionPlugin_FlipEulerAngles_mB5A2A2890526B45D639D67C0D82A7290EFF4808F,
	QuaternionPlugin__ctor_m300B7DE9FF8C992D2A5A7E069AFD7A2054D9960C,
	RectOffsetPlugin_Reset_m94F0DAB51730C6879FDF35EC1B1063950C16DD93,
	RectOffsetPlugin_SetFrom_m450A669373A57D51711CA2F4BDDA5F47F6A8A3A9,
	RectOffsetPlugin_SetFrom_mA2D46286DA1C8A8321B60B37178AF10B1DCAC2A8,
	RectOffsetPlugin_ConvertToStartValue_m0FD425737084A6587013AF55BAE8E4A4DF478025,
	RectOffsetPlugin_SetRelativeEndValue_mBD08440BECDFC30E1C2DF9019ED71925C23EAF35,
	RectOffsetPlugin_SetChangeValue_m56A91F6ABD143A79390BA34C4218B4A7F79D9D61,
	RectOffsetPlugin_GetSpeedBasedDuration_m0A00284A81996026211253E284C1FDDAE195E504,
	RectOffsetPlugin_EvaluateAndApply_mB4F799834E657E60C3EE2C9CD385847D2C5233D7,
	RectOffsetPlugin__ctor_m889C8F4862C868D55130CED884544F46AA4A4066,
	RectOffsetPlugin__cctor_mDEE0140B33EF6EA1F0DC496427FBD4F505427071,
	RectPlugin_Reset_m608F606EA7062DD500E16CF96D44D1A803AC1CC0,
	RectPlugin_SetFrom_m364E3B14FB559ECA15C19CF930EBB07D3A619D71,
	RectPlugin_SetFrom_mFDB4209FD9AB4F0EA55D7A23E2A036EAA55C2711,
	RectPlugin_ConvertToStartValue_m3584F27A3B1467B5184137FC28302E391EB1C828,
	RectPlugin_SetRelativeEndValue_mA2683C712701D3C1978D2648A07C0567BCF11518,
	RectPlugin_SetChangeValue_m51E32400665D3D389B296EABAF8A9F31BD654466,
	RectPlugin_GetSpeedBasedDuration_m6898F84A4B2C11EA25628AF6C81261F6FD965D42,
	RectPlugin_EvaluateAndApply_m8A244A6FFE476677C065055C9A1CE56F35A82894,
	RectPlugin__ctor_mC8C57164273B43CA3968BD9892AF8FAFF86431ED,
	UintPlugin_Reset_m70C443EF07DEF1E40EE7775F3273819C457718A3,
	UintPlugin_SetFrom_mD132C598273DD59BCE7D4F749BCB2CDBBDB8E4FC,
	UintPlugin_SetFrom_m86C9E8677D2055A6DB0B30905ADFF50DBAD4E905,
	UintPlugin_ConvertToStartValue_m3DEA6264D97583AC1E26E44D58496406ADDF5711,
	UintPlugin_SetRelativeEndValue_m3239D0B60543DE9E7A58EEAFCAEC224B81C95C91,
	UintPlugin_SetChangeValue_m5F1E259311413FE8EC8857E3FAAFB165AF7066E5,
	UintPlugin_GetSpeedBasedDuration_mAF144DF48A8B58A24DE487C81E932181FD8C329F,
	UintPlugin_EvaluateAndApply_m1F772D5D22A9704A5E6B9D5ED0C320F77DAF2776,
	UintPlugin__ctor_mB34F86253C685C80872915B2976E4546D1D8E897,
	Vector2Plugin_Reset_mC73661350E837DB36291164C3C728BA866B087AC,
	Vector2Plugin_SetFrom_mABACF87EB7C31E680DEB311AD713B3EFF80C94C3,
	Vector2Plugin_SetFrom_m298EB269C4EAD37DF4B7C54814FD90D954850672,
	Vector2Plugin_ConvertToStartValue_m77193485E58C5FA705208BAD82CEFD51A5CD0A55,
	Vector2Plugin_SetRelativeEndValue_m4669281FAC1BC5858C42AE3F74F570F8FDC8B511,
	Vector2Plugin_SetChangeValue_m8EF0B36B68AA1D37982E0A781448A02363BF0505,
	Vector2Plugin_GetSpeedBasedDuration_m8F03E4891B90E95E147600C8652258F403DBD9D8,
	Vector2Plugin_EvaluateAndApply_m5E9A974C9840D7FFB7AA8D2A75551317A187BD85,
	Vector2Plugin__ctor_m2023C1B763C66A6B5DADA4EA95AABC590996058C,
	Vector4Plugin_Reset_m77B2CBFC27C6F6F64FA0BB07FBBD72A7B7C9F360,
	Vector4Plugin_SetFrom_m8B96E0FCBA5D14E5E5D6D8C37EE36704D270A264,
	Vector4Plugin_SetFrom_mD3513AE4A93D72127D3B987CF14C9C861F961D7C,
	Vector4Plugin_ConvertToStartValue_m1D126F93BDF693BD05F2A4E3379CA87B979FD512,
	Vector4Plugin_SetRelativeEndValue_m69588D0E049A70F6B0ACABD34DCBDF8E19CC5652,
	Vector4Plugin_SetChangeValue_m69D1C0143C4D88BF84ABF66533A0E815D26F6FBE,
	Vector4Plugin_GetSpeedBasedDuration_mBA1AF59D5B2CB2A44BFED869AAAD474902E7122C,
	Vector4Plugin_EvaluateAndApply_mF75883427661BC86C345394F7415F8177E432611,
	Vector4Plugin__ctor_mF9E2DC11518FE5B03FE1BE2B6C930318307C2002,
	StringPlugin_SetFrom_mA6E09A5AB048B4210846D451F5D83821518CA7A3,
	StringPlugin_SetFrom_m7F541B13D8C91B1ED197C70AD49AA94A480E6FD5,
	StringPlugin_Reset_mCE185934F14CBC66803FFE6326EF09B5D8B3C0D8,
	StringPlugin_ConvertToStartValue_m34D7B1A6492C9FBCA91B13E86865E1C450E231A6,
	StringPlugin_SetRelativeEndValue_m281AAEA3815035C5354255DDEA8AE9AEBAF7B81E,
	StringPlugin_SetChangeValue_m154A8DEFEC01D8C683858C11602A2CB62123841B,
	StringPlugin_GetSpeedBasedDuration_mA2F5245600035283DBF76137C655DC84268B0B4D,
	StringPlugin_EvaluateAndApply_m840D7E6F40F85B6AB5DBC825829141A7B41D42F3,
	StringPlugin_Append_m393FFA9AFB5B28A407DEE6BEA599F096A2AD4BD1,
	StringPlugin_ScrambledCharsToUse_mB94A339E51635D7F97F27274382BEF24D40A85FB,
	StringPlugin__ctor_m431ECBCEC5363E152AA20286544F3444FC0BD70A,
	StringPlugin__cctor_m43D24AE51E83690FDDDF7DA16CF9BCD78E38672C,
	StringPluginExtensions__cctor_m0EF08249B07EA4E0C6FADEC570B9B33E308FEDC7,
	StringPluginExtensions_ScrambleChars_mEF2DC4717926EAEECA971FD887352673AC8683F2,
	StringPluginExtensions_AppendScrambledChars_mB1EC8925920C497377D1B1E8958C0B13BF88AF3F,
	FloatPlugin_Reset_mDCE52CE11F99836C0653864F7F58BD29B65439C4,
	FloatPlugin_SetFrom_m232CA49201386D4A445C495174456277698E3356,
	FloatPlugin_SetFrom_mCED12A8DE946A659A94D436A3E4DABCD3071B679,
	FloatPlugin_ConvertToStartValue_m0158D70929F3E0AAD5ACB0085AC8E55729019B6E,
	FloatPlugin_SetRelativeEndValue_m62E86FC1881D233AF898B585417FCC59AB8FC9E4,
	FloatPlugin_SetChangeValue_mBDCF4EFF8AC92B8BF2CBFBC4A9B88474C976DE50,
	FloatPlugin_GetSpeedBasedDuration_m069F5CAC863F1D352534798BA23B9CD53ADA76D0,
	FloatPlugin_EvaluateAndApply_m9266A96307CA39D509331D9253E5114195B7F869,
	FloatPlugin__ctor_mDDE3C38DE5AF13E77CCAFC0F11823A547D4723C7,
	Vector3Plugin_Reset_m3917A46D223807D40CA67E69C3F45FA2E00AA338,
	Vector3Plugin_SetFrom_m675F63D9FD954A1B29876A6E2C5E445476D54046,
	Vector3Plugin_SetFrom_mB473F1F765B584CCEB7F120C02E59C03D255DB50,
	Vector3Plugin_ConvertToStartValue_m42E52ED0EAAE4671EA20E8261F495A1657BBFBC8,
	Vector3Plugin_SetRelativeEndValue_m2C1B8ECD82B9174C83EB7E1E4FBE121F54209249,
	Vector3Plugin_SetChangeValue_m08F27B7FB6DE60F46C78163F088FA5E41DCFA98C,
	Vector3Plugin_GetSpeedBasedDuration_m415AFD7A8572E5DC5A8BF1601705B12967BC5317,
	Vector3Plugin_EvaluateAndApply_m3E6600D619A76F183A9B4E67B2E1310F49D91A79,
	Vector3Plugin__ctor_m83B9306A6E1F7F6A4ED2F534D0614235705C8AEC,
	NULL,
	PathOptions_Reset_mCF088F48CAA5F7D294ACE99D8D12CD07F4BEA060,
	QuaternionOptions_Reset_mFC1BCF4AC4D766AFE3BF6E93A16C5CB2146C2B9E,
	UintOptions_Reset_m3A17AAA2C83433C4B0246CFFDB26210823E19EFC,
	Vector3ArrayOptions_Reset_m5FBD5DB30FA9B5B3ECFF1AB327B53C95179E9589,
	NoOptions_Reset_m4C770E8106D2D141A2C9E1347A9EDF05CD4F0B9E,
	ColorOptions_Reset_m61D38CFBD63AA3DB49D4ABAC7460191E9EA425A4,
	FloatOptions_Reset_m7796B4F4CD7ACD23C0D11F4191A6DEC735E515DE,
	RectOptions_Reset_m06D669A680AF4A4BE9C113D5748F2727C9AF0300,
	StringOptions_Reset_mC44E99B51F61681EE22B9D7FB9D0F4698A568397,
	VectorOptions_Reset_mD2EB7C2BEE4AD4B30E6552CD5CF925B06B1EDEFE,
	SpecialPluginsUtils_SetLookAt_m032BE1B008809A24C0B4BD53A9689F7DD70F7FCF,
	SpecialPluginsUtils_SetPunch_m65C9920310ABC6FC6C483A6CEC088F3A360EE3CA,
	SpecialPluginsUtils_SetShake_m6F23CBF51E66BEAB3791161223E67374E976FF20,
	SpecialPluginsUtils_SetCameraShakePosition_m49B0FC03FA80CF51647589CCCB44BA1487B037A3,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PluginsManager_PurgeAll_m9EDAE828FEBAFA93291F199449C5AE6648FB949D,
	CubicBezierDecoder_get_minInputWaypoints_m7B83624FDC0792C7D99C66E4293B78C3CAD551DF,
	CubicBezierDecoder_FinalizePath_mE98400AC5E2006EC8BF639774C71A37C3E47E03C,
	CubicBezierDecoder_GetPoint_m1FA6505950448A4973E190441F2762203F2C0C71,
	CubicBezierDecoder_SetTimeToLengthTables_m8DE088CFF73E9F1E1A90A428D307B856FC92094C,
	CubicBezierDecoder_SetWaypointsLengths_mC384E54972E37D9157D8799E6E502BB0093CBC40,
	CubicBezierDecoder__ctor_mEF0D332E62BCB16F6DA71192746F7A43A9B3B461,
	CubicBezierDecoder__cctor_mF060F45215DCDEF59F446852B0B71F2E578B015D,
	ControlPoint__ctor_m61705A2A5C9615DC8E052515CB9B1F62A4600668,
	ControlPoint_op_Addition_m273B684A11735A299C1886F36F41C7C6CB27E49D,
	ControlPoint_ToString_m160B7B0BCC744DE7EF312A520CE814561A24C236,
	NULL,
	NULL,
	NULL,
	ABSPathDecoder__ctor_m9000360B01DBC18CF8310F47FFEC61E0FC0E4C96,
	CatmullRomDecoder_get_minInputWaypoints_m153ED0D4FC6DD92AE23C25BAD56C028CD94EFAA0,
	CatmullRomDecoder_FinalizePath_m9BF4FC78055D6A3E593E05BD4B05A496215A4D4E,
	CatmullRomDecoder_GetPoint_mCDAE4F84C87110712EA863C289A715BC1E232835,
	CatmullRomDecoder_SetTimeToLengthTables_m0E06C670A5FBDCA57207AB97B869ED8191D3ABED,
	CatmullRomDecoder_SetWaypointsLengths_mFB5DE54C8D41124A153BA13614F79F49BEC3ACC1,
	CatmullRomDecoder__ctor_m40C3B62E2B6753C8C604B192AE38CB0E00751B63,
	CatmullRomDecoder__cctor_m171A41BE4CB68DC3602E24837E2F5E067FB150D0,
	LinearDecoder_get_minInputWaypoints_m281051707CDDE5604573AD93F1080AE15B74B507,
	LinearDecoder_FinalizePath_m8A38C47B480F0AFC98FDC223622982AED28DAF65,
	LinearDecoder_GetPoint_m923A45C4AECF4B832BACDFE7D338611E3014172E,
	LinearDecoder_SetTimeToLengthTables_mB336D6CE0306247E6C80F9B472E86F744520550B,
	LinearDecoder_SetWaypointsLengths_mCBF22C43656162E062D89990E7E091A40A9E3417,
	LinearDecoder__ctor_mD23879EA491AC8A5105D0FC69F6D16BDA0998CA5,
	Path_get_minInputWaypoints_m95720C5307FB3D3A9F6F750389FE589302340693,
	Path__ctor_mE17CD95D405E8FF0440A6631D97C8876074B4824,
	Path__ctor_mE316C2800B03412006B86883F27ECC5CC08CA3AB,
	Path_FinalizePath_m2D9A9AE99327DBCDF8EDBAE55E149ED1F4BD3BB7,
	Path_GetPoint_m6D04BA28C0F375D3030DD2714E2295A2A656AFE1,
	Path_ConvertToConstantPathPerc_m946321C867B331E56BFBF358F57391804611B570,
	Path_GetWaypointIndexFromPerc_m3C41176271872EA1D2B5AE1C18EFAAE729CB2C00,
	Path_GetDrawPoints_mB91D933EBB4F22E74F19B72611C147089E0D9157,
	Path_RefreshNonLinearDrawWps_m80E683DA112432AAE39D0E8C3F2C8B41CBB60285,
	Path_Destroy_mB5139AE354F434F76149B4672ACE7B835FBE029C,
	Path_CloneIncremental_m02F0B70C4F51FBAEA81DB888E37FBA2E4962D428,
	Path_AssignWaypoints_mF34F707A39321C2912B6C7E4D23DAEEEEF61D2F8,
	Path_AssignDecoder_mD3A97C75986B01E822BF9726461B65DE77BC0B98,
	Path_Draw_mAE9CA81D5ED87ECC9C1213EFCA01639CF0AE2B32,
	Path_Draw_m1D276328F6B71518A310A8D1CCB4B29B5455E33C,
	Path_ConvertToDrawPoint_mA82DE42189D5973E0C7D917C099490B9A2A796E6,
	PureQuaternionPlugin_Plug_m9E68B44A606149BDC72B0A34B213F4D69D69B057,
	PureQuaternionPlugin_Reset_mB5AFC237DF652F5063FD9AB3DF61022E67A0CC59,
	PureQuaternionPlugin_SetFrom_mFF76A1E62A2E98213522B7CDF98943A46A3923B6,
	PureQuaternionPlugin_SetFrom_m36CC738145B37615D47FFB8C9A5977D5DBA47691,
	PureQuaternionPlugin_ConvertToStartValue_mA9949A5D3656481E2BDF410B5EE57F9BC14C9025,
	PureQuaternionPlugin_SetRelativeEndValue_m0395FF9730B5E046CCABEFB12C33926176F3F04F,
	PureQuaternionPlugin_SetChangeValue_m51824249B9B13071AB74B6EB35171469AF86C448,
	PureQuaternionPlugin_GetSpeedBasedDuration_mD2607B3BA5EEC0012E01D0A2E7631DAECC980DEF,
	PureQuaternionPlugin_EvaluateAndApply_m93205FF7464FD7AA204CBC01BE7052DB75B479BA,
	PureQuaternionPlugin__ctor_mA8C3C2835915524E8267F4F6DA7A63DA6AE6A7EE,
	ABSSequentiable__ctor_m70B4D2A525C71C87049ED3177A821B264D682A7B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Debugger_get_logPriority_m7AC0D46AE0BA9AC3ED1ED50AD20734033D16D0AC,
	Debugger_Log_mED54C2BED376B5F8631F5B23490BE1608675E456,
	Debugger_LogWarning_mB528DCD3175EB2D670A62A6507A656F8DE76D06E,
	Debugger_LogError_mBAD7F720F13F12B84A27F564F83F00B66EEE6B9E,
	Debugger_LogSafeModeCapturedError_m0EE4B4F14B9243B4A9BD757853CAB77B5A888ED6,
	Debugger_LogReport_m2A1E8B46BC302E45B799171C5C51BFA4DD07693C,
	Debugger_LogSafeModeReport_m2B8F77FCC5EAA0AD7F936AF62EFBC4FDE5D19E71,
	Debugger_LogInvalidTween_mEC44C60EC29E4EE401EFE473B01A209D79F3A83F,
	Debugger_LogNestedTween_m409C30CE26DFF284388E93FDD37738CF2116F128,
	Debugger_LogNullTween_m9C6A433A68F2C3DFCDFF127434135F8BBC116740,
	Debugger_LogNonPathTween_m49C992074A1EA55D708A26531D3FE7078A66405E,
	Debugger_LogMissingMaterialProperty_m49458D73DDB5BBBFB7AE84BA0A1C638A6A5B27C7,
	Debugger_LogMissingMaterialProperty_m8847B17D16E01FB04A460D0984CAD3FEEF9AB854,
	Debugger_LogRemoveActiveTweenError_m30A8AF0E0595F76045BF409584252064AE8460CD,
	Debugger_LogAddActiveTweenError_mAD5420AA037EB3CAAF612D228AF6055A0D80200E,
	Debugger_SetLogPriority_m14859DD9F9DD27E2D875BF50939F31D0F7043D6E,
	Debugger_ShouldLogSafeModeCapturedError_m5EE800D2A5FD98C95C119D9AF68035095A123C47,
	Debugger_GetDebugDataMessage_m08353947E413DB5642A38FE8A746F79F6F880C4B,
	Debugger_AddDebugDataToMessage_mDA43D632BCA0B5487160E204FBCA074DA97AC509,
	Sequence_LogAddToNullSequence_m9D28C74E58E7BFD59961AEAAA7C84E35C33F09E9,
	Sequence_LogAddToInactiveSequence_m91917D80E18F30B5AA21BE2ECD5B1716ABB802E1,
	Sequence_LogAddToLockedSequence_m0ACADB5C3D96FDF32DD765792D5ED9A9D11795A2,
	Sequence_LogAddNullTween_m0A4624D91F7056D1A511BE858284E2C57E6757B2,
	Sequence_LogAddInactiveTween_m14520D9DA312BAE265A62A4E8DAB68A7FE48AEF9,
	Sequence_LogAddAlreadySequencedTween_mEEF676C9EB96FDDBD4D590787DA4FA923B2EC87C,
	DOTweenComponent_Awake_m2B8731C5F756551F8CF374ABA9BBA5D97C36A956,
	DOTweenComponent_Start_m14BD516448C0D8B487AA199C84AFA5984EE38E71,
	DOTweenComponent_Update_m1EB628942CC4EA34F24E9AA2326532D8AD5B0541,
	DOTweenComponent_LateUpdate_mB523F2163A4033594262DB217EDC62C6806DBAF8,
	DOTweenComponent_FixedUpdate_m5D4B2412E608B1F490333A9A5680074A5C02A7C0,
	DOTweenComponent_OnDrawGizmos_m28A4D3B2A76DC3FE70A1BBAE4D5E993624526FCE,
	DOTweenComponent_OnDestroy_m813D82282FC9C21C3DF589E5A954FA11FAFEA3BA,
	DOTweenComponent_OnApplicationPause_m93370BF7B8AA301B841B0BBCC61E9579AD3CDDF7,
	DOTweenComponent_OnApplicationQuit_mCF7BB912F912C5B91A6D6A6141DE8E085D8C3E7F,
	DOTweenComponent_SetCapacity_m3B4936853427792607B2BC9A022300724C6275B8,
	DOTweenComponent_WaitForCompletion_m1F26E152C4B17EF6797B49AD8070C40B872D615C,
	DOTweenComponent_WaitForRewind_m7719D9F5E014FC7403F70045B4E732D40428CB27,
	DOTweenComponent_WaitForKill_m53130F016C0B24C5345C66972D287236F374AC37,
	DOTweenComponent_WaitForElapsedLoops_mE9D67BF81141C1D92F85ECD20FFC51E2E8777774,
	DOTweenComponent_WaitForPosition_m0F07976F5331F98F88289288ACE220D0F708FE84,
	DOTweenComponent_WaitForStart_m91F59DF680F874160B5ED20691215591B75A4B65,
	DOTweenComponent_Create_m2F098BE99694BEFEF39E22C3D8294ED04E9A11D1,
	DOTweenComponent_DestroyInstance_mA0743B89EF2340212A90DA6C777A021BB346723E,
	DOTweenComponent__ctor_mA7CF37E0C0B8644ED78CCB9DD9A0386349236501,
	U3CWaitForCompletionU3Ed__17__ctor_m9B9BD6B6B1C86B245E628C7BBD96C48A4FEC90A7,
	U3CWaitForCompletionU3Ed__17_System_IDisposable_Dispose_m6E7622FD584C46143CE48AE8D901B82D6B81E2C2,
	U3CWaitForCompletionU3Ed__17_MoveNext_m2F1960A98359AC8B9E035986BD45BD5405679C4B,
	U3CWaitForCompletionU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC05B77AA530F9675D5FDDCA1A4733BC27E3C2439,
	U3CWaitForCompletionU3Ed__17_System_Collections_IEnumerator_Reset_m7BFAF4BCB2EB8824994930872AA1F4C9F937F8D6,
	U3CWaitForCompletionU3Ed__17_System_Collections_IEnumerator_get_Current_m9BD50CD51DBAC1EF925608B2C7F90B8514730A93,
	U3CWaitForElapsedLoopsU3Ed__20__ctor_m0E883FEA0DB5A2F545307245677A55F01475573D,
	U3CWaitForElapsedLoopsU3Ed__20_System_IDisposable_Dispose_mB245A3DDFCDA84977DC07B90F241B2EDAECABF12,
	U3CWaitForElapsedLoopsU3Ed__20_MoveNext_m66D5E529747FA0233925AD4E0019ECE0A1566281,
	U3CWaitForElapsedLoopsU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3F1FFC50C883BF778D0BAB6CD66026A11C271745,
	U3CWaitForElapsedLoopsU3Ed__20_System_Collections_IEnumerator_Reset_m3AEF8C8FF47ED0146C773C81FF6E95551AC5656B,
	U3CWaitForElapsedLoopsU3Ed__20_System_Collections_IEnumerator_get_Current_m168026DFCA711B3CCBB9FE3A9DC9C5AF591E7644,
	U3CWaitForKillU3Ed__19__ctor_mAD5A32E6A99FF369B4E92459748D6DBE55DB3352,
	U3CWaitForKillU3Ed__19_System_IDisposable_Dispose_m09429E49D6D5A1F3A4ADD0AAFBD069F31B0A327E,
	U3CWaitForKillU3Ed__19_MoveNext_m73B95ED60A8D89F7128C3127B0E32E262C03D4CE,
	U3CWaitForKillU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1F3DB7C082A0CBAE4F421246790D84510B33F5BC,
	U3CWaitForKillU3Ed__19_System_Collections_IEnumerator_Reset_m6F137BD0A99B05D514028F733A890657CB13F3C5,
	U3CWaitForKillU3Ed__19_System_Collections_IEnumerator_get_Current_mC7DAE3183F86C69AF2D4D0E342672FE0D9C05063,
	U3CWaitForPositionU3Ed__21__ctor_m00B910EE847D915D805BE8C2D4B22C563F046EDE,
	U3CWaitForPositionU3Ed__21_System_IDisposable_Dispose_mAEEE1414E694730A874AF5B7F0165A1CB6BA2C01,
	U3CWaitForPositionU3Ed__21_MoveNext_m757E20ED1DF079EEABC7CE8C48234E22FA0301F9,
	U3CWaitForPositionU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2A0D03647872D4D91D71A0D5C6173C64177A0B9E,
	U3CWaitForPositionU3Ed__21_System_Collections_IEnumerator_Reset_mEA4BB9475FFEBB94C16F9A06B75CDC46B32A34B1,
	U3CWaitForPositionU3Ed__21_System_Collections_IEnumerator_get_Current_m81559CB25BBA4D58F7D3DDEB7BA928991066A120,
	U3CWaitForRewindU3Ed__18__ctor_mC655F5292F44006BDA6120321B57CD4707238199,
	U3CWaitForRewindU3Ed__18_System_IDisposable_Dispose_mE0758E2A6C4D0B18D5C4F0DB89311E29DF5BD801,
	U3CWaitForRewindU3Ed__18_MoveNext_m569E1D17475CE2D1DD9B6E4EA6AA8482D903F220,
	U3CWaitForRewindU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7E0F9DC591C57BA1AB0145C09C455E9B347401B3,
	U3CWaitForRewindU3Ed__18_System_Collections_IEnumerator_Reset_mE4C432CFFEFE442C513474BF671398BB10409C21,
	U3CWaitForRewindU3Ed__18_System_Collections_IEnumerator_get_Current_m6CE4043738217E1AD69C9B6B68BEFA4A78381893,
	U3CWaitForStartU3Ed__22__ctor_mB0D561103C778281B449CB6461946393D663BB85,
	U3CWaitForStartU3Ed__22_System_IDisposable_Dispose_m0DC72886B4B6493E87B5F923E7EBDFDF95C7C67A,
	U3CWaitForStartU3Ed__22_MoveNext_mAC382D1D241D54067037E5075ED30789C2EE45AD,
	U3CWaitForStartU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8C6F2B13B215B38084A5BD79B9A57D1D2E0746B7,
	U3CWaitForStartU3Ed__22_System_Collections_IEnumerator_Reset_m4DC2F72D4A6F3B962EE5CB28AFA1E454A5C2E11E,
	U3CWaitForStartU3Ed__22_System_Collections_IEnumerator_get_Current_m16EF6B19F0927A778093A6157BF2D6CEE5625804,
	DOTweenSettings__ctor_m887E5989C5E07DD06A56C7BB9651D513DBE8E04A,
	SafeModeOptions__ctor_m30119D0CFA2C471B7AA0F2AFBE5AE8130D4AADA7,
	ModulesSetup__ctor_mFC121E34A299D3C21E06DDCDA8392FF3EC4CDB28,
	NULL,
	NULL,
	NULL,
	DOTweenExternalCommand_add_SetOrientationOnPath_mBD8B37B1978CBE4534F9B8868C734F5C0A2B2804,
	DOTweenExternalCommand_remove_SetOrientationOnPath_m552309BE5B7623397429DDAC02D5C6FDB0C0FB44,
	DOTweenExternalCommand_Dispatch_SetOrientationOnPath_m66381EB3F8DF2CB2F8D0FF0462C40E2AEA21EE3C,
	SafeModeReport_get_totMissingTargetOrFieldErrors_mFC456B568815DFA27E1C10970DC3926B210BFFE5,
	SafeModeReport_set_totMissingTargetOrFieldErrors_m6D3CEE836FE7F2CFFA794FED0E551DDD0ADF7AA9,
	SafeModeReport_get_totCallbackErrors_m31FC475B782E0CCD82744DB4A5079F28D3973133,
	SafeModeReport_set_totCallbackErrors_m4215E716C771AE5BBDEE51AA0520815AF1690D98,
	SafeModeReport_get_totStartupErrors_m5ED6052FB179745BA7C225BC301811A253B15790,
	SafeModeReport_set_totStartupErrors_m293BB3C69C6A944F04276A9DFAB2EDCAE91C9082,
	SafeModeReport_get_totUnsetErrors_m136EA51EF49FF199E3939A17AD7575C0E9785840,
	SafeModeReport_set_totUnsetErrors_m9232EA974F7A04A1D816F7B49B268D9F2F3E837C,
	SafeModeReport_Add_mC993434DFE20DDF1E44389DACD34085F31E6ED20,
	SafeModeReport_GetTotErrors_mE0AC77EDDFA876A6332177C51DA53E4A94F5ED70,
	SequenceCallback__ctor_m0013E5DAA8ACE7366348DF78CED5E181DF620BF5,
	TweenLink__ctor_mBAF95830E1F98BEF3183462386B086ED0B4C6828,
	TweenManager__cctor_m32BF1B0E0166CC1C1832F3980654478B2C445F07,
	NULL,
	TweenManager_GetSequence_m64E8F2E01C9BDE2A4006C4265C84614F1DF92CB2,
	TweenManager_SetUpdateType_m4C1E97DD675C70ABA66E53D283EDABB6337E44B4,
	TweenManager_AddActiveTweenToSequence_m2D83B988481444A51AA9330D5AB9E63BF1FD3B18,
	TweenManager_DespawnAll_mBBA75EE14B48B5F3802ECE79F8C05D318DD40EDC,
	TweenManager_Despawn_mF56A8F1D2B480230F68F0149F6122D0BBAA129FC,
	TweenManager_PurgeAll_m73DD4E5679DCEFA742B176F67493E245603A685D,
	TweenManager_PurgePools_m00794B3E7D39DF9078C9524DA48A8E9C500F9C05,
	TweenManager_AddTweenLink_mF49C6E8A327858C52795CF0F2FA665C1A82A840C,
	TweenManager_RemoveTweenLink_mA00A9ACA1D2E241FF5E4C63D88686194CB0D092E,
	TweenManager_ResetCapacities_m6B74BE623F2ADFCDD966F76EDBF2B2D5DD7898B8,
	TweenManager_SetCapacities_m1A1D0CA467C77D297C7CD9AB105DB5609112175F,
	TweenManager_Validate_m78792D55B7C684959FBF548DD7D4FE4473140164,
	TweenManager_Update_mB27C9AE8D928418163CE32FD6EF1C82F9A9F63C7,
	TweenManager_Update_mC74314D555CD02AD5E70C57AEDE614E5B7FAA928,
	TweenManager_FilteredOperation_mEF0C87B677542616CB4324153651CA06F548BE39,
	TweenManager_Complete_mCE26F442C189C358288018529BD88F0F1D99C390,
	TweenManager_Flip_mAE0BD66D990CE593A474271E4D48B994451AF5B7,
	TweenManager_ForceInit_m490AB9D6D7F427844FBC663194066EF146853737,
	TweenManager_Goto_m04F0FF9EC0394154CCFE8552D797374147D31CDC,
	TweenManager_Pause_mF53E744566930DFD9A6642B63E872D12F23B4269,
	TweenManager_Play_m16548C8C4203A88870C6E54E44F03CDA11763DA2,
	TweenManager_PlayBackwards_mCC2A5578B578BEE7005204D8D7A546957BE605E9,
	TweenManager_PlayForward_mD5568593F49E0036FA185E22CC1B24A22B35DEF2,
	TweenManager_Restart_mB3E06722F56E0371559BC8DA43D5D09D8D391A7C,
	TweenManager_Rewind_mF610181130C4218E0F462966D54EE193E8171A7F,
	TweenManager_SmoothRewind_m3C5DE57B8A3DDE3EBBA223EC0AA2CDFD9E567E82,
	TweenManager_TogglePause_m9352B05E74FE75A5CC99DF8E0AB7B03D020B9C49,
	TweenManager_TotalPooledTweens_m945DAF1DACA5D98A100FC3A9DDF46FA131E0E843,
	TweenManager_TotalPlayingTweens_mCF20DFCFC526CE2C1D4F689BAA49FBC36C013A44,
	TweenManager_TotalTweensById_mB2121BF73CEBFC4800F5D82484E6437A644BD1AB,
	TweenManager_GetActiveTweens_m4C669787FB938BA2B578095A31AEDAD387A92607,
	TweenManager_GetTweensById_mA095AA370EEF92919F7F7946250702BC38802B69,
	TweenManager_DoGetTweensById_m60609672BA165F4B190DA84F6B0958A04525FBFC,
	TweenManager_GetTweensByTarget_mB7E24BC65ECF8FAC509628A3CA842745D7B77BCD,
	TweenManager_MarkForKilling_m3D50AC8F9D12E17BF9D7E0CE6F6235C0A558403E,
	TweenManager_EvaluateTweenLink_mE5358C53588B9201A77D284C6EA08C13090F5398,
	TweenManager_AddActiveTween_mDE4BACC61DA83AB6BD50B1AA57CB16F40272CD05,
	TweenManager_ReorganizeActiveTweens_mB40CEDBB24B90540B86274D77D7795EAE1330EA3,
	TweenManager_DespawnActiveTweens_m61D3A0AA83721EB57E1E3DF0C9C75F9F71632AC3,
	TweenManager_RemoveActiveTween_m666B3E84DC9E1CD8F8E8F79A44EBFE16CA03AE49,
	TweenManager_ClearTweenArray_mA86AEF4A53B4DCEF3319D39B9769AECE4E0093E8,
	TweenManager_IncreaseCapacities_m5316415F2F95212320B0745CAF123B139A8CDA62,
	TweenManager_ManageOnRewindCallbackWhenAlreadyRewinded_m1054CA72AB6AC784DD9A50AE4F89EE2D4DC38480,
	DOTweenUtils_Vector3FromAngle_m8CEB04AC772D36B9F06F5C3A2B6EED469E300E63,
	DOTweenUtils_Angle2D_m7261977166A00A58FAC3A27EB2F36CAA3689996B,
	DOTweenUtils_RotateAroundPivot_m12A75F16D4ADF0F1BC9760092FC8C6A0A6982896,
	DOTweenUtils_GetPointOnCircle_m****************************************,
	DOTweenUtils_Vector3AreApproximatelyEqual_m10059FD0B451D4D005575F6A92A304DD2514A0A3,
	DOTweenUtils_GetLooseScriptType_m0E6B311478C18FB4A2DC3268573C05656627C95E,
	DOTweenUtils__cctor_m4A00E4531C84F391F5F8BBA5EDC7B3C4CAA52A1D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Bounce_EaseIn_m1253ADF94B39EF139C56EEE7AD5EC2F5E0C29769,
	Bounce_EaseOut_mEF5499252352724ADC55887B541B5053DACFC27F,
	Bounce_EaseInOut_m6D386BE3A485A50DE77E480D40008D01ADDE79F0,
	EaseManager_Evaluate_mD683DD74534996BFA45C075DCC2927E089C4E26E,
	EaseManager_Evaluate_m26A532BC322B246C5CE9D45ABC16384F58F8389A,
	EaseManager_ToEaseFunction_m2A02F664C835CDD47ED6725D6D2D40980E8F090F,
	EaseManager_IsFlashEase_mB7D47A96B8C663F7FA56AD8D10B9586C76204FF6,
	U3CU3Ec__cctor_m0EDCB89FD689F09CA60721AC58164F4A6F8D5829,
	U3CU3Ec__ctor_m493D21EFED4AA24CF19C03307F647E3509C676D7,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_0_mA591CD137EB5B6D02B6971FBC63B661AC250EC75,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_1_m57D1C907CE498E49D3BB97C93F17DF219D768D39,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_2_m21BE7DFB1D3B3FB48430C5F8808A2BB23B1D2B4D,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_3_mB965A8E5686FF467D23FE3E5EF3564ABA36C56D2,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_4_m638274DD909D0E6DF66C0A0D7C0F40E8312029CB,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_5_mFF2D7DC4AA25EE6E7044305C57FA8816631D80CF,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_6_m4A06F8BB2D4B4F436E14D46CA73C41D4560A4AC6,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_7_m0FA0259C9E4CF4FB47077B12BA6F6D9ECC6107FC,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_8_m5E29AABC5E842BD7E4DBADFC4AE227D489BBC4D1,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_9_m36315113AE76BC13E5D5744624A50F7C70D71A7C,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_10_m67931A9DE04B64D996D4D75887E68EB788B0A06F,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_11_m20CABBCE05D3787A592230CD7297001E7F35A596,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_12_m508362096DB00AE0E73BF7579CB6BC45DF2D9687,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_13_mC0AE923467BF23F592DC3A8B0E2CF016C5629789,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_14_mBA8044322F5D001ABE2EE659D719182A4B466305,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_15_m143B41512ED2E1512E2D7B2C7697A7583AAC79F0,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_16_mC78B573955B2410B75554D0DC28AC93AB94C10C6,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_17_m20C33BC6179A293D6EA71D95849E5DAD91A166DB,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_18_m03DC93147BECBFF659B4A535F5B570DAE38C9D51,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_19_mB7E0AB1B73C108145A8F25D53E8BFDDF9C7EED1F,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_20_m6CF35FA9B64182DABCE68C45B7F4D1EC12625F1A,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_21_mEFB97FC4EF2F2013C16CEB5C595C4AE50A952D17,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_22_m3AB7A98254DE9433AAB67A5E5514EAD314473E53,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_23_m0B6D784AC89C1E97C747D139D1B1ED1ED17D9FBE,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_24_mE1A867F687DDC6784BB3F07B6155B944E372B676,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_25_m4311927391BC4689C23C4879F2A3A480DBCFA83A,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_26_m0D8C4E72A899A604290EC23AFB7FEBB610D1DFC9,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_27_m0B5C0A9408EF2C04CB8485D882A5CFC1E4AE6405,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_28_mDBE83F3AD26C7FCA54203A989CE269D45059337C,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_29_mFBCD6FCE05841BC79641430A90A2977771EE7EBD,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_30_m380700CA1A01498516F89AE80941E0CCBEAD913D,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_31_m66D73E4257B74EED5B9A2D69C031F1B90130E510,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_32_m204E047E92BBE9BEB4FDEACB8663D533CA934CD7,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_33_m56E998A9C20DBF9308DDF064FCCA0A0E99B07A0D,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_34_m2998C252D5D9DB9E120CFF0A224648C9E356A1A6,
	U3CU3Ec_U3CToEaseFunctionU3Eb__4_35_m83FA33A1ECD1CC5CFB64C678728D5E1D6862C382,
	EaseCurve__ctor_m29BD0E232922C8CE2E4AC877F79EF6096C34EDFB,
	EaseCurve_Evaluate_m036A88A768920A29FA6C7EA6E78646F679C9DFEE,
	Flash_Ease_mA28C135D4B118A9A4469FEFFDEC3329226E6A096,
	Flash_EaseIn_m7A2DCE17466DCF086004A0147F534851240EADFA,
	Flash_EaseOut_m306F24AC2A2EF38682E1C0AB8834FAC036658955,
	Flash_EaseInOut_m71C9C9CD9B50F446B635F0ABB97D1AFA7F52F4A9,
	Flash_WeightedEase_mF9EC6A43BAEE75E4D2E93FCA21E099B0FA8CCA35,
};
extern void Color2__ctor_m0D4E285146629A09FA473F8FDE3D4A4F3889D58E_AdjustorThunk (void);
extern void CircleOptions_Reset_m6AFD52048392CA538A62BD5A05F3CC96DCF8ECBF_AdjustorThunk (void);
extern void CircleOptions_Initialize_m****************************************_AdjustorThunk (void);
extern void PathOptions_Reset_mCF088F48CAA5F7D294ACE99D8D12CD07F4BEA060_AdjustorThunk (void);
extern void QuaternionOptions_Reset_mFC1BCF4AC4D766AFE3BF6E93A16C5CB2146C2B9E_AdjustorThunk (void);
extern void UintOptions_Reset_m3A17AAA2C83433C4B0246CFFDB26210823E19EFC_AdjustorThunk (void);
extern void Vector3ArrayOptions_Reset_m5FBD5DB30FA9B5B3ECFF1AB327B53C95179E9589_AdjustorThunk (void);
extern void NoOptions_Reset_m4C770E8106D2D141A2C9E1347A9EDF05CD4F0B9E_AdjustorThunk (void);
extern void ColorOptions_Reset_m61D38CFBD63AA3DB49D4ABAC7460191E9EA425A4_AdjustorThunk (void);
extern void FloatOptions_Reset_m7796B4F4CD7ACD23C0D11F4191A6DEC735E515DE_AdjustorThunk (void);
extern void RectOptions_Reset_m06D669A680AF4A4BE9C113D5748F2727C9AF0300_AdjustorThunk (void);
extern void StringOptions_Reset_mC44E99B51F61681EE22B9D7FB9D0F4698A568397_AdjustorThunk (void);
extern void VectorOptions_Reset_mD2EB7C2BEE4AD4B30E6552CD5CF925B06B1EDEFE_AdjustorThunk (void);
extern void ControlPoint__ctor_m61705A2A5C9615DC8E052515CB9B1F62A4600668_AdjustorThunk (void);
extern void ControlPoint_ToString_m160B7B0BCC744DE7EF312A520CE814561A24C236_AdjustorThunk (void);
extern void SafeModeReport_get_totMissingTargetOrFieldErrors_mFC456B568815DFA27E1C10970DC3926B210BFFE5_AdjustorThunk (void);
extern void SafeModeReport_set_totMissingTargetOrFieldErrors_m6D3CEE836FE7F2CFFA794FED0E551DDD0ADF7AA9_AdjustorThunk (void);
extern void SafeModeReport_get_totCallbackErrors_m31FC475B782E0CCD82744DB4A5079F28D3973133_AdjustorThunk (void);
extern void SafeModeReport_set_totCallbackErrors_m4215E716C771AE5BBDEE51AA0520815AF1690D98_AdjustorThunk (void);
extern void SafeModeReport_get_totStartupErrors_m5ED6052FB179745BA7C225BC301811A253B15790_AdjustorThunk (void);
extern void SafeModeReport_set_totStartupErrors_m293BB3C69C6A944F04276A9DFAB2EDCAE91C9082_AdjustorThunk (void);
extern void SafeModeReport_get_totUnsetErrors_m136EA51EF49FF199E3939A17AD7575C0E9785840_AdjustorThunk (void);
extern void SafeModeReport_set_totUnsetErrors_m9232EA974F7A04A1D816F7B49B268D9F2F3E837C_AdjustorThunk (void);
extern void SafeModeReport_Add_mC993434DFE20DDF1E44389DACD34085F31E6ED20_AdjustorThunk (void);
extern void SafeModeReport_GetTotErrors_mE0AC77EDDFA876A6332177C51DA53E4A94F5ED70_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[25] = 
{
	{ 0x06000001, Color2__ctor_m0D4E285146629A09FA473F8FDE3D4A4F3889D58E_AdjustorThunk },
	{ 0x060002B7, CircleOptions_Reset_m6AFD52048392CA538A62BD5A05F3CC96DCF8ECBF_AdjustorThunk },
	{ 0x060002B8, CircleOptions_Initialize_m****************************************_AdjustorThunk },
	{ 0x0600036B, PathOptions_Reset_mCF088F48CAA5F7D294ACE99D8D12CD07F4BEA060_AdjustorThunk },
	{ 0x0600036C, QuaternionOptions_Reset_mFC1BCF4AC4D766AFE3BF6E93A16C5CB2146C2B9E_AdjustorThunk },
	{ 0x0600036D, UintOptions_Reset_m3A17AAA2C83433C4B0246CFFDB26210823E19EFC_AdjustorThunk },
	{ 0x0600036E, Vector3ArrayOptions_Reset_m5FBD5DB30FA9B5B3ECFF1AB327B53C95179E9589_AdjustorThunk },
	{ 0x0600036F, NoOptions_Reset_m4C770E8106D2D141A2C9E1347A9EDF05CD4F0B9E_AdjustorThunk },
	{ 0x06000370, ColorOptions_Reset_m61D38CFBD63AA3DB49D4ABAC7460191E9EA425A4_AdjustorThunk },
	{ 0x06000371, FloatOptions_Reset_m7796B4F4CD7ACD23C0D11F4191A6DEC735E515DE_AdjustorThunk },
	{ 0x06000372, RectOptions_Reset_m06D669A680AF4A4BE9C113D5748F2727C9AF0300_AdjustorThunk },
	{ 0x06000373, StringOptions_Reset_mC44E99B51F61681EE22B9D7FB9D0F4698A568397_AdjustorThunk },
	{ 0x06000374, VectorOptions_Reset_mD2EB7C2BEE4AD4B30E6552CD5CF925B06B1EDEFE_AdjustorThunk },
	{ 0x06000390, ControlPoint__ctor_m61705A2A5C9615DC8E052515CB9B1F62A4600668_AdjustorThunk },
	{ 0x06000392, ControlPoint_ToString_m160B7B0BCC744DE7EF312A520CE814561A24C236_AdjustorThunk },
	{ 0x06000420, SafeModeReport_get_totMissingTargetOrFieldErrors_mFC456B568815DFA27E1C10970DC3926B210BFFE5_AdjustorThunk },
	{ 0x06000421, SafeModeReport_set_totMissingTargetOrFieldErrors_m6D3CEE836FE7F2CFFA794FED0E551DDD0ADF7AA9_AdjustorThunk },
	{ 0x06000422, SafeModeReport_get_totCallbackErrors_m31FC475B782E0CCD82744DB4A5079F28D3973133_AdjustorThunk },
	{ 0x06000423, SafeModeReport_set_totCallbackErrors_m4215E716C771AE5BBDEE51AA0520815AF1690D98_AdjustorThunk },
	{ 0x06000424, SafeModeReport_get_totStartupErrors_m5ED6052FB179745BA7C225BC301811A253B15790_AdjustorThunk },
	{ 0x06000425, SafeModeReport_set_totStartupErrors_m293BB3C69C6A944F04276A9DFAB2EDCAE91C9082_AdjustorThunk },
	{ 0x06000426, SafeModeReport_get_totUnsetErrors_m136EA51EF49FF199E3939A17AD7575C0E9785840_AdjustorThunk },
	{ 0x06000427, SafeModeReport_set_totUnsetErrors_m9232EA974F7A04A1D816F7B49B268D9F2F3E837C_AdjustorThunk },
	{ 0x06000428, SafeModeReport_Add_mC993434DFE20DDF1E44389DACD34085F31E6ED20_AdjustorThunk },
	{ 0x06000429, SafeModeReport_GetTotErrors_mE0AC77EDDFA876A6332177C51DA53E4A94F5ED70_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1188] = 
{
	2213,
	7996,
	7996,
	7997,
	2704,
	6037,
	2088,
	4809,
	0,
	0,
	0,
	0,
	2704,
	822,
	188,
	4352,
	6692,
	6679,
	6456,
	9108,
	8969,
	9083,
	8961,
	9083,
	8961,
	7463,
	9162,
	7017,
	8340,
	8961,
	8330,
	9162,
	9108,
	8396,
	7061,
	7047,
	7051,
	7062,
	7053,
	7063,
	7057,
	7064,
	7066,
	7067,
	7066,
	7045,
	7058,
	7057,
	0,
	6663,
	7061,
	7079,
	6400,
	6235,
	6266,
	6217,
	7056,
	7046,
	9116,
	8761,
	8657,
	8070,
	9108,
	8669,
	8073,
	8669,
	9108,
	8669,
	8080,
	7425,
	8657,
	8058,
	8070,
	7421,
	9108,
	8669,
	9108,
	8669,
	8073,
	9108,
	8669,
	8073,
	9108,
	8669,
	8073,
	8657,
	7412,
	6903,
	8657,
	8070,
	9108,
	8669,
	9108,
	8669,
	7911,
	9108,
	9108,
	9108,
	9108,
	8070,
	8761,
	8761,
	7490,
	7490,
	9162,
	0,
	6037,
	9162,
	6037,
	5968,
	4856,
	7101,
	7015,
	7107,
	7110,
	7010,
	7119,
	6685,
	6412,
	7120,
	7133,
	6691,
	6418,
	7134,
	7529,
	6037,
	5968,
	4856,
	6037,
	6037,
	5887,
	4785,
	6037,
	6037,
	6026,
	4907,
	6037,
	6037,
	6028,
	4909,
	6037,
	6037,
	5820,
	4718,
	6037,
	8128,
	8133,
	8133,
	6037,
	6037,
	822,
	0,
	8973,
	8358,
	0,
	8973,
	8973,
	7750,
	7750,
	7232,
	8358,
	7753,
	0,
	0,
	8973,
	8973,
	7720,
	8358,
	8973,
	8973,
	7726,
	8761,
	8761,
	8761,
	8144,
	8151,
	8761,
	8669,
	8862,
	8862,
	8217,
	8217,
	8217,
	8862,
	8564,
	8564,
	8564,
	8564,
	8564,
	8564,
	8669,
	8276,
	8144,
	8862,
	6037,
	8149,
	7508,
	8151,
	8151,
	7508,
	4353,
	6037,
	5818,
	5818,
	171,
	8973,
	8564,
	6364,
	6298,
	8973,
	8564,
	7516,
	7492,
	7516,
	7516,
	7516,
	7516,
	7511,
	7511,
	6315,
	6316,
	6315,
	6316,
	7492,
	7516,
	7516,
	7028,
	7492,
	7026,
	7025,
	7516,
	7077,
	7076,
	7077,
	7076,
	7522,
	7088,
	7522,
	7088,
	7097,
	7096,
	7079,
	7516,
	7093,
	7078,
	7078,
	7078,
	7093,
	7078,
	7078,
	7078,
	7094,
	7510,
	7094,
	7510,
	7526,
	7516,
	7516,
	7516,
	7516,
	6673,
	6673,
	6405,
	6406,
	6674,
	6674,
	6269,
	6271,
	6315,
	6316,
	6315,
	6316,
	6406,
	6406,
	6311,
	6311,
	7060,
	7060,
	7516,
	7492,
	7492,
	7026,
	7025,
	7093,
	7093,
	7094,
	7094,
	6674,
	7526,
	8070,
	8070,
	8070,
	8070,
	8669,
	8669,
	7425,
	7425,
	8669,
	8669,
	8669,
	8669,
	8669,
	8669,
	8669,
	8669,
	8070,
	8070,
	8070,
	8070,
	8669,
	8669,
	8669,
	8669,
	6037,
	5968,
	4856,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	5820,
	4718,
	6037,
	5968,
	4856,
	6037,
	5968,
	4856,
	6037,
	5821,
	4719,
	6037,
	5820,
	4718,
	6037,
	5820,
	4718,
	6037,
	5820,
	4718,
	6037,
	5820,
	4718,
	6037,
	5820,
	4718,
	6037,
	5820,
	4718,
	6037,
	5820,
	4718,
	6037,
	5968,
	4856,
	6037,
	5968,
	4856,
	6037,
	6026,
	4907,
	6037,
	6026,
	4907,
	6037,
	6026,
	4907,
	6037,
	6026,
	4907,
	6037,
	6030,
	4911,
	6037,
	6030,
	4911,
	6037,
	5968,
	4856,
	6037,
	6026,
	4907,
	6037,
	5968,
	4856,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	5968,
	4856,
	6037,
	5931,
	4825,
	6037,
	5931,
	4825,
	6037,
	5931,
	4825,
	6037,
	5931,
	4825,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	5968,
	4856,
	6037,
	5931,
	4825,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	5968,
	4856,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6028,
	4909,
	6037,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6028,
	4909,
	6037,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	5968,
	4856,
	6037,
	5820,
	4718,
	6037,
	5820,
	4718,
	6037,
	5938,
	4830,
	6037,
	5820,
	4718,
	6037,
	5820,
	4718,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	5931,
	4825,
	6037,
	5931,
	4825,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	5938,
	4830,
	6037,
	6028,
	4909,
	6037,
	6028,
	4909,
	6037,
	5913,
	4241,
	4254,
	4254,
	4247,
	4254,
	2067,
	1149,
	4254,
	4254,
	4241,
	4241,
	2069,
	4254,
	4254,
	4254,
	4254,
	4254,
	4254,
	4254,
	4254,
	4258,
	4241,
	4241,
	9162,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8149,
	8149,
	8149,
	7515,
	8151,
	8151,
	8149,
	8149,
	8149,
	7515,
	7340,
	0,
	0,
	0,
	0,
	7075,
	7075,
	7075,
	0,
	0,
	0,
	0,
	0,
	0,
	8140,
	8140,
	7497,
	8140,
	7497,
	8140,
	7497,
	8140,
	8140,
	8140,
	7023,
	8140,
	7497,
	7075,
	7498,
	7022,
	7091,
	7523,
	7043,
	7504,
	7074,
	7513,
	6264,
	7712,
	5818,
	4715,
	5818,
	4715,
	5968,
	4856,
	5818,
	5818,
	4715,
	5968,
	4856,
	6037,
	0,
	4353,
	0,
	0,
	6816,
	7917,
	0,
	6037,
	6037,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6037,
	2790,
	4809,
	2692,
	974,
	9116,
	2160,
	4809,
	4809,
	1227,
	9,
	2156,
	6037,
	4809,
	2692,
	897,
	1778,
	4809,
	4809,
	1229,
	11,
	6037,
	4809,
	2692,
	898,
	1785,
	4809,
	4809,
	1234,
	14,
	6037,
	4809,
	2692,
	936,
	2045,
	4809,
	4809,
	1236,
	16,
	6037,
	4809,
	2692,
	973,
	2155,
	4809,
	4809,
	1239,
	19,
	6037,
	4809,
	2692,
	941,
	2090,
	4809,
	4809,
	1252,
	26,
	6037,
	4809,
	2692,
	941,
	9116,
	2090,
	4809,
	4809,
	1244,
	20,
	233,
	2171,
	2171,
	6037,
	4809,
	2692,
	896,
	1776,
	4809,
	4809,
	1228,
	10,
	6037,
	4809,
	2692,
	925,
	1871,
	4809,
	4809,
	1235,
	15,
	6037,
	4809,
	2692,
	975,
	2166,
	4809,
	4809,
	1245,
	21,
	1267,
	4455,
	6037,
	4809,
	2692,
	941,
	2088,
	4809,
	4809,
	1237,
	17,
	6037,
	9162,
	4809,
	2692,
	964,
	2125,
	4809,
	4809,
	1246,
	22,
	6037,
	4809,
	2692,
	972,
	2153,
	4809,
	4809,
	1250,
	25,
	6037,
	4809,
	2692,
	974,
	2160,
	4809,
	4809,
	1253,
	27,
	6037,
	4809,
	2692,
	977,
	2173,
	4809,
	4809,
	1255,
	29,
	6037,
	2692,
	941,
	4809,
	2088,
	4809,
	4809,
	1249,
	24,
	795,
	4260,
	6037,
	9162,
	9162,
	8973,
	7499,
	4809,
	2692,
	966,
	2135,
	4809,
	4809,
	1230,
	12,
	6037,
	4809,
	2692,
	975,
	2167,
	4809,
	4809,
	1254,
	28,
	6037,
	0,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	8564,
	8564,
	8564,
	8564,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9162,
	5887,
	1410,
	826,
	2702,
	2702,
	6037,
	9162,
	2797,
	8002,
	5913,
	0,
	0,
	0,
	6037,
	5887,
	1410,
	826,
	2702,
	2702,
	6037,
	9162,
	5887,
	1410,
	826,
	2702,
	2702,
	6037,
	5887,
	882,
	6037,
	1303,
	2168,
	4353,
	1901,
	8144,
	8973,
	6037,
	4247,
	2692,
	4785,
	6037,
	8973,
	8283,
	9116,
	4809,
	2692,
	963,
	2120,
	4809,
	4809,
	1238,
	18,
	6037,
	6037,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9108,
	8973,
	8373,
	8373,
	8373,
	8973,
	8973,
	8973,
	8973,
	8973,
	8973,
	8973,
	8969,
	8373,
	8373,
	8969,
	9083,
	8761,
	8324,
	9162,
	9162,
	9162,
	9162,
	8973,
	8973,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	4715,
	6037,
	2071,
	4254,
	4254,
	4254,
	2086,
	2089,
	4254,
	9162,
	9162,
	6037,
	4785,
	6037,
	5818,
	5913,
	6037,
	5913,
	4785,
	6037,
	5818,
	5913,
	6037,
	5913,
	4785,
	6037,
	5818,
	5913,
	6037,
	5913,
	4785,
	6037,
	5818,
	5913,
	6037,
	5913,
	4785,
	6037,
	5818,
	5913,
	6037,
	5913,
	4785,
	6037,
	5818,
	5913,
	6037,
	5913,
	6037,
	6037,
	6037,
	0,
	0,
	0,
	8973,
	8973,
	7240,
	5887,
	4785,
	5887,
	4785,
	5887,
	4785,
	5887,
	4785,
	4785,
	5887,
	2757,
	2702,
	9162,
	0,
	9116,
	7726,
	8973,
	9108,
	8358,
	8961,
	9162,
	8373,
	8973,
	9162,
	8340,
	9108,
	7687,
	6817,
	6300,
	7329,
	8564,
	8358,
	6815,
	8564,
	8564,
	8564,
	8564,
	7330,
	7911,
	8564,
	8564,
	9108,
	9108,
	8070,
	8123,
	7490,
	6880,
	7490,
	8358,
	8973,
	8973,
	9162,
	8973,
	8973,
	8973,
	8969,
	8358,
	8280,
	8226,
	7599,
	7595,
	7979,
	8761,
	9162,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7121,
	7121,
	7121,
	6684,
	6409,
	8757,
	8561,
	9162,
	6037,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	822,
	4809,
	822,
	7121,
	7121,
	7121,
	7121,
	6411,
};
static const Il2CppTokenRangePair s_rgctxIndices[60] = 
{
	{ 0x020000B9, { 91, 20 } },
	{ 0x06000031, { 0, 1 } },
	{ 0x0600006A, { 1, 2 } },
	{ 0x0600009B, { 3, 1 } },
	{ 0x060000A3, { 4, 1 } },
	{ 0x060000A4, { 5, 1 } },
	{ 0x06000244, { 6, 1 } },
	{ 0x06000245, { 7, 1 } },
	{ 0x06000246, { 8, 1 } },
	{ 0x06000247, { 9, 1 } },
	{ 0x06000248, { 10, 1 } },
	{ 0x06000249, { 11, 1 } },
	{ 0x0600024A, { 12, 1 } },
	{ 0x0600024B, { 13, 1 } },
	{ 0x0600024C, { 14, 1 } },
	{ 0x0600024D, { 15, 1 } },
	{ 0x0600024E, { 16, 1 } },
	{ 0x0600024F, { 17, 1 } },
	{ 0x06000250, { 18, 1 } },
	{ 0x06000251, { 19, 1 } },
	{ 0x06000252, { 20, 1 } },
	{ 0x06000253, { 21, 1 } },
	{ 0x06000254, { 22, 1 } },
	{ 0x06000255, { 23, 1 } },
	{ 0x06000256, { 24, 1 } },
	{ 0x06000257, { 25, 1 } },
	{ 0x06000258, { 26, 1 } },
	{ 0x06000259, { 27, 1 } },
	{ 0x0600025A, { 28, 1 } },
	{ 0x0600025B, { 29, 1 } },
	{ 0x0600025C, { 30, 1 } },
	{ 0x0600025D, { 31, 1 } },
	{ 0x0600025E, { 32, 1 } },
	{ 0x0600025F, { 33, 1 } },
	{ 0x06000260, { 34, 1 } },
	{ 0x06000261, { 35, 1 } },
	{ 0x06000262, { 36, 1 } },
	{ 0x06000263, { 37, 1 } },
	{ 0x06000264, { 38, 1 } },
	{ 0x06000270, { 39, 1 } },
	{ 0x06000271, { 40, 1 } },
	{ 0x06000272, { 41, 1 } },
	{ 0x06000273, { 42, 2 } },
	{ 0x06000277, { 44, 1 } },
	{ 0x06000278, { 45, 1 } },
	{ 0x06000279, { 46, 1 } },
	{ 0x0600027A, { 47, 1 } },
	{ 0x0600027B, { 48, 1 } },
	{ 0x0600027C, { 49, 1 } },
	{ 0x060002A7, { 50, 2 } },
	{ 0x060002AF, { 52, 1 } },
	{ 0x060002B1, { 53, 8 } },
	{ 0x060002B2, { 61, 4 } },
	{ 0x060002B3, { 65, 7 } },
	{ 0x060002B4, { 72, 4 } },
	{ 0x060002B6, { 76, 2 } },
	{ 0x06000386, { 78, 3 } },
	{ 0x06000387, { 81, 4 } },
	{ 0x0600041A, { 85, 1 } },
	{ 0x0600042D, { 86, 5 } },
};
extern const uint32_t g_rgctx_DOTween_ApplyTo_TisT1_tB29B2CFDD69968D6D628B779C9710C73006C2417_TisT2_tE16D758978DC41937EFE53526735423128DEB6FA_TisTPlugOptions_tF4E5BC1FB1B6881D042D606EC83D30BFF0805B0D_mC8A118B50D9A693DCA46E4A53C9B87E446A20C3F;
extern const uint32_t g_rgctx_TweenManager_GetTweener_TisT1_tF11D66D53F34C9586467E282349637F99CC4AC67_TisT2_tF01CCBCD3E34E6D72A045C92DD4892353990C150_TisTPlugOptions_tC6FC5A71472FF3CE6598FCCFD787FDCDDB3B5729_mC72DDD522C47490B4E486D817A446D67C717EDE4;
extern const uint32_t g_rgctx_Tweener_Setup_TisT1_tF11D66D53F34C9586467E282349637F99CC4AC67_TisT2_tF01CCBCD3E34E6D72A045C92DD4892353990C150_TisTPlugOptions_tC6FC5A71472FF3CE6598FCCFD787FDCDDB3B5729_mA26311B5B8F1E244E67F28AF0A11DBF8C76E17F4;
extern const uint32_t g_rgctx_T_t647B973EDBEDB4EA05877D23B18DC52EE8165BB2;
extern const uint32_t g_rgctx_T_t8E0A9C9428635DAAAC14C08267E9609154E4177C;
extern const uint32_t g_rgctx_T_t996D6B7E8FA1BABB3D1A1B9690735FD622C102DA;
extern const uint32_t g_rgctx_T_t7A316E2A5D823B80767ACDD0BA3B735AD46BC8BA;
extern const uint32_t g_rgctx_T_tA5DD03EABE06D18D770FBEDC46B4EE855F5F6A98;
extern const uint32_t g_rgctx_T_t584A471A843B66DC705F34D7C8E9E300F8F29BD7;
extern const uint32_t g_rgctx_T_tC95AA700112143744CE040FCE8CA1F37DDA97E1C;
extern const uint32_t g_rgctx_T_t951669B1F3F37729EE5D742F10553E31B9339A8E;
extern const uint32_t g_rgctx_T_tA85B3E9444B2EAD9B9DB850F2D1D08E6D80CA04D;
extern const uint32_t g_rgctx_T_tE3ADFA8AF8FB4DF6FBAA11A9E2B7CC0102C95B12;
extern const uint32_t g_rgctx_T_t11F01B69A544DF4BB1C6AAE5C052D9224BB1E5F5;
extern const uint32_t g_rgctx_T_t382B9157F22FB5BA8E3B84B8750DF129BA6D90DD;
extern const uint32_t g_rgctx_T_t8A99B97BA032ACB6F023EEDB0853639277FB0BA7;
extern const uint32_t g_rgctx_T_t1C191339A523137758AF2831B20DCE2459EFAA84;
extern const uint32_t g_rgctx_T_t9E4E0E3BD70089DE1141C7635B101BB399658BCD;
extern const uint32_t g_rgctx_T_t3D29A198B0A68881A83A520C1CDE12117C86ED9B;
extern const uint32_t g_rgctx_T_t1DF72BEDBB34F7C74155B5A45061FE8F7BE3BD55;
extern const uint32_t g_rgctx_T_tD1A054936384FC8D66C462E18D301BAAF67181A3;
extern const uint32_t g_rgctx_T_t7D49DB513105022DCD381AB4244AEA9FD52C782C;
extern const uint32_t g_rgctx_T_t3A788B28E1AFE06E8CB6A360707BECC771BE0A46;
extern const uint32_t g_rgctx_T_t17AF76E85A75C68E983331BFDFAD0241E5D02DE7;
extern const uint32_t g_rgctx_T_t0A357877DEBBD01658525941B74E1B086E183ED2;
extern const uint32_t g_rgctx_T_tCAFB95606ED8BA7451990DCAE757E7E66BE0573E;
extern const uint32_t g_rgctx_T_t6E3B2B52B2835AA8DCA73E87B62743DB33F4B94C;
extern const uint32_t g_rgctx_T_tDAED752DB45087A311D55C68389DE07C85DFD0F1;
extern const uint32_t g_rgctx_T_t3A687DEB8CCB201E6BB6ED4E79D2A551E735877D;
extern const uint32_t g_rgctx_T_t306587FEA3FE65775A7721321233D245CE355F75;
extern const uint32_t g_rgctx_T_t46A633D6B27D2FEF31E59535C4F798A4E1D367D2;
extern const uint32_t g_rgctx_T_tDD42A76EC9AED48E0D0DD3BD09330B288F2F7CF3;
extern const uint32_t g_rgctx_T_t7C35FCE102F563100BDAAA642AC03FBD188B66A0;
extern const uint32_t g_rgctx_T_t4CF9E03112E6C01B8A4E1C87E694D5A0827C748D;
extern const uint32_t g_rgctx_T_t8F96B3349E77C59590A65A4F70A037A1472920EA;
extern const uint32_t g_rgctx_T_tA3BB1FFFA0765247C57E4C7BC37CD0F8BEBF2C4E;
extern const uint32_t g_rgctx_T_t0A60D3A3E9F3E33FCDDF5C11758E25810DD038AA;
extern const uint32_t g_rgctx_T_tB1D9AD83C185409FA2E6EE4BB3A1F02B16BC0FA7;
extern const uint32_t g_rgctx_T_t2F9AD71E5BBA9CBB21365E119A26721947DFCFD2;
extern const uint32_t g_rgctx_TweenSettingsExtensions_From_TisT_tBC225FB000932FCD9546ADCCA14FC48DBB6536B1_m645C1D5E170537E337CC15D4D496829D37872CEC;
extern const uint32_t g_rgctx_TweenSettingsExtensions_From_TisT_tDC1DF5AEAC70F0309289880BEADEF11125E6F2E0_m400B4962EA61ABF5CCD06E106FFF3BCA7D285D91;
extern const uint32_t g_rgctx_T_t7C610AB36C8931815FD4ADCB64A1AB35F7480EF0;
extern const uint32_t g_rgctx_TweenerCore_3_tEA7A5DDFEBCCE86D5377325B7393C94EF99ADC79;
extern const uint32_t g_rgctx_TweenerCore_3_SetFrom_m005CB7749E3E0E5BE802FE34954B5DEDA96073CC;
extern const uint32_t g_rgctx_T_t15C5A937AE468E0CEF3F8E0B1BDF36E3D5B53C81;
extern const uint32_t g_rgctx_T_tDB854F951B944F410CE814D500FC06655363A0D8;
extern const uint32_t g_rgctx_T_tC3E28A59EB6F378A0D8DAA40A693372D731DA1D2;
extern const uint32_t g_rgctx_T_tB2B8731EB1F33484765DC971CE3E4B99B2072D39;
extern const uint32_t g_rgctx_T_t3A36DEF11383848C17EF894E4143FF466AA49F44;
extern const uint32_t g_rgctx_T_t1FD10E672157EE0B9CD7EADD5D256FA7AD41EB3A;
extern const uint32_t g_rgctx_TweenCallback_1_t877925C635725FEEA8C8EB4D45DC7793D93D28B4;
extern const uint32_t g_rgctx_TweenCallback_1_Invoke_mB35201EA284C80915255DDC706CBB58D64CFC0FE;
extern const uint32_t g_rgctx_PluginsManager_GetDefaultPlugin_TisT1_t0E372CEAC223E24829181714BD7081D1ED0E8855_TisT2_t1A317392D2BC29857CB52C1A930D6A4DD8BD2535_TisTPlugOptions_tD1D47CBD660D9137AD25629864DBC7ECAD9B4B21_m98665500FF3C2637C791DF60ABE06E95ACF02A1D;
extern const uint32_t g_rgctx_Tweener_DOStartupSpecials_TisT1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35_TisT2_tC4A76B819474D2BA458497F91AF356C820C52665_TisTPlugOptions_t5A83E4DE28BE3F411DB15E4B0AB1CC567A47C48C_m6FC23B801A9C5649CF6479553FE514E796906A89;
extern const uint32_t g_rgctx_DOGetter_1_t88C4DFDF86DFFB6407F08DF6037B3695F7F5BAE1;
extern const uint32_t g_rgctx_DOGetter_1_Invoke_mA2E144F27E67179DAE3B6109B55EE262F92A7BFB;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_tE15BC2F103281E62B718D20B7BB9DDD3CA3AFC28;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_ConvertToStartValue_mAD5DB662E7C52EFEE5CEFA21FD0202566A2011D1;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetRelativeEndValue_m822FDD211A289A90039247BB1837E19D55BB8BAD;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetChangeValue_m7E722039C4FA3663DC761B3131878BBE455CACCF;
extern const uint32_t g_rgctx_Tweener_DOStartupDurationBased_TisT1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35_TisT2_tC4A76B819474D2BA458497F91AF356C820C52665_TisTPlugOptions_t5A83E4DE28BE3F411DB15E4B0AB1CC567A47C48C_mE7A85ABBA6CBC87082DBF866E455D7625738A121;
extern const uint32_t g_rgctx_Tweener_DOStartupSpecials_TisT1_t2104A653C13FAF50D39CD458C9046A94C6D85BA6_TisT2_t23F7CECB4341A1AE4C5196BBD5EE5EB0570DFA3D_TisTPlugOptions_t654B76998D7C1CC0436FB3CC5C585D68C47D4122_mB8A69A1F3FA0458AEA13F45E634A96ACBBCEA264;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_tFA65378B03CF81C1F301F2E178B3A47998608F10;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetChangeValue_m432AF70FA4E07E5B72C5B42286AD4E67875A951B;
extern const uint32_t g_rgctx_Tweener_DOStartupDurationBased_TisT1_t2104A653C13FAF50D39CD458C9046A94C6D85BA6_TisT2_t23F7CECB4341A1AE4C5196BBD5EE5EB0570DFA3D_TisTPlugOptions_t654B76998D7C1CC0436FB3CC5C585D68C47D4122_mFA526CB8AAA67029C42BC84827644C3880E6B679;
extern const uint32_t g_rgctx_Tweener_DOStartupSpecials_TisT1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4_TisT2_tE48501F6F32076F79C09C65FC49FD7C48012689A_TisTPlugOptions_tB643754D9EDC8BE0118A9AC83507E6467E22C9E5_mD6E69265EA8A0D3688C27732AF45932E724EACB2;
extern const uint32_t g_rgctx_DOGetter_1_t23BC1B197BDFE6BE418FA0E21E33A23B49D3807F;
extern const uint32_t g_rgctx_DOGetter_1_Invoke_m41E85F763632E85D83AAC979D5DE5C44D11A1A6A;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_tBA2E2F88BF83E9A87073C91C5FD73DFF23A23BA0;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_ConvertToStartValue_mFF5A754363FEA2C83960B675F143A94BE54F387A;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetChangeValue_m1B2E33F9FB1A52B4B475D87506FCFB87BD811186;
extern const uint32_t g_rgctx_Tweener_DOStartupDurationBased_TisT1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4_TisT2_tE48501F6F32076F79C09C65FC49FD7C48012689A_TisTPlugOptions_tB643754D9EDC8BE0118A9AC83507E6467E22C9E5_m1F1B3EE9D5471D06FB46CE9AD30833F49F7392AF;
extern const uint32_t g_rgctx_Tweener_DOStartupSpecials_TisT1_t321E4C0C4045A524AE7542A26B7AAABA01A84404_TisT2_t80EDABB5B8C693B560C659787D3262C463D74344_TisTPlugOptions_t88E9ED18FA03BDFBD0F90187F1EB9E902DE72E9C_m3DB5DA854695B3272C2BA612EF797DCE5BA293E8;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_t7D9746AD117BBB10F861A19C051B40740C28BE6D;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetChangeValue_mE763874CD75BCEFD67EDFF17C3D2BE0B55CA5427;
extern const uint32_t g_rgctx_Tweener_DOStartupDurationBased_TisT1_t321E4C0C4045A524AE7542A26B7AAABA01A84404_TisT2_t80EDABB5B8C693B560C659787D3262C463D74344_TisTPlugOptions_t88E9ED18FA03BDFBD0F90187F1EB9E902DE72E9C_m77CC2D93A01D7E32961B4D0ECE3615A5006636D9;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_tCA2FCF7AA9959A985B00252151C895B69EA3A15F;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_GetSpeedBasedDuration_m61C28E14F638BE4A1E7A27125FF7174E76615B3E;
extern const uint32_t g_rgctx_T1_t56A1D382DD6C07F3648B8262CDB7AD3604F1DD50;
extern const uint32_t g_rgctx_T2_tC288634F7135CC961B6CBCC0354BA7262633F50A;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_t85A5DEEAB068C58207EBA88688D8D5B186828792;
extern const uint32_t g_rgctx_TPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_t456028CE0EA15DC4A666100047C0558E4C6E82CD;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisTPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0_m96BD2338C04130B436D330F23E8960CBA097AB06;
extern const uint32_t g_rgctx_TPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0;
extern const uint32_t g_rgctx_T_t744CCA93F42B6AB407D56BA97402304508E8D126;
extern const uint32_t g_rgctx_T1_tB93B55C6E17E38076065933CF07D49689DB02FE9;
extern const uint32_t g_rgctx_T2_tD013770946BAD079A42F798BC5F230E0FF5F69EE;
extern const uint32_t g_rgctx_TPlugOptions_t9A66C58DDECE4DACC0C69B129D543BEE21B3F31B;
extern const uint32_t g_rgctx_TweenerCore_3_t1A91562B894654D9A32CD90D2B7B3DC2EBEF280F;
extern const uint32_t g_rgctx_TweenerCore_3__ctor_m191BF86F9477FAADDABF38521D973DF5C3F4D3E3;
extern const uint32_t g_rgctx_T1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA;
extern const uint32_t g_rgctx_T2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00;
extern const uint32_t g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F;
extern const uint32_t g_rgctx_TweenerCore_3_ValidateChangeValueType_m41A2FA3F3241F2D158714EF7AC814D2329C7E7E4;
extern const uint32_t g_rgctx_T2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00;
extern const uint32_t g_rgctx_Tweener_DoChangeStartValue_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mC2C765778A348131855D1ECD6AFDDF86B311B388;
extern const uint32_t g_rgctx_Tweener_DoChangeEndValue_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mE766FEF8C2F23A334FD3F30CBC396FBB176329EC;
extern const uint32_t g_rgctx_Tweener_DoChangeValues_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_m942BDAD64731B7EA80356118B8C2B38C812FA1B1;
extern const uint32_t g_rgctx_TweenerCore_3_ChangeEndValue_m2143E63FC5C8E188B602BFC47078BD216C8C079B;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_t5AB0AED1E5225912CAC7F48FA4FF66BAE1933071;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetFrom_m48C5D8F8A180AB9DC080E05C59645B234BEC379D;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetFrom_m803889439454E0C901C2580A935EA034681899D2;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_Reset_mA12C9B5353FD460FA20AA3CD721D5B17FC95B0D2;
extern const uint32_t g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F;
extern const Il2CppRGCTXConstrainedData g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_IPlugOptions_Reset_m55A4D8608C4E079C3A7E9CD41AC6688929CC7606;
extern const uint32_t g_rgctx_DOGetter_1_t7802D68DE7558EF7801E6CA418C5D553AD76CA6F;
extern const uint32_t g_rgctx_DOGetter_1_Invoke_m2A3CCE8F304A93B6E327389F2F4B73E47E924EC1;
extern const uint32_t g_rgctx_Tweener_DoUpdateDelay_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_m35E1A653C5CD8389FB8E96C215A42ACFEF43742E;
extern const uint32_t g_rgctx_Tweener_DoStartup_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mA9E5230B08C67D3E34A768D86717D3B469806DD5;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_EvaluateAndApply_m3CE32BF19DBFF9F01673C7573B4DFB61FA4F2CB2;
static const Il2CppRGCTXDefinition s_rgctxValues[111] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DOTween_ApplyTo_TisT1_tB29B2CFDD69968D6D628B779C9710C73006C2417_TisT2_tE16D758978DC41937EFE53526735423128DEB6FA_TisTPlugOptions_tF4E5BC1FB1B6881D042D606EC83D30BFF0805B0D_mC8A118B50D9A693DCA46E4A53C9B87E446A20C3F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenManager_GetTweener_TisT1_tF11D66D53F34C9586467E282349637F99CC4AC67_TisT2_tF01CCBCD3E34E6D72A045C92DD4892353990C150_TisTPlugOptions_tC6FC5A71472FF3CE6598FCCFD787FDCDDB3B5729_mC72DDD522C47490B4E486D817A446D67C717EDE4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_Setup_TisT1_tF11D66D53F34C9586467E282349637F99CC4AC67_TisT2_tF01CCBCD3E34E6D72A045C92DD4892353990C150_TisTPlugOptions_tC6FC5A71472FF3CE6598FCCFD787FDCDDB3B5729_mA26311B5B8F1E244E67F28AF0A11DBF8C76E17F4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t647B973EDBEDB4EA05877D23B18DC52EE8165BB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8E0A9C9428635DAAAC14C08267E9609154E4177C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t996D6B7E8FA1BABB3D1A1B9690735FD622C102DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7A316E2A5D823B80767ACDD0BA3B735AD46BC8BA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA5DD03EABE06D18D770FBEDC46B4EE855F5F6A98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t584A471A843B66DC705F34D7C8E9E300F8F29BD7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC95AA700112143744CE040FCE8CA1F37DDA97E1C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t951669B1F3F37729EE5D742F10553E31B9339A8E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA85B3E9444B2EAD9B9DB850F2D1D08E6D80CA04D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE3ADFA8AF8FB4DF6FBAA11A9E2B7CC0102C95B12 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t11F01B69A544DF4BB1C6AAE5C052D9224BB1E5F5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t382B9157F22FB5BA8E3B84B8750DF129BA6D90DD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8A99B97BA032ACB6F023EEDB0853639277FB0BA7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1C191339A523137758AF2831B20DCE2459EFAA84 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9E4E0E3BD70089DE1141C7635B101BB399658BCD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3D29A198B0A68881A83A520C1CDE12117C86ED9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1DF72BEDBB34F7C74155B5A45061FE8F7BE3BD55 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD1A054936384FC8D66C462E18D301BAAF67181A3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7D49DB513105022DCD381AB4244AEA9FD52C782C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3A788B28E1AFE06E8CB6A360707BECC771BE0A46 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t17AF76E85A75C68E983331BFDFAD0241E5D02DE7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0A357877DEBBD01658525941B74E1B086E183ED2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCAFB95606ED8BA7451990DCAE757E7E66BE0573E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6E3B2B52B2835AA8DCA73E87B62743DB33F4B94C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDAED752DB45087A311D55C68389DE07C85DFD0F1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3A687DEB8CCB201E6BB6ED4E79D2A551E735877D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t306587FEA3FE65775A7721321233D245CE355F75 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t46A633D6B27D2FEF31E59535C4F798A4E1D367D2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDD42A76EC9AED48E0D0DD3BD09330B288F2F7CF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7C35FCE102F563100BDAAA642AC03FBD188B66A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4CF9E03112E6C01B8A4E1C87E694D5A0827C748D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8F96B3349E77C59590A65A4F70A037A1472920EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA3BB1FFFA0765247C57E4C7BC37CD0F8BEBF2C4E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0A60D3A3E9F3E33FCDDF5C11758E25810DD038AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB1D9AD83C185409FA2E6EE4BB3A1F02B16BC0FA7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2F9AD71E5BBA9CBB21365E119A26721947DFCFD2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenSettingsExtensions_From_TisT_tBC225FB000932FCD9546ADCCA14FC48DBB6536B1_m645C1D5E170537E337CC15D4D496829D37872CEC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenSettingsExtensions_From_TisT_tDC1DF5AEAC70F0309289880BEADEF11125E6F2E0_m400B4962EA61ABF5CCD06E106FFF3BCA7D285D91 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7C610AB36C8931815FD4ADCB64A1AB35F7480EF0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_tEA7A5DDFEBCCE86D5377325B7393C94EF99ADC79 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenerCore_3_SetFrom_m005CB7749E3E0E5BE802FE34954B5DEDA96073CC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t15C5A937AE468E0CEF3F8E0B1BDF36E3D5B53C81 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDB854F951B944F410CE814D500FC06655363A0D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC3E28A59EB6F378A0D8DAA40A693372D731DA1D2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB2B8731EB1F33484765DC971CE3E4B99B2072D39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3A36DEF11383848C17EF894E4143FF466AA49F44 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1FD10E672157EE0B9CD7EADD5D256FA7AD41EB3A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenCallback_1_t877925C635725FEEA8C8EB4D45DC7793D93D28B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenCallback_1_Invoke_mB35201EA284C80915255DDC706CBB58D64CFC0FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PluginsManager_GetDefaultPlugin_TisT1_t0E372CEAC223E24829181714BD7081D1ED0E8855_TisT2_t1A317392D2BC29857CB52C1A930D6A4DD8BD2535_TisTPlugOptions_tD1D47CBD660D9137AD25629864DBC7ECAD9B4B21_m98665500FF3C2637C791DF60ABE06E95ACF02A1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupSpecials_TisT1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35_TisT2_tC4A76B819474D2BA458497F91AF356C820C52665_TisTPlugOptions_t5A83E4DE28BE3F411DB15E4B0AB1CC567A47C48C_m6FC23B801A9C5649CF6479553FE514E796906A89 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOGetter_1_t88C4DFDF86DFFB6407F08DF6037B3695F7F5BAE1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DOGetter_1_Invoke_mA2E144F27E67179DAE3B6109B55EE262F92A7BFB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_tE15BC2F103281E62B718D20B7BB9DDD3CA3AFC28 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_ConvertToStartValue_mAD5DB662E7C52EFEE5CEFA21FD0202566A2011D1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetRelativeEndValue_m822FDD211A289A90039247BB1837E19D55BB8BAD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetChangeValue_m7E722039C4FA3663DC761B3131878BBE455CACCF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupDurationBased_TisT1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35_TisT2_tC4A76B819474D2BA458497F91AF356C820C52665_TisTPlugOptions_t5A83E4DE28BE3F411DB15E4B0AB1CC567A47C48C_mE7A85ABBA6CBC87082DBF866E455D7625738A121 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupSpecials_TisT1_t2104A653C13FAF50D39CD458C9046A94C6D85BA6_TisT2_t23F7CECB4341A1AE4C5196BBD5EE5EB0570DFA3D_TisTPlugOptions_t654B76998D7C1CC0436FB3CC5C585D68C47D4122_mB8A69A1F3FA0458AEA13F45E634A96ACBBCEA264 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_tFA65378B03CF81C1F301F2E178B3A47998608F10 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetChangeValue_m432AF70FA4E07E5B72C5B42286AD4E67875A951B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupDurationBased_TisT1_t2104A653C13FAF50D39CD458C9046A94C6D85BA6_TisT2_t23F7CECB4341A1AE4C5196BBD5EE5EB0570DFA3D_TisTPlugOptions_t654B76998D7C1CC0436FB3CC5C585D68C47D4122_mFA526CB8AAA67029C42BC84827644C3880E6B679 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupSpecials_TisT1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4_TisT2_tE48501F6F32076F79C09C65FC49FD7C48012689A_TisTPlugOptions_tB643754D9EDC8BE0118A9AC83507E6467E22C9E5_mD6E69265EA8A0D3688C27732AF45932E724EACB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOGetter_1_t23BC1B197BDFE6BE418FA0E21E33A23B49D3807F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DOGetter_1_Invoke_m41E85F763632E85D83AAC979D5DE5C44D11A1A6A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_tBA2E2F88BF83E9A87073C91C5FD73DFF23A23BA0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_ConvertToStartValue_mFF5A754363FEA2C83960B675F143A94BE54F387A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetChangeValue_m1B2E33F9FB1A52B4B475D87506FCFB87BD811186 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupDurationBased_TisT1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4_TisT2_tE48501F6F32076F79C09C65FC49FD7C48012689A_TisTPlugOptions_tB643754D9EDC8BE0118A9AC83507E6467E22C9E5_m1F1B3EE9D5471D06FB46CE9AD30833F49F7392AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupSpecials_TisT1_t321E4C0C4045A524AE7542A26B7AAABA01A84404_TisT2_t80EDABB5B8C693B560C659787D3262C463D74344_TisTPlugOptions_t88E9ED18FA03BDFBD0F90187F1EB9E902DE72E9C_m3DB5DA854695B3272C2BA612EF797DCE5BA293E8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_t7D9746AD117BBB10F861A19C051B40740C28BE6D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetChangeValue_mE763874CD75BCEFD67EDFF17C3D2BE0B55CA5427 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupDurationBased_TisT1_t321E4C0C4045A524AE7542A26B7AAABA01A84404_TisT2_t80EDABB5B8C693B560C659787D3262C463D74344_TisTPlugOptions_t88E9ED18FA03BDFBD0F90187F1EB9E902DE72E9C_m77CC2D93A01D7E32961B4D0ECE3615A5006636D9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_tCA2FCF7AA9959A985B00252151C895B69EA3A15F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_GetSpeedBasedDuration_m61C28E14F638BE4A1E7A27125FF7174E76615B3E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_t56A1D382DD6C07F3648B8262CDB7AD3604F1DD50 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T2_tC288634F7135CC961B6CBCC0354BA7262633F50A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_t85A5DEEAB068C58207EBA88688D8D5B186828792 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_t456028CE0EA15DC4A666100047C0558E4C6E82CD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisTPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0_m96BD2338C04130B436D330F23E8960CBA097AB06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t744CCA93F42B6AB407D56BA97402304508E8D126 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_tB93B55C6E17E38076065933CF07D49689DB02FE9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T2_tD013770946BAD079A42F798BC5F230E0FF5F69EE },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TPlugOptions_t9A66C58DDECE4DACC0C69B129D543BEE21B3F31B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t1A91562B894654D9A32CD90D2B7B3DC2EBEF280F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenerCore_3__ctor_m191BF86F9477FAADDABF38521D973DF5C3F4D3E3 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenerCore_3_ValidateChangeValueType_m41A2FA3F3241F2D158714EF7AC814D2329C7E7E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DoChangeStartValue_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mC2C765778A348131855D1ECD6AFDDF86B311B388 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DoChangeEndValue_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mE766FEF8C2F23A334FD3F30CBC396FBB176329EC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DoChangeValues_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_m942BDAD64731B7EA80356118B8C2B38C812FA1B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenerCore_3_ChangeEndValue_m2143E63FC5C8E188B602BFC47078BD216C8C079B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_t5AB0AED1E5225912CAC7F48FA4FF66BAE1933071 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetFrom_m48C5D8F8A180AB9DC080E05C59645B234BEC379D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetFrom_m803889439454E0C901C2580A935EA034681899D2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_Reset_mA12C9B5353FD460FA20AA3CD721D5B17FC95B0D2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_IPlugOptions_Reset_m55A4D8608C4E079C3A7E9CD41AC6688929CC7606 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOGetter_1_t7802D68DE7558EF7801E6CA418C5D553AD76CA6F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DOGetter_1_Invoke_m2A3CCE8F304A93B6E327389F2F4B73E47E924EC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DoUpdateDelay_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_m35E1A653C5CD8389FB8E96C215A42ACFEF43742E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DoStartup_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mA9E5230B08C67D3E34A768D86717D3B469806DD5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_EvaluateAndApply_m3CE32BF19DBFF9F01673C7573B4DFB61FA4F2CB2 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_DOTween_CodeGenModule;
const Il2CppCodeGenModule g_DOTween_CodeGenModule = 
{
	"DOTween.dll",
	1188,
	s_methodPointers,
	25,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	60,
	s_rgctxIndices,
	111,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
