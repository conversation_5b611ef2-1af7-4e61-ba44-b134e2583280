﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Api.AdError::.ctor(GoogleMobileAds.Common.IAdErrorClient)
extern void AdError__ctor_m15B953B294A80F8AF84ECECC285670B48A188F07 (void);
// 0x00000002 System.String GoogleMobileAds.Api.AdError::GetMessage()
extern void AdError_GetMessage_m1D90CEA0A4CE678016C5D06D73C3F111D6BA370C (void);
// 0x00000003 System.String GoogleMobileAds.Api.AdError::ToString()
extern void AdError_ToString_mD2183315C9A1F6504900AD43F22BB385765FA9F9 (void);
// 0x00000004 System.Void GoogleMobileAds.Api.AppOpenAd::.ctor(GoogleMobileAds.Common.IAppOpenAdClient)
extern void AppOpenAd__ctor_m8712CC46DE91737B91B231D8A52D26075994A20C (void);
// 0x00000005 System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void AppOpenAd_add_OnAdPaid_mC6A173F1C25378D51E0CA1EE26AC1D175E50918D (void);
// 0x00000006 System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void AppOpenAd_remove_OnAdPaid_mD7AE74221EA71AA54EEAA6F6B958FE68D69087AC (void);
// 0x00000007 System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdFullScreenContentOpened(System.Action)
extern void AppOpenAd_add_OnAdFullScreenContentOpened_m5BF498515C6EE855420C3C368C62385C2BF7F7BA (void);
// 0x00000008 System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdFullScreenContentOpened(System.Action)
extern void AppOpenAd_remove_OnAdFullScreenContentOpened_m522500F8D3143BB63D7E6227C22BE875B4E98B6A (void);
// 0x00000009 System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdFullScreenContentClosed(System.Action)
extern void AppOpenAd_add_OnAdFullScreenContentClosed_m9AFCAA1DF7CF8CCD46B3E62169ADA2B02D49C2CD (void);
// 0x0000000A System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdFullScreenContentClosed(System.Action)
extern void AppOpenAd_remove_OnAdFullScreenContentClosed_m4C89A4830D5EF6A6A51F0E163ED8048036B01546 (void);
// 0x0000000B System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void AppOpenAd_add_OnAdFullScreenContentFailed_m4D52DC4F7D85A0DA305BAAAD27031C528006CE44 (void);
// 0x0000000C System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void AppOpenAd_remove_OnAdFullScreenContentFailed_m578143F55D37FE0B507C12EBDF506B9DC2B97570 (void);
// 0x0000000D System.Void GoogleMobileAds.Api.AppOpenAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError>)
extern void AppOpenAd_Load_mF96F81FEF60491CD62225A17670A8D68D846F276 (void);
// 0x0000000E System.Boolean GoogleMobileAds.Api.AppOpenAd::CanShowAd()
extern void AppOpenAd_CanShowAd_m822270A031DF79CB0ED3AD52240BC4100F0DFAB1 (void);
// 0x0000000F System.Void GoogleMobileAds.Api.AppOpenAd::Show()
extern void AppOpenAd_Show_m2037E04CE3FAE7C677CEA702F39C20390CAB9024 (void);
// 0x00000010 System.Void GoogleMobileAds.Api.AppOpenAd::Destroy()
extern void AppOpenAd_Destroy_mD1F8CE10A274F9681F7D7FF7A591781F6A5E5351 (void);
// 0x00000011 System.Void GoogleMobileAds.Api.AppOpenAd::RegisterAdEvents()
extern void AppOpenAd_RegisterAdEvents_m942A3CA51674726CDD0C3F6A9BDA83C0EF721B53 (void);
// 0x00000012 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__0()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__0_m27A1F8B773ED5CDE8007F65DFDAFD05B60381651 (void);
// 0x00000013 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__1_m9C4BA7627EDA74D79F3EC01511390054892404AC (void);
// 0x00000014 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__2_m699D6740C2F6D1033E611DC12965B18B303A4B2F (void);
// 0x00000015 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__3_mB562EF2E861A7E54D09DE5A4A2A0F7157D4A0BAE (void);
// 0x00000016 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__4_m58B88A7402B3A92907539DAD22DE4315A4821F14 (void);
// 0x00000017 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__5(GoogleMobileAds.Api.AdValue)
extern void AppOpenAd_U3CRegisterAdEventsU3Em__5_mE9280DC930E3912764FEE6F905CDF95514891997 (void);
// 0x00000018 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__6()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__6_mDE5D7116D371A3AF16B62867E4171868A6CE0578 (void);
// 0x00000019 System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__7()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__7_m2DE1DA8A99D930261053267DAACD6E5D5DFD0121 (void);
// 0x0000001A System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__8()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__8_m3BB0C197A41B2420EB74BBB2EA03B87401E54923 (void);
// 0x0000001B System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__9()
extern void AppOpenAd_U3CRegisterAdEventsU3Em__9_mD5262901450D3C3E94AE21F828AC0C688B6D5E0C (void);
// 0x0000001C System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_mC3285F87E7CF1CDD7E8646879D08F71323457FFB (void);
// 0x0000001D System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m49C42950EE386C1E4C6044643D9521A264974CF3 (void);
// 0x0000001E System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mBB7E2D5335953D85AE80D33F59EDC5B3B2A455F3 (void);
// 0x0000001F System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::<>m__2()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mA3783D4C7BBE5618206F759BAA70878E3DDE1950 (void);
// 0x00000020 System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_m536EDFA1E591F37667D32F2BB8F2CDA45711279A (void);
// 0x00000021 System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1DFCB8AA6D258C3576F33092D33A9479496A74EB (void);
// 0x00000022 System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m960091194D004208900F150D954F8985848871B1 (void);
// 0x00000023 System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m87FEA735EB953A658AE9766E47EBC3B9D318E538 (void);
// 0x00000024 System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m05E9BE4D156BBC16C191C438B22867C2C7142317 (void);
// 0x00000025 System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mEEB5DB9D7121D67FB5B1CF48EE12E0F96F002A20 (void);
// 0x00000026 System.Void GoogleMobileAds.Api.AppStateEventNotifier::.cctor()
extern void AppStateEventNotifier__cctor_m741EDC54C3A65311800A5B0AC88FAFF6A93A06B3 (void);
// 0x00000027 System.Void GoogleMobileAds.Api.AppStateEventNotifier::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventNotifier_add_AppStateChanged_m11DAB106CCCE3EF78F377722C46ED63BCCF36704 (void);
// 0x00000028 System.Void GoogleMobileAds.Api.AppStateEventNotifier::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventNotifier_remove_AppStateChanged_m9C6E767D608F2F692DF3BEA4DB4EA9FE615E5364 (void);
// 0x00000029 System.Void GoogleMobileAds.Api.BannerView::.ctor(System.String,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
extern void BannerView__ctor_m3C156986754CF612D73D61061B92BE3468FF6FDC (void);
// 0x0000002A System.Void GoogleMobileAds.Api.BannerView::add_OnBannerAdLoaded(System.Action)
extern void BannerView_add_OnBannerAdLoaded_mF3A6AA44B521D6AFF697B4FDF73C02139F1DEAE6 (void);
// 0x0000002B System.Void GoogleMobileAds.Api.BannerView::remove_OnBannerAdLoaded(System.Action)
extern void BannerView_remove_OnBannerAdLoaded_mAF3A48277FF13423864DFB8A25DB7688F51BEEA9 (void);
// 0x0000002C System.Void GoogleMobileAds.Api.BannerView::add_OnBannerAdLoadFailed(System.Action`1<GoogleMobileAds.Api.LoadAdError>)
extern void BannerView_add_OnBannerAdLoadFailed_m90B2772E9E91E19DA78644420A09DB4EC190597C (void);
// 0x0000002D System.Void GoogleMobileAds.Api.BannerView::remove_OnBannerAdLoadFailed(System.Action`1<GoogleMobileAds.Api.LoadAdError>)
extern void BannerView_remove_OnBannerAdLoadFailed_m3515C651D8F4110C5C4E0CD28C88478619089635 (void);
// 0x0000002E System.Void GoogleMobileAds.Api.BannerView::Destroy()
extern void BannerView_Destroy_mCD660269DF04D3EA92CC22EC1045B00C1E6CDC01 (void);
// 0x0000002F GoogleMobileAds.Api.ResponseInfo GoogleMobileAds.Api.BannerView::GetResponseInfo()
extern void BannerView_GetResponseInfo_m9FB398199E74F3AB52D8BF16121E426E9FD06D4C (void);
// 0x00000030 System.Single GoogleMobileAds.Api.BannerView::GetHeightInPixels()
extern void BannerView_GetHeightInPixels_m7E9AEE3A333CC8370B35DBF8E8C6ED89EFDEB4B8 (void);
// 0x00000031 System.Single GoogleMobileAds.Api.BannerView::GetWidthInPixels()
extern void BannerView_GetWidthInPixels_m30250357790E167D473D206B4586A6EA6792A8ED (void);
// 0x00000032 System.Void GoogleMobileAds.Api.BannerView::LoadAd(GoogleMobileAds.Api.AdRequest)
extern void BannerView_LoadAd_m56BF83CC97DE20C40457B9452D0ABEFD97933FFF (void);
// 0x00000033 System.Void GoogleMobileAds.Api.BannerView::Show()
extern void BannerView_Show_m90ACC1B7ED13065667AB7948722F0B2CBD3A84AE (void);
// 0x00000034 System.Void GoogleMobileAds.Api.BannerView::Hide()
extern void BannerView_Hide_m54630CB9110A9D38DFC00BC047B775C778EA22BD (void);
// 0x00000035 System.Void GoogleMobileAds.Api.BannerView::ConfigureBannerEvents()
extern void BannerView_ConfigureBannerEvents_m04F0638EF968385C04608F12F9BBFF1811F1437A (void);
// 0x00000036 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__0(System.Object,System.EventArgs)
extern void BannerView_U3CConfigureBannerEventsU3Em__0_m950132C20D9FF852333840439DE8B7E912AED86E (void);
// 0x00000037 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void BannerView_U3CConfigureBannerEventsU3Em__1_m33285EB72E7B98E9DDE1A768A6E9AED39973E367 (void);
// 0x00000038 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__2(System.Object,System.EventArgs)
extern void BannerView_U3CConfigureBannerEventsU3Em__2_mE7CB7EF05F18F046FEFE2051610EADC5F7DB26B1 (void);
// 0x00000039 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__3(System.Object,System.EventArgs)
extern void BannerView_U3CConfigureBannerEventsU3Em__3_mA1831E8828F53A1179D0A9FEB046D01B6C6FFC9D (void);
// 0x0000003A System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__4(GoogleMobileAds.Api.AdValue)
extern void BannerView_U3CConfigureBannerEventsU3Em__4_m7145FAC85AB6EEA6289BA99FE9776DFF4CE4B876 (void);
// 0x0000003B System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__5()
extern void BannerView_U3CConfigureBannerEventsU3Em__5_m16FA9EC5112461943D6912C09B316B1D0878581C (void);
// 0x0000003C System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__6()
extern void BannerView_U3CConfigureBannerEventsU3Em__6_m6850799F2427F7E1098F13861874B2C678534643 (void);
// 0x0000003D System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__7()
extern void BannerView_U3CConfigureBannerEventsU3Em__7_m9AF0E557AEE8A07DD4222E3FCD89B5105BA5D077 (void);
// 0x0000003E System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__8()
extern void BannerView_U3CConfigureBannerEventsU3Em__8_m3A928D0457E7D80A84DC9309BD0E3B94EB8ACFC2 (void);
// 0x0000003F System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__9()
extern void BannerView_U3CConfigureBannerEventsU3Em__9_mE15693E2BB35B4112B0B3085064CD0AF30DF6071 (void);
// 0x00000040 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__A()
extern void BannerView_U3CConfigureBannerEventsU3Em__A_m6B9B50D77E6054F2863F2A1762CC3C167947F202 (void);
// 0x00000041 System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__B()
extern void BannerView_U3CConfigureBannerEventsU3Em__B_m69174F10E32D424CB92DD84998F2FB04CFF5D9F8 (void);
// 0x00000042 System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0::.ctor()
extern void U3CConfigureBannerEventsU3Ec__AnonStorey0__ctor_m0EA663E8A0E2B5707941BF2F08E4F4B32B72823E (void);
// 0x00000043 System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0::<>m__0()
extern void U3CConfigureBannerEventsU3Ec__AnonStorey0_U3CU3Em__0_mB8F05C7553294A657561161F2EBEF25060CDB69B (void);
// 0x00000044 System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1::.ctor()
extern void U3CConfigureBannerEventsU3Ec__AnonStorey1__ctor_m10CBFCB9AF0E5B4775D5AAFB583930A64958EF88 (void);
// 0x00000045 System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1::<>m__0()
extern void U3CConfigureBannerEventsU3Ec__AnonStorey1_U3CU3Em__0_m9045774A489D908E618F158776547721382934C4 (void);
// 0x00000046 System.Void GoogleMobileAds.Api.InitializationStatus::.ctor(GoogleMobileAds.Common.IInitializationStatusClient)
extern void InitializationStatus__ctor_m1ECBC8D450C9F80E7D8A8F960722F3ED05AD2F15 (void);
// 0x00000047 System.Void GoogleMobileAds.Api.InterstitialAd::.ctor(GoogleMobileAds.Common.IInterstitialClient)
extern void InterstitialAd__ctor_mF55A705F3B7BB445AE117E57F55A8B8D810854CA (void);
// 0x00000048 System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void InterstitialAd_add_OnAdPaid_m3B50D1F1600BDC3552B5A4908A49F5FEA7FFD1A3 (void);
// 0x00000049 System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void InterstitialAd_remove_OnAdPaid_m718C856E51DBFBB28E130954FAE8FD20EC5301ED (void);
// 0x0000004A System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdFullScreenContentOpened(System.Action)
extern void InterstitialAd_add_OnAdFullScreenContentOpened_mB5E6127049339D2D6BB4ACB8DBBE03A68C4A09AF (void);
// 0x0000004B System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdFullScreenContentOpened(System.Action)
extern void InterstitialAd_remove_OnAdFullScreenContentOpened_mC6B904E24552B94D85EF1949A475C986F0636179 (void);
// 0x0000004C System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdFullScreenContentClosed(System.Action)
extern void InterstitialAd_add_OnAdFullScreenContentClosed_m752CF051CA5A0DA7F958D5C3A1813F30D09030E7 (void);
// 0x0000004D System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdFullScreenContentClosed(System.Action)
extern void InterstitialAd_remove_OnAdFullScreenContentClosed_m51AAA11D9E43921F8E442BC3CC4888117AA7A0E5 (void);
// 0x0000004E System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void InterstitialAd_add_OnAdFullScreenContentFailed_m0A88B3B74B846FCA5CF7BB461E8ABF98FC269C85 (void);
// 0x0000004F System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void InterstitialAd_remove_OnAdFullScreenContentFailed_mED3D4983E4BF4AA5FB76264B5BA9693C8BF3D170 (void);
// 0x00000050 System.Void GoogleMobileAds.Api.InterstitialAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError>)
extern void InterstitialAd_Load_m7582DD601991C297C7E7E61BBE53B639466A73A1 (void);
// 0x00000051 System.Boolean GoogleMobileAds.Api.InterstitialAd::CanShowAd()
extern void InterstitialAd_CanShowAd_m76764B2A422FA2DE78D9E29DE630380BE6E65A0E (void);
// 0x00000052 System.Void GoogleMobileAds.Api.InterstitialAd::Show()
extern void InterstitialAd_Show_m70188D3BE2543E7A0B58579A5991DA5A9E11CBAB (void);
// 0x00000053 System.Void GoogleMobileAds.Api.InterstitialAd::Destroy()
extern void InterstitialAd_Destroy_m5C5C9367FF6A3500C3B61FD2BDBE5B6AF2FF0778 (void);
// 0x00000054 System.Void GoogleMobileAds.Api.InterstitialAd::RegisterAdEvents()
extern void InterstitialAd_RegisterAdEvents_m9FB37519C9D7FB812BAA0363DEE49DA56F858309 (void);
// 0x00000055 System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__0()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__0_m15F794DFA1B2D15AF3FD46DD0535B35E8E39BD70 (void);
// 0x00000056 System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__1_m77C5D5D530946D3B60F32AE12E82C6730C8CE18F (void);
// 0x00000057 System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__2_m694739D06B2C43CAF858120C9F931BC94B9E80BF (void);
// 0x00000058 System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__3_m47237067EBB70CD501CC51CB495AEF7AA00C8194 (void);
// 0x00000059 System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__4_mA938230C35A0425F720B36F5E01E1F1395BEE9C8 (void);
// 0x0000005A System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__5(GoogleMobileAds.Api.AdValue)
extern void InterstitialAd_U3CRegisterAdEventsU3Em__5_m457A38FDD193CDBAD62505C2F50A8169E425AD89 (void);
// 0x0000005B System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__6()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__6_m6AE447D420E294BFA27C74F33F6AB7AB24C12D25 (void);
// 0x0000005C System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__7()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__7_m45C19CF4E9FD6FBD9C1232A71CD3FFA354F4D91E (void);
// 0x0000005D System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__8()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__8_mA46671F488D6BB9275D7AFB99691B279995E82F3 (void);
// 0x0000005E System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__9()
extern void InterstitialAd_U3CRegisterAdEventsU3Em__9_m990102B76D6D8BA076566A3057B1EEE9ABAF1759 (void);
// 0x0000005F System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_m86A02532C966283C1C91EEBB986FAF423FB6FC5C (void);
// 0x00000060 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m631331ABD2466F9B2AE59D8DFF7723E436983B20 (void);
// 0x00000061 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mCDAD16B0470ADCBA74835F69B270E5174950D92B (void);
// 0x00000062 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_mE9E302BFC9D729F14AB85D057E5C81233F4C6AED (void);
// 0x00000063 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m64E945F2EC125802BFC95173075BF6ABE5438E52 (void);
// 0x00000064 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2::.ctor()
extern void U3CLoadU3Ec__AnonStorey2__ctor_m97059AC37568D3E45311BFEAC1BA0370E4345E2A (void);
// 0x00000065 System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2::<>m__0()
extern void U3CLoadU3Ec__AnonStorey2_U3CU3Em__0_m0132A6A39D8BDC7093D0C265AA5BCFCE1B8417DC (void);
// 0x00000066 System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m9A8FC0128A1F434EE1B3A709FEE69705B9424E5B (void);
// 0x00000067 System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mA89B79A7FE9DB7FBB22C1E1B20F33DBA46B20D7F (void);
// 0x00000068 System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mDB67C29C7A47AEDD47CF4908138B0EEBA8BCA5F9 (void);
// 0x00000069 System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_mCE5D71C37A91D8E6C882E40EECBA76A8EF23E74B (void);
// 0x0000006A System.Void GoogleMobileAds.Api.LoadAdError::.ctor(GoogleMobileAds.Common.ILoadAdErrorClient)
extern void LoadAdError__ctor_m616B349928B08AD5C222B3136301B43465DE2A8F (void);
// 0x0000006B System.String GoogleMobileAds.Api.LoadAdError::ToString()
extern void LoadAdError_ToString_mC2DBA6C870EEBF993492FF04A29F4AE567D45C53 (void);
// 0x0000006C System.Void GoogleMobileAds.Api.MobileAds::.cctor()
extern void MobileAds__cctor_mB05D140F842D0D647DE6DBA9F70D77B8565984F9 (void);
// 0x0000006D System.Void GoogleMobileAds.Api.MobileAds::.ctor()
extern void MobileAds__ctor_mE209BFED2C4B3EDEA77A1D92FA94E692454F9B10 (void);
// 0x0000006E GoogleMobileAds.Api.MobileAds GoogleMobileAds.Api.MobileAds::get_Instance()
extern void MobileAds_get_Instance_m90A099F9597B22FEEC2CB4739F31219727C3614A (void);
// 0x0000006F System.Boolean GoogleMobileAds.Api.MobileAds::get_RaiseAdEventsOnUnityMainThread()
extern void MobileAds_get_RaiseAdEventsOnUnityMainThread_m42F779998BC24C982D13184FF938C9F78939D5B5 (void);
// 0x00000070 System.Void GoogleMobileAds.Api.MobileAds::set_RaiseAdEventsOnUnityMainThread(System.Boolean)
extern void MobileAds_set_RaiseAdEventsOnUnityMainThread_mCC9684511E440AD0F0D20CA15E36AE96BB552A9C (void);
// 0x00000071 System.Void GoogleMobileAds.Api.MobileAds::Initialize(System.Action`1<GoogleMobileAds.Api.InitializationStatus>)
extern void MobileAds_Initialize_m9151E3C38E0FAD4439DA5020A217BAB90ABB76AD (void);
// 0x00000072 System.Void GoogleMobileAds.Api.MobileAds::SetRequestConfiguration(GoogleMobileAds.Api.RequestConfiguration)
extern void MobileAds_SetRequestConfiguration_m1F0110671038CF67255ADAA3CFDB4CFC6E1E7AF5 (void);
// 0x00000073 System.Void GoogleMobileAds.Api.MobileAds::SetiOSAppPauseOnBackground(System.Boolean)
extern void MobileAds_SetiOSAppPauseOnBackground_m2626F076EEDF748681C27AC832E79AC9E93756E7 (void);
// 0x00000074 GoogleMobileAds.IClientFactory GoogleMobileAds.Api.MobileAds::GetClientFactory()
extern void MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E (void);
// 0x00000075 System.Void GoogleMobileAds.Api.MobileAds::RaiseAction(System.Action)
extern void MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2 (void);
// 0x00000076 System.Void GoogleMobileAds.Api.MobileAds::SetUnityMainThreadSynchronizationContext()
extern void MobileAds_SetUnityMainThreadSynchronizationContext_mC7C80D814660CF6742100D36D03F879120BBE27F (void);
// 0x00000077 GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.Api.MobileAds::GetMobileAdsClient()
extern void MobileAds_GetMobileAdsClient_m1A2ADAC0C61D673C13E8B06068EDFBACC58FCD14 (void);
// 0x00000078 System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0::.ctor()
extern void U3CInitializeU3Ec__AnonStorey0__ctor_m6DFCA2A06E9120467C5E1B941B84435F48BC9D74 (void);
// 0x00000079 System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0::<>m__0(GoogleMobileAds.Common.IInitializationStatusClient)
extern void U3CInitializeU3Ec__AnonStorey0_U3CU3Em__0_m8D6DA3278704BF784A5424EF89AAAC90A79F4414 (void);
// 0x0000007A System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1::.ctor()
extern void U3CInitializeU3Ec__AnonStorey1__ctor_m8E0E7248D227077736A33E327508E616F92FCCF3 (void);
// 0x0000007B System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1::<>m__0()
extern void U3CInitializeU3Ec__AnonStorey1_U3CU3Em__0_m083F4C7632BD37BCDBEB2AC535DED3635645FD24 (void);
// 0x0000007C System.Void GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7::.ctor()
extern void U3CRaiseActionU3Ec__AnonStorey7__ctor_mBA567D38964549F7F8BD83C1B4AD5E28B50AC6E7 (void);
// 0x0000007D System.Void GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7::<>m__0(System.Object)
extern void U3CRaiseActionU3Ec__AnonStorey7_U3CU3Em__0_mB73A491487F829D0FFA3430C2C0C8B701D1C27DE (void);
// 0x0000007E System.Void GoogleMobileAds.Api.ResponseInfo::.ctor(GoogleMobileAds.Common.IResponseInfoClient)
extern void ResponseInfo__ctor_m5CDD2ADA5EF001A9269D2A0BACF9390D0B044DFD (void);
// 0x0000007F System.String GoogleMobileAds.Api.ResponseInfo::ToString()
extern void ResponseInfo_ToString_m937551A7EFF5F3C279EF1FB99B59B8163C7294C6 (void);
// 0x00000080 System.Void GoogleMobileAds.Api.RewardedAd::.ctor(GoogleMobileAds.Common.IRewardedAdClient)
extern void RewardedAd__ctor_m25C00373D1190A96D49AE296BB1F51480DAF6340 (void);
// 0x00000081 System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void RewardedAd_add_OnAdPaid_m292BAC920593D4ABEBEBF931F10D642F37828048 (void);
// 0x00000082 System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void RewardedAd_remove_OnAdPaid_m16C1A0CCC241E747DFA25B1F197CB27FACA8ED4F (void);
// 0x00000083 System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdFullScreenContentOpened(System.Action)
extern void RewardedAd_add_OnAdFullScreenContentOpened_m90888ED613765C8605BB70CFE0B3C00A840666D6 (void);
// 0x00000084 System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdFullScreenContentOpened(System.Action)
extern void RewardedAd_remove_OnAdFullScreenContentOpened_m42DAD1BC8CB02CC59571F4929BCB0F4919BE804B (void);
// 0x00000085 System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdFullScreenContentClosed(System.Action)
extern void RewardedAd_add_OnAdFullScreenContentClosed_m1D80D5D072FDB7CFBB922CFED5992A1A28EB76F7 (void);
// 0x00000086 System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdFullScreenContentClosed(System.Action)
extern void RewardedAd_remove_OnAdFullScreenContentClosed_mDB17DFCE47160D1917E5282AA96D9555CB6AAF30 (void);
// 0x00000087 System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void RewardedAd_add_OnAdFullScreenContentFailed_mFC50DBBC23574A8C6CA195F91259C699559BC0D9 (void);
// 0x00000088 System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void RewardedAd_remove_OnAdFullScreenContentFailed_m27BFDA16911CF1BA56606ACB9B7FBE810CEFF57B (void);
// 0x00000089 System.Void GoogleMobileAds.Api.RewardedAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError>)
extern void RewardedAd_Load_m898A7CFD8143AE42D14C47EC3EAE3CE40B0B5741 (void);
// 0x0000008A System.Boolean GoogleMobileAds.Api.RewardedAd::CanShowAd()
extern void RewardedAd_CanShowAd_m6DFCA02D9029DADED78F05E9590366781FCFFBC6 (void);
// 0x0000008B System.Void GoogleMobileAds.Api.RewardedAd::Show(System.Action`1<GoogleMobileAds.Api.Reward>)
extern void RewardedAd_Show_m63D6659798C98BD34A7F0A1479C0B35E8616A71F (void);
// 0x0000008C System.Void GoogleMobileAds.Api.RewardedAd::Destroy()
extern void RewardedAd_Destroy_mF51E7A752A2B5C378E94553FE48482D19BDF7319 (void);
// 0x0000008D System.Void GoogleMobileAds.Api.RewardedAd::RegisterAdEvents()
extern void RewardedAd_RegisterAdEvents_m87AD72161981214787EB23E154912B7576320C44 (void);
// 0x0000008E System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__0()
extern void RewardedAd_U3CRegisterAdEventsU3Em__0_mBDB96FE2F0D0B64613312D662B0D41C8B7AC947A (void);
// 0x0000008F System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
extern void RewardedAd_U3CRegisterAdEventsU3Em__1_mAD587B14AFE8BD4B01EDF6C3FD4018D2E4DE116B (void);
// 0x00000090 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
extern void RewardedAd_U3CRegisterAdEventsU3Em__2_mB81B3439AA18349F79F5738F64233CB763E9B7CC (void);
// 0x00000091 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
extern void RewardedAd_U3CRegisterAdEventsU3Em__3_m42FE908963AFFF13DBAD5ECB22F5A0BEC0DE24E9 (void);
// 0x00000092 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
extern void RewardedAd_U3CRegisterAdEventsU3Em__4_m27416EDD3572F8060F77B0461F80F172FF2E055E (void);
// 0x00000093 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__5(GoogleMobileAds.Api.AdValue)
extern void RewardedAd_U3CRegisterAdEventsU3Em__5_m42972B2D6E17712FCBF6E1E022875E9B7EC926EA (void);
// 0x00000094 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__6(System.Object,GoogleMobileAds.Api.Reward)
extern void RewardedAd_U3CRegisterAdEventsU3Em__6_m7633930CBB12361843D2AE6B7957C072CAB8F566 (void);
// 0x00000095 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__7()
extern void RewardedAd_U3CRegisterAdEventsU3Em__7_mABE846450E8F394B3B78997B9725549A5BCFC0E2 (void);
// 0x00000096 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__8()
extern void RewardedAd_U3CRegisterAdEventsU3Em__8_m526BCA48602DFCDF581927E7EEDC13F2D8A46432 (void);
// 0x00000097 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__9()
extern void RewardedAd_U3CRegisterAdEventsU3Em__9_m7F7275675F95CE5B63EC9D372E59D6A828389FC1 (void);
// 0x00000098 System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__A()
extern void RewardedAd_U3CRegisterAdEventsU3Em__A_m3DF1400112B787277179F1E09B02C84F923FFE8C (void);
// 0x00000099 System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_m811853FCD1B2648F4113B428D255007083B1DDEA (void);
// 0x0000009A System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m4581D6C04844FB1A0AA18AB75480F7289D2B7BE8 (void);
// 0x0000009B System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m19E293E74E612E80F6D6F1DCF39F804E35F23102 (void);
// 0x0000009C System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::<>m__2()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBC573DE6617412CE7E357B55B5B193145DE26ABC (void);
// 0x0000009D System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_m536F748986C564BA479A6ACEF9EF32F65CCD403D (void);
// 0x0000009E System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1EC3AD4342AF9318AE41E9426F3B5F0F71391F74 (void);
// 0x0000009F System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m0BFAEFFA6CDBF3C353153DAA828D50D74A2801C9 (void);
// 0x000000A0 System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m48DEF449D149433666CB734E67DDD51416E0DC64 (void);
// 0x000000A1 System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3D4D7516A12306F7BDF65F140FEE86BAFC2DC69F (void);
// 0x000000A2 System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m518FD0F0C488E83549321B07B3A73E39E75569A4 (void);
// 0x000000A3 System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mCA18C893E9A99D7080F8FF71D49C20D9C5C7BEBE (void);
// 0x000000A4 System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m674619E42C7991FC207FA28DC577B4946C184960 (void);
// 0x000000A5 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::.ctor(GoogleMobileAds.Common.IRewardedInterstitialAdClient)
extern void RewardedInterstitialAd__ctor_mC4C75695E5B5F02F6D36BAEE3D7257CC5281EDE6 (void);
// 0x000000A6 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void RewardedInterstitialAd_add_OnAdPaid_m61642D408C52ADA6C593D22FD4EE2DCC4126AB4A (void);
// 0x000000A7 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void RewardedInterstitialAd_remove_OnAdPaid_mEC3145923EE1CE4BE356F3D4989B7413A2776464 (void);
// 0x000000A8 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdFullScreenContentOpened(System.Action)
extern void RewardedInterstitialAd_add_OnAdFullScreenContentOpened_m96432FC5B54D014F1238FAEE85F1B7F1CFFA6C33 (void);
// 0x000000A9 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdFullScreenContentOpened(System.Action)
extern void RewardedInterstitialAd_remove_OnAdFullScreenContentOpened_m95AFD690EC9AAF4819A7DF600730BD73AC29A254 (void);
// 0x000000AA System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdFullScreenContentClosed(System.Action)
extern void RewardedInterstitialAd_add_OnAdFullScreenContentClosed_m5D29C92E895DBCD5CED5F7CCB96DBFDCFF66D9AA (void);
// 0x000000AB System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdFullScreenContentClosed(System.Action)
extern void RewardedInterstitialAd_remove_OnAdFullScreenContentClosed_m98FD000B830A28446F251FA10804C7B7DD1A4CD7 (void);
// 0x000000AC System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void RewardedInterstitialAd_add_OnAdFullScreenContentFailed_m00FC7F022CB2D11C511B6FDC5DA7E59704721191 (void);
// 0x000000AD System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
extern void RewardedInterstitialAd_remove_OnAdFullScreenContentFailed_m1530533CFA30FA9724CEEF403B526B7CB27B22AB (void);
// 0x000000AE System.Void GoogleMobileAds.Api.RewardedInterstitialAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError>)
extern void RewardedInterstitialAd_Load_m680D1B87997D82C62B04B27CFB9D40C1E0E1BC97 (void);
// 0x000000AF System.Boolean GoogleMobileAds.Api.RewardedInterstitialAd::CanShowAd()
extern void RewardedInterstitialAd_CanShowAd_m374F4E205E398A48456BEC924CAA5DDD3A443CB0 (void);
// 0x000000B0 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::Show(System.Action`1<GoogleMobileAds.Api.Reward>)
extern void RewardedInterstitialAd_Show_m94EC715CC0BB7273E1207CAD0B5D6D0E2248363E (void);
// 0x000000B1 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::Destroy()
extern void RewardedInterstitialAd_Destroy_m1795BB7A135FE3D473385CD77D9D0FAD9987765E (void);
// 0x000000B2 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::RegisterAdEvents()
extern void RewardedInterstitialAd_RegisterAdEvents_mDDD067D0EA422486BB2BFC590E8D457C5F9EDCD4 (void);
// 0x000000B3 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__0()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m9886A4BC1AD46360532A23EEE8C50BD57F89D1A0 (void);
// 0x000000B4 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m543CC73960970B9313A63C6503E6734B824AB6D6 (void);
// 0x000000B5 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_m1B2DC3A87AC052C420FA21CD7087ED63C13CC6D8 (void);
// 0x000000B6 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m25AFB0FBFF3283D64AFC8D8598A7ED8D50565B45 (void);
// 0x000000B7 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m5A5F20A6DE1364C4418FC45C08523C9C60127663 (void);
// 0x000000B8 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__5(GoogleMobileAds.Api.AdValue)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m7189FFA485880D772791054308DAFB9E764B1896 (void);
// 0x000000B9 System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__6(System.Object,GoogleMobileAds.Api.Reward)
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_mDA873076A460C83926EE0A2B31152F8B6833280A (void);
// 0x000000BA System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__7()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_mDEE50CCD5BA5BB53759827D2DCF0EC56B9788056 (void);
// 0x000000BB System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__8()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mE545D8C8B6907A1C842B464D38482C969C1CDFF8 (void);
// 0x000000BC System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__9()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m2EF9C4DAEC1AB7E310DEEEA400DC2B8E95AE94F3 (void);
// 0x000000BD System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__A()
extern void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m83F066CB15114D6BF8100BE17D85FE5D5BC20351 (void);
// 0x000000BE System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_m72D0D4C8650D2D70F35636BEB5903C21DD1ECB85 (void);
// 0x000000BF System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mECC5EC99E7320E24831FE0BC35E73A02D534CC9E (void);
// 0x000000C0 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mC67F1CCB4AB9D24C87624C3AC7E8672691DA5ED8 (void);
// 0x000000C1 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::<>m__2()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBFE23EE08DE80A726C8A781AA3B9E9116FEE27CB (void);
// 0x000000C2 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_m2C44857B565DBA1D613F287A035A7E68D2E1EA88 (void);
// 0x000000C3 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m289B43C2132F933F53D4AA7B603EB8D7DC80C851 (void);
// 0x000000C4 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m2C583B4F9EFE0317753EB8FDA605DFCB04926F09 (void);
// 0x000000C5 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m3C9928038A84F013D06EE3BCD7E45200A810FB6E (void);
// 0x000000C6 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3E834E57E2F90B6AFA9BF6D2FA8146EB240AA407 (void);
// 0x000000C7 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m3BDDE5CF59A3BDCD7115943D7EB1E51D4EE58775 (void);
// 0x000000C8 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mF94D025E5468729D99B7669F52F788661C5971EA (void);
// 0x000000C9 System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
extern void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m87AC6E348C2E82696AE9F8E152E483A64F53539B (void);
static Il2CppMethodPointer s_methodPointers[201] = 
{
	AdError__ctor_m15B953B294A80F8AF84ECECC285670B48A188F07,
	AdError_GetMessage_m1D90CEA0A4CE678016C5D06D73C3F111D6BA370C,
	AdError_ToString_mD2183315C9A1F6504900AD43F22BB385765FA9F9,
	AppOpenAd__ctor_m8712CC46DE91737B91B231D8A52D26075994A20C,
	AppOpenAd_add_OnAdPaid_mC6A173F1C25378D51E0CA1EE26AC1D175E50918D,
	AppOpenAd_remove_OnAdPaid_mD7AE74221EA71AA54EEAA6F6B958FE68D69087AC,
	AppOpenAd_add_OnAdFullScreenContentOpened_m5BF498515C6EE855420C3C368C62385C2BF7F7BA,
	AppOpenAd_remove_OnAdFullScreenContentOpened_m522500F8D3143BB63D7E6227C22BE875B4E98B6A,
	AppOpenAd_add_OnAdFullScreenContentClosed_m9AFCAA1DF7CF8CCD46B3E62169ADA2B02D49C2CD,
	AppOpenAd_remove_OnAdFullScreenContentClosed_m4C89A4830D5EF6A6A51F0E163ED8048036B01546,
	AppOpenAd_add_OnAdFullScreenContentFailed_m4D52DC4F7D85A0DA305BAAAD27031C528006CE44,
	AppOpenAd_remove_OnAdFullScreenContentFailed_m578143F55D37FE0B507C12EBDF506B9DC2B97570,
	AppOpenAd_Load_mF96F81FEF60491CD62225A17670A8D68D846F276,
	AppOpenAd_CanShowAd_m822270A031DF79CB0ED3AD52240BC4100F0DFAB1,
	AppOpenAd_Show_m2037E04CE3FAE7C677CEA702F39C20390CAB9024,
	AppOpenAd_Destroy_mD1F8CE10A274F9681F7D7FF7A591781F6A5E5351,
	AppOpenAd_RegisterAdEvents_m942A3CA51674726CDD0C3F6A9BDA83C0EF721B53,
	AppOpenAd_U3CRegisterAdEventsU3Em__0_m27A1F8B773ED5CDE8007F65DFDAFD05B60381651,
	AppOpenAd_U3CRegisterAdEventsU3Em__1_m9C4BA7627EDA74D79F3EC01511390054892404AC,
	AppOpenAd_U3CRegisterAdEventsU3Em__2_m699D6740C2F6D1033E611DC12965B18B303A4B2F,
	AppOpenAd_U3CRegisterAdEventsU3Em__3_mB562EF2E861A7E54D09DE5A4A2A0F7157D4A0BAE,
	AppOpenAd_U3CRegisterAdEventsU3Em__4_m58B88A7402B3A92907539DAD22DE4315A4821F14,
	AppOpenAd_U3CRegisterAdEventsU3Em__5_mE9280DC930E3912764FEE6F905CDF95514891997,
	AppOpenAd_U3CRegisterAdEventsU3Em__6_mDE5D7116D371A3AF16B62867E4171868A6CE0578,
	AppOpenAd_U3CRegisterAdEventsU3Em__7_m2DE1DA8A99D930261053267DAACD6E5D5DFD0121,
	AppOpenAd_U3CRegisterAdEventsU3Em__8_m3BB0C197A41B2420EB74BBB2EA03B87401E54923,
	AppOpenAd_U3CRegisterAdEventsU3Em__9_mD5262901450D3C3E94AE21F828AC0C688B6D5E0C,
	U3CLoadU3Ec__AnonStorey0__ctor_mC3285F87E7CF1CDD7E8646879D08F71323457FFB,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m49C42950EE386C1E4C6044643D9521A264974CF3,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mBB7E2D5335953D85AE80D33F59EDC5B3B2A455F3,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mA3783D4C7BBE5618206F759BAA70878E3DDE1950,
	U3CLoadU3Ec__AnonStorey1__ctor_m536EDFA1E591F37667D32F2BB8F2CDA45711279A,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1DFCB8AA6D258C3576F33092D33A9479496A74EB,
	U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m960091194D004208900F150D954F8985848871B1,
	U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m87FEA735EB953A658AE9766E47EBC3B9D318E538,
	U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m05E9BE4D156BBC16C191C438B22867C2C7142317,
	U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mEEB5DB9D7121D67FB5B1CF48EE12E0F96F002A20,
	AppStateEventNotifier__cctor_m741EDC54C3A65311800A5B0AC88FAFF6A93A06B3,
	AppStateEventNotifier_add_AppStateChanged_m11DAB106CCCE3EF78F377722C46ED63BCCF36704,
	AppStateEventNotifier_remove_AppStateChanged_m9C6E767D608F2F692DF3BEA4DB4EA9FE615E5364,
	BannerView__ctor_m3C156986754CF612D73D61061B92BE3468FF6FDC,
	BannerView_add_OnBannerAdLoaded_mF3A6AA44B521D6AFF697B4FDF73C02139F1DEAE6,
	BannerView_remove_OnBannerAdLoaded_mAF3A48277FF13423864DFB8A25DB7688F51BEEA9,
	BannerView_add_OnBannerAdLoadFailed_m90B2772E9E91E19DA78644420A09DB4EC190597C,
	BannerView_remove_OnBannerAdLoadFailed_m3515C651D8F4110C5C4E0CD28C88478619089635,
	BannerView_Destroy_mCD660269DF04D3EA92CC22EC1045B00C1E6CDC01,
	BannerView_GetResponseInfo_m9FB398199E74F3AB52D8BF16121E426E9FD06D4C,
	BannerView_GetHeightInPixels_m7E9AEE3A333CC8370B35DBF8E8C6ED89EFDEB4B8,
	BannerView_GetWidthInPixels_m30250357790E167D473D206B4586A6EA6792A8ED,
	BannerView_LoadAd_m56BF83CC97DE20C40457B9452D0ABEFD97933FFF,
	BannerView_Show_m90ACC1B7ED13065667AB7948722F0B2CBD3A84AE,
	BannerView_Hide_m54630CB9110A9D38DFC00BC047B775C778EA22BD,
	BannerView_ConfigureBannerEvents_m04F0638EF968385C04608F12F9BBFF1811F1437A,
	BannerView_U3CConfigureBannerEventsU3Em__0_m950132C20D9FF852333840439DE8B7E912AED86E,
	BannerView_U3CConfigureBannerEventsU3Em__1_m33285EB72E7B98E9DDE1A768A6E9AED39973E367,
	BannerView_U3CConfigureBannerEventsU3Em__2_mE7CB7EF05F18F046FEFE2051610EADC5F7DB26B1,
	BannerView_U3CConfigureBannerEventsU3Em__3_mA1831E8828F53A1179D0A9FEB046D01B6C6FFC9D,
	BannerView_U3CConfigureBannerEventsU3Em__4_m7145FAC85AB6EEA6289BA99FE9776DFF4CE4B876,
	BannerView_U3CConfigureBannerEventsU3Em__5_m16FA9EC5112461943D6912C09B316B1D0878581C,
	BannerView_U3CConfigureBannerEventsU3Em__6_m6850799F2427F7E1098F13861874B2C678534643,
	BannerView_U3CConfigureBannerEventsU3Em__7_m9AF0E557AEE8A07DD4222E3FCD89B5105BA5D077,
	BannerView_U3CConfigureBannerEventsU3Em__8_m3A928D0457E7D80A84DC9309BD0E3B94EB8ACFC2,
	BannerView_U3CConfigureBannerEventsU3Em__9_mE15693E2BB35B4112B0B3085064CD0AF30DF6071,
	BannerView_U3CConfigureBannerEventsU3Em__A_m6B9B50D77E6054F2863F2A1762CC3C167947F202,
	BannerView_U3CConfigureBannerEventsU3Em__B_m69174F10E32D424CB92DD84998F2FB04CFF5D9F8,
	U3CConfigureBannerEventsU3Ec__AnonStorey0__ctor_m0EA663E8A0E2B5707941BF2F08E4F4B32B72823E,
	U3CConfigureBannerEventsU3Ec__AnonStorey0_U3CU3Em__0_mB8F05C7553294A657561161F2EBEF25060CDB69B,
	U3CConfigureBannerEventsU3Ec__AnonStorey1__ctor_m10CBFCB9AF0E5B4775D5AAFB583930A64958EF88,
	U3CConfigureBannerEventsU3Ec__AnonStorey1_U3CU3Em__0_m9045774A489D908E618F158776547721382934C4,
	InitializationStatus__ctor_m1ECBC8D450C9F80E7D8A8F960722F3ED05AD2F15,
	InterstitialAd__ctor_mF55A705F3B7BB445AE117E57F55A8B8D810854CA,
	InterstitialAd_add_OnAdPaid_m3B50D1F1600BDC3552B5A4908A49F5FEA7FFD1A3,
	InterstitialAd_remove_OnAdPaid_m718C856E51DBFBB28E130954FAE8FD20EC5301ED,
	InterstitialAd_add_OnAdFullScreenContentOpened_mB5E6127049339D2D6BB4ACB8DBBE03A68C4A09AF,
	InterstitialAd_remove_OnAdFullScreenContentOpened_mC6B904E24552B94D85EF1949A475C986F0636179,
	InterstitialAd_add_OnAdFullScreenContentClosed_m752CF051CA5A0DA7F958D5C3A1813F30D09030E7,
	InterstitialAd_remove_OnAdFullScreenContentClosed_m51AAA11D9E43921F8E442BC3CC4888117AA7A0E5,
	InterstitialAd_add_OnAdFullScreenContentFailed_m0A88B3B74B846FCA5CF7BB461E8ABF98FC269C85,
	InterstitialAd_remove_OnAdFullScreenContentFailed_mED3D4983E4BF4AA5FB76264B5BA9693C8BF3D170,
	InterstitialAd_Load_m7582DD601991C297C7E7E61BBE53B639466A73A1,
	InterstitialAd_CanShowAd_m76764B2A422FA2DE78D9E29DE630380BE6E65A0E,
	InterstitialAd_Show_m70188D3BE2543E7A0B58579A5991DA5A9E11CBAB,
	InterstitialAd_Destroy_m5C5C9367FF6A3500C3B61FD2BDBE5B6AF2FF0778,
	InterstitialAd_RegisterAdEvents_m9FB37519C9D7FB812BAA0363DEE49DA56F858309,
	InterstitialAd_U3CRegisterAdEventsU3Em__0_m15F794DFA1B2D15AF3FD46DD0535B35E8E39BD70,
	InterstitialAd_U3CRegisterAdEventsU3Em__1_m77C5D5D530946D3B60F32AE12E82C6730C8CE18F,
	InterstitialAd_U3CRegisterAdEventsU3Em__2_m694739D06B2C43CAF858120C9F931BC94B9E80BF,
	InterstitialAd_U3CRegisterAdEventsU3Em__3_m47237067EBB70CD501CC51CB495AEF7AA00C8194,
	InterstitialAd_U3CRegisterAdEventsU3Em__4_mA938230C35A0425F720B36F5E01E1F1395BEE9C8,
	InterstitialAd_U3CRegisterAdEventsU3Em__5_m457A38FDD193CDBAD62505C2F50A8169E425AD89,
	InterstitialAd_U3CRegisterAdEventsU3Em__6_m6AE447D420E294BFA27C74F33F6AB7AB24C12D25,
	InterstitialAd_U3CRegisterAdEventsU3Em__7_m45C19CF4E9FD6FBD9C1232A71CD3FFA354F4D91E,
	InterstitialAd_U3CRegisterAdEventsU3Em__8_mA46671F488D6BB9275D7AFB99691B279995E82F3,
	InterstitialAd_U3CRegisterAdEventsU3Em__9_m990102B76D6D8BA076566A3057B1EEE9ABAF1759,
	U3CLoadU3Ec__AnonStorey0__ctor_m86A02532C966283C1C91EEBB986FAF423FB6FC5C,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m631331ABD2466F9B2AE59D8DFF7723E436983B20,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mCDAD16B0470ADCBA74835F69B270E5174950D92B,
	U3CLoadU3Ec__AnonStorey1__ctor_mE9E302BFC9D729F14AB85D057E5C81233F4C6AED,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m64E945F2EC125802BFC95173075BF6ABE5438E52,
	U3CLoadU3Ec__AnonStorey2__ctor_m97059AC37568D3E45311BFEAC1BA0370E4345E2A,
	U3CLoadU3Ec__AnonStorey2_U3CU3Em__0_m0132A6A39D8BDC7093D0C265AA5BCFCE1B8417DC,
	U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m9A8FC0128A1F434EE1B3A709FEE69705B9424E5B,
	U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mA89B79A7FE9DB7FBB22C1E1B20F33DBA46B20D7F,
	U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mDB67C29C7A47AEDD47CF4908138B0EEBA8BCA5F9,
	U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_mCE5D71C37A91D8E6C882E40EECBA76A8EF23E74B,
	LoadAdError__ctor_m616B349928B08AD5C222B3136301B43465DE2A8F,
	LoadAdError_ToString_mC2DBA6C870EEBF993492FF04A29F4AE567D45C53,
	MobileAds__cctor_mB05D140F842D0D647DE6DBA9F70D77B8565984F9,
	MobileAds__ctor_mE209BFED2C4B3EDEA77A1D92FA94E692454F9B10,
	MobileAds_get_Instance_m90A099F9597B22FEEC2CB4739F31219727C3614A,
	MobileAds_get_RaiseAdEventsOnUnityMainThread_m42F779998BC24C982D13184FF938C9F78939D5B5,
	MobileAds_set_RaiseAdEventsOnUnityMainThread_mCC9684511E440AD0F0D20CA15E36AE96BB552A9C,
	MobileAds_Initialize_m9151E3C38E0FAD4439DA5020A217BAB90ABB76AD,
	MobileAds_SetRequestConfiguration_m1F0110671038CF67255ADAA3CFDB4CFC6E1E7AF5,
	MobileAds_SetiOSAppPauseOnBackground_m2626F076EEDF748681C27AC832E79AC9E93756E7,
	MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E,
	MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2,
	MobileAds_SetUnityMainThreadSynchronizationContext_mC7C80D814660CF6742100D36D03F879120BBE27F,
	MobileAds_GetMobileAdsClient_m1A2ADAC0C61D673C13E8B06068EDFBACC58FCD14,
	U3CInitializeU3Ec__AnonStorey0__ctor_m6DFCA2A06E9120467C5E1B941B84435F48BC9D74,
	U3CInitializeU3Ec__AnonStorey0_U3CU3Em__0_m8D6DA3278704BF784A5424EF89AAAC90A79F4414,
	U3CInitializeU3Ec__AnonStorey1__ctor_m8E0E7248D227077736A33E327508E616F92FCCF3,
	U3CInitializeU3Ec__AnonStorey1_U3CU3Em__0_m083F4C7632BD37BCDBEB2AC535DED3635645FD24,
	U3CRaiseActionU3Ec__AnonStorey7__ctor_mBA567D38964549F7F8BD83C1B4AD5E28B50AC6E7,
	U3CRaiseActionU3Ec__AnonStorey7_U3CU3Em__0_mB73A491487F829D0FFA3430C2C0C8B701D1C27DE,
	ResponseInfo__ctor_m5CDD2ADA5EF001A9269D2A0BACF9390D0B044DFD,
	ResponseInfo_ToString_m937551A7EFF5F3C279EF1FB99B59B8163C7294C6,
	RewardedAd__ctor_m25C00373D1190A96D49AE296BB1F51480DAF6340,
	RewardedAd_add_OnAdPaid_m292BAC920593D4ABEBEBF931F10D642F37828048,
	RewardedAd_remove_OnAdPaid_m16C1A0CCC241E747DFA25B1F197CB27FACA8ED4F,
	RewardedAd_add_OnAdFullScreenContentOpened_m90888ED613765C8605BB70CFE0B3C00A840666D6,
	RewardedAd_remove_OnAdFullScreenContentOpened_m42DAD1BC8CB02CC59571F4929BCB0F4919BE804B,
	RewardedAd_add_OnAdFullScreenContentClosed_m1D80D5D072FDB7CFBB922CFED5992A1A28EB76F7,
	RewardedAd_remove_OnAdFullScreenContentClosed_mDB17DFCE47160D1917E5282AA96D9555CB6AAF30,
	RewardedAd_add_OnAdFullScreenContentFailed_mFC50DBBC23574A8C6CA195F91259C699559BC0D9,
	RewardedAd_remove_OnAdFullScreenContentFailed_m27BFDA16911CF1BA56606ACB9B7FBE810CEFF57B,
	RewardedAd_Load_m898A7CFD8143AE42D14C47EC3EAE3CE40B0B5741,
	RewardedAd_CanShowAd_m6DFCA02D9029DADED78F05E9590366781FCFFBC6,
	RewardedAd_Show_m63D6659798C98BD34A7F0A1479C0B35E8616A71F,
	RewardedAd_Destroy_mF51E7A752A2B5C378E94553FE48482D19BDF7319,
	RewardedAd_RegisterAdEvents_m87AD72161981214787EB23E154912B7576320C44,
	RewardedAd_U3CRegisterAdEventsU3Em__0_mBDB96FE2F0D0B64613312D662B0D41C8B7AC947A,
	RewardedAd_U3CRegisterAdEventsU3Em__1_mAD587B14AFE8BD4B01EDF6C3FD4018D2E4DE116B,
	RewardedAd_U3CRegisterAdEventsU3Em__2_mB81B3439AA18349F79F5738F64233CB763E9B7CC,
	RewardedAd_U3CRegisterAdEventsU3Em__3_m42FE908963AFFF13DBAD5ECB22F5A0BEC0DE24E9,
	RewardedAd_U3CRegisterAdEventsU3Em__4_m27416EDD3572F8060F77B0461F80F172FF2E055E,
	RewardedAd_U3CRegisterAdEventsU3Em__5_m42972B2D6E17712FCBF6E1E022875E9B7EC926EA,
	RewardedAd_U3CRegisterAdEventsU3Em__6_m7633930CBB12361843D2AE6B7957C072CAB8F566,
	RewardedAd_U3CRegisterAdEventsU3Em__7_mABE846450E8F394B3B78997B9725549A5BCFC0E2,
	RewardedAd_U3CRegisterAdEventsU3Em__8_m526BCA48602DFCDF581927E7EEDC13F2D8A46432,
	RewardedAd_U3CRegisterAdEventsU3Em__9_m7F7275675F95CE5B63EC9D372E59D6A828389FC1,
	RewardedAd_U3CRegisterAdEventsU3Em__A_m3DF1400112B787277179F1E09B02C84F923FFE8C,
	U3CLoadU3Ec__AnonStorey0__ctor_m811853FCD1B2648F4113B428D255007083B1DDEA,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m4581D6C04844FB1A0AA18AB75480F7289D2B7BE8,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m19E293E74E612E80F6D6F1DCF39F804E35F23102,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBC573DE6617412CE7E357B55B5B193145DE26ABC,
	U3CLoadU3Ec__AnonStorey1__ctor_m536F748986C564BA479A6ACEF9EF32F65CCD403D,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1EC3AD4342AF9318AE41E9426F3B5F0F71391F74,
	U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m0BFAEFFA6CDBF3C353153DAA828D50D74A2801C9,
	U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m48DEF449D149433666CB734E67DDD51416E0DC64,
	U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3D4D7516A12306F7BDF65F140FEE86BAFC2DC69F,
	U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m518FD0F0C488E83549321B07B3A73E39E75569A4,
	U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mCA18C893E9A99D7080F8FF71D49C20D9C5C7BEBE,
	U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m674619E42C7991FC207FA28DC577B4946C184960,
	RewardedInterstitialAd__ctor_mC4C75695E5B5F02F6D36BAEE3D7257CC5281EDE6,
	RewardedInterstitialAd_add_OnAdPaid_m61642D408C52ADA6C593D22FD4EE2DCC4126AB4A,
	RewardedInterstitialAd_remove_OnAdPaid_mEC3145923EE1CE4BE356F3D4989B7413A2776464,
	RewardedInterstitialAd_add_OnAdFullScreenContentOpened_m96432FC5B54D014F1238FAEE85F1B7F1CFFA6C33,
	RewardedInterstitialAd_remove_OnAdFullScreenContentOpened_m95AFD690EC9AAF4819A7DF600730BD73AC29A254,
	RewardedInterstitialAd_add_OnAdFullScreenContentClosed_m5D29C92E895DBCD5CED5F7CCB96DBFDCFF66D9AA,
	RewardedInterstitialAd_remove_OnAdFullScreenContentClosed_m98FD000B830A28446F251FA10804C7B7DD1A4CD7,
	RewardedInterstitialAd_add_OnAdFullScreenContentFailed_m00FC7F022CB2D11C511B6FDC5DA7E59704721191,
	RewardedInterstitialAd_remove_OnAdFullScreenContentFailed_m1530533CFA30FA9724CEEF403B526B7CB27B22AB,
	RewardedInterstitialAd_Load_m680D1B87997D82C62B04B27CFB9D40C1E0E1BC97,
	RewardedInterstitialAd_CanShowAd_m374F4E205E398A48456BEC924CAA5DDD3A443CB0,
	RewardedInterstitialAd_Show_m94EC715CC0BB7273E1207CAD0B5D6D0E2248363E,
	RewardedInterstitialAd_Destroy_m1795BB7A135FE3D473385CD77D9D0FAD9987765E,
	RewardedInterstitialAd_RegisterAdEvents_mDDD067D0EA422486BB2BFC590E8D457C5F9EDCD4,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m9886A4BC1AD46360532A23EEE8C50BD57F89D1A0,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m543CC73960970B9313A63C6503E6734B824AB6D6,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_m1B2DC3A87AC052C420FA21CD7087ED63C13CC6D8,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m25AFB0FBFF3283D64AFC8D8598A7ED8D50565B45,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m5A5F20A6DE1364C4418FC45C08523C9C60127663,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m7189FFA485880D772791054308DAFB9E764B1896,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_mDA873076A460C83926EE0A2B31152F8B6833280A,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_mDEE50CCD5BA5BB53759827D2DCF0EC56B9788056,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mE545D8C8B6907A1C842B464D38482C969C1CDFF8,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m2EF9C4DAEC1AB7E310DEEEA400DC2B8E95AE94F3,
	RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m83F066CB15114D6BF8100BE17D85FE5D5BC20351,
	U3CLoadU3Ec__AnonStorey0__ctor_m72D0D4C8650D2D70F35636BEB5903C21DD1ECB85,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mECC5EC99E7320E24831FE0BC35E73A02D534CC9E,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mC67F1CCB4AB9D24C87624C3AC7E8672691DA5ED8,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBFE23EE08DE80A726C8A781AA3B9E9116FEE27CB,
	U3CLoadU3Ec__AnonStorey1__ctor_m2C44857B565DBA1D613F287A035A7E68D2E1EA88,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m289B43C2132F933F53D4AA7B603EB8D7DC80C851,
	U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m2C583B4F9EFE0317753EB8FDA605DFCB04926F09,
	U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m3C9928038A84F013D06EE3BCD7E45200A810FB6E,
	U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3E834E57E2F90B6AFA9BF6D2FA8146EB240AA407,
	U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m3BDDE5CF59A3BDCD7115943D7EB1E51D4EE58775,
	U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mF94D025E5468729D99B7669F52F788661C5971EA,
	U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m87AC6E348C2E82696AE9F8E152E483A64F53539B,
};
static const int32_t s_InvokerIndices[201] = 
{
	4809,
	5913,
	5913,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	7742,
	5818,
	6037,
	6037,
	6037,
	6037,
	2708,
	2708,
	2708,
	2708,
	4809,
	6037,
	6037,
	6037,
	6037,
	6037,
	2708,
	2708,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	9162,
	8973,
	8973,
	1412,
	4809,
	4809,
	4809,
	4809,
	6037,
	5913,
	5968,
	5968,
	4809,
	6037,
	6037,
	6037,
	2708,
	2708,
	2708,
	2708,
	4809,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	7742,
	5818,
	6037,
	6037,
	6037,
	6037,
	2708,
	2708,
	2708,
	2708,
	4809,
	6037,
	6037,
	6037,
	6037,
	6037,
	2708,
	2708,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	4809,
	5913,
	9162,
	6037,
	9116,
	9083,
	8961,
	8973,
	8973,
	8961,
	9116,
	8973,
	9162,
	9116,
	6037,
	4809,
	6037,
	6037,
	6037,
	4809,
	4809,
	5913,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	7742,
	5818,
	4809,
	6037,
	6037,
	6037,
	2708,
	2708,
	2708,
	2708,
	4809,
	2708,
	6037,
	6037,
	6037,
	6037,
	6037,
	2708,
	2708,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	7742,
	5818,
	4809,
	6037,
	6037,
	6037,
	2708,
	2708,
	2708,
	2708,
	4809,
	2708,
	6037,
	6037,
	6037,
	6037,
	6037,
	2708,
	2708,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_CodeGenModule = 
{
	"GoogleMobileAds.dll",
	201,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
