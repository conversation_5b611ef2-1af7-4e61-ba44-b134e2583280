﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void DG.Tweening.DOTweenVisualManager::Awake()
extern void DOTweenVisualManager_Awake_mA345901CF7D8BB2814FB13DC5508F66C25948CDA (void);
// 0x00000002 System.Void DG.Tweening.DOTweenVisualManager::Update()
extern void DOTweenVisualManager_Update_m577272759E1EBE625198034C066C1C3286762B20 (void);
// 0x00000003 System.Void DG.Tweening.DOTweenVisualManager::OnEnable()
extern void DOTweenVisualManager_OnEnable_m6A7CBF19C18F2BE6B7F43E0CB55DD7776B62ACBB (void);
// 0x00000004 System.Void DG.Tweening.DOTweenVisualManager::OnDisable()
extern void DOTweenVisualManager_OnDisable_mCF0717FD910F6847A7F88BA458913902E495DCFE (void);
// 0x00000005 System.Void DG.Tweening.DOTweenVisualManager::.ctor()
extern void DOTweenVisualManager__ctor_m1E5F230BCD35D65DF6745409DBA48F253D62DFDC (void);
// 0x00000006 System.Void DG.Tweening.DOTweenPath::add_OnReset(System.Action`1<DG.Tweening.DOTweenPath>)
extern void DOTweenPath_add_OnReset_m21CBBDBCFB0F1B526F87BBEC72CE33E5E5B2401E (void);
// 0x00000007 System.Void DG.Tweening.DOTweenPath::remove_OnReset(System.Action`1<DG.Tweening.DOTweenPath>)
extern void DOTweenPath_remove_OnReset_m1BCDCFE6545C6CD6E047FB1E939A6C886F785074 (void);
// 0x00000008 System.Void DG.Tweening.DOTweenPath::Dispatch_OnReset(DG.Tweening.DOTweenPath)
extern void DOTweenPath_Dispatch_OnReset_mD6B0898A2DA53E54CCFA2163150BF31169AF92B8 (void);
// 0x00000009 System.Void DG.Tweening.DOTweenPath::Awake()
extern void DOTweenPath_Awake_m7D6C11F58AD1E1DA053BBE8DAFCAA1DACFACB7B8 (void);
// 0x0000000A System.Void DG.Tweening.DOTweenPath::Reset()
extern void DOTweenPath_Reset_m155EC77D1DF99DDAFA0DE3A0795075CE7CF31EEC (void);
// 0x0000000B System.Void DG.Tweening.DOTweenPath::OnDestroy()
extern void DOTweenPath_OnDestroy_m018214A578ABF8AEE463C2F1DA71D9E2D475534F (void);
// 0x0000000C System.Void DG.Tweening.DOTweenPath::DOPlay()
extern void DOTweenPath_DOPlay_m29FCC0ADB694014D7CFCAD543AC3A47FC0DB6F7E (void);
// 0x0000000D System.Void DG.Tweening.DOTweenPath::DOPlayById(System.String)
extern void DOTweenPath_DOPlayById_mEF04EB9020710F3BBD6FDE0A4FA97891E59D6134 (void);
// 0x0000000E System.Void DG.Tweening.DOTweenPath::DOPlayAllById(System.String)
extern void DOTweenPath_DOPlayAllById_m2B4AD51FDC7138253EB593E94C7C060BE640610A (void);
// 0x0000000F System.Void DG.Tweening.DOTweenPath::DOPlayBackwards()
extern void DOTweenPath_DOPlayBackwards_mF2F621EF93318FD525EBE7F353542EFA0099B5CC (void);
// 0x00000010 System.Void DG.Tweening.DOTweenPath::DOPlayForward()
extern void DOTweenPath_DOPlayForward_mBD7112205D6CD6B65FC4759D17CDC7C3D3E834A6 (void);
// 0x00000011 System.Void DG.Tweening.DOTweenPath::DOPause()
extern void DOTweenPath_DOPause_mD0B8512BAFA8C47222BED72F9F8856EEC877C7F9 (void);
// 0x00000012 System.Void DG.Tweening.DOTweenPath::DOTogglePause()
extern void DOTweenPath_DOTogglePause_m9243080337815CDAFD0CCC1C6EE82EDF459EA195 (void);
// 0x00000013 System.Void DG.Tweening.DOTweenPath::DORewind()
extern void DOTweenPath_DORewind_m1E2BF4C0B521CD02970109ECC446332EC24F92AA (void);
// 0x00000014 System.Void DG.Tweening.DOTweenPath::DORestart()
extern void DOTweenPath_DORestart_m0B76F7F443A03DE1782C9108D8ED8C102984A85E (void);
// 0x00000015 System.Void DG.Tweening.DOTweenPath::DORestart(System.Boolean)
extern void DOTweenPath_DORestart_m6DFBC7C0A88F25D4F26A5F5A981F6F11FD36EB63 (void);
// 0x00000016 System.Void DG.Tweening.DOTweenPath::DOComplete()
extern void DOTweenPath_DOComplete_mA143B07E3B1CE7E45AD645254E3F9C6719B750F4 (void);
// 0x00000017 System.Void DG.Tweening.DOTweenPath::DOGotoAndPause(System.Single)
extern void DOTweenPath_DOGotoAndPause_m11523B1464C7A70B111D81A893C0EA0F69E2D60B (void);
// 0x00000018 System.Void DG.Tweening.DOTweenPath::DOGotoAndPlay(System.Single)
extern void DOTweenPath_DOGotoAndPlay_m86C356DED5F1222476489A424D58C65AEF9FE9E0 (void);
// 0x00000019 System.Void DG.Tweening.DOTweenPath::DOGoto(System.Single,System.Boolean)
extern void DOTweenPath_DOGoto_m883C407790A842C53A3F0536BE50924B98BEC76A (void);
// 0x0000001A System.Void DG.Tweening.DOTweenPath::DOKill()
extern void DOTweenPath_DOKill_m3F555B931C542E6105BB4408E70E1D793643262F (void);
// 0x0000001B System.Void DG.Tweening.DOTweenPath::DOKillAllById(System.String)
extern void DOTweenPath_DOKillAllById_mFF8BDA7CF574DAFC9F2FC8A580DEB6E9D0F44EC9 (void);
// 0x0000001C DG.Tweening.Tween DG.Tweening.DOTweenPath::GetTween()
extern void DOTweenPath_GetTween_mE609C8EF202A302ED7F01A7F5A9AE45736E4517A (void);
// 0x0000001D UnityEngine.Vector3[] DG.Tweening.DOTweenPath::GetDrawPoints()
extern void DOTweenPath_GetDrawPoints_mA277CBD6C8ABA5134F0D546B2BF25C2237486C6E (void);
// 0x0000001E UnityEngine.Vector3[] DG.Tweening.DOTweenPath::GetFullWps()
extern void DOTweenPath_GetFullWps_mDA2E0CD673C3565F7F9FBCC970EF84423E98BD5C (void);
// 0x0000001F System.Void DG.Tweening.DOTweenPath::ReEvaluateRelativeTween()
extern void DOTweenPath_ReEvaluateRelativeTween_m0BC03533A30F6D8D310E074BA90A624D20F0D3B4 (void);
// 0x00000020 System.Void DG.Tweening.DOTweenPath::.ctor()
extern void DOTweenPath__ctor_m51D5DE785B9613E96920B32CCCD4D53526C098C8 (void);
// 0x00000021 System.Void DG.Tweening.DOTweenPath::<Awake>b__44_0()
extern void DOTweenPath_U3CAwakeU3Eb__44_0_m2E87212F9F04919063EF4233D57D81D53A15C7E5 (void);
// 0x00000022 System.Void DG.Tweening.Plugins.SpiralOptions::Reset()
extern void SpiralOptions_Reset_m9278DCF7AB054A5C2D8CF9679ED7A6F411AD9856 (void);
// 0x00000023 System.Void DG.Tweening.Plugins.SpiralPlugin::Reset(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.SpiralOptions>)
extern void SpiralPlugin_Reset_m319A32901733352790FAF1A66AC4E776DDA58233 (void);
// 0x00000024 System.Void DG.Tweening.Plugins.SpiralPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.SpiralOptions>,System.Boolean)
extern void SpiralPlugin_SetFrom_mF4053B0805926521D8DEC23E170B69E2BA4875AA (void);
// 0x00000025 System.Void DG.Tweening.Plugins.SpiralPlugin::SetFrom(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.SpiralOptions>,UnityEngine.Vector3,System.Boolean,System.Boolean)
extern void SpiralPlugin_SetFrom_m58FFC5D8F892025A3C4E14C38D76015E51587825 (void);
// 0x00000026 DG.Tweening.Plugins.Core.ABSTweenPlugin`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.SpiralOptions> DG.Tweening.Plugins.SpiralPlugin::Get()
extern void SpiralPlugin_Get_mEF40CC56A5043E8122C572AFCBC224BA925BC326 (void);
// 0x00000027 UnityEngine.Vector3 DG.Tweening.Plugins.SpiralPlugin::ConvertToStartValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.SpiralOptions>,UnityEngine.Vector3)
extern void SpiralPlugin_ConvertToStartValue_m477DB84B81F2B437E7AF9B9084CAB25CCEF4C30B (void);
// 0x00000028 System.Void DG.Tweening.Plugins.SpiralPlugin::SetRelativeEndValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.SpiralOptions>)
extern void SpiralPlugin_SetRelativeEndValue_m77FD72DFFA7DC14B0D0D57D111301671EDBC6AD9 (void);
// 0x00000029 System.Void DG.Tweening.Plugins.SpiralPlugin::SetChangeValue(DG.Tweening.Core.TweenerCore`3<UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.SpiralOptions>)
extern void SpiralPlugin_SetChangeValue_m11B21CBFB6515DCA1F88EFCE169C3BD8BC23FE43 (void);
// 0x0000002A System.Single DG.Tweening.Plugins.SpiralPlugin::GetSpeedBasedDuration(DG.Tweening.Plugins.SpiralOptions,System.Single,UnityEngine.Vector3)
extern void SpiralPlugin_GetSpeedBasedDuration_m9411E3272E6440BE242640F06F3A750978A9250D (void);
// 0x0000002B System.Void DG.Tweening.Plugins.SpiralPlugin::EvaluateAndApply(DG.Tweening.Plugins.SpiralOptions,DG.Tweening.Tween,System.Boolean,DG.Tweening.Core.DOGetter`1<UnityEngine.Vector3>,DG.Tweening.Core.DOSetter`1<UnityEngine.Vector3>,System.Single,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Boolean,System.Int32,DG.Tweening.Core.Enums.UpdateNotice)
extern void SpiralPlugin_EvaluateAndApply_m49DBCEF982963D510C824FC336962C1330278AF6 (void);
// 0x0000002C System.Void DG.Tweening.Plugins.SpiralPlugin::.ctor()
extern void SpiralPlugin__ctor_m9ACFE0F9B14BBBD77A9570420667150892067033 (void);
// 0x0000002D System.Void DG.Tweening.Plugins.SpiralPlugin::.cctor()
extern void SpiralPlugin__cctor_m0DAEB3A86F770B21433A721D2ACD3A33A3F6DAB4 (void);
// 0x0000002E System.Void DG.Tweening.Core.ABSAnimationComponent::DOPlay()
// 0x0000002F System.Void DG.Tweening.Core.ABSAnimationComponent::DOPlayBackwards()
// 0x00000030 System.Void DG.Tweening.Core.ABSAnimationComponent::DOPlayForward()
// 0x00000031 System.Void DG.Tweening.Core.ABSAnimationComponent::DOPause()
// 0x00000032 System.Void DG.Tweening.Core.ABSAnimationComponent::DOTogglePause()
// 0x00000033 System.Void DG.Tweening.Core.ABSAnimationComponent::DORewind()
// 0x00000034 System.Void DG.Tweening.Core.ABSAnimationComponent::DORestart()
// 0x00000035 System.Void DG.Tweening.Core.ABSAnimationComponent::DORestart(System.Boolean)
// 0x00000036 System.Void DG.Tweening.Core.ABSAnimationComponent::DOComplete()
// 0x00000037 System.Void DG.Tweening.Core.ABSAnimationComponent::DOGotoAndPause(System.Single)
// 0x00000038 System.Void DG.Tweening.Core.ABSAnimationComponent::DOGotoAndPlay(System.Single)
// 0x00000039 System.Void DG.Tweening.Core.ABSAnimationComponent::DOKill()
// 0x0000003A System.Void DG.Tweening.Core.ABSAnimationComponent::.ctor()
extern void ABSAnimationComponent__ctor_mF2DC2EF90DDA4C57EC4858124EEEE03FE4CBB328 (void);
static Il2CppMethodPointer s_methodPointers[58] = 
{
	DOTweenVisualManager_Awake_mA345901CF7D8BB2814FB13DC5508F66C25948CDA,
	DOTweenVisualManager_Update_m577272759E1EBE625198034C066C1C3286762B20,
	DOTweenVisualManager_OnEnable_m6A7CBF19C18F2BE6B7F43E0CB55DD7776B62ACBB,
	DOTweenVisualManager_OnDisable_mCF0717FD910F6847A7F88BA458913902E495DCFE,
	DOTweenVisualManager__ctor_m1E5F230BCD35D65DF6745409DBA48F253D62DFDC,
	DOTweenPath_add_OnReset_m21CBBDBCFB0F1B526F87BBEC72CE33E5E5B2401E,
	DOTweenPath_remove_OnReset_m1BCDCFE6545C6CD6E047FB1E939A6C886F785074,
	DOTweenPath_Dispatch_OnReset_mD6B0898A2DA53E54CCFA2163150BF31169AF92B8,
	DOTweenPath_Awake_m7D6C11F58AD1E1DA053BBE8DAFCAA1DACFACB7B8,
	DOTweenPath_Reset_m155EC77D1DF99DDAFA0DE3A0795075CE7CF31EEC,
	DOTweenPath_OnDestroy_m018214A578ABF8AEE463C2F1DA71D9E2D475534F,
	DOTweenPath_DOPlay_m29FCC0ADB694014D7CFCAD543AC3A47FC0DB6F7E,
	DOTweenPath_DOPlayById_mEF04EB9020710F3BBD6FDE0A4FA97891E59D6134,
	DOTweenPath_DOPlayAllById_m2B4AD51FDC7138253EB593E94C7C060BE640610A,
	DOTweenPath_DOPlayBackwards_mF2F621EF93318FD525EBE7F353542EFA0099B5CC,
	DOTweenPath_DOPlayForward_mBD7112205D6CD6B65FC4759D17CDC7C3D3E834A6,
	DOTweenPath_DOPause_mD0B8512BAFA8C47222BED72F9F8856EEC877C7F9,
	DOTweenPath_DOTogglePause_m9243080337815CDAFD0CCC1C6EE82EDF459EA195,
	DOTweenPath_DORewind_m1E2BF4C0B521CD02970109ECC446332EC24F92AA,
	DOTweenPath_DORestart_m0B76F7F443A03DE1782C9108D8ED8C102984A85E,
	DOTweenPath_DORestart_m6DFBC7C0A88F25D4F26A5F5A981F6F11FD36EB63,
	DOTweenPath_DOComplete_mA143B07E3B1CE7E45AD645254E3F9C6719B750F4,
	DOTweenPath_DOGotoAndPause_m11523B1464C7A70B111D81A893C0EA0F69E2D60B,
	DOTweenPath_DOGotoAndPlay_m86C356DED5F1222476489A424D58C65AEF9FE9E0,
	DOTweenPath_DOGoto_m883C407790A842C53A3F0536BE50924B98BEC76A,
	DOTweenPath_DOKill_m3F555B931C542E6105BB4408E70E1D793643262F,
	DOTweenPath_DOKillAllById_mFF8BDA7CF574DAFC9F2FC8A580DEB6E9D0F44EC9,
	DOTweenPath_GetTween_mE609C8EF202A302ED7F01A7F5A9AE45736E4517A,
	DOTweenPath_GetDrawPoints_mA277CBD6C8ABA5134F0D546B2BF25C2237486C6E,
	DOTweenPath_GetFullWps_mDA2E0CD673C3565F7F9FBCC970EF84423E98BD5C,
	DOTweenPath_ReEvaluateRelativeTween_m0BC03533A30F6D8D310E074BA90A624D20F0D3B4,
	DOTweenPath__ctor_m51D5DE785B9613E96920B32CCCD4D53526C098C8,
	DOTweenPath_U3CAwakeU3Eb__44_0_m2E87212F9F04919063EF4233D57D81D53A15C7E5,
	SpiralOptions_Reset_m9278DCF7AB054A5C2D8CF9679ED7A6F411AD9856,
	SpiralPlugin_Reset_m319A32901733352790FAF1A66AC4E776DDA58233,
	SpiralPlugin_SetFrom_mF4053B0805926521D8DEC23E170B69E2BA4875AA,
	SpiralPlugin_SetFrom_m58FFC5D8F892025A3C4E14C38D76015E51587825,
	SpiralPlugin_Get_mEF40CC56A5043E8122C572AFCBC224BA925BC326,
	SpiralPlugin_ConvertToStartValue_m477DB84B81F2B437E7AF9B9084CAB25CCEF4C30B,
	SpiralPlugin_SetRelativeEndValue_m77FD72DFFA7DC14B0D0D57D111301671EDBC6AD9,
	SpiralPlugin_SetChangeValue_m11B21CBFB6515DCA1F88EFCE169C3BD8BC23FE43,
	SpiralPlugin_GetSpeedBasedDuration_m9411E3272E6440BE242640F06F3A750978A9250D,
	SpiralPlugin_EvaluateAndApply_m49DBCEF982963D510C824FC336962C1330278AF6,
	SpiralPlugin__ctor_m9ACFE0F9B14BBBD77A9570420667150892067033,
	SpiralPlugin__cctor_m0DAEB3A86F770B21433A721D2ACD3A33A3F6DAB4,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ABSAnimationComponent__ctor_mF2DC2EF90DDA4C57EC4858124EEEE03FE4CBB328,
};
extern void SpiralOptions_Reset_m9278DCF7AB054A5C2D8CF9679ED7A6F411AD9856_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[1] = 
{
	{ 0x06000022, SpiralOptions_Reset_m9278DCF7AB054A5C2D8CF9679ED7A6F411AD9856_AdjustorThunk },
};
static const int32_t s_InvokerIndices[58] = 
{
	6037,
	6037,
	6037,
	6037,
	6037,
	8973,
	8973,
	8973,
	6037,
	6037,
	6037,
	6037,
	4809,
	4809,
	6037,
	6037,
	6037,
	6037,
	6037,
	6037,
	4715,
	6037,
	4856,
	4856,
	2755,
	6037,
	4809,
	5913,
	5913,
	5913,
	6037,
	6037,
	6037,
	6037,
	4809,
	2692,
	975,
	9116,
	2167,
	4809,
	4809,
	1248,
	23,
	6037,
	9162,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6037,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_DOTweenPro_CodeGenModule;
const Il2CppCodeGenModule g_DOTweenPro_CodeGenModule = 
{
	"DOTweenPro.dll",
	58,
	s_methodPointers,
	1,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
