﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::.ctor()
extern void ConsentFormClient__ctor_m7E1B3CCF1A755D2E47B0CBE4E75D3B5B852B1422 (void);
// 0x00000002 GoogleMobileAds.Ump.Android.ConsentFormClient GoogleMobileAds.Ump.Android.ConsentFormClient::get_Instance()
extern void ConsentFormClient_get_Instance_m3A7328EFC16FA4907777C349E6A54B055CC2B8E1 (void);
// 0x00000003 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::Load(System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentFormClient_Load_mF1F4C123930E94CBD1429D69F8B57C7AA37E6BEC (void);
// 0x00000004 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::Show(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentFormClient_Show_m37767BC4A288BAA7EF92936E32A3FFBDC4E401BC (void);
// 0x00000005 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::LoadAndShowConsentFormIfRequired(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentFormClient_LoadAndShowConsentFormIfRequired_m4EF9A327D739F2A54C135B3493FF8DEBF49F035C (void);
// 0x00000006 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::ShowPrivacyOptionsForm(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentFormClient_ShowPrivacyOptionsForm_m00D5AA1D1BAC158F72CBD5F885C6F1EA4E77D38A (void);
// 0x00000007 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::onConsentFormDismissed(UnityEngine.AndroidJavaObject)
extern void ConsentFormClient_onConsentFormDismissed_m6E870F79B3318CF234F2E647293F211642635B39 (void);
// 0x00000008 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::.cctor()
extern void ConsentFormClient__cctor_m1554EAF6568E339F435C13AC2ED1CFA8EB42E2FF (void);
// 0x00000009 System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::<Show>m__0()
extern void ConsentFormClient_U3CShowU3Em__0_m3625CB74A5693AEB1032C8183EB59B91011E4E62 (void);
// 0x0000000A System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::<LoadAndShowConsentFormIfRequired>m__1()
extern void ConsentFormClient_U3CLoadAndShowConsentFormIfRequiredU3Em__1_m0DA0DE44426147017D499965D74177821B168582 (void);
// 0x0000000B System.Void GoogleMobileAds.Ump.Android.ConsentFormClient::<ShowPrivacyOptionsForm>m__2()
extern void ConsentFormClient_U3CShowPrivacyOptionsFormU3Em__2_m35D1DECABF64D9C0FEB7FF89AFC2D056475B4048 (void);
// 0x0000000C System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_mFB78BF651FFE5D9720184ABAFAEC338F8EC8884E (void);
// 0x0000000D System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::<>m__0(UnityEngine.AndroidJavaObject)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mF5112631374CFAC58737B64C5F7BF82BFE7DA5BF (void);
// 0x0000000E System.Void GoogleMobileAds.Ump.Android.ConsentFormClient/<Load>c__AnonStorey0::<>m__1()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mEA45C5564FA6D738F05920AEBEAE822EF71C1E35 (void);
// 0x0000000F System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::.ctor()
extern void ConsentInformationClient__ctor_mA617BBC7ED7A68D33788671BE7E23E628EB557A7 (void);
// 0x00000010 GoogleMobileAds.Ump.Android.ConsentInformationClient GoogleMobileAds.Ump.Android.ConsentInformationClient::get_Instance()
extern void ConsentInformationClient_get_Instance_m9B519B54A2628324821181E0A70EDB0A741B69C6 (void);
// 0x00000011 System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::Update(GoogleMobileAds.Ump.Api.ConsentRequestParameters,System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentInformationClient_Update_m720A6D2A77AEDA48A334C4C52B2EA606BF92D1AD (void);
// 0x00000012 System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::Reset()
extern void ConsentInformationClient_Reset_m91443CB27891CCFECB7039C47C83551D31B253F6 (void);
// 0x00000013 System.Int32 GoogleMobileAds.Ump.Android.ConsentInformationClient::GetConsentStatus()
extern void ConsentInformationClient_GetConsentStatus_m4B770F2663B8C7F7B2E3443F454B7A023CCB58EB (void);
// 0x00000014 System.Int32 GoogleMobileAds.Ump.Android.ConsentInformationClient::GetPrivacyOptionsRequirementStatus()
extern void ConsentInformationClient_GetPrivacyOptionsRequirementStatus_m1E1509575C149996B7D63867A59CF1052EFCD215 (void);
// 0x00000015 System.Boolean GoogleMobileAds.Ump.Android.ConsentInformationClient::CanRequestAds()
extern void ConsentInformationClient_CanRequestAds_m0FB6035CD2C19E42A2F0A748B0A4587F0436C3A2 (void);
// 0x00000016 System.Boolean GoogleMobileAds.Ump.Android.ConsentInformationClient::IsConsentFormAvailable()
extern void ConsentInformationClient_IsConsentFormAvailable_m710A47E7C470AFF7DC23B8D14D75D4FA0B51C267 (void);
// 0x00000017 System.Void GoogleMobileAds.Ump.Android.ConsentInformationClient::.cctor()
extern void ConsentInformationClient__cctor_mF27AB9B2B5CC81DB670DA86579FB9B51CD38FFA7 (void);
// 0x00000018 System.Void GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void OnConsentFormDismissedListener__ctor_m25B075F9D1DD54F7FE2E3143C2EB4C9D430000CE (void);
// 0x00000019 System.Void GoogleMobileAds.Ump.Android.OnConsentFormDismissedListener::onConsentFormDismissed(UnityEngine.AndroidJavaObject)
extern void OnConsentFormDismissedListener_onConsentFormDismissed_m31DB97C6E2131F94538BFF03EE2527A8B15C03DF (void);
// 0x0000001A System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void OnConsentFormLoadFailureListener__ctor_m73D2CAAC57FE65A3B45D4982A22B21460FD066F1 (void);
// 0x0000001B System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadFailureListener::onConsentFormLoadFailure(UnityEngine.AndroidJavaObject)
extern void OnConsentFormLoadFailureListener_onConsentFormLoadFailure_m93D767609232A7C84262D8FDF8F91AB1C916384D (void);
// 0x0000001C System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener::.ctor(System.Action`1<UnityEngine.AndroidJavaObject>)
extern void OnConsentFormLoadSuccessListener__ctor_mDDB00C939683DD05E9157ADE7D1D7A4FD0F00274 (void);
// 0x0000001D System.Void GoogleMobileAds.Ump.Android.OnConsentFormLoadSuccessListener::onConsentFormLoadSuccess(UnityEngine.AndroidJavaObject)
extern void OnConsentFormLoadSuccessListener_onConsentFormLoadSuccess_m80A8E26977EABF079EE10D8D35A914F9F808C5FE (void);
// 0x0000001E System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener::.ctor(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void OnConsentInfoUpdateFailureListener__ctor_m361E383AAC67FDBAF568E4ADF113D919E03141C8 (void);
// 0x0000001F System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateFailureListener::onConsentInfoUpdateFailure(UnityEngine.AndroidJavaObject)
extern void OnConsentInfoUpdateFailureListener_onConsentInfoUpdateFailure_m0E9EB5EEEDDF0261DCB9C5FFBC421C5B31574B52 (void);
// 0x00000020 System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener::.ctor(System.Action)
extern void OnConsentInfoUpdateSuccessListener__ctor_m60D900C440D1B9279E5D6D5C57B73B4B23CA2EBE (void);
// 0x00000021 System.Void GoogleMobileAds.Ump.Android.OnConsentInfoUpdateSuccessListener::onConsentInfoUpdateSuccess()
extern void OnConsentInfoUpdateSuccessListener_onConsentInfoUpdateSuccess_m52CC6820B88E0CE69C138B278E329EF34EA13958 (void);
// 0x00000022 System.Void GoogleMobileAds.Ump.Android.UmpClientFactory::.ctor()
extern void UmpClientFactory__ctor_mE0B5C6CDC13E8F1CE6420B0D6319845FDB7CB671 (void);
// 0x00000023 GoogleMobileAds.Ump.Common.IConsentFormClient GoogleMobileAds.Ump.Android.UmpClientFactory::ConsentFormClient()
extern void UmpClientFactory_ConsentFormClient_mF6F379AC68BED2A0C239926D0099E76B91227B6E (void);
// 0x00000024 GoogleMobileAds.Ump.Common.IConsentInformationClient GoogleMobileAds.Ump.Android.UmpClientFactory::ConsentInformationClient()
extern void UmpClientFactory_ConsentInformationClient_mA6E8322EFD8EDD0680E157B6E87C8A7A236B83A1 (void);
// 0x00000025 System.Void GoogleMobileAds.Ump.Android.Utils::.ctor()
extern void Utils__ctor_m7F081873A4BD1542285D9CE5E79D54F95E6BAB3D (void);
// 0x00000026 UnityEngine.AndroidJavaObject GoogleMobileAds.Ump.Android.Utils::GetConsentRequestParametersJavaObject(GoogleMobileAds.Ump.Api.ConsentRequestParameters,UnityEngine.AndroidJavaObject)
extern void Utils_GetConsentRequestParametersJavaObject_m3DC83E59A8C34C22DA9C02FF9DDFBE64A212B139 (void);
static Il2CppMethodPointer s_methodPointers[38] = 
{
	ConsentFormClient__ctor_m7E1B3CCF1A755D2E47B0CBE4E75D3B5B852B1422,
	ConsentFormClient_get_Instance_m3A7328EFC16FA4907777C349E6A54B055CC2B8E1,
	ConsentFormClient_Load_mF1F4C123930E94CBD1429D69F8B57C7AA37E6BEC,
	ConsentFormClient_Show_m37767BC4A288BAA7EF92936E32A3FFBDC4E401BC,
	ConsentFormClient_LoadAndShowConsentFormIfRequired_m4EF9A327D739F2A54C135B3493FF8DEBF49F035C,
	ConsentFormClient_ShowPrivacyOptionsForm_m00D5AA1D1BAC158F72CBD5F885C6F1EA4E77D38A,
	ConsentFormClient_onConsentFormDismissed_m6E870F79B3318CF234F2E647293F211642635B39,
	ConsentFormClient__cctor_m1554EAF6568E339F435C13AC2ED1CFA8EB42E2FF,
	ConsentFormClient_U3CShowU3Em__0_m3625CB74A5693AEB1032C8183EB59B91011E4E62,
	ConsentFormClient_U3CLoadAndShowConsentFormIfRequiredU3Em__1_m0DA0DE44426147017D499965D74177821B168582,
	ConsentFormClient_U3CShowPrivacyOptionsFormU3Em__2_m35D1DECABF64D9C0FEB7FF89AFC2D056475B4048,
	U3CLoadU3Ec__AnonStorey0__ctor_mFB78BF651FFE5D9720184ABAFAEC338F8EC8884E,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mF5112631374CFAC58737B64C5F7BF82BFE7DA5BF,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mEA45C5564FA6D738F05920AEBEAE822EF71C1E35,
	ConsentInformationClient__ctor_mA617BBC7ED7A68D33788671BE7E23E628EB557A7,
	ConsentInformationClient_get_Instance_m9B519B54A2628324821181E0A70EDB0A741B69C6,
	ConsentInformationClient_Update_m720A6D2A77AEDA48A334C4C52B2EA606BF92D1AD,
	ConsentInformationClient_Reset_m91443CB27891CCFECB7039C47C83551D31B253F6,
	ConsentInformationClient_GetConsentStatus_m4B770F2663B8C7F7B2E3443F454B7A023CCB58EB,
	ConsentInformationClient_GetPrivacyOptionsRequirementStatus_m1E1509575C149996B7D63867A59CF1052EFCD215,
	ConsentInformationClient_CanRequestAds_m0FB6035CD2C19E42A2F0A748B0A4587F0436C3A2,
	ConsentInformationClient_IsConsentFormAvailable_m710A47E7C470AFF7DC23B8D14D75D4FA0B51C267,
	ConsentInformationClient__cctor_mF27AB9B2B5CC81DB670DA86579FB9B51CD38FFA7,
	OnConsentFormDismissedListener__ctor_m25B075F9D1DD54F7FE2E3143C2EB4C9D430000CE,
	OnConsentFormDismissedListener_onConsentFormDismissed_m31DB97C6E2131F94538BFF03EE2527A8B15C03DF,
	OnConsentFormLoadFailureListener__ctor_m73D2CAAC57FE65A3B45D4982A22B21460FD066F1,
	OnConsentFormLoadFailureListener_onConsentFormLoadFailure_m93D767609232A7C84262D8FDF8F91AB1C916384D,
	OnConsentFormLoadSuccessListener__ctor_mDDB00C939683DD05E9157ADE7D1D7A4FD0F00274,
	OnConsentFormLoadSuccessListener_onConsentFormLoadSuccess_m80A8E26977EABF079EE10D8D35A914F9F808C5FE,
	OnConsentInfoUpdateFailureListener__ctor_m361E383AAC67FDBAF568E4ADF113D919E03141C8,
	OnConsentInfoUpdateFailureListener_onConsentInfoUpdateFailure_m0E9EB5EEEDDF0261DCB9C5FFBC421C5B31574B52,
	OnConsentInfoUpdateSuccessListener__ctor_m60D900C440D1B9279E5D6D5C57B73B4B23CA2EBE,
	OnConsentInfoUpdateSuccessListener_onConsentInfoUpdateSuccess_m52CC6820B88E0CE69C138B278E329EF34EA13958,
	UmpClientFactory__ctor_mE0B5C6CDC13E8F1CE6420B0D6319845FDB7CB671,
	UmpClientFactory_ConsentFormClient_mF6F379AC68BED2A0C239926D0099E76B91227B6E,
	UmpClientFactory_ConsentInformationClient_mA6E8322EFD8EDD0680E157B6E87C8A7A236B83A1,
	Utils__ctor_m7F081873A4BD1542285D9CE5E79D54F95E6BAB3D,
	Utils_GetConsentRequestParametersJavaObject_m3DC83E59A8C34C22DA9C02FF9DDFBE64A212B139,
};
static const int32_t s_InvokerIndices[38] = 
{
	6037,
	9116,
	2708,
	4809,
	4809,
	4809,
	4809,
	9162,
	6037,
	6037,
	6037,
	6037,
	4809,
	6037,
	6037,
	9116,
	1415,
	6037,
	5887,
	5887,
	5818,
	5818,
	9162,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	4809,
	6037,
	6037,
	5913,
	5913,
	6037,
	8149,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Ump_Android_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Ump_Android_CodeGenModule = 
{
	"GoogleMobileAds.Ump.Android.dll",
	38,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
