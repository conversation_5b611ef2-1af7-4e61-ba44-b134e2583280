﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

// System.Collections.Generic.List`1<System.Object>
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
// System.Collections.Generic.List`1<Waypoint>
struct List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE;
// UnityEngine.UI.CoroutineTween.TweenRunner`1<UnityEngine.UI.CoroutineTween.ColorTween>
struct TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4;
// System.Char[]
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
// UnityEngine.Collider[]
struct ColliderU5BU5D_t94A9D70F63D095AFF2A9B4613012A5F7F3141787;
// UnityEngine.ContactPoint[]
struct ContactPointU5BU5D_t3570603E8D0685B71B3D8BA07031674B00C5E411;
// UnityEngine.GameObject[]
struct GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF;
// System.IntPtr[]
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
// System.Object[]
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
// UnityEngine.ParticleSystem[]
struct ParticleSystemU5BU5D_tC5E33DA557C9C2064085128B3530C5F04D48F6E6;
// System.Diagnostics.StackTrace[]
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
// UnityEngine.UIVertex[]
struct UIVertexU5BU5D_tBC532486B45D071A520751A90E819C77BA4E3D2F;
// UnityEngine.Vector2[]
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA;
// UnityEngine.Vector3[]
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
// Waypoint[]
struct WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672;
// UnityEngine.Camera
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
// UnityEngine.Canvas
struct Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26;
// UnityEngine.CanvasRenderer
struct CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860;
// UnityEngine.Collider
struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76;
// UnityEngine.Collision
struct Collision_tBCC6AEBD9A63E6DA2E50660DAC03CDCB1FF7A9B0;
// UnityEngine.Component
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
// UnityEngine.Coroutine
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B;
// UnityEngine.UI.FontData
struct FontData_tB8E562846C6CB59C43260F69AE346B9BF3157224;
// UnityEngine.GameObject
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F;
// System.Collections.IDictionary
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
// System.Collections.IEnumerator
struct IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA;
// IgnoreCollisions
struct IgnoreCollisions_t97FF5E4A550ED022664DAE57777F03EF48A217EC;
// UnityEngine.Light
struct Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3;
// UnityEngine.Material
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3;
// UnityEngine.Mesh
struct Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4;
// UnityEngine.MonoBehaviour
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71;
// System.NotSupportedException
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A;
// UnityEngine.Object
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
// UnityEngine.ParticleSystem
struct ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1;
// VisCircle.PowerUpAnimation
struct PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF;
// UnityEngine.UI.RectMask2D
struct RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670;
// UnityEngine.RectTransform
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5;
// UnityEngine.Rigidbody
struct Rigidbody_t268697F5A994213ED97393309870968BC1C7393C;
// System.Runtime.Serialization.SafeSerializationManager
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
// SciFiArsenal.SciFiButtonScript
struct SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870;
// SciFiArsenal.SciFiDragMouseOrbit
struct SciFiDragMouseOrbit_t0B2E0A08B4A81730FC4F3BEF0C000EF569D3ED62;
// SciFiArsenal.SciFiFireProjectile
struct SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68;
// SciFiArsenal.SciFiLightFade
struct SciFiLightFade_tC9C3F15AB49D681770068C068E92DDEA5BB6470E;
// SciFiArsenal.SciFiLoadSceneOnClick
struct SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51;
// SciFiArsenal.SciFiLoadSceneOnClick2
struct SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319;
// SciFiArsenal.SciFiLoopScript
struct SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D;
// SciFiArsenal.SciFiProjectileScript
struct SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25;
// SciFiArsenal.SciFiRotation
struct SciFiRotation_t1F3E04A6A67FD04C3BD1CB92A53EBE7D1A5F53E3;
// System.String
struct String_t;
// UnityEngine.UI.Text
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62;
// UnityEngine.TextGenerator
struct TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC;
// UnityEngine.Texture2D
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4;
// UnityEngine.Transform
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
// UnityEngine.Events.UnityAction
struct UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7;
// UnityEngine.UI.VertexHelper
struct VertexHelper_tB905FCB02AE67CBEE5F265FE37A5938FC5D136FE;
// System.Void
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
// UnityEngine.WaitForSeconds
struct WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3;
// Waypoint
struct Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6;
// WaypointMover
struct WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273;
// WaypointsHolder
struct WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82;
// UnityEngine.WheelCollider
struct WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481;
// wheelAi
struct wheelAi_tCCC5B4FEDDC59EA37DE0318664D657D503BA4F43;
// UnityEngine.Camera/CameraCallback
struct CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD;
// UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
struct CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8;
// SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4
struct U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547;

IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GameObject_t76FEDD663AB33C991A9C9A23129337651094216F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RuntimeObject_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral0306C66ED2BF36EA4AD0D6D8C9A3F5DD04EE0D70;
IL2CPP_EXTERN_C String_t* _stringLiteral036E8DC7673BD2F2DB58497EB247496785DB4972;
IL2CPP_EXTERN_C String_t* _stringLiteral11D89EB85396FABB777CACDB484A47B9EA0A0AF1;
IL2CPP_EXTERN_C String_t* _stringLiteral1619C5C335E412CB53A1A03D5D6E8B83E2E20626;
IL2CPP_EXTERN_C String_t* _stringLiteral16DD21BE77B115D392226EB71A2D3A9FDC29E3F0;
IL2CPP_EXTERN_C String_t* _stringLiteral1A82671F2C34BEA09C35354DDB899812746CBCF9;
IL2CPP_EXTERN_C String_t* _stringLiteral1BF379A8C91717D27E696451740272046B579229;
IL2CPP_EXTERN_C String_t* _stringLiteral2BEA1E602FC6193FB49093F6DD05DD4B82ADE78C;
IL2CPP_EXTERN_C String_t* _stringLiteral40F46EE1E9B8B2D497BB832A96B0800F6A5E4082;
IL2CPP_EXTERN_C String_t* _stringLiteral4250553F1A33BB11D6ED51EB8D957D9EB9EAB359;
IL2CPP_EXTERN_C String_t* _stringLiteral54D0575E6058572F9374DC53FF19D2213EC18B3E;
IL2CPP_EXTERN_C String_t* _stringLiteral5DEA71BCADD074EB568D7812B9BE365919D7A81A;
IL2CPP_EXTERN_C String_t* _stringLiteral7474356D4679C1D2CC31EB5473525F3B3D936C04;
IL2CPP_EXTERN_C String_t* _stringLiteral77BBFACE7078076F9EC58DF14E9B5828E902DCD4;
IL2CPP_EXTERN_C String_t* _stringLiteral7945576977EFC6979B25BEA4BAE0C2079D5ABCFB;
IL2CPP_EXTERN_C String_t* _stringLiteral7C3058D929226D26583035E7F24EE258388DBAD6;
IL2CPP_EXTERN_C String_t* _stringLiteral7DE03E5EBA0308517D1762F2C0DF3B9E2A2F1870;
IL2CPP_EXTERN_C String_t* _stringLiteral7F0C56B69B17BB7182854EBA5EF0642800198091;
IL2CPP_EXTERN_C String_t* _stringLiteral8508A0A813BD56B9159332EE6373A855AC767E04;
IL2CPP_EXTERN_C String_t* _stringLiteral85AC1BD189D9C0F524186EC813E144C4547A4EA8;
IL2CPP_EXTERN_C String_t* _stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1;
IL2CPP_EXTERN_C String_t* _stringLiteral887D652C804CEF2C355B202D1A6466BD1054AA16;
IL2CPP_EXTERN_C String_t* _stringLiteral88BEE283254D7094E258B3A88730F4CC4F1E4AC7;
IL2CPP_EXTERN_C String_t* _stringLiteral8D6CC7AB013518017089598FFA1163BC9239BFC9;
IL2CPP_EXTERN_C String_t* _stringLiteral92E9796AF13FEEF7EE0A3D7079F97259971BBF9B;
IL2CPP_EXTERN_C String_t* _stringLiteral98411044963701D038055BC7ED6558A6DD29AA6A;
IL2CPP_EXTERN_C String_t* _stringLiteral9984DEC0742753663EB35FB5277D667AB9814D55;
IL2CPP_EXTERN_C String_t* _stringLiteralA378690B716D95393E276C02A666A54DA98B6669;
IL2CPP_EXTERN_C String_t* _stringLiteralA395A49C38624688BE663C163BC147794EFFEAAF;
IL2CPP_EXTERN_C String_t* _stringLiteralA89B817E70FE2E1839DC79CDD79D04CB9F485551;
IL2CPP_EXTERN_C String_t* _stringLiteralCE079A97B022AB27159197F1AE180B4DF5FEC4F1;
IL2CPP_EXTERN_C String_t* _stringLiteralCEE2E98CD60A1A62ADCCCD95617FA86B26E57552;
IL2CPP_EXTERN_C String_t* _stringLiteralD8A460B57C708AAE5B3FE032970AB4EB08FDAB9B;
IL2CPP_EXTERN_C String_t* _stringLiteralD92C8E4181068E77D27A2F15F7E6093FFFAF2BBA;
IL2CPP_EXTERN_C String_t* _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
IL2CPP_EXTERN_C String_t* _stringLiteralFC6687DC37346CD2569888E29764F727FAF530E0;
IL2CPP_EXTERN_C String_t* _stringLiteralFFD10011D1B2FBB6781371C70017213C9C9B088C;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisCollider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76_m820398EDBF1D3766C3166A0C323A127662A29A14_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_mB85C5C0EEF6535E3FC0DBFC14E39FA5A51B6F888_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisTransform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_m60E86366B3E431D4C4A549CF4FE5951087686F7F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponentsInChildren_TisParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1_m4A6A34D7CF3ABDD3C27C0FB3017B5B0D05AF407D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponentsInChildren_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_mB0FC9812323B908297FC1AFE88647432AA1505D4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObject_AddComponent_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_m785A0ABD5EABE3A4F7CF5226111DA1DB4273C782_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObject_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m2D7F86C77ECF9B82AAC077B511F1004280571B90_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObject_GetComponent_TisSciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870_mADBF88BCDCE4F53AF0E50C60E2BED90459FD18E3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObject_GetComponent_TisSciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68_m3B2EC44BA664B479AF49E79B2749BBAFE215FCA2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObject_GetComponent_TisSciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25_mBFE0AE078925AEB36508AEFAEC11E27428A77B11_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_RemoveAt_m83B7A7ED93B3F8BA72190C392A66062077991027_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mAAFCE295FBB7FE987F32A92E141CC10276F18691_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_Reset_m5B9934F58AB9DDEC6857EC2091B37ECCFFF734A0_RuntimeMethod_var;
struct ContactPoint_t241857959C0D517C21F541BB04B63FA6C1EAB3F9;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ColliderU5BU5D_t94A9D70F63D095AFF2A9B4613012A5F7F3141787;
struct GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct ParticleSystemU5BU5D_tC5E33DA557C9C2064085128B3530C5F04D48F6E6;
struct WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// System.Collections.Generic.List`1<System.Object>
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	// T[] System.Collections.Generic.List`1::_items
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items_1;
	// System.Int32 System.Collections.Generic.List`1::_size
	int32_t ____size_2;
	// System.Int32 System.Collections.Generic.List`1::_version
	int32_t ____version_3;
	// System.Object System.Collections.Generic.List`1::_syncRoot
	RuntimeObject* ____syncRoot_4;
};

// System.Collections.Generic.List`1<Waypoint>
struct List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE  : public RuntimeObject
{
	// T[] System.Collections.Generic.List`1::_items
	WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672* ____items_1;
	// System.Int32 System.Collections.Generic.List`1::_size
	int32_t ____size_2;
	// System.Int32 System.Collections.Generic.List`1::_version
	int32_t ____version_3;
	// System.Object System.Collections.Generic.List`1::_syncRoot
	RuntimeObject* ____syncRoot_4;
};

// <PrivateImplementationDetails>
struct U3CPrivateImplementationDetailsU3E_t0F5473E849A5A5185A9F4C5246F0C32816C49FCA  : public RuntimeObject
{
};

// System.String
struct String_t  : public RuntimeObject
{
	// System.Int32 System.String::_stringLength
	int32_t ____stringLength_4;
	// System.Char System.String::_firstChar
	Il2CppChar ____firstChar_5;
};

// System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};

// UnityEngine.YieldInstruction
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D  : public RuntimeObject
{
};
// Native definition for P/Invoke marshalling of UnityEngine.YieldInstruction
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
};
// Native definition for COM marshalling of UnityEngine.YieldInstruction
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
};

// SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4
struct U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547  : public RuntimeObject
{
	// System.Int32 SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::<>1__state
	int32_t ___U3CU3E1__state_0;
	// System.Object SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::<>2__current
	RuntimeObject* ___U3CU3E2__current_1;
	// SciFiArsenal.SciFiLoopScript SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::<>4__this
	SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* ___U3CU3E4__this_2;
	// UnityEngine.GameObject SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::<effectPlayer>5__2
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___U3CeffectPlayerU3E5__2_3;
};

// System.Boolean
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;
};

// UnityEngine.Color
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	// System.Single UnityEngine.Color::r
	float ___r_0;
	// System.Single UnityEngine.Color::g
	float ___g_1;
	// System.Single UnityEngine.Color::b
	float ___b_2;
	// System.Single UnityEngine.Color::a
	float ___a_3;
};

// System.Double
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	// System.Double System.Double::m_value
	double ___m_value_0;
};

// System.Enum
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};

// System.Int32
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	// System.Int32 System.Int32::m_value
	int32_t ___m_value_0;
};

// System.IntPtr
struct IntPtr_t 
{
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;
};

// UnityEngine.Quaternion
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 
{
	// System.Single UnityEngine.Quaternion::x
	float ___x_0;
	// System.Single UnityEngine.Quaternion::y
	float ___y_1;
	// System.Single UnityEngine.Quaternion::z
	float ___z_2;
	// System.Single UnityEngine.Quaternion::w
	float ___w_3;
};

// UnityEngine.Rect
struct Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D 
{
	// System.Single UnityEngine.Rect::m_XMin
	float ___m_XMin_0;
	// System.Single UnityEngine.Rect::m_YMin
	float ___m_YMin_1;
	// System.Single UnityEngine.Rect::m_Width
	float ___m_Width_2;
	// System.Single UnityEngine.Rect::m_Height
	float ___m_Height_3;
};

// System.Single
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	// System.Single System.Single::m_value
	float ___m_value_0;
};

// UnityEngine.Vector2
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	// System.Single UnityEngine.Vector2::x
	float ___x_0;
	// System.Single UnityEngine.Vector2::y
	float ___y_1;
};

// UnityEngine.Vector3
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	// System.Single UnityEngine.Vector3::x
	float ___x_2;
	// System.Single UnityEngine.Vector3::y
	float ___y_3;
	// System.Single UnityEngine.Vector3::z
	float ___z_4;
};

// UnityEngine.Vector4
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	// System.Single UnityEngine.Vector4::x
	float ___x_1;
	// System.Single UnityEngine.Vector4::y
	float ___y_2;
	// System.Single UnityEngine.Vector4::z
	float ___z_3;
	// System.Single UnityEngine.Vector4::w
	float ___w_4;
};

// System.Void
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};

// UnityEngine.WaitForSeconds
struct WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3  : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D
{
	// System.Single UnityEngine.WaitForSeconds::m_Seconds
	float ___m_Seconds_0;
};
// Native definition for P/Invoke marshalling of UnityEngine.WaitForSeconds
struct WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_marshaled_pinvoke : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
	float ___m_Seconds_0;
};
// Native definition for COM marshalling of UnityEngine.WaitForSeconds
struct WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_marshaled_com : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
	float ___m_Seconds_0;
};

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=12
struct __StaticArrayInitTypeSizeU3D12_t1BDD2193C3F925556BCD5FF35C0AC52DDB0CAB8F 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D12_t1BDD2193C3F925556BCD5FF35C0AC52DDB0CAB8F__padding[12];
	};
};

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
struct __StaticArrayInitTypeSizeU3D16_tFB2D94E174C3DFBC336BBEE6AD92E07462831A23 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D16_tFB2D94E174C3DFBC336BBEE6AD92E07462831A23__padding[16];
	};
};

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=20
struct __StaticArrayInitTypeSizeU3D20_tA394C0A7DC4F4F05D2190B09E23BDE8536718D72 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D20_tA394C0A7DC4F4F05D2190B09E23BDE8536718D72__padding[20];
	};
};

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=24
struct __StaticArrayInitTypeSizeU3D24_t3464DA68B6CCAB9A0A43F94B3DB9AA7E7FDDB19A 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D24_t3464DA68B6CCAB9A0A43F94B3DB9AA7E7FDDB19A__padding[24];
	};
};

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=28
struct __StaticArrayInitTypeSizeU3D28_t523FB00435F599517548D4C121316CFE1B43E6C2 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D28_t523FB00435F599517548D4C121316CFE1B43E6C2__padding[28];
	};
};

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=32
struct __StaticArrayInitTypeSizeU3D32_tC3894D25C1E879699FE1C9BAB1BBF2787B405069 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D32_tC3894D25C1E879699FE1C9BAB1BBF2787B405069__padding[32];
	};
};

// WaypointMover/UsedAxis
struct UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619 
{
	// System.Boolean WaypointMover/UsedAxis::x
	bool ___x_0;
	// System.Boolean WaypointMover/UsedAxis::y
	bool ___y_1;
	// System.Boolean WaypointMover/UsedAxis::z
	bool ___z_2;
};
// Native definition for P/Invoke marshalling of WaypointMover/UsedAxis
struct UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshaled_pinvoke
{
	int32_t ___x_0;
	int32_t ___y_1;
	int32_t ___z_2;
};
// Native definition for COM marshalling of WaypointMover/UsedAxis
struct UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshaled_com
{
	int32_t ___x_0;
	int32_t ___y_1;
	int32_t ___z_2;
};

// UnityEngine.Collision
struct Collision_tBCC6AEBD9A63E6DA2E50660DAC03CDCB1FF7A9B0  : public RuntimeObject
{
	// UnityEngine.Vector3 UnityEngine.Collision::m_Impulse
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Impulse_0;
	// UnityEngine.Vector3 UnityEngine.Collision::m_RelativeVelocity
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_RelativeVelocity_1;
	// UnityEngine.Component UnityEngine.Collision::m_Body
	Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* ___m_Body_2;
	// UnityEngine.Collider UnityEngine.Collision::m_Collider
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider_3;
	// System.Int32 UnityEngine.Collision::m_ContactCount
	int32_t ___m_ContactCount_4;
	// UnityEngine.ContactPoint[] UnityEngine.Collision::m_ReusedContacts
	ContactPointU5BU5D_t3570603E8D0685B71B3D8BA07031674B00C5E411* ___m_ReusedContacts_5;
	// UnityEngine.ContactPoint[] UnityEngine.Collision::m_LegacyContacts
	ContactPointU5BU5D_t3570603E8D0685B71B3D8BA07031674B00C5E411* ___m_LegacyContacts_6;
};
// Native definition for P/Invoke marshalling of UnityEngine.Collision
struct Collision_tBCC6AEBD9A63E6DA2E50660DAC03CDCB1FF7A9B0_marshaled_pinvoke
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Impulse_0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_RelativeVelocity_1;
	Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* ___m_Body_2;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider_3;
	int32_t ___m_ContactCount_4;
	ContactPoint_t241857959C0D517C21F541BB04B63FA6C1EAB3F9* ___m_ReusedContacts_5;
	ContactPoint_t241857959C0D517C21F541BB04B63FA6C1EAB3F9* ___m_LegacyContacts_6;
};
// Native definition for COM marshalling of UnityEngine.Collision
struct Collision_tBCC6AEBD9A63E6DA2E50660DAC03CDCB1FF7A9B0_marshaled_com
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Impulse_0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_RelativeVelocity_1;
	Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* ___m_Body_2;
	Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___m_Collider_3;
	int32_t ___m_ContactCount_4;
	ContactPoint_t241857959C0D517C21F541BB04B63FA6C1EAB3F9* ___m_ReusedContacts_5;
	ContactPoint_t241857959C0D517C21F541BB04B63FA6C1EAB3F9* ___m_LegacyContacts_6;
};

// UnityEngine.Coroutine
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B  : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D
{
	// System.IntPtr UnityEngine.Coroutine::m_Ptr
	intptr_t ___m_Ptr_0;
};
// Native definition for P/Invoke marshalling of UnityEngine.Coroutine
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B_marshaled_pinvoke : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
	intptr_t ___m_Ptr_0;
};
// Native definition for COM marshalling of UnityEngine.Coroutine
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B_marshaled_com : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
	intptr_t ___m_Ptr_0;
};

// System.Exception
struct Exception_t  : public RuntimeObject
{
	// System.String System.Exception::_className
	String_t* ____className_1;
	// System.String System.Exception::_message
	String_t* ____message_2;
	// System.Collections.IDictionary System.Exception::_data
	RuntimeObject* ____data_3;
	// System.Exception System.Exception::_innerException
	Exception_t* ____innerException_4;
	// System.String System.Exception::_helpURL
	String_t* ____helpURL_5;
	// System.Object System.Exception::_stackTrace
	RuntimeObject* ____stackTrace_6;
	// System.String System.Exception::_stackTraceString
	String_t* ____stackTraceString_7;
	// System.String System.Exception::_remoteStackTraceString
	String_t* ____remoteStackTraceString_8;
	// System.Int32 System.Exception::_remoteStackIndex
	int32_t ____remoteStackIndex_9;
	// System.Object System.Exception::_dynamicMethods
	RuntimeObject* ____dynamicMethods_10;
	// System.Int32 System.Exception::_HResult
	int32_t ____HResult_11;
	// System.String System.Exception::_source
	String_t* ____source_12;
	// System.Runtime.Serialization.SafeSerializationManager System.Exception::_safeSerializationManager
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager_13;
	// System.Diagnostics.StackTrace[] System.Exception::captured_traces
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces_14;
	// System.IntPtr[] System.Exception::native_trace_ips
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips_15;
	// System.Int32 System.Exception::caught_in_unmanaged
	int32_t ___caught_in_unmanaged_16;
};
// Native definition for P/Invoke marshalling of System.Exception
struct Exception_t_marshaled_pinvoke
{
	char* ____className_1;
	char* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_pinvoke* ____innerException_4;
	char* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	char* ____stackTraceString_7;
	char* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	char* ____source_12;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager_13;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
	int32_t ___caught_in_unmanaged_16;
};
// Native definition for COM marshalling of System.Exception
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className_1;
	Il2CppChar* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_com* ____innerException_4;
	Il2CppChar* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	Il2CppChar* ____stackTraceString_7;
	Il2CppChar* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	Il2CppChar* ____source_12;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager_13;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
	int32_t ___caught_in_unmanaged_16;
};

// UnityEngine.KeyCode
struct KeyCode_t75B9ECCC26D858F55040DDFF9523681E996D17E9 
{
	// System.Int32 UnityEngine.KeyCode::value__
	int32_t ___value___2;
};

// UnityEngine.Object
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	// System.IntPtr UnityEngine.Object::m_CachedPtr
	intptr_t ___m_CachedPtr_0;
};
// Native definition for P/Invoke marshalling of UnityEngine.Object
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr_0;
};
// Native definition for COM marshalling of UnityEngine.Object
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr_0;
};

// UnityEngine.Ray
struct Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00 
{
	// UnityEngine.Vector3 UnityEngine.Ray::m_Origin
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Origin_0;
	// UnityEngine.Vector3 UnityEngine.Ray::m_Direction
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Direction_1;
};

// UnityEngine.RaycastHit
struct RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 
{
	// UnityEngine.Vector3 UnityEngine.RaycastHit::m_Point
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point_0;
	// UnityEngine.Vector3 UnityEngine.RaycastHit::m_Normal
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal_1;
	// System.UInt32 UnityEngine.RaycastHit::m_FaceID
	uint32_t ___m_FaceID_2;
	// System.Single UnityEngine.RaycastHit::m_Distance
	float ___m_Distance_3;
	// UnityEngine.Vector2 UnityEngine.RaycastHit::m_UV
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_UV_4;
	// System.Int32 UnityEngine.RaycastHit::m_Collider
	int32_t ___m_Collider_5;
};

// UnityEngine.SendMessageOptions
struct SendMessageOptions_t8C6881C01B06BF874EE578D27D8CF237EC2BFD54 
{
	// System.Int32 UnityEngine.SendMessageOptions::value__
	int32_t ___value___2;
};

// UnityEngine.Space
struct Space_tF043E93E06B702DD05199C28C6F779049B38A969 
{
	// System.Int32 UnityEngine.Space::value__
	int32_t ___value___2;
};

// VisCircle.PowerUpAnimation/RotationType
struct RotationType_t30CE09B0EB481984C08CC67D16DBC4AF72C360F0 
{
	// System.Int32 VisCircle.PowerUpAnimation/RotationType::value__
	int32_t ___value___2;
};

// SciFiArsenal.SciFiRotation/spaceEnum
struct spaceEnum_tC21A0E1C4772B1B60D2C648B58674C5B131225E4 
{
	// System.Int32 SciFiArsenal.SciFiRotation/spaceEnum::value__
	int32_t ___value___2;
};

// WaypointMover/FollowType
struct FollowType_tCB33392302F7DDD8B5F6003B9D64642CEE57FE0C 
{
	// System.Int32 WaypointMover/FollowType::value__
	int32_t ___value___2;
};

// WaypointMover/LoopType
struct LoopType_t245ACE3F8FFBD894B62E8010BC415B4F8E557C7A 
{
	// System.Int32 WaypointMover/LoopType::value__
	int32_t ___value___2;
};

// UnityEngine.Component
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};

// UnityEngine.GameObject
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};

// System.SystemException
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};

// UnityEngine.Behaviour
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};

// UnityEngine.Collider
struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};

// System.NotSupportedException
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};

// UnityEngine.ParticleSystem
struct ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};

// UnityEngine.Rigidbody
struct Rigidbody_t268697F5A994213ED97393309870968BC1C7393C  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};

// UnityEngine.Transform
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};

// UnityEngine.Camera
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};

// UnityEngine.Light
struct Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	// System.Int32 UnityEngine.Light::m_BakedIndex
	int32_t ___m_BakedIndex_4;
};

// UnityEngine.MonoBehaviour
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};

// UnityEngine.WheelCollider
struct WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481  : public Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76
{
};

// IgnoreCollisions
struct IgnoreCollisions_t97FF5E4A550ED022664DAE57777F03EF48A217EC  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.Collider[] IgnoreCollisions::ignoreCollisionWith
	ColliderU5BU5D_t94A9D70F63D095AFF2A9B4613012A5F7F3141787* ___ignoreCollisionWith_4;
};

// VisCircle.PowerUpAnimation
struct PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// System.Boolean VisCircle.PowerUpAnimation::_animateRotation
	bool ____animateRotation_4;
	// UnityEngine.Vector3 VisCircle.PowerUpAnimation::rotationSpeedsInDegreePerSecond
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rotationSpeedsInDegreePerSecond_5;
	// VisCircle.PowerUpAnimation/RotationType VisCircle.PowerUpAnimation::rotationType
	int32_t ___rotationType_6;
	// System.Boolean VisCircle.PowerUpAnimation::_animateScale
	bool ____animateScale_7;
	// System.Single VisCircle.PowerUpAnimation::scaleMin
	float ___scaleMin_8;
	// System.Single VisCircle.PowerUpAnimation::scaleMax
	float ___scaleMax_9;
	// System.Single VisCircle.PowerUpAnimation::scaleCycleDuration
	float ___scaleCycleDuration_10;
	// System.Boolean VisCircle.PowerUpAnimation::_animateYOffset
	bool ____animateYOffset_11;
	// System.Single VisCircle.PowerUpAnimation::yOffsetAmplitude
	float ___yOffsetAmplitude_12;
	// System.Single VisCircle.PowerUpAnimation::yOffsetCycleDuration
	float ___yOffsetCycleDuration_13;
	// UnityEngine.Vector3 VisCircle.PowerUpAnimation::_startLocalPosition
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____startLocalPosition_14;
	// UnityEngine.Quaternion VisCircle.PowerUpAnimation::_startLocalRotation
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ____startLocalRotation_15;
	// UnityEngine.Vector3 VisCircle.PowerUpAnimation::_startLocalScale
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____startLocalScale_16;
	// UnityEngine.Transform VisCircle.PowerUpAnimation::_transform
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform_17;
};

// SciFiArsenal.SciFiButtonScript
struct SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.GameObject SciFiArsenal.SciFiButtonScript::Button
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___Button_4;
	// UnityEngine.UI.Text SciFiArsenal.SciFiButtonScript::MyButtonText
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___MyButtonText_5;
	// System.String SciFiArsenal.SciFiButtonScript::projectileParticleName
	String_t* ___projectileParticleName_6;
	// SciFiArsenal.SciFiFireProjectile SciFiArsenal.SciFiButtonScript::effectScript
	SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* ___effectScript_7;
	// SciFiArsenal.SciFiProjectileScript SciFiArsenal.SciFiButtonScript::projectileScript
	SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25* ___projectileScript_8;
	// System.Single SciFiArsenal.SciFiButtonScript::buttonsX
	float ___buttonsX_9;
	// System.Single SciFiArsenal.SciFiButtonScript::buttonsY
	float ___buttonsY_10;
	// System.Single SciFiArsenal.SciFiButtonScript::buttonsSizeX
	float ___buttonsSizeX_11;
	// System.Single SciFiArsenal.SciFiButtonScript::buttonsSizeY
	float ___buttonsSizeY_12;
	// System.Single SciFiArsenal.SciFiButtonScript::buttonsDistance
	float ___buttonsDistance_13;
};

// SciFiArsenal.SciFiDragMouseOrbit
struct SciFiDragMouseOrbit_t0B2E0A08B4A81730FC4F3BEF0C000EF569D3ED62  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.Transform SciFiArsenal.SciFiDragMouseOrbit::target
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___target_4;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::distance
	float ___distance_5;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::xSpeed
	float ___xSpeed_6;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::ySpeed
	float ___ySpeed_7;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::yMinLimit
	float ___yMinLimit_8;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::yMaxLimit
	float ___yMaxLimit_9;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::distanceMin
	float ___distanceMin_10;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::distanceMax
	float ___distanceMax_11;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::smoothTime
	float ___smoothTime_12;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::rotationYAxis
	float ___rotationYAxis_13;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::rotationXAxis
	float ___rotationXAxis_14;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::velocityX
	float ___velocityX_15;
	// System.Single SciFiArsenal.SciFiDragMouseOrbit::velocityY
	float ___velocityY_16;
};

// SciFiArsenal.SciFiFireProjectile
struct SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.RaycastHit SciFiArsenal.SciFiFireProjectile::hit
	RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 ___hit_4;
	// UnityEngine.GameObject[] SciFiArsenal.SciFiFireProjectile::projectiles
	GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* ___projectiles_5;
	// UnityEngine.Transform SciFiArsenal.SciFiFireProjectile::spawnPosition
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___spawnPosition_6;
	// System.Int32 SciFiArsenal.SciFiFireProjectile::currentProjectile
	int32_t ___currentProjectile_7;
	// System.Single SciFiArsenal.SciFiFireProjectile::speed
	float ___speed_8;
	// SciFiArsenal.SciFiButtonScript SciFiArsenal.SciFiFireProjectile::selectedProjectileButton
	SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* ___selectedProjectileButton_9;
};

// SciFiArsenal.SciFiLightFade
struct SciFiLightFade_tC9C3F15AB49D681770068C068E92DDEA5BB6470E  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// System.Single SciFiArsenal.SciFiLightFade::life
	float ___life_4;
	// System.Boolean SciFiArsenal.SciFiLightFade::killAfterLife
	bool ___killAfterLife_5;
	// UnityEngine.Light SciFiArsenal.SciFiLightFade::li
	Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* ___li_6;
	// System.Single SciFiArsenal.SciFiLightFade::initIntensity
	float ___initIntensity_7;
};

// SciFiArsenal.SciFiLoadSceneOnClick
struct SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};

// SciFiArsenal.SciFiLoadSceneOnClick2
struct SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};

// SciFiArsenal.SciFiLoopScript
struct SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.GameObject SciFiArsenal.SciFiLoopScript::chosenEffect
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___chosenEffect_4;
	// System.Single SciFiArsenal.SciFiLoopScript::loopTimeLimit
	float ___loopTimeLimit_5;
};

// SciFiArsenal.SciFiProjectileScript
struct SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.GameObject SciFiArsenal.SciFiProjectileScript::impactParticle
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___impactParticle_4;
	// UnityEngine.GameObject SciFiArsenal.SciFiProjectileScript::projectileParticle
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___projectileParticle_5;
	// UnityEngine.GameObject SciFiArsenal.SciFiProjectileScript::muzzleParticle
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___muzzleParticle_6;
	// UnityEngine.GameObject[] SciFiArsenal.SciFiProjectileScript::trailParticles
	GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* ___trailParticles_7;
	// UnityEngine.Vector3 SciFiArsenal.SciFiProjectileScript::impactNormal
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___impactNormal_8;
	// System.Boolean SciFiArsenal.SciFiProjectileScript::hasCollided
	bool ___hasCollided_9;
};

// SciFiArsenal.SciFiRotation
struct SciFiRotation_t1F3E04A6A67FD04C3BD1CB92A53EBE7D1A5F53E3  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.Vector3 SciFiArsenal.SciFiRotation::rotateVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rotateVector_4;
	// SciFiArsenal.SciFiRotation/spaceEnum SciFiArsenal.SciFiRotation::rotateSpace
	int32_t ___rotateSpace_5;
};

// UnityEngine.EventSystems.UIBehaviour
struct UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};

// Waypoint
struct Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.Color Waypoint::color
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___color_4;
	// System.Single Waypoint::radius
	float ___radius_5;
	// System.String Waypoint::iconName
	String_t* ___iconName_6;
	// System.Single Waypoint::delay
	float ___delay_7;
	// System.Single Waypoint::angle
	float ___angle_8;
	// System.String Waypoint::callFunction
	String_t* ___callFunction_9;
	// System.String Waypoint::callExitFunction
	String_t* ___callExitFunction_10;
	// System.Single Waypoint::newMoverSpeed
	float ___newMoverSpeed_11;
};

// WaypointMover
struct WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// WaypointsHolder WaypointMover::waypointsHolder
	WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* ___waypointsHolder_4;
	// WaypointMover/FollowType WaypointMover::followingType
	int32_t ___followingType_5;
	// WaypointMover/LoopType WaypointMover::loopingType
	int32_t ___loopingType_6;
	// System.Boolean WaypointMover::MoveOnWayImmediately
	bool ___MoveOnWayImmediately_7;
	// System.Boolean WaypointMover::StartFromNearestWaypoint
	bool ___StartFromNearestWaypoint_8;
	// WaypointMover/UsedAxis WaypointMover::ignorePositionAtAxis
	UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619 ___ignorePositionAtAxis_9;
	// System.Single WaypointMover::damping
	float ___damping_10;
	// System.Single WaypointMover::movementSpeed
	float ___movementSpeed_11;
	// System.Single WaypointMover::waypointActivationDistance
	float ___waypointActivationDistance_12;
	// System.Int32 WaypointMover::numberOfLoops
	int32_t ___numberOfLoops_13;
	// System.Single WaypointMover::preventCollisionDistance
	float ___preventCollisionDistance_14;
	// System.Boolean WaypointMover::smoothCollisionPreventing
	bool ___smoothCollisionPreventing_15;
	// System.Boolean WaypointMover::dynamicWaypointsUpdate
	bool ___dynamicWaypointsUpdate_16;
	// System.Int32 WaypointMover::currentWaypoint
	int32_t ___currentWaypoint_17;
	// System.Int32 WaypointMover::direction
	int32_t ___direction_18;
	// UnityEngine.Vector3 WaypointMover::velocity
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___velocity_19;
	// UnityEngine.Vector3 WaypointMover::targetPosition
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___targetPosition_20;
	// System.Single WaypointMover::delayTillTime
	float ___delayTillTime_21;
	// System.Int32 WaypointMover::loopNumber
	int32_t ___loopNumber_22;
	// System.Boolean WaypointMover::inMove
	bool ___inMove_23;
	// System.Boolean WaypointMover::suspended
	bool ___suspended_24;
	// System.Int32 WaypointMover::previousWaypoint
	int32_t ___previousWaypoint_25;
	// System.Single WaypointMover::initialMovementSpeed
	float ___initialMovementSpeed_26;
	// System.Boolean WaypointMover::callExitFunction
	bool ___callExitFunction_27;
	// System.Boolean WaypointMover::onWaypoint
	bool ___onWaypoint_28;
};

// WaypointsHolder
struct WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.Color WaypointsHolder::color
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___color_4;
	// System.Collections.Generic.List`1<Waypoint> WaypointsHolder::waypoints
	List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* ___waypoints_5;
	// System.Boolean WaypointsHolder::colorizeWaypoints
	bool ___colorizeWaypoints_6;
};

// wheelAi
struct wheelAi_tCCC5B4FEDDC59EA37DE0318664D657D503BA4F43  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	// UnityEngine.WheelCollider wheelAi::AIFL
	WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* ___AIFL_4;
	// UnityEngine.WheelCollider wheelAi::AIFR
	WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* ___AIFR_5;
	// UnityEngine.WheelCollider wheelAi::AIRL
	WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* ___AIRL_6;
	// UnityEngine.WheelCollider wheelAi::AIRR
	WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* ___AIRR_7;
	// UnityEngine.Transform wheelAi::AITFL
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___AITFL_8;
	// UnityEngine.Transform wheelAi::AITFR
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___AITFR_9;
	// UnityEngine.Transform wheelAi::AITRL
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___AITRL_10;
	// UnityEngine.Transform wheelAi::AITRR
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___AITRR_11;
	// UnityEngine.Vector3 wheelAi::AIPOS
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___AIPOS_12;
	// UnityEngine.Quaternion wheelAi::AIROT
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___AIROT_13;
};

// UnityEngine.UI.Graphic
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	// UnityEngine.Material UnityEngine.UI.Graphic::m_Material
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_Material_6;
	// UnityEngine.Color UnityEngine.UI.Graphic::m_Color
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_Color_7;
	// System.Boolean UnityEngine.UI.Graphic::m_SkipLayoutUpdate
	bool ___m_SkipLayoutUpdate_8;
	// System.Boolean UnityEngine.UI.Graphic::m_SkipMaterialUpdate
	bool ___m_SkipMaterialUpdate_9;
	// System.Boolean UnityEngine.UI.Graphic::m_RaycastTarget
	bool ___m_RaycastTarget_10;
	// System.Boolean UnityEngine.UI.Graphic::m_RaycastTargetCache
	bool ___m_RaycastTargetCache_11;
	// UnityEngine.Vector4 UnityEngine.UI.Graphic::m_RaycastPadding
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___m_RaycastPadding_12;
	// UnityEngine.RectTransform UnityEngine.UI.Graphic::m_RectTransform
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_RectTransform_13;
	// UnityEngine.CanvasRenderer UnityEngine.UI.Graphic::m_CanvasRenderer
	CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860* ___m_CanvasRenderer_14;
	// UnityEngine.Canvas UnityEngine.UI.Graphic::m_Canvas
	Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26* ___m_Canvas_15;
	// System.Boolean UnityEngine.UI.Graphic::m_VertsDirty
	bool ___m_VertsDirty_16;
	// System.Boolean UnityEngine.UI.Graphic::m_MaterialDirty
	bool ___m_MaterialDirty_17;
	// UnityEngine.Events.UnityAction UnityEngine.UI.Graphic::m_OnDirtyLayoutCallback
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyLayoutCallback_18;
	// UnityEngine.Events.UnityAction UnityEngine.UI.Graphic::m_OnDirtyVertsCallback
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyVertsCallback_19;
	// UnityEngine.Events.UnityAction UnityEngine.UI.Graphic::m_OnDirtyMaterialCallback
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyMaterialCallback_20;
	// UnityEngine.Mesh UnityEngine.UI.Graphic::m_CachedMesh
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___m_CachedMesh_23;
	// UnityEngine.Vector2[] UnityEngine.UI.Graphic::m_CachedUvs
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___m_CachedUvs_24;
	// UnityEngine.UI.CoroutineTween.TweenRunner`1<UnityEngine.UI.CoroutineTween.ColorTween> UnityEngine.UI.Graphic::m_ColorTweenRunner
	TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4* ___m_ColorTweenRunner_25;
	// System.Boolean UnityEngine.UI.Graphic::<useLegacyMeshGeneration>k__BackingField
	bool ___U3CuseLegacyMeshGenerationU3Ek__BackingField_26;
};

// UnityEngine.UI.MaskableGraphic
struct MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E  : public Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931
{
	// System.Boolean UnityEngine.UI.MaskableGraphic::m_ShouldRecalculateStencil
	bool ___m_ShouldRecalculateStencil_27;
	// UnityEngine.Material UnityEngine.UI.MaskableGraphic::m_MaskMaterial
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_MaskMaterial_28;
	// UnityEngine.UI.RectMask2D UnityEngine.UI.MaskableGraphic::m_ParentMask
	RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670* ___m_ParentMask_29;
	// System.Boolean UnityEngine.UI.MaskableGraphic::m_Maskable
	bool ___m_Maskable_30;
	// System.Boolean UnityEngine.UI.MaskableGraphic::m_IsMaskingGraphic
	bool ___m_IsMaskingGraphic_31;
	// System.Boolean UnityEngine.UI.MaskableGraphic::m_IncludeForMasking
	bool ___m_IncludeForMasking_32;
	// UnityEngine.UI.MaskableGraphic/CullStateChangedEvent UnityEngine.UI.MaskableGraphic::m_OnCullStateChanged
	CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8* ___m_OnCullStateChanged_33;
	// System.Boolean UnityEngine.UI.MaskableGraphic::m_ShouldRecalculate
	bool ___m_ShouldRecalculate_34;
	// System.Int32 UnityEngine.UI.MaskableGraphic::m_StencilValue
	int32_t ___m_StencilValue_35;
	// UnityEngine.Vector3[] UnityEngine.UI.MaskableGraphic::m_Corners
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___m_Corners_36;
};

// UnityEngine.UI.Text
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62  : public MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E
{
	// UnityEngine.UI.FontData UnityEngine.UI.Text::m_FontData
	FontData_tB8E562846C6CB59C43260F69AE346B9BF3157224* ___m_FontData_37;
	// System.String UnityEngine.UI.Text::m_Text
	String_t* ___m_Text_38;
	// UnityEngine.TextGenerator UnityEngine.UI.Text::m_TextCache
	TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC* ___m_TextCache_39;
	// UnityEngine.TextGenerator UnityEngine.UI.Text::m_TextCacheForLayout
	TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC* ___m_TextCacheForLayout_40;
	// System.Boolean UnityEngine.UI.Text::m_DisableFontTextureRebuiltCallback
	bool ___m_DisableFontTextureRebuiltCallback_42;
	// UnityEngine.UIVertex[] UnityEngine.UI.Text::m_TempVerts
	UIVertexU5BU5D_tBC532486B45D071A520751A90E819C77BA4E3D2F* ___m_TempVerts_43;
};

// System.Collections.Generic.List`1<System.Object>
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	// T[] System.Collections.Generic.List`1::s_emptyArray
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray_5;
};

// System.Collections.Generic.List`1<System.Object>

// System.Collections.Generic.List`1<Waypoint>
struct List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE_StaticFields
{
	// T[] System.Collections.Generic.List`1::s_emptyArray
	WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672* ___s_emptyArray_5;
};

// System.Collections.Generic.List`1<Waypoint>

// <PrivateImplementationDetails>
struct U3CPrivateImplementationDetailsU3E_t0F5473E849A5A5185A9F4C5246F0C32816C49FCA_StaticFields
{
	// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=12 <PrivateImplementationDetails>::0AD6CA551DC63F32A74A6E84B339F15D3A5E6063EB3C1CBD6EB700E005950269
	__StaticArrayInitTypeSizeU3D12_t1BDD2193C3F925556BCD5FF35C0AC52DDB0CAB8F ___0AD6CA551DC63F32A74A6E84B339F15D3A5E6063EB3C1CBD6EB700E005950269_0;
	// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=28 <PrivateImplementationDetails>::2CE7CCE3C170C1F0C6470496187D1D488AA18C369B7A1F2146AE977DCF48B416
	__StaticArrayInitTypeSizeU3D28_t523FB00435F599517548D4C121316CFE1B43E6C2 ___2CE7CCE3C170C1F0C6470496187D1D488AA18C369B7A1F2146AE977DCF48B416_1;
	// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=24 <PrivateImplementationDetails>::8AFDDA54360724A4B1FDC926F302229C2FB6DFC5891C57FD334A1A63FAE0349C
	__StaticArrayInitTypeSizeU3D24_t3464DA68B6CCAB9A0A43F94B3DB9AA7E7FDDB19A ___8AFDDA54360724A4B1FDC926F302229C2FB6DFC5891C57FD334A1A63FAE0349C_2;
	// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=20 <PrivateImplementationDetails>::8F7EEB07CF2A9E78F510CCB1044268C5EF1C06655E68BE175A96639CCAFFF6A5
	__StaticArrayInitTypeSizeU3D20_tA394C0A7DC4F4F05D2190B09E23BDE8536718D72 ___8F7EEB07CF2A9E78F510CCB1044268C5EF1C06655E68BE175A96639CCAFFF6A5_3;
	// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=16 <PrivateImplementationDetails>::A9E47E832D6F7C74B6202E4CC8190BFE88B1FE66B991E29C90F78623B71FB662
	__StaticArrayInitTypeSizeU3D16_tFB2D94E174C3DFBC336BBEE6AD92E07462831A23 ___A9E47E832D6F7C74B6202E4CC8190BFE88B1FE66B991E29C90F78623B71FB662_4;
	// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=32 <PrivateImplementationDetails>::F444F91505F99AEE74BF25DB97C04353F46226E1B82B7CEED34537165E1F9C52
	__StaticArrayInitTypeSizeU3D32_tC3894D25C1E879699FE1C9BAB1BBF2787B405069 ___F444F91505F99AEE74BF25DB97C04353F46226E1B82B7CEED34537165E1F9C52_5;
};

// <PrivateImplementationDetails>

// System.String
struct String_t_StaticFields
{
	// System.String System.String::Empty
	String_t* ___Empty_6;
};

// System.String

// SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4

// SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4

// System.Boolean
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;
};

// System.Boolean

// UnityEngine.Color

// UnityEngine.Color

// System.Double

// System.Double

// System.Int32

// System.Int32

// UnityEngine.Quaternion
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_StaticFields
{
	// UnityEngine.Quaternion UnityEngine.Quaternion::identityQuaternion
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___identityQuaternion_4;
};

// UnityEngine.Quaternion

// UnityEngine.Rect

// UnityEngine.Rect

// System.Single

// System.Single

// UnityEngine.Vector2
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	// UnityEngine.Vector2 UnityEngine.Vector2::zeroVector
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector_2;
	// UnityEngine.Vector2 UnityEngine.Vector2::oneVector
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector_3;
	// UnityEngine.Vector2 UnityEngine.Vector2::upVector
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector_4;
	// UnityEngine.Vector2 UnityEngine.Vector2::downVector
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector_5;
	// UnityEngine.Vector2 UnityEngine.Vector2::leftVector
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector_6;
	// UnityEngine.Vector2 UnityEngine.Vector2::rightVector
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector_7;
	// UnityEngine.Vector2 UnityEngine.Vector2::positiveInfinityVector
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector_8;
	// UnityEngine.Vector2 UnityEngine.Vector2::negativeInfinityVector
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector_9;
};

// UnityEngine.Vector2

// UnityEngine.Vector3
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	// UnityEngine.Vector3 UnityEngine.Vector3::zeroVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector_5;
	// UnityEngine.Vector3 UnityEngine.Vector3::oneVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector_6;
	// UnityEngine.Vector3 UnityEngine.Vector3::upVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector_7;
	// UnityEngine.Vector3 UnityEngine.Vector3::downVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector_8;
	// UnityEngine.Vector3 UnityEngine.Vector3::leftVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector_9;
	// UnityEngine.Vector3 UnityEngine.Vector3::rightVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector_10;
	// UnityEngine.Vector3 UnityEngine.Vector3::forwardVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector_11;
	// UnityEngine.Vector3 UnityEngine.Vector3::backVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector_12;
	// UnityEngine.Vector3 UnityEngine.Vector3::positiveInfinityVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector_13;
	// UnityEngine.Vector3 UnityEngine.Vector3::negativeInfinityVector
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector_14;
};

// UnityEngine.Vector3

// System.Void

// System.Void

// UnityEngine.WaitForSeconds

// UnityEngine.WaitForSeconds

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=12

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=12

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=16

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=16

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=20

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=20

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=24

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=24

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=28

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=28

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=32

// <PrivateImplementationDetails>/__StaticArrayInitTypeSize=32

// WaypointMover/UsedAxis

// WaypointMover/UsedAxis

// UnityEngine.Collision

// UnityEngine.Collision

// UnityEngine.Coroutine

// UnityEngine.Coroutine

// UnityEngine.KeyCode

// UnityEngine.KeyCode

// UnityEngine.Object
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	// System.Int32 UnityEngine.Object::OffsetOfInstanceIDInCPlusPlusObject
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject_1;
};

// UnityEngine.Object

// UnityEngine.Ray

// UnityEngine.Ray

// UnityEngine.RaycastHit

// UnityEngine.RaycastHit

// UnityEngine.SendMessageOptions

// UnityEngine.SendMessageOptions

// UnityEngine.Space

// UnityEngine.Space

// VisCircle.PowerUpAnimation/RotationType

// VisCircle.PowerUpAnimation/RotationType

// SciFiArsenal.SciFiRotation/spaceEnum

// SciFiArsenal.SciFiRotation/spaceEnum

// WaypointMover/FollowType

// WaypointMover/FollowType

// WaypointMover/LoopType

// WaypointMover/LoopType

// UnityEngine.Component

// UnityEngine.Component

// UnityEngine.GameObject

// UnityEngine.GameObject

// UnityEngine.Collider

// UnityEngine.Collider

// System.NotSupportedException

// System.NotSupportedException

// UnityEngine.ParticleSystem

// UnityEngine.ParticleSystem

// UnityEngine.Rigidbody

// UnityEngine.Rigidbody

// UnityEngine.Transform

// UnityEngine.Transform

// UnityEngine.Camera
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields
{
	// UnityEngine.Camera/CameraCallback UnityEngine.Camera::onPreCull
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreCull_4;
	// UnityEngine.Camera/CameraCallback UnityEngine.Camera::onPreRender
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreRender_5;
	// UnityEngine.Camera/CameraCallback UnityEngine.Camera::onPostRender
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPostRender_6;
};

// UnityEngine.Camera

// UnityEngine.Light

// UnityEngine.Light

// UnityEngine.MonoBehaviour

// UnityEngine.MonoBehaviour

// UnityEngine.WheelCollider

// UnityEngine.WheelCollider

// IgnoreCollisions

// IgnoreCollisions

// VisCircle.PowerUpAnimation

// VisCircle.PowerUpAnimation

// SciFiArsenal.SciFiButtonScript

// SciFiArsenal.SciFiButtonScript

// SciFiArsenal.SciFiDragMouseOrbit

// SciFiArsenal.SciFiDragMouseOrbit

// SciFiArsenal.SciFiFireProjectile

// SciFiArsenal.SciFiFireProjectile

// SciFiArsenal.SciFiLightFade

// SciFiArsenal.SciFiLightFade

// SciFiArsenal.SciFiLoadSceneOnClick

// SciFiArsenal.SciFiLoadSceneOnClick

// SciFiArsenal.SciFiLoadSceneOnClick2

// SciFiArsenal.SciFiLoadSceneOnClick2

// SciFiArsenal.SciFiLoopScript

// SciFiArsenal.SciFiLoopScript

// SciFiArsenal.SciFiProjectileScript

// SciFiArsenal.SciFiProjectileScript

// SciFiArsenal.SciFiRotation

// SciFiArsenal.SciFiRotation

// Waypoint

// Waypoint

// WaypointMover

// WaypointMover

// WaypointsHolder

// WaypointsHolder

// wheelAi

// wheelAi

// UnityEngine.UI.Text
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_StaticFields
{
	// UnityEngine.Material UnityEngine.UI.Text::s_DefaultText
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___s_DefaultText_41;
};

// UnityEngine.UI.Text
#ifdef __clang__
#pragma clang diagnostic pop
#endif
// UnityEngine.Collider[]
struct ColliderU5BU5D_t94A9D70F63D095AFF2A9B4613012A5F7F3141787  : public RuntimeArray
{
	ALIGN_FIELD (8) Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* m_Items[1];

	inline Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
// Waypoint[]
struct WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672  : public RuntimeArray
{
	ALIGN_FIELD (8) Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* m_Items[1];

	inline Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
// UnityEngine.GameObject[]
struct GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF  : public RuntimeArray
{
	ALIGN_FIELD (8) GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* m_Items[1];

	inline GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline GameObject_t76FEDD663AB33C991A9C9A23129337651094216F** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline GameObject_t76FEDD663AB33C991A9C9A23129337651094216F** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
// UnityEngine.ParticleSystem[]
struct ParticleSystemU5BU5D_tC5E33DA557C9C2064085128B3530C5F04D48F6E6  : public RuntimeArray
{
	ALIGN_FIELD (8) ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* m_Items[1];

	inline ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
// System.Object[]
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


// T UnityEngine.Component::GetComponent<System.Object>()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
// T System.Collections.Generic.List`1<System.Object>::get_Item(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
// System.Int32 System.Collections.Generic.List`1<System.Object>::get_Count()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
// T[] UnityEngine.Component::GetComponentsInChildren<System.Object>()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* Component_GetComponentsInChildren_TisRuntimeObject_m1F5B6FC0689B07D4FAAC0C605D9B2933A9B32543_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
// System.Void System.Collections.Generic.List`1<System.Object>::Add(T)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
// System.Void System.Collections.Generic.List`1<System.Object>::RemoveAt(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1_RemoveAt_m54F62297ADEE4D4FDA697F49ED807BF901201B54_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
// T UnityEngine.GameObject::AddComponent<System.Object>()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* GameObject_AddComponent_TisRuntimeObject_m69B93700FACCF372F5753371C6E8FB780800B824_gshared (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method) ;
// System.Void System.Collections.Generic.List`1<System.Object>::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
// T UnityEngine.GameObject::GetComponent<System.Object>()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* GameObject_GetComponent_TisRuntimeObject_m6EAED4AA356F0F48288F67899E5958792395563B_gshared (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method) ;
// T UnityEngine.Object::Instantiate<System.Object>(T,UnityEngine.Vector3,UnityEngine.Quaternion)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Object_Instantiate_TisRuntimeObject_m249A6BA4F2F19C2D3CE217D4D31847DF0EF03EFE_gshared (RuntimeObject* ___0_original, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_position, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___2_rotation, const RuntimeMethod* method) ;

// System.Void UnityEngine.WheelCollider::GetWorldPose(UnityEngine.Vector3&,UnityEngine.Quaternion&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890 (WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___0_pos, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* ___1_quat, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::set_position(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::set_rotation(UnityEngine.Quaternion)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_value, const RuntimeMethod* method) ;
// System.Void UnityEngine.MonoBehaviour::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, const RuntimeMethod* method) ;
// T UnityEngine.Component::GetComponent<UnityEngine.Collider>()
inline Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* Component_GetComponent_TisCollider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76_m820398EDBF1D3766C3166A0C323A127662A29A14 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
// System.Boolean UnityEngine.Object::op_Implicit(UnityEngine.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_exists, const RuntimeMethod* method) ;
// System.Void UnityEngine.Physics::IgnoreCollision(UnityEngine.Collider,UnityEngine.Collider)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Physics_IgnoreCollision_mFBAAD9B91D488802086C1A1C96447CE4C869211D (Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___0_collider1, Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___1_collider2, const RuntimeMethod* method) ;
// System.Void UnityEngine.Gizmos::set_color(UnityEngine.Color)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Gizmos_set_color_m53927A2741937484180B20B55F7F20F8F60C5797 (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_value, const RuntimeMethod* method) ;
// UnityEngine.Transform UnityEngine.Component::get_transform()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Transform::get_position()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
// System.Void UnityEngine.Gizmos::DrawSphere(UnityEngine.Vector3,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Gizmos_DrawSphere_mC7B2862BBDB3141A63B83F0F1E56E30101D4F472 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_center, float ___1_radius, const RuntimeMethod* method) ;
// System.Boolean System.String::op_Inequality(System.String,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
// System.Void UnityEngine.Vector3::.ctor(System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
// System.Void UnityEngine.Gizmos::DrawIcon(UnityEngine.Vector3,System.String,System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Gizmos_DrawIcon_m676C6510EFA7C3A425A7B42BD2745B1731A261CE (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_center, String_t* ___1_name, bool ___2_allowScaling, const RuntimeMethod* method) ;
// System.Boolean UnityEngine.Object::op_Equality(UnityEngine.Object,UnityEngine.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
// UnityEngine.GameObject UnityEngine.Component::get_gameObject()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
// System.String UnityEngine.Object::get_name()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
// System.String System.String::Concat(System.String,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
// System.Void UnityEngine.Debug::LogWarning(System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
// T UnityEngine.Component::GetComponent<UnityEngine.Rigidbody>()
inline Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
// System.Void UnityEngine.Rigidbody::set_freezeRotation(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody_set_freezeRotation_m6D049F82E9133020C31EEFB35A179A56364325DC (Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* __this, bool ___0_value, const RuntimeMethod* method) ;
// T System.Collections.Generic.List`1<Waypoint>::get_Item(System.Int32)
inline Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789 (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* (*) (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
// UnityEngine.Transform UnityEngine.GameObject::get_transform()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method) ;
// UnityEngine.Vector3 WaypointMover::IgnorePositionByAxis(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WaypointMover_IgnorePositionByAxis_m63B933CD9AFDA9355409D65913F757D6150A21FE (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_positionToUpdate, const RuntimeMethod* method) ;
// System.Single UnityEngine.Vector3::Distance(UnityEngine.Vector3,UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
// System.Int32 System.Collections.Generic.List`1<Waypoint>::get_Count()
inline int32_t List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
// UnityEngine.Vector3 UnityEngine.Transform::get_forward()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_forward_mFCFACF7165FDAB21E80E384C494DF278386CEE2F (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
// System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Physics_CapsuleCast_m0A540F025E5170C56348DBB377795CFA2EE9AFEE (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_point1, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_point2, float ___2_radius, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___3_direction, RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* ___4_hitInfo, float ___5_maxDistance, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.RaycastHit::get_point()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39 (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* __this, const RuntimeMethod* method) ;
// System.Boolean UnityEngine.Vector3::op_Inequality(UnityEngine.Vector3,UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Inequality_m9F170CDFBF1E490E559DA5D06D6547501A402BBF_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::LookAt(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_LookAt_mFEF7353E4CAEB85D5F7CEEF9276C3B8D6E314C6C (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_worldPosition, const RuntimeMethod* method) ;
// System.Single UnityEngine.Time::get_time()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B (const RuntimeMethod* method) ;
// System.Void UnityEngine.Component::SendMessage(System.String,UnityEngine.SendMessageOptions)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Component_SendMessage_mE7EC28A90F64DCD40E4406BDDBA471CF278DDA37 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, String_t* ___0_methodName, int32_t ___1_options, const RuntimeMethod* method) ;
// System.Void WaypointMover::ChangeWaypointMoverSpeed(System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void WaypointMover_ChangeWaypointMoverSpeed_m5A78628E9ACA8F9076662C4F69EA401E8D175620_inline (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, float ___0_newSpeed, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::op_Subtraction(UnityEngine.Vector3,UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
// UnityEngine.Quaternion UnityEngine.Quaternion::LookRotation(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_LookRotation_mDB2CCA75B8E1AB98104F2A6E1A1EA57D0D1298D7 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_forward, const RuntimeMethod* method) ;
// UnityEngine.Quaternion UnityEngine.Transform::get_rotation()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
// System.Single UnityEngine.Time::get_deltaTime()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865 (const RuntimeMethod* method) ;
// UnityEngine.Quaternion UnityEngine.Quaternion::Slerp(UnityEngine.Quaternion,UnityEngine.Quaternion,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Slerp_m0A9969F500E7716EA4F6BC4E7D5464372D8E9E15 (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_a, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___1_b, float ___2_t, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::get_forward()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A_inline (const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::op_Multiply(UnityEngine.Vector3,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::Translate(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_Translate_m018D015E89C8CB743C54A21B4A1C5202EBF6297A (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_translation, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::get_up()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline (const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::Rotate(UnityEngine.Vector3,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_Rotate_m35B44707FE16FF8015D519D8C162C0B4A85D6D1F (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_axis, float ___1_angle, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::op_UnaryNegation(UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::MoveTowards(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_MoveTowards_m0363264647799F3173AC37F8E819F98298249B08_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_current, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_target, float ___2_maxDistanceDelta, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::SmoothDamp(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3&,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_SmoothDamp_mF673AC30464B7DF671A0556140EB6E9DD75827ED_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_current, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_target, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___2_currentVelocity, float ___3_smoothTime, const RuntimeMethod* method) ;
// UnityEngine.Vector2 UnityEngine.Vector2::op_Implicit(UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_v, const RuntimeMethod* method) ;
// System.Void WaypointMover::SmoothLookAt2D(UnityEngine.Transform,UnityEngine.Vector2,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_SmoothLookAt2D_m03C96B7DFF675A3BF07E5C31AEFE3E5075BC6100 (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_objectTransform, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_targetPosition, float ___2_smoothingValue, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::get_right()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0_inline (const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Transform::get_right()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_right_mC6DC057C23313802E2186A9E0DB760D795A758A4 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
// System.Single UnityEngine.Vector3::Angle(UnityEngine.Vector3,UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Angle_mB16906B482814C140FE5BA9D041D2DC11E42A68D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_from, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_to, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Quaternion::get_eulerAngles()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::set_eulerAngles(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_eulerAngles_m9F0BC484A7915A51FAB87230644229B75BACA004 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
// System.Single UnityEngine.Mathf::Lerp(System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline (float ___0_a, float ___1_b, float ___2_t, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Transform::get_localEulerAngles()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_localEulerAngles_m358AA9AE8FA24FD1BB7842D231C8644D1C2910C6 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::set_localEulerAngles(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_localEulerAngles_m0458551662A1A51FDCA4C0417282B25D391661DF (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector2::op_Implicit(UnityEngine.Vector2)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_v, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Transform::InverseTransformPoint(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_InverseTransformPoint_m18CD395144D9C78F30E15A5B82B6670E792DBA5D (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_position, const RuntimeMethod* method) ;
// System.Single UnityEngine.Mathf::LerpAngle(System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_LerpAngle_m0653422E15193C2E4A4E5AF05236B6315C789C23_inline (float ___0_a, float ___1_b, float ___2_t, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::Rotate(System.Single,System.Single,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_Rotate_m7EA47AD57F43D478CCB0523D179950EE49CDA3E2 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, float ___0_xAngle, float ___1_yAngle, float ___2_zAngle, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::get_zero()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) ;
// T[] UnityEngine.Component::GetComponentsInChildren<Waypoint>()
inline WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672* Component_GetComponentsInChildren_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_mB0FC9812323B908297FC1AFE88647432AA1505D4 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponentsInChildren_TisRuntimeObject_m1F5B6FC0689B07D4FAAC0C605D9B2933A9B32543_gshared)(__this, method);
}
// System.Void System.Collections.Generic.List`1<Waypoint>::Add(T)
inline void List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_inline (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* __this, Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE*, Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
// System.Void WaypointsHolder::Clean()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointsHolder_Clean_m1DD3720DD6533BF7BCE3740F346D256A343C578A (WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* __this, const RuntimeMethod* method) ;
// System.Void System.Collections.Generic.List`1<Waypoint>::RemoveAt(System.Int32)
inline void List_1_RemoveAt_m83B7A7ED93B3F8BA72190C392A66062077991027 (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	((  void (*) (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE*, int32_t, const RuntimeMethod*))List_1_RemoveAt_m54F62297ADEE4D4FDA697F49ED807BF901201B54_gshared)(__this, ___0_index, method);
}
// System.Void UnityEngine.GameObject::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameObject__ctor_m7D0340DE160786E6EFA8DABD39EC3B694DA30AAD (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method) ;
// System.Void UnityEngine.Object::set_name(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_set_name_mC79E6DC8FFD72479C90F0C4CC7F42A0FEAF5AE47 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, String_t* ___0_value, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::set_parent(UnityEngine.Transform)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_parent_m9BD5E563B539DD5BEC342736B03F97B38A243234 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_value, const RuntimeMethod* method) ;
// T UnityEngine.GameObject::AddComponent<Waypoint>()
inline Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* GameObject_AddComponent_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_m785A0ABD5EABE3A4F7CF5226111DA1DB4273C782 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method)
{
	return ((  Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))GameObject_AddComponent_TisRuntimeObject_m69B93700FACCF372F5753371C6E8FB780800B824_gshared)(__this, method);
}
// System.Void UnityEngine.Gizmos::DrawLine(UnityEngine.Vector3,UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Gizmos_DrawLine_mB139054F55D615637A39A3127AADB16043387F8A (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_from, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_to, const RuntimeMethod* method) ;
// System.Void UnityEngine.Color::.ctor(System.Single,System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) ;
// System.Void System.Collections.Generic.List`1<Waypoint>::.ctor()
inline void List_1__ctor_mAAFCE295FBB7FE987F32A92E141CC10276F18691 (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
// T UnityEngine.Component::GetComponent<UnityEngine.Transform>()
inline Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* Component_GetComponent_TisTransform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_m60E86366B3E431D4C4A549CF4FE5951087686F7F (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
// UnityEngine.Vector3 UnityEngine.Transform::get_localPosition()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
// UnityEngine.Quaternion UnityEngine.Transform::get_localRotation()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Transform_get_localRotation_mD53D37611A5DAE93EC6C7BBCAC337408C5CACA77 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Transform::get_localScale()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_localScale_m804A002A53A645CDFCD15BB0F37209162720363F (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::op_Addition(UnityEngine.Vector3,UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::set_localPosition(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_localPosition_mDE1C997F7D79C0885210B7732B4BA50EE7D73134 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
// System.Single UnityEngine.Mathf::InverseLerp(System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D_inline (float ___0_a, float ___1_b, float ___2_value, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::op_Multiply(System.Single,UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline (float ___0_d, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_a, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::set_localScale(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::Rotate(UnityEngine.Vector3,UnityEngine.Space)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_Rotate_mAE711E1B1F639FDBA7B456E1E1B35DB90EEB737A (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_eulers, int32_t ___1_relativeTo, const RuntimeMethod* method) ;
// System.Boolean UnityEngine.Application::get_isPlaying()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Application_get_isPlaying_m25B0ABDFEF54F5370CD3F263A813540843D00F34 (const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::set_localRotation(UnityEngine.Quaternion)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_localRotation_mAB4A011D134BA58AB780BECC0025CA65F16185FA (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_value, const RuntimeMethod* method) ;
// UnityEngine.GameObject UnityEngine.GameObject::Find(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* GameObject_Find_m7A669B4EEC2617AB82F6E3FF007CDCD9F21DB300 (String_t* ___0_name, const RuntimeMethod* method) ;
// T UnityEngine.GameObject::GetComponent<SciFiArsenal.SciFiFireProjectile>()
inline SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* GameObject_GetComponent_TisSciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68_m3B2EC44BA664B479AF49E79B2749BBAFE215FCA2 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method)
{
	return ((  SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))GameObject_GetComponent_TisRuntimeObject_m6EAED4AA356F0F48288F67899E5958792395563B_gshared)(__this, method);
}
// System.Void SciFiArsenal.SciFiButtonScript::getProjectileNames()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiButtonScript_getProjectileNames_mB5530CC4D84424EAF09EDD6F83BCAA7FC7B427E2 (SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* __this, const RuntimeMethod* method) ;
// UnityEngine.Transform UnityEngine.Transform::Find(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* Transform_Find_m3087032B0E1C5B96A2D2C27020BAEAE2DA08F932 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, String_t* ___0_n, const RuntimeMethod* method) ;
// T UnityEngine.Component::GetComponent<UnityEngine.UI.Text>()
inline Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* Component_GetComponent_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_mB85C5C0EEF6535E3FC0DBFC14E39FA5A51B6F888 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
// T UnityEngine.GameObject::GetComponent<SciFiArsenal.SciFiProjectileScript>()
inline SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25* GameObject_GetComponent_TisSciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25_mBFE0AE078925AEB36508AEFAEC11E27428A77B11 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method)
{
	return ((  SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))GameObject_GetComponent_TisRuntimeObject_m6EAED4AA356F0F48288F67899E5958792395563B_gshared)(__this, method);
}
// System.Void UnityEngine.Rect::.ctor(System.Single,System.Single,System.Single,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23 (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, float ___0_x, float ___1_y, float ___2_width, float ___3_height, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Input::get_mousePosition()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C (const RuntimeMethod* method) ;
// System.Int32 UnityEngine.Screen::get_height()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Screen_get_height_m01A3102DE71EE1FBEA51D09D6B0261CF864FE8F9 (const RuntimeMethod* method) ;
// System.Void UnityEngine.Vector2::.ctor(System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) ;
// System.Boolean UnityEngine.Rect::Contains(UnityEngine.Vector2)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_point, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Transform::get_eulerAngles()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_eulerAngles_mCAAF48EFCF628F1ED91C2FFE75A4FD19C039DD6A (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
// System.Boolean UnityEngine.Input::GetMouseButton(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA (int32_t ___0_button, const RuntimeMethod* method) ;
// System.Single UnityEngine.Input::GetAxis(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62 (String_t* ___0_axisName, const RuntimeMethod* method) ;
// System.Single SciFiArsenal.SciFiDragMouseOrbit::ClampAngle(System.Single,System.Single,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float SciFiDragMouseOrbit_ClampAngle_m5B97010144919B1F791AFC6EEE973742D1B36C5C (float ___0_angle, float ___1_min, float ___2_max, const RuntimeMethod* method) ;
// UnityEngine.Quaternion UnityEngine.Quaternion::Euler(System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Euler_m9262AB29E3E9CE94EF71051F38A28E82AEC73F90_inline (float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
// System.Single UnityEngine.Mathf::Clamp(System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF_inline (float ___0_value, float ___1_min, float ___2_max, const RuntimeMethod* method) ;
// System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Physics_Linecast_m4F2A0744FE106002EE26D12B6137FC21C019B932 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_start, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_end, RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* ___2_hitInfo, const RuntimeMethod* method) ;
// System.Single UnityEngine.RaycastHit::get_distance()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78 (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* __this, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Quaternion::op_Multiply(UnityEngine.Quaternion,UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_op_Multiply_mE1EBA73F9173432B50F8F17CE8190C5A7986FB8C (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_rotation, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_point, const RuntimeMethod* method) ;
// T UnityEngine.GameObject::GetComponent<SciFiArsenal.SciFiButtonScript>()
inline SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* GameObject_GetComponent_TisSciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870_mADBF88BCDCE4F53AF0E50C60E2BED90459FD18E3 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method)
{
	return ((  SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))GameObject_GetComponent_TisRuntimeObject_m6EAED4AA356F0F48288F67899E5958792395563B_gshared)(__this, method);
}
// System.Boolean UnityEngine.Input::GetKeyDown(UnityEngine.KeyCode)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2 (int32_t ___0_key, const RuntimeMethod* method) ;
// System.Void SciFiArsenal.SciFiFireProjectile::nextEffect()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiFireProjectile_nextEffect_mB33DF89F8CDFA0E871CF6EB2239FA93756A47937 (SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* __this, const RuntimeMethod* method) ;
// System.Void SciFiArsenal.SciFiFireProjectile::previousEffect()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiFireProjectile_previousEffect_m502F4A3D08CB0A577AF6681B6A22FACB1AED00AD (SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* __this, const RuntimeMethod* method) ;
// System.Boolean SciFiArsenal.SciFiButtonScript::overButton()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SciFiButtonScript_overButton_m20FB775D82D1508950C858477034292F63F75886 (SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* __this, const RuntimeMethod* method) ;
// UnityEngine.Camera UnityEngine.Camera::get_main()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF (const RuntimeMethod* method) ;
// UnityEngine.Ray UnityEngine.Camera::ScreenPointToRay(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00 Camera_ScreenPointToRay_m2887B9A49880B7AB670C57D66B67D6A6689FE315 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_pos, const RuntimeMethod* method) ;
// System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685 (Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00 ___0_ray, RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* ___1_hitInfo, float ___2_maxDistance, const RuntimeMethod* method) ;
// UnityEngine.Quaternion UnityEngine.Quaternion::get_identity()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_get_identity_m7E701AE095ED10FD5EA0B50ABCFDE2EEFF2173A5_inline (const RuntimeMethod* method) ;
// T UnityEngine.Object::Instantiate<UnityEngine.GameObject>(T,UnityEngine.Vector3,UnityEngine.Quaternion)
inline GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___0_original, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_position, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___2_rotation, const RuntimeMethod* method)
{
	return ((  GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974, const RuntimeMethod*))Object_Instantiate_TisRuntimeObject_m249A6BA4F2F19C2D3CE217D4D31847DF0EF03EFE_gshared)(___0_original, ___1_position, ___2_rotation, method);
}
// T UnityEngine.GameObject::GetComponent<UnityEngine.Rigidbody>()
inline Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* GameObject_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m2D7F86C77ECF9B82AAC077B511F1004280571B90 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method)
{
	return ((  Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))GameObject_GetComponent_TisRuntimeObject_m6EAED4AA356F0F48288F67899E5958792395563B_gshared)(__this, method);
}
// System.Void UnityEngine.Rigidbody::AddForce(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198 (Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_force, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.RaycastHit::get_normal()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5 (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* __this, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Ray::get_origin()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Ray_get_origin_m97604A8F180316A410DCD77B7D74D04522FA1BA6 (Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00* __this, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Ray::get_direction()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Ray_get_direction_m21C2D22D3BD4A683BD4DC191AB22DD05F5EC2086 (Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00* __this, const RuntimeMethod* method) ;
// UnityEngine.Color UnityEngine.Color::get_yellow()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_yellow_m66637FA14383E8D74F24AE256B577CE1D55D469F_inline (const RuntimeMethod* method) ;
// System.Void UnityEngine.Debug::DrawRay(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Color)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_DrawRay_mB172868181856F153732BB56C0BE1C58EE598F53 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_start, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_dir, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___2_color, const RuntimeMethod* method) ;
// System.Void UnityEngine.SceneManagement.SceneManager::LoadScene(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E (String_t* ___0_sceneName, const RuntimeMethod* method) ;
// System.Void SciFiArsenal.SciFiLoopScript::PlayEffect()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoopScript_PlayEffect_m44618A0F38C4FF16D37CB07CD245B716BBCC8FBD (SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* __this, const RuntimeMethod* method) ;
// UnityEngine.Coroutine UnityEngine.MonoBehaviour::StartCoroutine(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* MonoBehaviour_StartCoroutine_m10C4B693B96175C42B0FD00911E072701C220DB4 (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, String_t* ___0_methodName, const RuntimeMethod* method) ;
// System.Void SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::.ctor(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CEffectLoopU3Ed__4__ctor_m128D913002532BD9711F8B37A031CBE97A31D174 (U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
// System.Void System.Object::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
// System.Void UnityEngine.WaitForSeconds::.ctor(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* __this, float ___0_seconds, const RuntimeMethod* method) ;
// System.Void UnityEngine.Object::Destroy(UnityEngine.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_obj, const RuntimeMethod* method) ;
// System.Void System.NotSupportedException::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* __this, const RuntimeMethod* method) ;
// UnityEngine.Quaternion UnityEngine.Quaternion::op_Multiply(UnityEngine.Quaternion,UnityEngine.Quaternion)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_op_Multiply_mCB375FCCC12A2EC8F9EB824A1BFB4453B58C2012_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_lhs, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___1_rhs, const RuntimeMethod* method) ;
// System.Void UnityEngine.Object::Destroy(UnityEngine.Object,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_obj, float ___1_t, const RuntimeMethod* method) ;
// UnityEngine.Quaternion UnityEngine.Quaternion::FromToRotation(UnityEngine.Vector3,UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_FromToRotation_mCB3100F93637E72455388B901C36EF8A25DFDB9A (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_fromDirection, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_toDirection, const RuntimeMethod* method) ;
// UnityEngine.GameObject UnityEngine.Collision::get_gameObject()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E (Collision_tBCC6AEBD9A63E6DA2E50660DAC03CDCB1FF7A9B0* __this, const RuntimeMethod* method) ;
// System.String UnityEngine.GameObject::get_tag()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* GameObject_get_tag_mEDD27BF795072834D656B286CBE51B2C99747805 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method) ;
// System.Boolean System.String::op_Equality(System.String,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
// System.String System.String::Concat(System.String,System.String,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
// T[] UnityEngine.Component::GetComponentsInChildren<UnityEngine.ParticleSystem>()
inline ParticleSystemU5BU5D_tC5E33DA557C9C2064085128B3530C5F04D48F6E6* Component_GetComponentsInChildren_TisParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1_m4A6A34D7CF3ABDD3C27C0FB3017B5B0D05AF407D (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  ParticleSystemU5BU5D_tC5E33DA557C9C2064085128B3530C5F04D48F6E6* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponentsInChildren_TisRuntimeObject_m1F5B6FC0689B07D4FAAC0C605D9B2933A9B32543_gshared)(__this, method);
}
// System.Boolean System.String::Contains(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_Contains_m6D77B121FADA7CA5F397C0FABB65DA62DF03B6C3 (String_t* __this, String_t* ___0_value, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::SetParent(UnityEngine.Transform)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_SetParent_m6677538B60246D958DD91F931C50F969CCBB5250 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_p, const RuntimeMethod* method) ;
// T UnityEngine.GameObject::GetComponent<UnityEngine.Light>()
inline Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method)
{
	return ((  Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))GameObject_GetComponent_TisRuntimeObject_m6EAED4AA356F0F48288F67899E5958792395563B_gshared)(__this, method);
}
// System.Single UnityEngine.Light::get_intensity()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Light_get_intensity_m8FA28D515853068A93FA68B2148809BBEE4E710F (Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* __this, const RuntimeMethod* method) ;
// System.Void UnityEngine.MonoBehaviour::print(System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour_print_m9E6FF71C673B651F35DD418C293CFC50C46803B6 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
// System.Void UnityEngine.Light::set_intensity(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Light_set_intensity_mE4820C7F39F490B92ED5EA0C3AADA7C0775BE854 (Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* __this, float ___0_value, const RuntimeMethod* method) ;
// System.Void UnityEngine.Transform::Rotate(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_Rotate_m2A308205498AFEEA3DF784B1C86E4F7C126CA2EE (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_eulers, const RuntimeMethod* method) ;
// System.Boolean UnityEngine.Vector3::op_Equality(UnityEngine.Vector3,UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Vector3::SmoothDamp(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3&,System.Single,System.Single,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_SmoothDamp_mAF61EA22D4906BF87DD00A91FB4F6AC0C54C495A (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_current, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_target, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___2_currentVelocity, float ___3_smoothTime, float ___4_maxSpeed, float ___5_deltaTime, const RuntimeMethod* method) ;
// System.Single UnityEngine.Vector3::get_sqrMagnitude()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) ;
// System.Single UnityEngine.Vector3::Dot(UnityEngine.Vector3,UnityEngine.Vector3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Quaternion::Internal_ToEulerRad(UnityEngine.Quaternion)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_Internal_ToEulerRad_m5BD0EEC543120C320DC77FCCDFD2CE2E6BD3F1A8 (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_rotation, const RuntimeMethod* method) ;
// UnityEngine.Vector3 UnityEngine.Quaternion::Internal_MakePositive(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_Internal_MakePositive_m73E2D01920CB0DFE661A55022C129E8617F0C9A8 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) ;
// System.Single UnityEngine.Mathf::Clamp01(System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) ;
// System.Single UnityEngine.Mathf::Repeat(System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Repeat_m6F1560A163481BB311D685294E1B463C3E4EB3BA_inline (float ___0_t, float ___1_length, const RuntimeMethod* method) ;
// UnityEngine.Quaternion UnityEngine.Quaternion::Internal_FromEulerRad(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Internal_FromEulerRad_m66D4475341F53949471E6870FB5C5E4A5E9BA93E (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) ;
// System.Void UnityEngine.Quaternion::.ctor(System.Single,System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void wheelAi::FixedUpdate()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void wheelAi_FixedUpdate_m2DD8A6001DC1C53AA7EFE26C0D6A447DC45CEF5C (wheelAi_tCCC5B4FEDDC59EA37DE0318664D657D503BA4F43* __this, const RuntimeMethod* method) 
{
	{
		// AIFL.GetWorldPose(out AIPOS, out AIROT);
		WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* L_0 = __this->___AIFL_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_1 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___AIPOS_12);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* L_2 = (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)(&__this->___AIROT_13);
		NullCheck(L_0);
		WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890(L_0, L_1, L_2, NULL);
		// AITFL.position = AIPOS;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3 = __this->___AITFL_8;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = __this->___AIPOS_12;
		NullCheck(L_3);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_3, L_4, NULL);
		// AITFL.rotation = AIROT;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_5 = __this->___AITFL_8;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_6 = __this->___AIROT_13;
		NullCheck(L_5);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_5, L_6, NULL);
		// AIFR.GetWorldPose(out AIPOS, out AIROT);
		WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* L_7 = __this->___AIFR_5;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_8 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___AIPOS_12);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* L_9 = (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)(&__this->___AIROT_13);
		NullCheck(L_7);
		WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890(L_7, L_8, L_9, NULL);
		// AITFR.position = AIPOS;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_10 = __this->___AITFR_9;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11 = __this->___AIPOS_12;
		NullCheck(L_10);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_10, L_11, NULL);
		// AITFR.rotation = AIROT;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_12 = __this->___AITFR_9;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_13 = __this->___AIROT_13;
		NullCheck(L_12);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_12, L_13, NULL);
		// AIRL.GetWorldPose(out AIPOS, out AIROT);
		WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* L_14 = __this->___AIRL_6;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_15 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___AIPOS_12);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* L_16 = (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)(&__this->___AIROT_13);
		NullCheck(L_14);
		WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890(L_14, L_15, L_16, NULL);
		// AITRL.position = AIPOS;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_17 = __this->___AITRL_10;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18 = __this->___AIPOS_12;
		NullCheck(L_17);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_17, L_18, NULL);
		// AITRL.rotation = AIROT;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_19 = __this->___AITRL_10;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_20 = __this->___AIROT_13;
		NullCheck(L_19);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_19, L_20, NULL);
		// AIRR.GetWorldPose(out AIPOS, out AIROT);
		WheelCollider_t4E35407C7AFEFA3DB30E9FFE3C38C9A5C5933481* L_21 = __this->___AIRR_7;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_22 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___AIPOS_12);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* L_23 = (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)(&__this->___AIROT_13);
		NullCheck(L_21);
		WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890(L_21, L_22, L_23, NULL);
		// AITRR.position = AIPOS;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_24 = __this->___AITRR_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25 = __this->___AIPOS_12;
		NullCheck(L_24);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_24, L_25, NULL);
		// AITRR.rotation = AIROT;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_26 = __this->___AITRR_11;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_27 = __this->___AIROT_13;
		NullCheck(L_26);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_26, L_27, NULL);
		// }
		return;
	}
}
// System.Void wheelAi::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void wheelAi__ctor_m2A46BF09B779AD45D5A683724E839916A9DDE550 (wheelAi_tCCC5B4FEDDC59EA37DE0318664D657D503BA4F43* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void IgnoreCollisions::Start()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IgnoreCollisions_Start_mB222A5F88D64F8B807D648083E3E289557710CE4 (IgnoreCollisions_t97FF5E4A550ED022664DAE57777F03EF48A217EC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisCollider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76_m820398EDBF1D3766C3166A0C323A127662A29A14_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		// if (ignoreCollisionWith.Length > 0  &&  GetComponent<Collider>())
		ColliderU5BU5D_t94A9D70F63D095AFF2A9B4613012A5F7F3141787* L_0 = __this->___ignoreCollisionWith_4;
		NullCheck(L_0);
		if (!(((RuntimeArray*)L_0)->max_length))
		{
			goto IL_003c;
		}
	}
	{
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_1;
		L_1 = Component_GetComponent_TisCollider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76_m820398EDBF1D3766C3166A0C323A127662A29A14(__this, Component_GetComponent_TisCollider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76_m820398EDBF1D3766C3166A0C323A127662A29A14_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_1, NULL);
		if (!L_2)
		{
			goto IL_003c;
		}
	}
	{
		// for (int i = 0; i< ignoreCollisionWith.Length; i++)
		V_0 = 0;
		goto IL_0031;
	}

IL_001a:
	{
		// Physics.IgnoreCollision(GetComponent<Collider>(), ignoreCollisionWith[i]);
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_3;
		L_3 = Component_GetComponent_TisCollider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76_m820398EDBF1D3766C3166A0C323A127662A29A14(__this, Component_GetComponent_TisCollider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76_m820398EDBF1D3766C3166A0C323A127662A29A14_RuntimeMethod_var);
		ColliderU5BU5D_t94A9D70F63D095AFF2A9B4613012A5F7F3141787* L_4 = __this->___ignoreCollisionWith_4;
		int32_t L_5 = V_0;
		NullCheck(L_4);
		int32_t L_6 = L_5;
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_7 = (L_4)->GetAt(static_cast<il2cpp_array_size_t>(L_6));
		Physics_IgnoreCollision_mFBAAD9B91D488802086C1A1C96447CE4C869211D(L_3, L_7, NULL);
		// for (int i = 0; i< ignoreCollisionWith.Length; i++)
		int32_t L_8 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_8, 1));
	}

IL_0031:
	{
		// for (int i = 0; i< ignoreCollisionWith.Length; i++)
		int32_t L_9 = V_0;
		ColliderU5BU5D_t94A9D70F63D095AFF2A9B4613012A5F7F3141787* L_10 = __this->___ignoreCollisionWith_4;
		NullCheck(L_10);
		if ((((int32_t)L_9) < ((int32_t)((int32_t)(((RuntimeArray*)L_10)->max_length)))))
		{
			goto IL_001a;
		}
	}

IL_003c:
	{
		// }
		return;
	}
}
// System.Void IgnoreCollisions::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IgnoreCollisions__ctor_m6BF01957EC3B05A78B27ED10D59E988F7FA03FE3 (IgnoreCollisions_t97FF5E4A550ED022664DAE57777F03EF48A217EC* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void Waypoint::OnDrawGizmos()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Waypoint_OnDrawGizmos_m6507803A8CDB9BCE8D88995700EE0DCD4F4EB88D (Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	{
		// Gizmos.color = color;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = __this->___color_4;
		Gizmos_set_color_m53927A2741937484180B20B55F7F20F8F60C5797(L_0, NULL);
		// Gizmos.DrawSphere(transform.position, radius);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1;
		L_1 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_1);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_1, NULL);
		float L_3 = __this->___radius_5;
		Gizmos_DrawSphere_mC7B2862BBDB3141A63B83F0F1E56E30101D4F472(L_2, L_3, NULL);
		// if (iconName !="")
		String_t* L_4 = __this->___iconName_6;
		bool L_5;
		L_5 = String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6(L_4, _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709, NULL);
		if (!L_5)
		{
			goto IL_0081;
		}
	}
	{
		// Gizmos.DrawIcon (new Vector3(transform.position.x, transform.position.y+radius*1.5f, transform.position.z), iconName, true);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_6;
		L_6 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_6);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_6, NULL);
		float L_8 = L_7.___x_2;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_9;
		L_9 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_9);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10;
		L_10 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_9, NULL);
		float L_11 = L_10.___y_3;
		float L_12 = __this->___radius_5;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_13;
		L_13 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_13);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14;
		L_14 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_13, NULL);
		float L_15 = L_14.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16;
		memset((&L_16), 0, sizeof(L_16));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_16), L_8, ((float)il2cpp_codegen_add(L_11, ((float)il2cpp_codegen_multiply(L_12, (1.5f))))), L_15, /*hidden argument*/NULL);
		String_t* L_17 = __this->___iconName_6;
		Gizmos_DrawIcon_m676C6510EFA7C3A425A7B42BD2745B1731A261CE(L_16, L_17, (bool)1, NULL);
	}

IL_0081:
	{
		// }
		return;
	}
}
// System.Void Waypoint::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Waypoint__ctor_m6DE0E34AE59F18AB7F0BFCCD477772876C4CBD2C (Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* __this, const RuntimeMethod* method) 
{
	{
		// public float radius = 0.25f;       // Waypoint gizmo size
		__this->___radius_5 = (0.25f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void WaypointMover::Start()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_Start_m106F5FC601DA03969683A6F6D361115F4CD88A76 (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD92C8E4181068E77D27A2F15F7E6093FFFAF2BBA);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	int32_t V_4 = 0;
	{
		// if (waypointsHolder == null)
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_0 = __this->___waypointsHolder_4;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0029;
		}
	}
	{
		// Debug.LogWarning ("No WaypointsHolder attached to " + gameObject.name);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_2;
		L_2 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_2);
		String_t* L_3;
		L_3 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_2, NULL);
		String_t* L_4;
		L_4 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteralD92C8E4181068E77D27A2F15F7E6093FFFAF2BBA, L_3, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9(L_4, NULL);
		// return;
		return;
	}

IL_0029:
	{
		// if (GetComponent<Rigidbody>())
		Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* L_5;
		L_5 = Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8(__this, Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_6;
		L_6 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_5, NULL);
		if (!L_6)
		{
			goto IL_0042;
		}
	}
	{
		// GetComponent<Rigidbody>().freezeRotation = true;
		Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* L_7;
		L_7 = Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8(__this, Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8_RuntimeMethod_var);
		NullCheck(L_7);
		Rigidbody_set_freezeRotation_m6D049F82E9133020C31EEFB35A179A56364325DC(L_7, (bool)1, NULL);
	}

IL_0042:
	{
		// initialMovementSpeed = movementSpeed;
		float L_8 = __this->___movementSpeed_11;
		__this->___initialMovementSpeed_26 = L_8;
		// if (StartFromNearestWaypoint)
		bool L_9 = __this->___StartFromNearestWaypoint_8;
		if (!L_9)
		{
			goto IL_00cb;
		}
	}
	{
		// int nearestWaypointID = 0;
		V_1 = 0;
		// float previousSmallestDistance = Mathf.Infinity;
		V_2 = (std::numeric_limits<float>::infinity());
		// for (int i = 0; i < waypointsHolder.waypoints.Count; i++)
		V_4 = 0;
		goto IL_00ae;
	}

IL_0063:
	{
		// waypointPosition = waypointsHolder.waypoints[i].gameObject.transform.position;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_10 = __this->___waypointsHolder_4;
		NullCheck(L_10);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_11 = L_10->___waypoints_5;
		int32_t L_12 = V_4;
		NullCheck(L_11);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_13;
		L_13 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_11, L_12, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_13);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_14;
		L_14 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_13, NULL);
		NullCheck(L_14);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_15;
		L_15 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_14, NULL);
		NullCheck(L_15);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16;
		L_16 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_15, NULL);
		V_0 = L_16;
		// waypointPosition = IgnorePositionByAxis(waypointPosition);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_17 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18;
		L_18 = WaypointMover_IgnorePositionByAxis_m63B933CD9AFDA9355409D65913F757D6150A21FE(__this, L_17, NULL);
		V_0 = L_18;
		// distance = Vector3.Distance(transform.position, waypointPosition);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_19;
		L_19 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_19);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_20;
		L_20 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_19, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_21 = V_0;
		float L_22;
		L_22 = Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline(L_20, L_21, NULL);
		V_3 = L_22;
		// if (distance < previousSmallestDistance)
		float L_23 = V_3;
		float L_24 = V_2;
		if ((!(((float)L_23) < ((float)L_24))))
		{
			goto IL_00a8;
		}
	}
	{
		// nearestWaypointID = i;
		int32_t L_25 = V_4;
		V_1 = L_25;
		// previousSmallestDistance = distance;
		float L_26 = V_3;
		V_2 = L_26;
	}

IL_00a8:
	{
		// for (int i = 0; i < waypointsHolder.waypoints.Count; i++)
		int32_t L_27 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_27, 1));
	}

IL_00ae:
	{
		// for (int i = 0; i < waypointsHolder.waypoints.Count; i++)
		int32_t L_28 = V_4;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_29 = __this->___waypointsHolder_4;
		NullCheck(L_29);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_30 = L_29->___waypoints_5;
		NullCheck(L_30);
		int32_t L_31;
		L_31 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_30, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		if ((((int32_t)L_28) < ((int32_t)L_31)))
		{
			goto IL_0063;
		}
	}
	{
		// currentWaypoint = nearestWaypointID;
		int32_t L_32 = V_1;
		__this->___currentWaypoint_17 = L_32;
		goto IL_00d2;
	}

IL_00cb:
	{
		// currentWaypoint = 0;
		__this->___currentWaypoint_17 = 0;
	}

IL_00d2:
	{
		// if(MoveOnWayImmediately)
		bool L_33 = __this->___MoveOnWayImmediately_7;
		if (!L_33)
		{
			goto IL_010a;
		}
	}
	{
		// transform.position = waypointsHolder.waypoints[currentWaypoint].gameObject.transform.position;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_34;
		L_34 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_35 = __this->___waypointsHolder_4;
		NullCheck(L_35);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_36 = L_35->___waypoints_5;
		int32_t L_37 = __this->___currentWaypoint_17;
		NullCheck(L_36);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_38;
		L_38 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_36, L_37, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_38);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_39;
		L_39 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_38, NULL);
		NullCheck(L_39);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_40;
		L_40 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_39, NULL);
		NullCheck(L_40);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_41;
		L_41 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_40, NULL);
		NullCheck(L_34);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_34, L_41, NULL);
	}

IL_010a:
	{
		// targetPosition = waypointsHolder.waypoints[currentWaypoint].gameObject.transform.position;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_42 = __this->___waypointsHolder_4;
		NullCheck(L_42);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_43 = L_42->___waypoints_5;
		int32_t L_44 = __this->___currentWaypoint_17;
		NullCheck(L_43);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_45;
		L_45 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_43, L_44, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_45);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_46;
		L_46 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_45, NULL);
		NullCheck(L_46);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_47;
		L_47 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_46, NULL);
		NullCheck(L_47);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_48;
		L_48 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_47, NULL);
		__this->___targetPosition_20 = L_48;
		// targetPosition = IgnorePositionByAxis(targetPosition);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_49 = __this->___targetPosition_20;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_50;
		L_50 = WaypointMover_IgnorePositionByAxis_m63B933CD9AFDA9355409D65913F757D6150A21FE(__this, L_49, NULL);
		__this->___targetPosition_20 = L_50;
		// }
		return;
	}
}
// System.Void WaypointMover::Update()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_Update_m995EA7D26DEF76896E47D9C892DE9D1A516DFA30 (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral11D89EB85396FABB777CACDB484A47B9EA0A0AF1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 V_1;
	memset((&V_1), 0, sizeof(V_1));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_3;
	memset((&V_3), 0, sizeof(V_3));
	int32_t V_4 = 0;
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_5;
	memset((&V_5), 0, sizeof(V_5));
	float V_6 = 0.0f;
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_7;
	memset((&V_7), 0, sizeof(V_7));
	float V_8 = 0.0f;
	float V_9 = 0.0f;
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* G_B31_0 = NULL;
	WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* G_B30_0 = NULL;
	int32_t G_B32_0 = 0;
	WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* G_B32_1 = NULL;
	{
		// if (waypointsHolder == null)  return;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_0 = __this->___waypointsHolder_4;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_000f;
		}
	}
	{
		// if (waypointsHolder == null)  return;
		return;
	}

IL_000f:
	{
		// bool collisionPrevented = false;
		V_0 = (bool)0;
		// Vector3 p1 = transform.position;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_2;
		L_2 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_2);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_2, NULL);
		V_2 = L_3;
		// Vector3 p2 = p1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = V_2;
		V_3 = L_4;
		// if (preventCollisionDistance > 0)
		float L_5 = __this->___preventCollisionDistance_14;
		if ((!(((float)L_5) > ((float)(0.0f)))))
		{
			goto IL_00a9;
		}
	}
	{
		// if (Physics.CapsuleCast (p1, p2, 0.5f, transform.forward, out hit, preventCollisionDistance))
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = V_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = V_3;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_8;
		L_8 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_8);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		L_9 = Transform_get_forward_mFCFACF7165FDAB21E80E384C494DF278386CEE2F(L_8, NULL);
		float L_10 = __this->___preventCollisionDistance_14;
		bool L_11;
		L_11 = Physics_CapsuleCast_m0A540F025E5170C56348DBB377795CFA2EE9AFEE(L_6, L_7, (0.5f), L_9, (&V_1), L_10, NULL);
		if (!L_11)
		{
			goto IL_009d;
		}
	}
	{
		// if (!smoothCollisionPreventing)
		bool L_12 = __this->___smoothCollisionPreventing_15;
		if (L_12)
		{
			goto IL_0059;
		}
	}
	{
		// collisionPrevented = true;
		V_0 = (bool)1;
		goto IL_00a9;
	}

IL_0059:
	{
		// movementSpeed = initialMovementSpeed * Vector3.Distance(transform.position, hit.point) / preventCollisionDistance;
		float L_13 = __this->___initialMovementSpeed_26;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_14;
		L_14 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_14);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_15;
		L_15 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_14, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16;
		L_16 = RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39((&V_1), NULL);
		float L_17;
		L_17 = Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline(L_15, L_16, NULL);
		float L_18 = __this->___preventCollisionDistance_14;
		__this->___movementSpeed_11 = ((float)(((float)il2cpp_codegen_multiply(L_13, L_17))/L_18));
		// if (movementSpeed < initialMovementSpeed/preventCollisionDistance)
		float L_19 = __this->___movementSpeed_11;
		float L_20 = __this->___initialMovementSpeed_26;
		float L_21 = __this->___preventCollisionDistance_14;
		if ((!(((float)L_19) < ((float)((float)(L_20/L_21))))))
		{
			goto IL_00a9;
		}
	}
	{
		// collisionPrevented = true;
		V_0 = (bool)1;
		goto IL_00a9;
	}

IL_009d:
	{
		// movementSpeed = initialMovementSpeed;
		float L_22 = __this->___initialMovementSpeed_26;
		__this->___movementSpeed_11 = L_22;
	}

IL_00a9:
	{
		// if (dynamicWaypointsUpdate  &&  currentWaypoint >= 0)
		bool L_23 = __this->___dynamicWaypointsUpdate_16;
		if (!L_23)
		{
			goto IL_0140;
		}
	}
	{
		int32_t L_24 = __this->___currentWaypoint_17;
		if ((((int32_t)L_24) < ((int32_t)0)))
		{
			goto IL_0140;
		}
	}
	{
		// if(targetPosition != waypointsHolder.waypoints[currentWaypoint].gameObject.transform.position)
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25 = __this->___targetPosition_20;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_26 = __this->___waypointsHolder_4;
		NullCheck(L_26);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_27 = L_26->___waypoints_5;
		int32_t L_28 = __this->___currentWaypoint_17;
		NullCheck(L_27);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_29;
		L_29 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_27, L_28, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_29);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_30;
		L_30 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_29, NULL);
		NullCheck(L_30);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_31;
		L_31 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_30, NULL);
		NullCheck(L_31);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_32;
		L_32 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_31, NULL);
		bool L_33;
		L_33 = Vector3_op_Inequality_m9F170CDFBF1E490E559DA5D06D6547501A402BBF_inline(L_25, L_32, NULL);
		if (!L_33)
		{
			goto IL_0140;
		}
	}
	{
		// targetPosition = waypointsHolder.waypoints[currentWaypoint].gameObject.transform.position;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_34 = __this->___waypointsHolder_4;
		NullCheck(L_34);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_35 = L_34->___waypoints_5;
		int32_t L_36 = __this->___currentWaypoint_17;
		NullCheck(L_35);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_37;
		L_37 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_35, L_36, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_37);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_38;
		L_38 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_37, NULL);
		NullCheck(L_38);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_39;
		L_39 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_38, NULL);
		NullCheck(L_39);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_40;
		L_40 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_39, NULL);
		__this->___targetPosition_20 = L_40;
		// transform.LookAt(targetPosition);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_41;
		L_41 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_42 = __this->___targetPosition_20;
		NullCheck(L_41);
		Transform_LookAt_mFEF7353E4CAEB85D5F7CEEF9276C3B8D6E314C6C(L_41, L_42, NULL);
		// targetPosition = IgnorePositionByAxis(targetPosition);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_43 = __this->___targetPosition_20;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_44;
		L_44 = WaypointMover_IgnorePositionByAxis_m63B933CD9AFDA9355409D65913F757D6150A21FE(__this, L_43, NULL);
		__this->___targetPosition_20 = L_44;
	}

IL_0140:
	{
		// if (!suspended  &&  !collisionPrevented  &&  currentWaypoint >= 0  &&  delayTillTime < Time.time)
		bool L_45 = __this->___suspended_24;
		if (L_45)
		{
			goto IL_0922;
		}
	}
	{
		bool L_46 = V_0;
		if (L_46)
		{
			goto IL_0922;
		}
	}
	{
		int32_t L_47 = __this->___currentWaypoint_17;
		if ((((int32_t)L_47) < ((int32_t)0)))
		{
			goto IL_0922;
		}
	}
	{
		float L_48 = __this->___delayTillTime_21;
		float L_49;
		L_49 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		if ((!(((float)L_48) < ((float)L_49))))
		{
			goto IL_0922;
		}
	}
	{
		// inMove = true;
		__this->___inMove_23 = (bool)1;
		// if(Vector3.Distance(transform.position, targetPosition) < waypointActivationDistance)
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_50;
		L_50 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_50);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_51;
		L_51 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_50, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_52 = __this->___targetPosition_20;
		float L_53;
		L_53 = Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline(L_51, L_52, NULL);
		float L_54 = __this->___waypointActivationDistance_12;
		if ((!(((float)L_53) < ((float)L_54))))
		{
			goto IL_0418;
		}
	}
	{
		// if (waypointsHolder.waypoints[currentWaypoint].delay > 0)
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_55 = __this->___waypointsHolder_4;
		NullCheck(L_55);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_56 = L_55->___waypoints_5;
		int32_t L_57 = __this->___currentWaypoint_17;
		NullCheck(L_56);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_58;
		L_58 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_56, L_57, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_58);
		float L_59 = L_58->___delay_7;
		if ((!(((float)L_59) > ((float)(0.0f)))))
		{
			goto IL_01de;
		}
	}
	{
		// delayTillTime = Time.time + waypointsHolder.waypoints[currentWaypoint].delay;
		float L_60;
		L_60 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_61 = __this->___waypointsHolder_4;
		NullCheck(L_61);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_62 = L_61->___waypoints_5;
		int32_t L_63 = __this->___currentWaypoint_17;
		NullCheck(L_62);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_64;
		L_64 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_62, L_63, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_64);
		float L_65 = L_64->___delay_7;
		__this->___delayTillTime_21 = ((float)il2cpp_codegen_add(L_60, L_65));
	}

IL_01de:
	{
		// if (waypointsHolder.waypoints[currentWaypoint].callFunction != "")
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_66 = __this->___waypointsHolder_4;
		NullCheck(L_66);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_67 = L_66->___waypoints_5;
		int32_t L_68 = __this->___currentWaypoint_17;
		NullCheck(L_67);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_69;
		L_69 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_67, L_68, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_69);
		String_t* L_70 = L_69->___callFunction_9;
		bool L_71;
		L_71 = String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6(L_70, _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709, NULL);
		if (!L_71)
		{
			goto IL_0227;
		}
	}
	{
		// SendMessage (waypointsHolder.waypoints[currentWaypoint].callFunction, SendMessageOptions.DontRequireReceiver);
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_72 = __this->___waypointsHolder_4;
		NullCheck(L_72);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_73 = L_72->___waypoints_5;
		int32_t L_74 = __this->___currentWaypoint_17;
		NullCheck(L_73);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_75;
		L_75 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_73, L_74, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_75);
		String_t* L_76 = L_75->___callFunction_9;
		Component_SendMessage_mE7EC28A90F64DCD40E4406BDDBA471CF278DDA37(__this, L_76, 1, NULL);
	}

IL_0227:
	{
		// if(waypointsHolder.waypoints[currentWaypoint].newMoverSpeed > 0)
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_77 = __this->___waypointsHolder_4;
		NullCheck(L_77);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_78 = L_77->___waypoints_5;
		int32_t L_79 = __this->___currentWaypoint_17;
		NullCheck(L_78);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_80;
		L_80 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_78, L_79, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_80);
		float L_81 = L_80->___newMoverSpeed_11;
		if ((!(((float)L_81) > ((float)(0.0f)))))
		{
			goto IL_026a;
		}
	}
	{
		// ChangeWaypointMoverSpeed(waypointsHolder.waypoints[currentWaypoint].newMoverSpeed);
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_82 = __this->___waypointsHolder_4;
		NullCheck(L_82);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_83 = L_82->___waypoints_5;
		int32_t L_84 = __this->___currentWaypoint_17;
		NullCheck(L_83);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_85;
		L_85 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_83, L_84, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_85);
		float L_86 = L_85->___newMoverSpeed_11;
		WaypointMover_ChangeWaypointMoverSpeed_m5A78628E9ACA8F9076662C4F69EA401E8D175620_inline(__this, L_86, NULL);
	}

IL_026a:
	{
		// previousWaypoint = currentWaypoint;
		int32_t L_87 = __this->___currentWaypoint_17;
		__this->___previousWaypoint_25 = L_87;
		// currentWaypoint += direction;
		int32_t L_88 = __this->___currentWaypoint_17;
		int32_t L_89 = __this->___direction_18;
		__this->___currentWaypoint_17 = ((int32_t)il2cpp_codegen_add(L_88, L_89));
		// onWaypoint = true;
		__this->___onWaypoint_28 = (bool)1;
		// if(currentWaypoint > waypointsHolder.waypoints.Count-1 || currentWaypoint<0)
		int32_t L_90 = __this->___currentWaypoint_17;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_91 = __this->___waypointsHolder_4;
		NullCheck(L_91);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_92 = L_91->___waypoints_5;
		NullCheck(L_92);
		int32_t L_93;
		L_93 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_92, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		if ((((int32_t)L_90) > ((int32_t)((int32_t)il2cpp_codegen_subtract(L_93, 1)))))
		{
			goto IL_02b6;
		}
	}
	{
		int32_t L_94 = __this->___currentWaypoint_17;
		if ((((int32_t)L_94) >= ((int32_t)0)))
		{
			goto IL_0354;
		}
	}

IL_02b6:
	{
		// switch (loopingType)
		int32_t L_95 = __this->___loopingType_6;
		V_4 = L_95;
		int32_t L_96 = V_4;
		switch (L_96)
		{
			case 0:
			{
				goto IL_02d7;
			}
			case 1:
			{
				goto IL_02e0;
			}
			case 2:
			{
				goto IL_0306;
			}
			case 3:
			{
				goto IL_0328;
			}
		}
	}
	{
		goto IL_0354;
	}

IL_02d7:
	{
		// currentWaypoint = -1;
		__this->___currentWaypoint_17 = (-1);
		// break;
		goto IL_0354;
	}

IL_02e0:
	{
		// currentWaypoint = currentWaypoint < 0 ? waypointsHolder.waypoints.Count-1 : 0;
		int32_t L_97 = __this->___currentWaypoint_17;
		G_B30_0 = __this;
		if ((((int32_t)L_97) < ((int32_t)0)))
		{
			G_B31_0 = __this;
			goto IL_02ed;
		}
	}
	{
		G_B32_0 = 0;
		G_B32_1 = G_B30_0;
		goto IL_02ff;
	}

IL_02ed:
	{
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_98 = __this->___waypointsHolder_4;
		NullCheck(L_98);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_99 = L_98->___waypoints_5;
		NullCheck(L_99);
		int32_t L_100;
		L_100 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_99, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		G_B32_0 = ((int32_t)il2cpp_codegen_subtract(L_100, 1));
		G_B32_1 = G_B31_0;
	}

IL_02ff:
	{
		NullCheck(G_B32_1);
		G_B32_1->___currentWaypoint_17 = G_B32_0;
		// break;
		goto IL_0354;
	}

IL_0306:
	{
		// direction = -direction;
		int32_t L_101 = __this->___direction_18;
		__this->___direction_18 = ((-L_101));
		// currentWaypoint += direction;
		int32_t L_102 = __this->___currentWaypoint_17;
		int32_t L_103 = __this->___direction_18;
		__this->___currentWaypoint_17 = ((int32_t)il2cpp_codegen_add(L_102, L_103));
		// break;
		goto IL_0354;
	}

IL_0328:
	{
		// if (loopNumber < numberOfLoops)
		int32_t L_104 = __this->___loopNumber_22;
		int32_t L_105 = __this->___numberOfLoops_13;
		if ((((int32_t)L_104) >= ((int32_t)L_105)))
		{
			goto IL_034d;
		}
	}
	{
		// currentWaypoint = 0;
		__this->___currentWaypoint_17 = 0;
		// loopNumber++;
		int32_t L_106 = __this->___loopNumber_22;
		__this->___loopNumber_22 = ((int32_t)il2cpp_codegen_add(L_106, 1));
		goto IL_0354;
	}

IL_034d:
	{
		// currentWaypoint = -1;
		__this->___currentWaypoint_17 = (-1);
	}

IL_0354:
	{
		// if(currentWaypoint >= 0  &&  waypointsHolder.waypoints[currentWaypoint])
		int32_t L_107 = __this->___currentWaypoint_17;
		if ((((int32_t)L_107) < ((int32_t)0)))
		{
			goto IL_03b9;
		}
	}
	{
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_108 = __this->___waypointsHolder_4;
		NullCheck(L_108);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_109 = L_108->___waypoints_5;
		int32_t L_110 = __this->___currentWaypoint_17;
		NullCheck(L_109);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_111;
		L_111 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_109, L_110, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_112;
		L_112 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_111, NULL);
		if (!L_112)
		{
			goto IL_03b9;
		}
	}
	{
		// targetPosition = waypointsHolder.waypoints[currentWaypoint].gameObject.transform.position;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_113 = __this->___waypointsHolder_4;
		NullCheck(L_113);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_114 = L_113->___waypoints_5;
		int32_t L_115 = __this->___currentWaypoint_17;
		NullCheck(L_114);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_116;
		L_116 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_114, L_115, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_116);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_117;
		L_117 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_116, NULL);
		NullCheck(L_117);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_118;
		L_118 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_117, NULL);
		NullCheck(L_118);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_119;
		L_119 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_118, NULL);
		__this->___targetPosition_20 = L_119;
		// targetPosition = IgnorePositionByAxis(targetPosition);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_120 = __this->___targetPosition_20;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_121;
		L_121 = WaypointMover_IgnorePositionByAxis_m63B933CD9AFDA9355409D65913F757D6150A21FE(__this, L_120, NULL);
		__this->___targetPosition_20 = L_121;
		goto IL_040c;
	}

IL_03b9:
	{
		// if (currentWaypoint < waypointsHolder.waypoints.Count  &&  currentWaypoint >= 0)
		int32_t L_122 = __this->___currentWaypoint_17;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_123 = __this->___waypointsHolder_4;
		NullCheck(L_123);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_124 = L_123->___waypoints_5;
		NullCheck(L_124);
		int32_t L_125;
		L_125 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_124, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		if ((((int32_t)L_122) >= ((int32_t)L_125)))
		{
			goto IL_040c;
		}
	}
	{
		int32_t L_126 = __this->___currentWaypoint_17;
		if ((((int32_t)L_126) < ((int32_t)0)))
		{
			goto IL_040c;
		}
	}
	{
		// currentWaypoint -= direction;
		int32_t L_127 = __this->___currentWaypoint_17;
		int32_t L_128 = __this->___direction_18;
		__this->___currentWaypoint_17 = ((int32_t)il2cpp_codegen_subtract(L_127, L_128));
		// Debug.LogWarning("Waypoint is missed in " + waypointsHolder.gameObject.name);
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_129 = __this->___waypointsHolder_4;
		NullCheck(L_129);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_130;
		L_130 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_129, NULL);
		NullCheck(L_130);
		String_t* L_131;
		L_131 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_130, NULL);
		String_t* L_132;
		L_132 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteral11D89EB85396FABB777CACDB484A47B9EA0A0AF1, L_131, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9(L_132, NULL);
	}

IL_040c:
	{
		// callExitFunction = true;
		__this->___callExitFunction_27 = (bool)1;
		goto IL_04b4;
	}

IL_0418:
	{
		// onWaypoint = false;
		__this->___onWaypoint_28 = (bool)0;
		// if(waypointsHolder.waypoints[previousWaypoint].callExitFunction != ""  &&  callExitFunction)
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_133 = __this->___waypointsHolder_4;
		NullCheck(L_133);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_134 = L_133->___waypoints_5;
		int32_t L_135 = __this->___previousWaypoint_25;
		NullCheck(L_134);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_136;
		L_136 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_134, L_135, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_136);
		String_t* L_137 = L_136->___callExitFunction_10;
		bool L_138;
		L_138 = String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6(L_137, _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709, NULL);
		if (!L_138)
		{
			goto IL_04b4;
		}
	}
	{
		bool L_139 = __this->___callExitFunction_27;
		if (!L_139)
		{
			goto IL_04b4;
		}
	}
	{
		// if(Vector3.Distance(transform.position, waypointsHolder.waypoints[previousWaypoint].gameObject.transform.position) < waypointActivationDistance)
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_140;
		L_140 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_140);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_141;
		L_141 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_140, NULL);
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_142 = __this->___waypointsHolder_4;
		NullCheck(L_142);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_143 = L_142->___waypoints_5;
		int32_t L_144 = __this->___previousWaypoint_25;
		NullCheck(L_143);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_145;
		L_145 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_143, L_144, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_145);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_146;
		L_146 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_145, NULL);
		NullCheck(L_146);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_147;
		L_147 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_146, NULL);
		NullCheck(L_147);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_148;
		L_148 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_147, NULL);
		float L_149;
		L_149 = Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline(L_141, L_148, NULL);
		float L_150 = __this->___waypointActivationDistance_12;
		if ((!(((float)L_149) < ((float)L_150))))
		{
			goto IL_04b4;
		}
	}
	{
		// SendMessage (waypointsHolder.waypoints[previousWaypoint].callExitFunction, SendMessageOptions.DontRequireReceiver);
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_151 = __this->___waypointsHolder_4;
		NullCheck(L_151);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_152 = L_151->___waypoints_5;
		int32_t L_153 = __this->___previousWaypoint_25;
		NullCheck(L_152);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_154;
		L_154 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_152, L_153, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_154);
		String_t* L_155 = L_154->___callExitFunction_10;
		Component_SendMessage_mE7EC28A90F64DCD40E4406BDDBA471CF278DDA37(__this, L_155, 1, NULL);
		// callExitFunction = false;
		__this->___callExitFunction_27 = (bool)0;
	}

IL_04b4:
	{
		// if (followingType == FollowType.SmoothFacing)
		int32_t L_156 = __this->___followingType_5;
		if ((!(((uint32_t)L_156) == ((uint32_t)2))))
		{
			goto IL_0528;
		}
	}
	{
		// Quaternion rotation = Quaternion.LookRotation(targetPosition - transform.position);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_157 = __this->___targetPosition_20;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_158;
		L_158 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_158);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_159;
		L_159 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_158, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_160;
		L_160 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_157, L_159, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_161;
		L_161 = Quaternion_LookRotation_mDB2CCA75B8E1AB98104F2A6E1A1EA57D0D1298D7(L_160, NULL);
		V_5 = L_161;
		// transform.rotation = Quaternion.Slerp(transform.rotation, rotation, Time.deltaTime * damping);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_162;
		L_162 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_163;
		L_163 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_163);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_164;
		L_164 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_163, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_165 = V_5;
		float L_166;
		L_166 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_167 = __this->___damping_10;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_168;
		L_168 = Quaternion_Slerp_m0A9969F500E7716EA4F6BC4E7D5464372D8E9E15(L_164, L_165, ((float)il2cpp_codegen_multiply(L_166, L_167)), NULL);
		NullCheck(L_162);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_162, L_168, NULL);
		// transform.Translate(Vector3.forward*movementSpeed*Time.deltaTime);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_169;
		L_169 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_170;
		L_170 = Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A_inline(NULL);
		float L_171 = __this->___movementSpeed_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_172;
		L_172 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_170, L_171, NULL);
		float L_173;
		L_173 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_174;
		L_174 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_172, L_173, NULL);
		NullCheck(L_169);
		Transform_Translate_m018D015E89C8CB743C54A21B4A1C5202EBF6297A(L_169, L_174, NULL);
	}

IL_0528:
	{
		// if (followingType == FollowType.Facing)
		int32_t L_175 = __this->___followingType_5;
		if ((!(((uint32_t)L_175) == ((uint32_t)1))))
		{
			goto IL_0567;
		}
	}
	{
		// transform.LookAt(targetPosition);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_176;
		L_176 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_177 = __this->___targetPosition_20;
		NullCheck(L_176);
		Transform_LookAt_mFEF7353E4CAEB85D5F7CEEF9276C3B8D6E314C6C(L_176, L_177, NULL);
		// transform.Translate(Vector3.forward*movementSpeed*Time.deltaTime);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_178;
		L_178 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_179;
		L_179 = Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A_inline(NULL);
		float L_180 = __this->___movementSpeed_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_181;
		L_181 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_179, L_180, NULL);
		float L_182;
		L_182 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_183;
		L_183 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_181, L_182, NULL);
		NullCheck(L_178);
		Transform_Translate_m018D015E89C8CB743C54A21B4A1C5202EBF6297A(L_178, L_183, NULL);
	}

IL_0567:
	{
		// if (followingType == FollowType.InvertedFacing)
		int32_t L_184 = __this->___followingType_5;
		if ((!(((uint32_t)L_184) == ((uint32_t)8))))
		{
			goto IL_05c0;
		}
	}
	{
		// transform.LookAt(targetPosition);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_185;
		L_185 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_186 = __this->___targetPosition_20;
		NullCheck(L_185);
		Transform_LookAt_mFEF7353E4CAEB85D5F7CEEF9276C3B8D6E314C6C(L_185, L_186, NULL);
		// transform.Rotate(Vector3.up, 180);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_187;
		L_187 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_188;
		L_188 = Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline(NULL);
		NullCheck(L_187);
		Transform_Rotate_m35B44707FE16FF8015D519D8C162C0B4A85D6D1F(L_187, L_188, (180.0f), NULL);
		// transform.Translate(-Vector3.forward*movementSpeed*Time.deltaTime);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_189;
		L_189 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_190;
		L_190 = Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A_inline(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_191;
		L_191 = Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline(L_190, NULL);
		float L_192 = __this->___movementSpeed_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_193;
		L_193 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_191, L_192, NULL);
		float L_194;
		L_194 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_195;
		L_195 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_193, L_194, NULL);
		NullCheck(L_189);
		Transform_Translate_m018D015E89C8CB743C54A21B4A1C5202EBF6297A(L_189, L_195, NULL);
	}

IL_05c0:
	{
		// if (followingType == FollowType.Simple)
		int32_t L_196 = __this->___followingType_5;
		if (L_196)
		{
			goto IL_05f5;
		}
	}
	{
		// transform.position = Vector3.MoveTowards(transform.position, targetPosition, movementSpeed * Time.deltaTime);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_197;
		L_197 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_198;
		L_198 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_198);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_199;
		L_199 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_198, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_200 = __this->___targetPosition_20;
		float L_201 = __this->___movementSpeed_11;
		float L_202;
		L_202 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_203;
		L_203 = Vector3_MoveTowards_m0363264647799F3173AC37F8E819F98298249B08_inline(L_199, L_200, ((float)il2cpp_codegen_multiply(L_201, L_202)), NULL);
		NullCheck(L_197);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_197, L_203, NULL);
	}

IL_05f5:
	{
		// if (followingType == FollowType.SmoothDamping)
		int32_t L_204 = __this->___followingType_5;
		if ((!(((uint32_t)L_204) == ((uint32_t)3))))
		{
			goto IL_062b;
		}
	}
	{
		// transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, movementSpeed);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_205;
		L_205 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_206;
		L_206 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_206);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_207;
		L_207 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_206, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_208 = __this->___targetPosition_20;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_209 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___velocity_19);
		float L_210 = __this->___movementSpeed_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_211;
		L_211 = Vector3_SmoothDamp_mF673AC30464B7DF671A0556140EB6E9DD75827ED_inline(L_207, L_208, L_209, L_210, NULL);
		NullCheck(L_205);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_205, L_211, NULL);
	}

IL_062b:
	{
		// if (followingType == FollowType.Simple2D)
		int32_t L_212 = __this->___followingType_5;
		if ((!(((uint32_t)L_212) == ((uint32_t)4))))
		{
			goto IL_0676;
		}
	}
	{
		// SmoothLookAt2D(transform, targetPosition, damping);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_213;
		L_213 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_214 = __this->___targetPosition_20;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_215;
		L_215 = Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline(L_214, NULL);
		float L_216 = __this->___damping_10;
		WaypointMover_SmoothLookAt2D_m03C96B7DFF675A3BF07E5C31AEFE3E5075BC6100(__this, L_213, L_215, L_216, NULL);
		// transform.Translate(Vector3.right * movementSpeed * Time.deltaTime);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_217;
		L_217 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_218;
		L_218 = Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0_inline(NULL);
		float L_219 = __this->___movementSpeed_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_220;
		L_220 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_218, L_219, NULL);
		float L_221;
		L_221 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_222;
		L_222 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_220, L_221, NULL);
		NullCheck(L_217);
		Transform_Translate_m018D015E89C8CB743C54A21B4A1C5202EBF6297A(L_217, L_222, NULL);
	}

IL_0676:
	{
		// if (followingType == FollowType.Facing2D)
		int32_t L_223 = __this->___followingType_5;
		if ((!(((uint32_t)L_223) == ((uint32_t)5))))
		{
			goto IL_0736;
		}
	}
	{
		// Vector3 targetDir = targetPosition - transform.position;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_224 = __this->___targetPosition_20;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_225;
		L_225 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_225);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_226;
		L_226 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_225, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_227;
		L_227 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_224, L_226, NULL);
		// float angle = Vector3.Angle(targetDir, transform.right);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_228;
		L_228 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_228);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_229;
		L_229 = Transform_get_right_mC6DC057C23313802E2186A9E0DB760D795A758A4(L_228, NULL);
		float L_230;
		L_230 = Vector3_Angle_mB16906B482814C140FE5BA9D041D2DC11E42A68D_inline(L_227, L_229, NULL);
		V_6 = L_230;
		// if(angle > 3)
		float L_231 = V_6;
		if ((!(((float)L_231) > ((float)(3.0f)))))
		{
			goto IL_0711;
		}
	}
	{
		// transform.eulerAngles = new Vector3 (
		//                                         transform.rotation.eulerAngles.x,
		//                                         transform.rotation.eulerAngles.y,
		//                                         transform.rotation.eulerAngles.z + angle
		//                                     );
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_232;
		L_232 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_233;
		L_233 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_233);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_234;
		L_234 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_233, NULL);
		V_7 = L_234;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_235;
		L_235 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_7), NULL);
		float L_236 = L_235.___x_2;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_237;
		L_237 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_237);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_238;
		L_238 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_237, NULL);
		V_7 = L_238;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_239;
		L_239 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_7), NULL);
		float L_240 = L_239.___y_3;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_241;
		L_241 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_241);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_242;
		L_242 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_241, NULL);
		V_7 = L_242;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_243;
		L_243 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_7), NULL);
		float L_244 = L_243.___z_4;
		float L_245 = V_6;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_246;
		memset((&L_246), 0, sizeof(L_246));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_246), L_236, L_240, ((float)il2cpp_codegen_add(L_244, L_245)), /*hidden argument*/NULL);
		NullCheck(L_232);
		Transform_set_eulerAngles_m9F0BC484A7915A51FAB87230644229B75BACA004(L_232, L_246, NULL);
	}

IL_0711:
	{
		// transform.Translate(Vector3.right * movementSpeed * Time.deltaTime);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_247;
		L_247 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_248;
		L_248 = Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0_inline(NULL);
		float L_249 = __this->___movementSpeed_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_250;
		L_250 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_248, L_249, NULL);
		float L_251;
		L_251 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_252;
		L_252 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_250, L_251, NULL);
		NullCheck(L_247);
		Transform_Translate_m018D015E89C8CB743C54A21B4A1C5202EBF6297A(L_247, L_252, NULL);
	}

IL_0736:
	{
		// if (followingType == FollowType.SmoothFacing2D)
		int32_t L_253 = __this->___followingType_5;
		if ((!(((uint32_t)L_253) == ((uint32_t)6))))
		{
			goto IL_0823;
		}
	}
	{
		// Vector3 targetDir = targetPosition - transform.position;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_254 = __this->___targetPosition_20;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_255;
		L_255 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_255);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_256;
		L_256 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_255, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_257;
		L_257 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_254, L_256, NULL);
		// float angle = Vector3.Angle(targetDir, transform.right);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_258;
		L_258 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_258);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_259;
		L_259 = Transform_get_right_mC6DC057C23313802E2186A9E0DB760D795A758A4(L_258, NULL);
		float L_260;
		L_260 = Vector3_Angle_mB16906B482814C140FE5BA9D041D2DC11E42A68D_inline(L_257, L_259, NULL);
		V_8 = L_260;
		// if(angle > 3)
		float L_261 = V_8;
		if ((!(((float)L_261) > ((float)(3.0f)))))
		{
			goto IL_07fe;
		}
	}
	{
		// transform.eulerAngles = new Vector3
		//         (
		//             transform.rotation.eulerAngles.x,
		//             transform.rotation.eulerAngles.y,
		//             Mathf.Lerp(transform.rotation.eulerAngles.z, transform.rotation.eulerAngles.z - angle, damping * Time.deltaTime)
		//         );
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_262;
		L_262 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_263;
		L_263 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_263);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_264;
		L_264 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_263, NULL);
		V_7 = L_264;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_265;
		L_265 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_7), NULL);
		float L_266 = L_265.___x_2;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_267;
		L_267 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_267);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_268;
		L_268 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_267, NULL);
		V_7 = L_268;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_269;
		L_269 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_7), NULL);
		float L_270 = L_269.___y_3;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_271;
		L_271 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_271);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_272;
		L_272 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_271, NULL);
		V_7 = L_272;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_273;
		L_273 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_7), NULL);
		float L_274 = L_273.___z_4;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_275;
		L_275 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_275);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_276;
		L_276 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_275, NULL);
		V_7 = L_276;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_277;
		L_277 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_7), NULL);
		float L_278 = L_277.___z_4;
		float L_279 = V_8;
		float L_280 = __this->___damping_10;
		float L_281;
		L_281 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_282;
		L_282 = Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline(L_274, ((float)il2cpp_codegen_subtract(L_278, L_279)), ((float)il2cpp_codegen_multiply(L_280, L_281)), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_283;
		memset((&L_283), 0, sizeof(L_283));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_283), L_266, L_270, L_282, /*hidden argument*/NULL);
		NullCheck(L_262);
		Transform_set_eulerAngles_m9F0BC484A7915A51FAB87230644229B75BACA004(L_262, L_283, NULL);
	}

IL_07fe:
	{
		// transform.Translate(Vector3.right * movementSpeed * Time.deltaTime);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_284;
		L_284 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_285;
		L_285 = Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0_inline(NULL);
		float L_286 = __this->___movementSpeed_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_287;
		L_287 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_285, L_286, NULL);
		float L_288;
		L_288 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_289;
		L_289 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_287, L_288, NULL);
		NullCheck(L_284);
		Transform_Translate_m018D015E89C8CB743C54A21B4A1C5202EBF6297A(L_284, L_289, NULL);
	}

IL_0823:
	{
	}
	try
	{// begin try (depth: 1)
		{
			// if (followingType == FollowType.Teleport)
			int32_t L_290 = __this->___followingType_5;
			if ((!(((uint32_t)L_290) == ((uint32_t)7))))
			{
				goto IL_083e_1;
			}
		}
		{
			// transform.position = targetPosition;
			Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_291;
			L_291 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
			Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_292 = __this->___targetPosition_20;
			NullCheck(L_291);
			Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_291, L_292, NULL);
		}

IL_083e_1:
		{
			// float value = Vector3.Distance(waypointsHolder.waypoints[previousWaypoint].gameObject.transform.position, waypointsHolder.waypoints[currentWaypoint].gameObject.transform.position);
			WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_293 = __this->___waypointsHolder_4;
			NullCheck(L_293);
			List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_294 = L_293->___waypoints_5;
			int32_t L_295 = __this->___previousWaypoint_25;
			NullCheck(L_294);
			Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_296;
			L_296 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_294, L_295, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
			NullCheck(L_296);
			GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_297;
			L_297 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_296, NULL);
			NullCheck(L_297);
			Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_298;
			L_298 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_297, NULL);
			NullCheck(L_298);
			Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_299;
			L_299 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_298, NULL);
			WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_300 = __this->___waypointsHolder_4;
			NullCheck(L_300);
			List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_301 = L_300->___waypoints_5;
			int32_t L_302 = __this->___currentWaypoint_17;
			NullCheck(L_301);
			Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_303;
			L_303 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_301, L_302, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
			NullCheck(L_303);
			GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_304;
			L_304 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_303, NULL);
			NullCheck(L_304);
			Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_305;
			L_305 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_304, NULL);
			NullCheck(L_305);
			Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_306;
			L_306 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_305, NULL);
			float L_307;
			L_307 = Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline(L_299, L_306, NULL);
			V_9 = L_307;
			// value = Vector3.Distance(transform.position, targetPosition) / value;
			Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_308;
			L_308 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
			NullCheck(L_308);
			Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_309;
			L_309 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_308, NULL);
			Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_310 = __this->___targetPosition_20;
			float L_311;
			L_311 = Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline(L_309, L_310, NULL);
			float L_312 = V_9;
			V_9 = ((float)(L_311/L_312));
			// transform.localEulerAngles = new Vector3(
			//             transform.localEulerAngles.x,
			//             transform.localEulerAngles.y,
			//             Mathf.Lerp(waypointsHolder.waypoints[previousWaypoint].angle, waypointsHolder.waypoints[currentWaypoint].angle, 1.0f - value)
			//         );
			Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_313;
			L_313 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
			Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_314;
			L_314 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
			NullCheck(L_314);
			Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_315;
			L_315 = Transform_get_localEulerAngles_m358AA9AE8FA24FD1BB7842D231C8644D1C2910C6(L_314, NULL);
			float L_316 = L_315.___x_2;
			Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_317;
			L_317 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
			NullCheck(L_317);
			Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_318;
			L_318 = Transform_get_localEulerAngles_m358AA9AE8FA24FD1BB7842D231C8644D1C2910C6(L_317, NULL);
			float L_319 = L_318.___y_3;
			WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_320 = __this->___waypointsHolder_4;
			NullCheck(L_320);
			List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_321 = L_320->___waypoints_5;
			int32_t L_322 = __this->___previousWaypoint_25;
			NullCheck(L_321);
			Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_323;
			L_323 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_321, L_322, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
			NullCheck(L_323);
			float L_324 = L_323->___angle_8;
			WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_325 = __this->___waypointsHolder_4;
			NullCheck(L_325);
			List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_326 = L_325->___waypoints_5;
			int32_t L_327 = __this->___currentWaypoint_17;
			NullCheck(L_326);
			Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_328;
			L_328 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_326, L_327, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
			NullCheck(L_328);
			float L_329 = L_328->___angle_8;
			float L_330 = V_9;
			float L_331;
			L_331 = Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline(L_324, L_329, ((float)il2cpp_codegen_subtract((1.0f), L_330)), NULL);
			Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_332;
			memset((&L_332), 0, sizeof(L_332));
			Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_332), L_316, L_319, L_331, /*hidden argument*/NULL);
			NullCheck(L_313);
			Transform_set_localEulerAngles_m0458551662A1A51FDCA4C0417282B25D391661DF(L_313, L_332, NULL);
			// }
			goto IL_0929;
		}
	}// end try (depth: 1)
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&RuntimeObject_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_091f;
		}
		throw e;
	}

CATCH_091f:
	{// begin catch(System.Object)
		RuntimeObject* L_333 = ((RuntimeObject*)IL2CPP_GET_ACTIVE_EXCEPTION(RuntimeObject*));;
		// catch { }
		// catch { }
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_0929;
	}// end catch (depth: 1)

IL_0922:
	{
		// inMove = false;
		__this->___inMove_23 = (bool)0;
	}

IL_0929:
	{
		// }
		return;
	}
}
// System.Void WaypointMover::ReverseDirection()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_ReverseDirection_m1A291106C1D60058F592BB4C50AE6E336A23A57C (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* G_B2_0 = NULL;
	WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* G_B3_1 = NULL;
	{
		// previousWaypoint = currentWaypoint;
		int32_t L_0 = __this->___currentWaypoint_17;
		__this->___previousWaypoint_25 = L_0;
		// direction = -direction;
		int32_t L_1 = __this->___direction_18;
		__this->___direction_18 = ((-L_1));
		// currentWaypoint =  direction > 0 ? currentWaypoint+1 : currentWaypoint-1;
		int32_t L_2 = __this->___direction_18;
		G_B1_0 = __this;
		if ((((int32_t)L_2) > ((int32_t)0)))
		{
			G_B2_0 = __this;
			goto IL_002d;
		}
	}
	{
		int32_t L_3 = __this->___currentWaypoint_17;
		G_B3_0 = ((int32_t)il2cpp_codegen_subtract(L_3, 1));
		G_B3_1 = G_B1_0;
		goto IL_0035;
	}

IL_002d:
	{
		int32_t L_4 = __this->___currentWaypoint_17;
		G_B3_0 = ((int32_t)il2cpp_codegen_add(L_4, 1));
		G_B3_1 = G_B2_0;
	}

IL_0035:
	{
		NullCheck(G_B3_1);
		G_B3_1->___currentWaypoint_17 = G_B3_0;
		// if (currentWaypoint < 0)
		int32_t L_5 = __this->___currentWaypoint_17;
		if ((((int32_t)L_5) >= ((int32_t)0)))
		{
			goto IL_005d;
		}
	}
	{
		// currentWaypoint =  waypointsHolder.waypoints.Count-1;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_6 = __this->___waypointsHolder_4;
		NullCheck(L_6);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_7 = L_6->___waypoints_5;
		NullCheck(L_7);
		int32_t L_8;
		L_8 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_7, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		__this->___currentWaypoint_17 = ((int32_t)il2cpp_codegen_subtract(L_8, 1));
		goto IL_007e;
	}

IL_005d:
	{
		// if (currentWaypoint > waypointsHolder.waypoints.Count-1)
		int32_t L_9 = __this->___currentWaypoint_17;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_10 = __this->___waypointsHolder_4;
		NullCheck(L_10);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_11 = L_10->___waypoints_5;
		NullCheck(L_11);
		int32_t L_12;
		L_12 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_11, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		if ((((int32_t)L_9) <= ((int32_t)((int32_t)il2cpp_codegen_subtract(L_12, 1)))))
		{
			goto IL_007e;
		}
	}
	{
		// currentWaypoint = 0;
		__this->___currentWaypoint_17 = 0;
	}

IL_007e:
	{
		// targetPosition = waypointsHolder.waypoints[currentWaypoint].gameObject.transform.position;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_13 = __this->___waypointsHolder_4;
		NullCheck(L_13);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_14 = L_13->___waypoints_5;
		int32_t L_15 = __this->___currentWaypoint_17;
		NullCheck(L_14);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_16;
		L_16 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_14, L_15, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_16);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_17;
		L_17 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_16, NULL);
		NullCheck(L_17);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_18;
		L_18 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_17, NULL);
		NullCheck(L_18);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_19;
		L_19 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_18, NULL);
		__this->___targetPosition_20 = L_19;
		// }
		return;
	}
}
// System.Void WaypointMover::SetDirection(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_SetDirection_mFF7DAF916DB54EC9335B86161FBC92548FBFCB70 (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, int32_t ___0__direction, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* G_B2_0 = NULL;
	WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* G_B3_1 = NULL;
	{
		// previousWaypoint = currentWaypoint;
		int32_t L_0 = __this->___currentWaypoint_17;
		__this->___previousWaypoint_25 = L_0;
		// currentWaypoint =  _direction > 0 ? currentWaypoint+1 : currentWaypoint-1;
		int32_t L_1 = ___0__direction;
		G_B1_0 = __this;
		if ((((int32_t)L_1) > ((int32_t)0)))
		{
			G_B2_0 = __this;
			goto IL_001b;
		}
	}
	{
		int32_t L_2 = __this->___currentWaypoint_17;
		G_B3_0 = ((int32_t)il2cpp_codegen_subtract(L_2, 1));
		G_B3_1 = G_B1_0;
		goto IL_0023;
	}

IL_001b:
	{
		int32_t L_3 = __this->___currentWaypoint_17;
		G_B3_0 = ((int32_t)il2cpp_codegen_add(L_3, 1));
		G_B3_1 = G_B2_0;
	}

IL_0023:
	{
		NullCheck(G_B3_1);
		G_B3_1->___currentWaypoint_17 = G_B3_0;
		// if (currentWaypoint < 0)
		int32_t L_4 = __this->___currentWaypoint_17;
		if ((((int32_t)L_4) >= ((int32_t)0)))
		{
			goto IL_004b;
		}
	}
	{
		// currentWaypoint =  waypointsHolder.waypoints.Count-1;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_5 = __this->___waypointsHolder_4;
		NullCheck(L_5);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_6 = L_5->___waypoints_5;
		NullCheck(L_6);
		int32_t L_7;
		L_7 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_6, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		__this->___currentWaypoint_17 = ((int32_t)il2cpp_codegen_subtract(L_7, 1));
		goto IL_006c;
	}

IL_004b:
	{
		// if (currentWaypoint > waypointsHolder.waypoints.Count-1)
		int32_t L_8 = __this->___currentWaypoint_17;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_9 = __this->___waypointsHolder_4;
		NullCheck(L_9);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_10 = L_9->___waypoints_5;
		NullCheck(L_10);
		int32_t L_11;
		L_11 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_10, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		if ((((int32_t)L_8) <= ((int32_t)((int32_t)il2cpp_codegen_subtract(L_11, 1)))))
		{
			goto IL_006c;
		}
	}
	{
		// currentWaypoint = 0;
		__this->___currentWaypoint_17 = 0;
	}

IL_006c:
	{
		// targetPosition = waypointsHolder.waypoints[currentWaypoint].gameObject.transform.position;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_12 = __this->___waypointsHolder_4;
		NullCheck(L_12);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_13 = L_12->___waypoints_5;
		int32_t L_14 = __this->___currentWaypoint_17;
		NullCheck(L_13);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_15;
		L_15 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_13, L_14, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_15);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_16;
		L_16 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_15, NULL);
		NullCheck(L_16);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_17;
		L_17 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_16, NULL);
		NullCheck(L_17);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18;
		L_18 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_17, NULL);
		__this->___targetPosition_20 = L_18;
		// }
		return;
	}
}
// System.Boolean WaypointMover::IsOnWaypoint()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WaypointMover_IsOnWaypoint_mFDCCDC9B7F1BC510861E9EFEEC272E3324889BAB (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, const RuntimeMethod* method) 
{
	{
		// return onWaypoint;
		bool L_0 = __this->___onWaypoint_28;
		return L_0;
	}
}
// System.Void WaypointMover::ReturnToPreviousWaypoint()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_ReturnToPreviousWaypoint_m6C0635536344A98A42881F390CCB8294EB4BA8EC (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// currentWaypoint = previousWaypoint;
		int32_t L_0 = __this->___previousWaypoint_25;
		__this->___currentWaypoint_17 = L_0;
		// transform.position = waypointsHolder.waypoints[previousWaypoint].gameObject.transform.position;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1;
		L_1 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_2 = __this->___waypointsHolder_4;
		NullCheck(L_2);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_3 = L_2->___waypoints_5;
		int32_t L_4 = __this->___previousWaypoint_25;
		NullCheck(L_3);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_5;
		L_5 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_3, L_4, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_5);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_6;
		L_6 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_5, NULL);
		NullCheck(L_6);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_7;
		L_7 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_6, NULL);
		NullCheck(L_7);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		L_8 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_7, NULL);
		NullCheck(L_1);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_1, L_8, NULL);
		// targetPosition = waypointsHolder.waypoints[previousWaypoint].gameObject.transform.position;
		WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* L_9 = __this->___waypointsHolder_4;
		NullCheck(L_9);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_10 = L_9->___waypoints_5;
		int32_t L_11 = __this->___previousWaypoint_25;
		NullCheck(L_10);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_12;
		L_12 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_10, L_11, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_12);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_13;
		L_13 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_12, NULL);
		NullCheck(L_13);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_14;
		L_14 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_13, NULL);
		NullCheck(L_14);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_15;
		L_15 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_14, NULL);
		__this->___targetPosition_20 = L_15;
		// }
		return;
	}
}
// System.Boolean WaypointMover::isMoving()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool WaypointMover_isMoving_m97B0949AF7ED2238857FFF37BE32FF37D8D8F703 (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, const RuntimeMethod* method) 
{
	{
		// return inMove;
		bool L_0 = __this->___inMove_23;
		return L_0;
	}
}
// System.Void WaypointMover::Suspend(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_Suspend_m74810D1E1A562C02C6A0F36E0E2FB19502AC0F50 (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, bool ___0_state, const RuntimeMethod* method) 
{
	{
		// suspended = state;
		bool L_0 = ___0_state;
		__this->___suspended_24 = L_0;
		// }
		return;
	}
}
// System.Void WaypointMover::Pause()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_Pause_mEC63A33E6D5C4345AD1B1CE2CA645BC9EF8A426C (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, const RuntimeMethod* method) 
{
	{
		// suspended = true;
		__this->___suspended_24 = (bool)1;
		// }
		return;
	}
}
// System.Void WaypointMover::Unpause()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_Unpause_m2CD8534533FC1018C54C23E8D062CDE226751F9C (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, const RuntimeMethod* method) 
{
	{
		// suspended = false;
		__this->___suspended_24 = (bool)0;
		// }
		return;
	}
}
// System.Void WaypointMover::ChangeWaypointMoverSpeed(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_ChangeWaypointMoverSpeed_m5A78628E9ACA8F9076662C4F69EA401E8D175620 (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, float ___0_newSpeed, const RuntimeMethod* method) 
{
	{
		// initialMovementSpeed = newSpeed;
		float L_0 = ___0_newSpeed;
		__this->___initialMovementSpeed_26 = L_0;
		// }
		return;
	}
}
// UnityEngine.Vector3 WaypointMover::IgnorePositionByAxis(UnityEngine.Vector3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 WaypointMover_IgnorePositionByAxis_m63B933CD9AFDA9355409D65913F757D6150A21FE (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_positionToUpdate, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// Vector3 updatedPos = positionToUpdate;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_positionToUpdate;
		V_0 = L_0;
		// if (ignorePositionAtAxis.x)  updatedPos.x = transform.position.x;
		UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619* L_1 = (UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619*)(&__this->___ignorePositionAtAxis_9);
		bool L_2 = L_1->___x_0;
		if (!L_2)
		{
			goto IL_0026;
		}
	}
	{
		// if (ignorePositionAtAxis.x)  updatedPos.x = transform.position.x;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3;
		L_3 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_3);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_3, NULL);
		float L_5 = L_4.___x_2;
		(&V_0)->___x_2 = L_5;
	}

IL_0026:
	{
		// if (ignorePositionAtAxis.y)  updatedPos.y = transform.position.y;
		UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619* L_6 = (UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619*)(&__this->___ignorePositionAtAxis_9);
		bool L_7 = L_6->___y_1;
		if (!L_7)
		{
			goto IL_004a;
		}
	}
	{
		// if (ignorePositionAtAxis.y)  updatedPos.y = transform.position.y;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_8;
		L_8 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_8);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		L_9 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_8, NULL);
		float L_10 = L_9.___y_3;
		(&V_0)->___y_3 = L_10;
	}

IL_004a:
	{
		// if (ignorePositionAtAxis.z)  updatedPos.z = transform.position.z;
		UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619* L_11 = (UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619*)(&__this->___ignorePositionAtAxis_9);
		bool L_12 = L_11->___z_2;
		if (!L_12)
		{
			goto IL_006e;
		}
	}
	{
		// if (ignorePositionAtAxis.z)  updatedPos.z = transform.position.z;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_13;
		L_13 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_13);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14;
		L_14 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_13, NULL);
		float L_15 = L_14.___z_4;
		(&V_0)->___z_4 = L_15;
	}

IL_006e:
	{
		// return updatedPos;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16 = V_0;
		return L_16;
	}
}
// System.Void WaypointMover::SmoothLookAt2D(UnityEngine.Transform,UnityEngine.Vector2,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover_SmoothLookAt2D_m03C96B7DFF675A3BF07E5C31AEFE3E5075BC6100 (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_objectTransform, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_targetPosition, float ___2_smoothingValue, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	float V_1 = 0.0f;
	{
		// Vector3 relative = objectTransform.InverseTransformPoint(targetPosition);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0 = ___0_objectTransform;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = ___1_targetPosition;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7_inline(L_1, NULL);
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Transform_InverseTransformPoint_m18CD395144D9C78F30E15A5B82B6670E792DBA5D(L_0, L_2, NULL);
		V_0 = L_3;
		// float angle = Mathf.Atan2(relative.y, relative.x) * Mathf.Rad2Deg;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = V_0;
		float L_5 = L_4.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = V_0;
		float L_7 = L_6.___x_2;
		float L_8;
		L_8 = atan2f(L_5, L_7);
		V_1 = ((float)il2cpp_codegen_multiply(L_8, (57.2957802f)));
		// objectTransform.Rotate (0, 0, Mathf.LerpAngle(0, angle, Time.deltaTime * smoothingValue) );
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_9 = ___0_objectTransform;
		float L_10 = V_1;
		float L_11;
		L_11 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_12 = ___2_smoothingValue;
		float L_13;
		L_13 = Mathf_LerpAngle_m0653422E15193C2E4A4E5AF05236B6315C789C23_inline((0.0f), L_10, ((float)il2cpp_codegen_multiply(L_11, L_12)), NULL);
		NullCheck(L_9);
		Transform_Rotate_m7EA47AD57F43D478CCB0523D179950EE49CDA3E2(L_9, (0.0f), (0.0f), L_13, NULL);
		// }
		return;
	}
}
// System.Void WaypointMover::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointMover__ctor_m5A6935A38CDB3B178B77E4A38E9D24F22F19165A (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, const RuntimeMethod* method) 
{
	{
		// public float damping = 3.0f;                                // Smooth facing/movement value
		__this->___damping_10 = (3.0f);
		// public float movementSpeed = 5.0f;                    // Speed of object movement along the path
		__this->___movementSpeed_11 = (5.0f);
		// public float waypointActivationDistance = 1.0f;    // How far should object be to waypoint for its activation and choosing new
		__this->___waypointActivationDistance_12 = (1.0f);
		// int direction = 1;
		__this->___direction_18 = 1;
		// Vector3 velocity = Vector3.zero;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0;
		L_0 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		__this->___velocity_19 = L_0;
		// int loopNumber = 1;
		__this->___loopNumber_22 = 1;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Conversion methods for marshalling of: WaypointMover/UsedAxis
IL2CPP_EXTERN_C void UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshal_pinvoke(const UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619& unmarshaled, UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshaled_pinvoke& marshaled)
{
	marshaled.___x_0 = static_cast<int32_t>(unmarshaled.___x_0);
	marshaled.___y_1 = static_cast<int32_t>(unmarshaled.___y_1);
	marshaled.___z_2 = static_cast<int32_t>(unmarshaled.___z_2);
}
IL2CPP_EXTERN_C void UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshal_pinvoke_back(const UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshaled_pinvoke& marshaled, UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619& unmarshaled)
{
	bool unmarshaledx_temp_0 = false;
	unmarshaledx_temp_0 = static_cast<bool>(marshaled.___x_0);
	unmarshaled.___x_0 = unmarshaledx_temp_0;
	bool unmarshaledy_temp_1 = false;
	unmarshaledy_temp_1 = static_cast<bool>(marshaled.___y_1);
	unmarshaled.___y_1 = unmarshaledy_temp_1;
	bool unmarshaledz_temp_2 = false;
	unmarshaledz_temp_2 = static_cast<bool>(marshaled.___z_2);
	unmarshaled.___z_2 = unmarshaledz_temp_2;
}
// Conversion method for clean up from marshalling of: WaypointMover/UsedAxis
IL2CPP_EXTERN_C void UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshal_pinvoke_cleanup(UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshaled_pinvoke& marshaled)
{
}
// Conversion methods for marshalling of: WaypointMover/UsedAxis
IL2CPP_EXTERN_C void UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshal_com(const UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619& unmarshaled, UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshaled_com& marshaled)
{
	marshaled.___x_0 = static_cast<int32_t>(unmarshaled.___x_0);
	marshaled.___y_1 = static_cast<int32_t>(unmarshaled.___y_1);
	marshaled.___z_2 = static_cast<int32_t>(unmarshaled.___z_2);
}
IL2CPP_EXTERN_C void UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshal_com_back(const UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshaled_com& marshaled, UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619& unmarshaled)
{
	bool unmarshaledx_temp_0 = false;
	unmarshaledx_temp_0 = static_cast<bool>(marshaled.___x_0);
	unmarshaled.___x_0 = unmarshaledx_temp_0;
	bool unmarshaledy_temp_1 = false;
	unmarshaledy_temp_1 = static_cast<bool>(marshaled.___y_1);
	unmarshaled.___y_1 = unmarshaledy_temp_1;
	bool unmarshaledz_temp_2 = false;
	unmarshaledz_temp_2 = static_cast<bool>(marshaled.___z_2);
	unmarshaled.___z_2 = unmarshaledz_temp_2;
}
// Conversion method for clean up from marshalling of: WaypointMover/UsedAxis
IL2CPP_EXTERN_C void UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshal_com_cleanup(UsedAxis_t24E076FB6A0CFBF38E70C4F7B8C0180D86A7B619_marshaled_com& marshaled)
{
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void WaypointsHolder::Awake()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointsHolder_Awake_m947DF23C97C27F353CCF1D967829F3049BE59D98 (WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponentsInChildren_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_mB0FC9812323B908297FC1AFE88647432AA1505D4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672* V_0 = NULL;
	int32_t V_1 = 0;
	Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* V_2 = NULL;
	{
		// if (waypoints == null  ||  waypoints.Count == 0)
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_0 = __this->___waypoints_5;
		if (!L_0)
		{
			goto IL_0015;
		}
	}
	{
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_1 = __this->___waypoints_5;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_1, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		if (L_2)
		{
			goto IL_003a;
		}
	}

IL_0015:
	{
		// Waypoint[] childrenWaypoints = GetComponentsInChildren<Waypoint>();
		WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672* L_3;
		L_3 = Component_GetComponentsInChildren_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_mB0FC9812323B908297FC1AFE88647432AA1505D4(__this, Component_GetComponentsInChildren_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_mB0FC9812323B908297FC1AFE88647432AA1505D4_RuntimeMethod_var);
		// foreach (Waypoint waypoint in childrenWaypoints)
		V_0 = L_3;
		V_1 = 0;
		goto IL_0034;
	}

IL_0020:
	{
		// foreach (Waypoint waypoint in childrenWaypoints)
		WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672* L_4 = V_0;
		int32_t L_5 = V_1;
		NullCheck(L_4);
		int32_t L_6 = L_5;
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_7 = (L_4)->GetAt(static_cast<il2cpp_array_size_t>(L_6));
		V_2 = L_7;
		// waypoints.Add(waypoint);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_8 = __this->___waypoints_5;
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_9 = V_2;
		NullCheck(L_8);
		List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_inline(L_8, L_9, List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_RuntimeMethod_var);
		int32_t L_10 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_10, 1));
	}

IL_0034:
	{
		// foreach (Waypoint waypoint in childrenWaypoints)
		int32_t L_11 = V_1;
		WaypointU5BU5D_t75076CEACBD5B1C921950D786C95D39450341672* L_12 = V_0;
		NullCheck(L_12);
		if ((((int32_t)L_11) < ((int32_t)((int32_t)(((RuntimeArray*)L_12)->max_length)))))
		{
			goto IL_0020;
		}
	}

IL_003a:
	{
		// Clean ();
		WaypointsHolder_Clean_m1DD3720DD6533BF7BCE3740F346D256A343C578A(__this, NULL);
		// }
		return;
	}
}
// System.Void WaypointsHolder::Clean()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointsHolder_Clean_m1DD3720DD6533BF7BCE3740F346D256A343C578A (WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_RemoveAt_m83B7A7ED93B3F8BA72190C392A66062077991027_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		// for (int i = 0; i < waypoints.Count; i++)
		V_0 = 0;
		goto IL_002c;
	}

IL_0004:
	{
		// if (waypoints[i] == null)
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_0 = __this->___waypoints_5;
		int32_t L_1 = V_0;
		NullCheck(L_0);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_2;
		L_2 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_0, L_1, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_2, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_3)
		{
			goto IL_0028;
		}
	}
	{
		// waypoints.RemoveAt (i);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_4 = __this->___waypoints_5;
		int32_t L_5 = V_0;
		NullCheck(L_4);
		List_1_RemoveAt_m83B7A7ED93B3F8BA72190C392A66062077991027(L_4, L_5, List_1_RemoveAt_m83B7A7ED93B3F8BA72190C392A66062077991027_RuntimeMethod_var);
		// i--;
		int32_t L_6 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_6, 1));
	}

IL_0028:
	{
		// for (int i = 0; i < waypoints.Count; i++)
		int32_t L_7 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_7, 1));
	}

IL_002c:
	{
		// for (int i = 0; i < waypoints.Count; i++)
		int32_t L_8 = V_0;
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_9 = __this->___waypoints_5;
		NullCheck(L_9);
		int32_t L_10;
		L_10 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_9, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		if ((((int32_t)L_8) < ((int32_t)L_10)))
		{
			goto IL_0004;
		}
	}
	{
		// }
		return;
	}
}
// System.Void WaypointsHolder::AddWaypoint(Waypoint)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointsHolder_AddWaypoint_m96EB0CFA10013DFD25D64F8E81156E105994F9E0 (WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* __this, Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* ___0__newWaypoint, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// waypoints.Add (_newWaypoint);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_0 = __this->___waypoints_5;
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_1 = ___0__newWaypoint;
		NullCheck(L_0);
		List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_inline(L_0, L_1, List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_RuntimeMethod_var);
		// }
		return;
	}
}
// System.Void WaypointsHolder::CreateWaypoint(UnityEngine.Vector3,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointsHolder_CreateWaypoint_m7FE324F3F416BE45BD92B418293BE9EA9107DE63 (WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0__position, String_t* ___1_name, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_AddComponent_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_m785A0ABD5EABE3A4F7CF5226111DA1DB4273C782_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_t76FEDD663AB33C991A9C9A23129337651094216F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* V_0 = NULL;
	{
		// GameObject newWaypoint = new GameObject();
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0 = (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*)il2cpp_codegen_object_new(GameObject_t76FEDD663AB33C991A9C9A23129337651094216F_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		GameObject__ctor_m7D0340DE160786E6EFA8DABD39EC3B694DA30AAD(L_0, NULL);
		V_0 = L_0;
		// newWaypoint.name = name;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_1 = V_0;
		String_t* L_2 = ___1_name;
		NullCheck(L_1);
		Object_set_name_mC79E6DC8FFD72479C90F0C4CC7F42A0FEAF5AE47(L_1, L_2, NULL);
		// newWaypoint.transform.parent = transform;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3 = V_0;
		NullCheck(L_3);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_4;
		L_4 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_3, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_5;
		L_5 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_4);
		Transform_set_parent_m9BD5E563B539DD5BEC342736B03F97B38A243234(L_4, L_5, NULL);
		// newWaypoint.transform.position = _position;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_6 = V_0;
		NullCheck(L_6);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_7;
		L_7 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_6, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0__position;
		NullCheck(L_7);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_7, L_8, NULL);
		// waypoints.Add (newWaypoint.AddComponent<Waypoint>());
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_9 = __this->___waypoints_5;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_10 = V_0;
		NullCheck(L_10);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_11;
		L_11 = GameObject_AddComponent_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_m785A0ABD5EABE3A4F7CF5226111DA1DB4273C782(L_10, GameObject_AddComponent_TisWaypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6_m785A0ABD5EABE3A4F7CF5226111DA1DB4273C782_RuntimeMethod_var);
		NullCheck(L_9);
		List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_inline(L_9, L_11, List_1_Add_mF1300B6233E258BD70D862F1EF37A61A34D6558C_RuntimeMethod_var);
		// }
		return;
	}
}
// System.Void WaypointsHolder::OnDrawGizmos()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointsHolder_OnDrawGizmos_m5982F218C5A5017E23FFFA82394300C31A298454 (WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		// Gizmos.color = color;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = __this->___color_4;
		Gizmos_set_color_m53927A2741937484180B20B55F7F20F8F60C5797(L_0, NULL);
		// if (waypoints.Count > 0)
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_1 = __this->___waypoints_5;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_1, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		if ((((int32_t)L_2) <= ((int32_t)0)))
		{
			goto IL_00c0;
		}
	}
	{
		// for (int i = 0; i<(waypoints.Count-1); i++)
		V_0 = 0;
		goto IL_00ad;
	}

IL_0023:
	{
		// if (waypoints[i] && waypoints[i+1])
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_3 = __this->___waypoints_5;
		int32_t L_4 = V_0;
		NullCheck(L_3);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_5;
		L_5 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_3, L_4, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_6;
		L_6 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_5, NULL);
		if (!L_6)
		{
			goto IL_00a9;
		}
	}
	{
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_7 = __this->___waypoints_5;
		int32_t L_8 = V_0;
		NullCheck(L_7);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_9;
		L_9 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_7, ((int32_t)il2cpp_codegen_add(L_8, 1)), List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_10;
		L_10 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_9, NULL);
		if (!L_10)
		{
			goto IL_00a9;
		}
	}
	{
		// Gizmos.DrawLine (waypoints[i].gameObject.transform.position, waypoints[i+1].gameObject.transform.position);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_11 = __this->___waypoints_5;
		int32_t L_12 = V_0;
		NullCheck(L_11);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_13;
		L_13 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_11, L_12, List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_13);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_14;
		L_14 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_13, NULL);
		NullCheck(L_14);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_15;
		L_15 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_14, NULL);
		NullCheck(L_15);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16;
		L_16 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_15, NULL);
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_17 = __this->___waypoints_5;
		int32_t L_18 = V_0;
		NullCheck(L_17);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_19;
		L_19 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_17, ((int32_t)il2cpp_codegen_add(L_18, 1)), List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		NullCheck(L_19);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_20;
		L_20 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_19, NULL);
		NullCheck(L_20);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_21;
		L_21 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_20, NULL);
		NullCheck(L_21);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_22;
		L_22 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_21, NULL);
		Gizmos_DrawLine_mB139054F55D615637A39A3127AADB16043387F8A(L_16, L_22, NULL);
		// if (colorizeWaypoints)
		bool L_23 = __this->___colorizeWaypoints_6;
		if (!L_23)
		{
			goto IL_00a9;
		}
	}
	{
		// waypoints[i+1].color = color;
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_24 = __this->___waypoints_5;
		int32_t L_25 = V_0;
		NullCheck(L_24);
		Waypoint_t10627C403A194C3D7901C1C01F3C6859FE7D6EE6* L_26;
		L_26 = List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789(L_24, ((int32_t)il2cpp_codegen_add(L_25, 1)), List_1_get_Item_m28438658DA7F6048EB7F434DAF3DAD0742156789_RuntimeMethod_var);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_27 = __this->___color_4;
		NullCheck(L_26);
		L_26->___color_4 = L_27;
	}

IL_00a9:
	{
		// for (int i = 0; i<(waypoints.Count-1); i++)
		int32_t L_28 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_28, 1));
	}

IL_00ad:
	{
		// for (int i = 0; i<(waypoints.Count-1); i++)
		int32_t L_29 = V_0;
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_30 = __this->___waypoints_5;
		NullCheck(L_30);
		int32_t L_31;
		L_31 = List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_inline(L_30, List_1_get_Count_m0F2ED9B3817E821E1A59EF29DFD4DD4E16B0C94D_RuntimeMethod_var);
		if ((((int32_t)L_29) < ((int32_t)((int32_t)il2cpp_codegen_subtract(L_31, 1)))))
		{
			goto IL_0023;
		}
	}

IL_00c0:
	{
		// }
		return;
	}
}
// System.Void WaypointsHolder::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaypointsHolder__ctor_m59EC2F3953721E56C33D6D8FDDF01BA385AAFD8E (WaypointsHolder_tED046CA1656A1D21252A54C52AF349F9E21A4E82* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mAAFCE295FBB7FE987F32A92E141CC10276F18691_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// public Color color = new Color (0, 1, 0, 0.5f);         // Debug path lines color
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (0.0f), (1.0f), (0.0f), (0.5f), /*hidden argument*/NULL);
		__this->___color_4 = L_0;
		// public List<Waypoint> waypoints = new List<Waypoint>();    // List of all waypoints assigned to this path
		List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE* L_1 = (List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE*)il2cpp_codegen_object_new(List_1_tFF498CF444E0A87989A199FF6684E049BE6C34DE_il2cpp_TypeInfo_var);
		NullCheck(L_1);
		List_1__ctor_mAAFCE295FBB7FE987F32A92E141CC10276F18691(L_1, List_1__ctor_mAAFCE295FBB7FE987F32A92E141CC10276F18691_RuntimeMethod_var);
		__this->___waypoints_5 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___waypoints_5), (void*)L_1);
		// public bool colorizeWaypoints = true;                   // Repaint all waypoints in the color
		__this->___colorizeWaypoints_6 = (bool)1;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void VisCircle.PowerUpAnimation::Awake()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PowerUpAnimation_Awake_m0104909296147709FE4E0BF687570DC417FADCE9 (PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisTransform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_m60E86366B3E431D4C4A549CF4FE5951087686F7F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// _transform = this.GetComponent<Transform>();
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_GetComponent_TisTransform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_m60E86366B3E431D4C4A549CF4FE5951087686F7F(__this, Component_GetComponent_TisTransform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_m60E86366B3E431D4C4A549CF4FE5951087686F7F_RuntimeMethod_var);
		__this->____transform_17 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____transform_17), (void*)L_0);
		// _startLocalPosition = _transform.localPosition;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1 = __this->____transform_17;
		NullCheck(L_1);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95(L_1, NULL);
		__this->____startLocalPosition_14 = L_2;
		// _startLocalRotation = _transform.localRotation;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3 = __this->____transform_17;
		NullCheck(L_3);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_4;
		L_4 = Transform_get_localRotation_mD53D37611A5DAE93EC6C7BBCAC337408C5CACA77(L_3, NULL);
		__this->____startLocalRotation_15 = L_4;
		// _startLocalScale = _transform.localScale;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_5 = __this->____transform_17;
		NullCheck(L_5);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Transform_get_localScale_m804A002A53A645CDFCD15BB0F37209162720363F(L_5, NULL);
		__this->____startLocalScale_16 = L_6;
		// }
		return;
	}
}
// System.Void VisCircle.PowerUpAnimation::Update()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PowerUpAnimation_Update_mD004F560E2567C6DEED7C4F00116A0B0492CCAE3 (PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	{
		// if (_animateYOffset) {
		bool L_0 = __this->____animateYOffset_11;
		if (!L_0)
		{
			goto IL_0068;
		}
	}
	{
		// if (yOffsetCycleDuration != 0) {
		float L_1 = __this->___yOffsetCycleDuration_13;
		if ((((float)L_1) == ((float)(0.0f))))
		{
			goto IL_003c;
		}
	}
	{
		// yOff = Mathf.Sin(Time.time / yOffsetCycleDuration * Mathf.PI * 2) * yOffsetAmplitude;
		float L_2;
		L_2 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_3 = __this->___yOffsetCycleDuration_13;
		float L_4;
		L_4 = sinf(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)(L_2/L_3)), (3.14159274f))), (2.0f))));
		float L_5 = __this->___yOffsetAmplitude_12;
		V_0 = ((float)il2cpp_codegen_multiply(L_4, L_5));
		goto IL_0042;
	}

IL_003c:
	{
		// yOff = 0;
		V_0 = (0.0f);
	}

IL_0042:
	{
		// this.transform.localPosition = _startLocalPosition + new Vector3(0, yOff, 0);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_6;
		L_6 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = __this->____startLocalPosition_14;
		float L_8 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), (0.0f), L_8, (0.0f), /*hidden argument*/NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10;
		L_10 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_7, L_9, NULL);
		NullCheck(L_6);
		Transform_set_localPosition_mDE1C997F7D79C0885210B7732B4BA50EE7D73134(L_6, L_10, NULL);
	}

IL_0068:
	{
		// if (_animateScale) {
		bool L_11 = __this->____animateScale_7;
		if (!L_11)
		{
			goto IL_00dc;
		}
	}
	{
		// if (scaleCycleDuration != 0) {
		float L_12 = __this->___scaleCycleDuration_10;
		if ((((float)L_12) == ((float)(0.0f))))
		{
			goto IL_00bf;
		}
	}
	{
		// float scaleT = Mathf.InverseLerp(-1, 1, Mathf.Sin(Time.time / scaleCycleDuration * Mathf.PI * 2));
		float L_13;
		L_13 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_14 = __this->___scaleCycleDuration_10;
		float L_15;
		L_15 = sinf(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)(L_13/L_14)), (3.14159274f))), (2.0f))));
		float L_16;
		L_16 = Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D_inline((-1.0f), (1.0f), L_15, NULL);
		V_2 = L_16;
		// scale = Mathf.Lerp(scaleMin, scaleMax, scaleT);
		float L_17 = __this->___scaleMin_8;
		float L_18 = __this->___scaleMax_9;
		float L_19 = V_2;
		float L_20;
		L_20 = Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline(L_17, L_18, L_19, NULL);
		V_1 = L_20;
		goto IL_00c5;
	}

IL_00bf:
	{
		// scale = 1;
		V_1 = (1.0f);
	}

IL_00c5:
	{
		// this.transform.localScale = scale * _startLocalScale;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_21;
		L_21 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		float L_22 = V_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_23 = __this->____startLocalScale_16;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_24;
		L_24 = Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline(L_22, L_23, NULL);
		NullCheck(L_21);
		Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633(L_21, L_24, NULL);
	}

IL_00dc:
	{
		// if (_animateRotation) {
		bool L_25 = __this->____animateRotation_4;
		if (!L_25)
		{
			goto IL_0126;
		}
	}
	{
		// if (rotationType == RotationType.WorldAxis) {
		int32_t L_26 = __this->___rotationType_6;
		if ((!(((uint32_t)L_26) == ((uint32_t)1))))
		{
			goto IL_010a;
		}
	}
	{
		// this.transform.Rotate(rotationSpeedsInDegreePerSecond * Time.deltaTime, Space.World);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_27;
		L_27 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_28 = __this->___rotationSpeedsInDegreePerSecond_5;
		float L_29;
		L_29 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_30;
		L_30 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_28, L_29, NULL);
		NullCheck(L_27);
		Transform_Rotate_mAE711E1B1F639FDBA7B456E1E1B35DB90EEB737A(L_27, L_30, 0, NULL);
		return;
	}

IL_010a:
	{
		// this.transform.Rotate(rotationSpeedsInDegreePerSecond * Time.deltaTime, Space.Self);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_31;
		L_31 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_32 = __this->___rotationSpeedsInDegreePerSecond_5;
		float L_33;
		L_33 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_34;
		L_34 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_32, L_33, NULL);
		NullCheck(L_31);
		Transform_Rotate_mAE711E1B1F639FDBA7B456E1E1B35DB90EEB737A(L_31, L_34, 1, NULL);
	}

IL_0126:
	{
		// }
		return;
	}
}
// System.Boolean VisCircle.PowerUpAnimation::GetAnimateScale()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PowerUpAnimation_GetAnimateScale_m34062883321E743F5D2514130B2AF3BA50D8F6FA (PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF* __this, const RuntimeMethod* method) 
{
	{
		// public bool GetAnimateScale() { return _animateScale; }
		bool L_0 = __this->____animateScale_7;
		return L_0;
	}
}
// System.Void VisCircle.PowerUpAnimation::SetAnimateScale(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PowerUpAnimation_SetAnimateScale_m6D65CA4AC485B9DE348D17EDFD5D4A385221140C (PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF* __this, bool ___0_newAnimateScale, const RuntimeMethod* method) 
{
	{
		// this._animateScale = newAnimateScale;
		bool L_0 = ___0_newAnimateScale;
		__this->____animateScale_7 = L_0;
		// if (!_animateScale && Application.isPlaying) {
		bool L_1 = __this->____animateScale_7;
		if (L_1)
		{
			goto IL_0027;
		}
	}
	{
		bool L_2;
		L_2 = Application_get_isPlaying_m25B0ABDFEF54F5370CD3F263A813540843D00F34(NULL);
		if (!L_2)
		{
			goto IL_0027;
		}
	}
	{
		// this.transform.localScale = _startLocalScale;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3;
		L_3 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = __this->____startLocalScale_16;
		NullCheck(L_3);
		Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633(L_3, L_4, NULL);
	}

IL_0027:
	{
		// }
		return;
	}
}
// System.Boolean VisCircle.PowerUpAnimation::GetAnimateYOffset()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PowerUpAnimation_GetAnimateYOffset_m169D039F0B89387C5F4E40ADF4248F76F53185B8 (PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF* __this, const RuntimeMethod* method) 
{
	{
		// public bool GetAnimateYOffset() { return _animateYOffset; }
		bool L_0 = __this->____animateYOffset_11;
		return L_0;
	}
}
// System.Void VisCircle.PowerUpAnimation::SetAnimateYOffset(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PowerUpAnimation_SetAnimateYOffset_m286CC8C747D68FEAC61823E226D615A2DBBC27F1 (PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF* __this, bool ___0_newAnimateYOffset, const RuntimeMethod* method) 
{
	{
		// this._animateYOffset = newAnimateYOffset;
		bool L_0 = ___0_newAnimateYOffset;
		__this->____animateYOffset_11 = L_0;
		// if (!_animateYOffset && Application.isPlaying) {
		bool L_1 = __this->____animateYOffset_11;
		if (L_1)
		{
			goto IL_0027;
		}
	}
	{
		bool L_2;
		L_2 = Application_get_isPlaying_m25B0ABDFEF54F5370CD3F263A813540843D00F34(NULL);
		if (!L_2)
		{
			goto IL_0027;
		}
	}
	{
		// this.transform.localPosition = _startLocalPosition;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3;
		L_3 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = __this->____startLocalPosition_14;
		NullCheck(L_3);
		Transform_set_localPosition_mDE1C997F7D79C0885210B7732B4BA50EE7D73134(L_3, L_4, NULL);
	}

IL_0027:
	{
		// }
		return;
	}
}
// System.Boolean VisCircle.PowerUpAnimation::GetAnimateRotation()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PowerUpAnimation_GetAnimateRotation_m34B2E93AAD761707F79DC03B71FBA9C4C03BFA5F (PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF* __this, const RuntimeMethod* method) 
{
	{
		// public bool GetAnimateRotation() { return _animateRotation; }
		bool L_0 = __this->____animateRotation_4;
		return L_0;
	}
}
// System.Void VisCircle.PowerUpAnimation::SetAnimateRotation(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PowerUpAnimation_SetAnimateRotation_mE105C23D08AB692F31014E618A83DD1F59003A2A (PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF* __this, bool ___0_newAnimateRotation, const RuntimeMethod* method) 
{
	{
		// this._animateRotation = newAnimateRotation;
		bool L_0 = ___0_newAnimateRotation;
		__this->____animateRotation_4 = L_0;
		// if (!_animateRotation && Application.isPlaying) {
		bool L_1 = __this->____animateRotation_4;
		if (L_1)
		{
			goto IL_0027;
		}
	}
	{
		bool L_2;
		L_2 = Application_get_isPlaying_m25B0ABDFEF54F5370CD3F263A813540843D00F34(NULL);
		if (!L_2)
		{
			goto IL_0027;
		}
	}
	{
		// this.transform.localRotation = _startLocalRotation;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3;
		L_3 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_4 = __this->____startLocalRotation_15;
		NullCheck(L_3);
		Transform_set_localRotation_mAB4A011D134BA58AB780BECC0025CA65F16185FA(L_3, L_4, NULL);
	}

IL_0027:
	{
		// }
		return;
	}
}
// System.Void VisCircle.PowerUpAnimation::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PowerUpAnimation__ctor_m604DFE7E5CC8173D2FCDB657DDF2678FEA5C14B8 (PowerUpAnimation_t7E1316BF1A1F946453AE79A79DDE1179AFDE37CF* __this, const RuntimeMethod* method) 
{
	{
		// private bool _animateRotation = true;
		__this->____animateRotation_4 = (bool)1;
		// private bool _animateScale = true;
		__this->____animateScale_7 = (bool)1;
		// public float scaleMin = 0.5f, scaleMax = 1.5f, scaleCycleDuration = 5;
		__this->___scaleMin_8 = (0.5f);
		// public float scaleMin = 0.5f, scaleMax = 1.5f, scaleCycleDuration = 5;
		__this->___scaleMax_9 = (1.5f);
		// public float scaleMin = 0.5f, scaleMax = 1.5f, scaleCycleDuration = 5;
		__this->___scaleCycleDuration_10 = (5.0f);
		// private bool _animateYOffset = true;
		__this->____animateYOffset_11 = (bool)1;
		// public float yOffsetAmplitude = 1, yOffsetCycleDuration = 5;
		__this->___yOffsetAmplitude_12 = (1.0f);
		// public float yOffsetAmplitude = 1, yOffsetCycleDuration = 5;
		__this->___yOffsetCycleDuration_13 = (5.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiButtonScript::Start()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiButtonScript_Start_m1742DF48ECFD8D84A71E37E1C08157CBD33531C9 (SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_mB85C5C0EEF6535E3FC0DBFC14E39FA5A51B6F888_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_GetComponent_TisSciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68_m3B2EC44BA664B479AF49E79B2749BBAFE215FCA2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1619C5C335E412CB53A1A03D5D6E8B83E2E20626);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1A82671F2C34BEA09C35354DDB899812746CBCF9);
		s_Il2CppMethodInitialized = true;
	}
	{
		// effectScript = GameObject.Find("SciFiFireProjectile").GetComponent<SciFiFireProjectile>();
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = GameObject_Find_m7A669B4EEC2617AB82F6E3FF007CDCD9F21DB300(_stringLiteral1619C5C335E412CB53A1A03D5D6E8B83E2E20626, NULL);
		NullCheck(L_0);
		SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* L_1;
		L_1 = GameObject_GetComponent_TisSciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68_m3B2EC44BA664B479AF49E79B2749BBAFE215FCA2(L_0, GameObject_GetComponent_TisSciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68_m3B2EC44BA664B479AF49E79B2749BBAFE215FCA2_RuntimeMethod_var);
		__this->___effectScript_7 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___effectScript_7), (void*)L_1);
		// getProjectileNames();
		SciFiButtonScript_getProjectileNames_mB5530CC4D84424EAF09EDD6F83BCAA7FC7B427E2(__this, NULL);
		// MyButtonText = Button.transform.Find("Text").GetComponent<Text>();
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_2 = __this->___Button_4;
		NullCheck(L_2);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3;
		L_3 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_2, NULL);
		NullCheck(L_3);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_4;
		L_4 = Transform_Find_m3087032B0E1C5B96A2D2C27020BAEAE2DA08F932(L_3, _stringLiteral1A82671F2C34BEA09C35354DDB899812746CBCF9, NULL);
		NullCheck(L_4);
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_5;
		L_5 = Component_GetComponent_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_mB85C5C0EEF6535E3FC0DBFC14E39FA5A51B6F888(L_4, Component_GetComponent_TisText_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_mB85C5C0EEF6535E3FC0DBFC14E39FA5A51B6F888_RuntimeMethod_var);
		__this->___MyButtonText_5 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___MyButtonText_5), (void*)L_5);
		// MyButtonText.text = projectileParticleName;
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_6 = __this->___MyButtonText_5;
		String_t* L_7 = __this->___projectileParticleName_6;
		NullCheck(L_6);
		VirtualActionInvoker1< String_t* >::Invoke(75 /* System.Void UnityEngine.UI.Text::set_text(System.String) */, L_6, L_7);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiButtonScript::Update()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiButtonScript_Update_mF42793829C5BF9DCACED711090ACCCE6E8C6770D (SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* __this, const RuntimeMethod* method) 
{
	{
		// MyButtonText.text = projectileParticleName;
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_0 = __this->___MyButtonText_5;
		String_t* L_1 = __this->___projectileParticleName_6;
		NullCheck(L_0);
		VirtualActionInvoker1< String_t* >::Invoke(75 /* System.Void UnityEngine.UI.Text::set_text(System.String) */, L_0, L_1);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiButtonScript::getProjectileNames()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiButtonScript_getProjectileNames_mB5530CC4D84424EAF09EDD6F83BCAA7FC7B427E2 (SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_GetComponent_TisSciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25_mBFE0AE078925AEB36508AEFAEC11E27428A77B11_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// projectileScript = effectScript.projectiles[effectScript.currentProjectile].GetComponent<SciFiProjectileScript>();  // Access the currently selected projectile's 'ProjectileScript'
		SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* L_0 = __this->___effectScript_7;
		NullCheck(L_0);
		GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* L_1 = L_0->___projectiles_5;
		SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* L_2 = __this->___effectScript_7;
		NullCheck(L_2);
		int32_t L_3 = L_2->___currentProjectile_7;
		NullCheck(L_1);
		int32_t L_4 = L_3;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_5 = (L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_4));
		NullCheck(L_5);
		SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25* L_6;
		L_6 = GameObject_GetComponent_TisSciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25_mBFE0AE078925AEB36508AEFAEC11E27428A77B11(L_5, GameObject_GetComponent_TisSciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25_mBFE0AE078925AEB36508AEFAEC11E27428A77B11_RuntimeMethod_var);
		__this->___projectileScript_8 = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___projectileScript_8), (void*)L_6);
		// projectileParticleName = projectileScript.projectileParticle.name; // Assign the name of the currently selected projectile to projectileParticleName
		SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25* L_7 = __this->___projectileScript_8;
		NullCheck(L_7);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_8 = L_7->___projectileParticle_5;
		NullCheck(L_8);
		String_t* L_9;
		L_9 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_8, NULL);
		__this->___projectileParticleName_6 = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___projectileParticleName_6), (void*)L_9);
		// }
		return;
	}
}
// System.Boolean SciFiArsenal.SciFiButtonScript::overButton()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SciFiButtonScript_overButton_m20FB775D82D1508950C858477034292F63F75886 (SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* __this, const RuntimeMethod* method) 
{
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D V_0;
	memset((&V_0), 0, sizeof(V_0));
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		// Rect button1 = new Rect(buttonsX, buttonsY, buttonsSizeX, buttonsSizeY);
		float L_0 = __this->___buttonsX_9;
		float L_1 = __this->___buttonsY_10;
		float L_2 = __this->___buttonsSizeX_11;
		float L_3 = __this->___buttonsSizeY_12;
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23((&V_0), L_0, L_1, L_2, L_3, NULL);
		// Rect button2 = new Rect(buttonsX + buttonsDistance, buttonsY, buttonsSizeX, buttonsSizeY);
		float L_4 = __this->___buttonsX_9;
		float L_5 = __this->___buttonsDistance_13;
		float L_6 = __this->___buttonsY_10;
		float L_7 = __this->___buttonsSizeX_11;
		float L_8 = __this->___buttonsSizeY_12;
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23((&V_1), ((float)il2cpp_codegen_add(L_4, L_5)), L_6, L_7, L_8, NULL);
		// if(button1.Contains(new Vector2(Input.mousePosition.x, Screen.height - Input.mousePosition.y)) ||
		//    button2.Contains(new Vector2(Input.mousePosition.x, Screen.height - Input.mousePosition.y)))
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		L_9 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		float L_10 = L_9.___x_2;
		int32_t L_11;
		L_11 = Screen_get_height_m01A3102DE71EE1FBEA51D09D6B0261CF864FE8F9(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		L_12 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		float L_13 = L_12.___y_3;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_14;
		memset((&L_14), 0, sizeof(L_14));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_14), L_10, ((float)il2cpp_codegen_subtract(((float)L_11), L_13)), /*hidden argument*/NULL);
		bool L_15;
		L_15 = Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B((&V_0), L_14, NULL);
		if (L_15)
		{
			goto IL_0097;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16;
		L_16 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		float L_17 = L_16.___x_2;
		int32_t L_18;
		L_18 = Screen_get_height_m01A3102DE71EE1FBEA51D09D6B0261CF864FE8F9(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_19;
		L_19 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		float L_20 = L_19.___y_3;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_21;
		memset((&L_21), 0, sizeof(L_21));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_21), L_17, ((float)il2cpp_codegen_subtract(((float)L_18), L_20)), /*hidden argument*/NULL);
		bool L_22;
		L_22 = Rect_Contains_mAB270D6B7E3B0009A50D142C569D63E8FE59F48B((&V_1), L_21, NULL);
		if (!L_22)
		{
			goto IL_0099;
		}
	}

IL_0097:
	{
		// return true;
		return (bool)1;
	}

IL_0099:
	{
		// return false;
		return (bool)0;
	}
}
// System.Void SciFiArsenal.SciFiButtonScript::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiButtonScript__ctor_m63584CC049C22FB504D986677B80AAF9A12ADFF1 (SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiDragMouseOrbit::Start()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiDragMouseOrbit_Start_m103CFF3B965B3B3BC23C8862FFF6E72C1D8106AF (SciFiDragMouseOrbit_t0B2E0A08B4A81730FC4F3BEF0C000EF569D3ED62* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// Vector3 angles = transform.eulerAngles;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Transform_get_eulerAngles_mCAAF48EFCF628F1ED91C2FFE75A4FD19C039DD6A(L_0, NULL);
		V_0 = L_1;
		// rotationYAxis = angles.y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = V_0;
		float L_3 = L_2.___y_3;
		__this->___rotationYAxis_13 = L_3;
		// rotationXAxis = angles.x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = V_0;
		float L_5 = L_4.___x_2;
		__this->___rotationXAxis_14 = L_5;
		// if (GetComponent<Rigidbody>())
		Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* L_6;
		L_6 = Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8(__this, Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_7;
		L_7 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_6, NULL);
		if (!L_7)
		{
			goto IL_003d;
		}
	}
	{
		// GetComponent<Rigidbody>().freezeRotation = true;
		Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* L_8;
		L_8 = Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8(__this, Component_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m4B5CAD64B52D153BEA96432633CA9A45FA523DD8_RuntimeMethod_var);
		NullCheck(L_8);
		Rigidbody_set_freezeRotation_m6D049F82E9133020C31EEFB35A179A56364325DC(L_8, (bool)1, NULL);
	}

IL_003d:
	{
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiDragMouseOrbit::LateUpdate()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiDragMouseOrbit_LateUpdate_m5D4946B636F2AC10648E62C97487B16EDFB97EB4 (SciFiDragMouseOrbit_t0B2E0A08B4A81730FC4F3BEF0C000EF569D3ED62* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral16DD21BE77B115D392226EB71A2D3A9FDC29E3F0);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral88BEE283254D7094E258B3A88730F4CC4F1E4AC7);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFC6687DC37346CD2569888E29764F727FAF530E0);
		s_Il2CppMethodInitialized = true;
	}
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 V_1;
	memset((&V_1), 0, sizeof(V_1));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_3;
	memset((&V_3), 0, sizeof(V_3));
	{
		// if (target)
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0 = __this->___target_4;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_0, NULL);
		if (!L_1)
		{
			goto IL_01ae;
		}
	}
	{
		// if (Input.GetMouseButton(1))
		bool L_2;
		L_2 = Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA(1, NULL);
		if (!L_2)
		{
			goto IL_0067;
		}
	}
	{
		// velocityX += xSpeed * Input.GetAxis("Mouse X") * distance * 0.02f;
		float L_3 = __this->___velocityX_15;
		float L_4 = __this->___xSpeed_6;
		float L_5;
		L_5 = Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62(_stringLiteral88BEE283254D7094E258B3A88730F4CC4F1E4AC7, NULL);
		float L_6 = __this->___distance_5;
		__this->___velocityX_15 = ((float)il2cpp_codegen_add(L_3, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_4, L_5)), L_6)), (0.0199999996f)))));
		// velocityY += ySpeed * Input.GetAxis("Mouse Y") * 0.02f;
		float L_7 = __this->___velocityY_16;
		float L_8 = __this->___ySpeed_7;
		float L_9;
		L_9 = Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62(_stringLiteral16DD21BE77B115D392226EB71A2D3A9FDC29E3F0, NULL);
		__this->___velocityY_16 = ((float)il2cpp_codegen_add(L_7, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_8, L_9)), (0.0199999996f)))));
	}

IL_0067:
	{
		// rotationYAxis += velocityX;
		float L_10 = __this->___rotationYAxis_13;
		float L_11 = __this->___velocityX_15;
		__this->___rotationYAxis_13 = ((float)il2cpp_codegen_add(L_10, L_11));
		// rotationXAxis -= velocityY;
		float L_12 = __this->___rotationXAxis_14;
		float L_13 = __this->___velocityY_16;
		__this->___rotationXAxis_14 = ((float)il2cpp_codegen_subtract(L_12, L_13));
		// rotationXAxis = ClampAngle(rotationXAxis, yMinLimit, yMaxLimit);
		float L_14 = __this->___rotationXAxis_14;
		float L_15 = __this->___yMinLimit_8;
		float L_16 = __this->___yMaxLimit_9;
		float L_17;
		L_17 = SciFiDragMouseOrbit_ClampAngle_m5B97010144919B1F791AFC6EEE973742D1B36C5C(L_14, L_15, L_16, NULL);
		__this->___rotationXAxis_14 = L_17;
		// Quaternion toRotation = Quaternion.Euler(rotationXAxis, rotationYAxis, 0);
		float L_18 = __this->___rotationXAxis_14;
		float L_19 = __this->___rotationYAxis_13;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_20;
		L_20 = Quaternion_Euler_m9262AB29E3E9CE94EF71051F38A28E82AEC73F90_inline(L_18, L_19, (0.0f), NULL);
		// Quaternion rotation = toRotation;
		V_0 = L_20;
		// distance = Mathf.Clamp(distance - Input.GetAxis("Mouse ScrollWheel") * 5, distanceMin, distanceMax);
		float L_21 = __this->___distance_5;
		float L_22;
		L_22 = Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62(_stringLiteralFC6687DC37346CD2569888E29764F727FAF530E0, NULL);
		float L_23 = __this->___distanceMin_10;
		float L_24 = __this->___distanceMax_11;
		float L_25;
		L_25 = Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF_inline(((float)il2cpp_codegen_subtract(L_21, ((float)il2cpp_codegen_multiply(L_22, (5.0f))))), L_23, L_24, NULL);
		__this->___distance_5 = L_25;
		// if (Physics.Linecast(target.position, transform.position, out hit))
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_26 = __this->___target_4;
		NullCheck(L_26);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_27;
		L_27 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_26, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_28;
		L_28 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_28);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_29;
		L_29 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_28, NULL);
		bool L_30;
		L_30 = Physics_Linecast_m4F2A0744FE106002EE26D12B6137FC21C019B932(L_27, L_29, (&V_1), NULL);
		if (!L_30)
		{
			goto IL_0122;
		}
	}
	{
		// distance -= hit.distance;
		float L_31 = __this->___distance_5;
		float L_32;
		L_32 = RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78((&V_1), NULL);
		__this->___distance_5 = ((float)il2cpp_codegen_subtract(L_31, L_32));
	}

IL_0122:
	{
		// Vector3 negDistance = new Vector3(0.0f, 0.0f, -distance);
		float L_33 = __this->___distance_5;
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&V_2), (0.0f), (0.0f), ((-L_33)), NULL);
		// Vector3 position = rotation * negDistance + target.position;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_34 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_35 = V_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_36;
		L_36 = Quaternion_op_Multiply_mE1EBA73F9173432B50F8F17CE8190C5A7986FB8C(L_34, L_35, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_37 = __this->___target_4;
		NullCheck(L_37);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_38;
		L_38 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_37, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_39;
		L_39 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_36, L_38, NULL);
		V_3 = L_39;
		// transform.rotation = rotation;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_40;
		L_40 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_41 = V_0;
		NullCheck(L_40);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_40, L_41, NULL);
		// transform.position = position;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_42;
		L_42 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_43 = V_3;
		NullCheck(L_42);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_42, L_43, NULL);
		// velocityX = Mathf.Lerp(velocityX, 0, Time.deltaTime * smoothTime);
		float L_44 = __this->___velocityX_15;
		float L_45;
		L_45 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_46 = __this->___smoothTime_12;
		float L_47;
		L_47 = Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline(L_44, (0.0f), ((float)il2cpp_codegen_multiply(L_45, L_46)), NULL);
		__this->___velocityX_15 = L_47;
		// velocityY = Mathf.Lerp(velocityY, 0, Time.deltaTime * smoothTime);
		float L_48 = __this->___velocityY_16;
		float L_49;
		L_49 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_50 = __this->___smoothTime_12;
		float L_51;
		L_51 = Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline(L_48, (0.0f), ((float)il2cpp_codegen_multiply(L_49, L_50)), NULL);
		__this->___velocityY_16 = L_51;
	}

IL_01ae:
	{
		// }
		return;
	}
}
// System.Single SciFiArsenal.SciFiDragMouseOrbit::ClampAngle(System.Single,System.Single,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float SciFiDragMouseOrbit_ClampAngle_m5B97010144919B1F791AFC6EEE973742D1B36C5C (float ___0_angle, float ___1_min, float ___2_max, const RuntimeMethod* method) 
{
	{
		// if (angle < -360F)
		float L_0 = ___0_angle;
		if ((!(((float)L_0) < ((float)(-360.0f)))))
		{
			goto IL_0011;
		}
	}
	{
		// angle += 360F;
		float L_1 = ___0_angle;
		___0_angle = ((float)il2cpp_codegen_add(L_1, (360.0f)));
	}

IL_0011:
	{
		// if (angle > 360F)
		float L_2 = ___0_angle;
		if ((!(((float)L_2) > ((float)(360.0f)))))
		{
			goto IL_0022;
		}
	}
	{
		// angle -= 360F;
		float L_3 = ___0_angle;
		___0_angle = ((float)il2cpp_codegen_subtract(L_3, (360.0f)));
	}

IL_0022:
	{
		// return Mathf.Clamp(angle, min, max);
		float L_4 = ___0_angle;
		float L_5 = ___1_min;
		float L_6 = ___2_max;
		float L_7;
		L_7 = Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF_inline(L_4, L_5, L_6, NULL);
		return L_7;
	}
}
// System.Void SciFiArsenal.SciFiDragMouseOrbit::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiDragMouseOrbit__ctor_mD724B1633836902A127732D1183205FE77C2B75A (SciFiDragMouseOrbit_t0B2E0A08B4A81730FC4F3BEF0C000EF569D3ED62* __this, const RuntimeMethod* method) 
{
	{
		// public float distance = 5.0f;
		__this->___distance_5 = (5.0f);
		// public float xSpeed = 120.0f;
		__this->___xSpeed_6 = (120.0f);
		// public float ySpeed = 120.0f;
		__this->___ySpeed_7 = (120.0f);
		// public float yMinLimit = -20f;
		__this->___yMinLimit_8 = (-20.0f);
		// public float yMaxLimit = 80f;
		__this->___yMaxLimit_9 = (80.0f);
		// public float distanceMin = .5f;
		__this->___distanceMin_10 = (0.5f);
		// public float distanceMax = 15f;
		__this->___distanceMax_11 = (15.0f);
		// public float smoothTime = 2f;
		__this->___smoothTime_12 = (2.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiFireProjectile::Start()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiFireProjectile_Start_m7F441FDF28D941EB849736F98A0F9A0461B15B7B (SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_GetComponent_TisSciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870_mADBF88BCDCE4F53AF0E50C60E2BED90459FD18E3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7DE03E5EBA0308517D1762F2C0DF3B9E2A2F1870);
		s_Il2CppMethodInitialized = true;
	}
	{
		// selectedProjectileButton = GameObject.Find("Button").GetComponent<SciFiButtonScript>();
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = GameObject_Find_m7A669B4EEC2617AB82F6E3FF007CDCD9F21DB300(_stringLiteral7DE03E5EBA0308517D1762F2C0DF3B9E2A2F1870, NULL);
		NullCheck(L_0);
		SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* L_1;
		L_1 = GameObject_GetComponent_TisSciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870_mADBF88BCDCE4F53AF0E50C60E2BED90459FD18E3(L_0, GameObject_GetComponent_TisSciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870_mADBF88BCDCE4F53AF0E50C60E2BED90459FD18E3_RuntimeMethod_var);
		__this->___selectedProjectileButton_9 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___selectedProjectileButton_9), (void*)L_1);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiFireProjectile::Update()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiFireProjectile_Update_m4BFFC9DD8A0DEF1444A7C39B0D94FF5AC2E34161 (SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m2D7F86C77ECF9B82AAC077B511F1004280571B90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_GetComponent_TisSciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25_mBFE0AE078925AEB36508AEFAEC11E27428A77B11_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* V_0 = NULL;
	Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00 V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		// if (Input.GetKeyDown(KeyCode.RightArrow))
		bool L_0;
		L_0 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(((int32_t)275), NULL);
		if (!L_0)
		{
			goto IL_001d;
		}
	}
	{
		// nextEffect();
		SciFiFireProjectile_nextEffect_mB33DF89F8CDFA0E871CF6EB2239FA93756A47937(__this, NULL);
		// selectedProjectileButton.getProjectileNames(); // Run the getProjectileNames() function in SciFiButtonScript
		SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* L_1 = __this->___selectedProjectileButton_9;
		NullCheck(L_1);
		SciFiButtonScript_getProjectileNames_mB5530CC4D84424EAF09EDD6F83BCAA7FC7B427E2(L_1, NULL);
	}

IL_001d:
	{
		// if (Input.GetKeyDown(KeyCode.D))
		bool L_2;
		L_2 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(((int32_t)100), NULL);
		if (!L_2)
		{
			goto IL_0037;
		}
	}
	{
		// nextEffect();
		SciFiFireProjectile_nextEffect_mB33DF89F8CDFA0E871CF6EB2239FA93756A47937(__this, NULL);
		// selectedProjectileButton.getProjectileNames();
		SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* L_3 = __this->___selectedProjectileButton_9;
		NullCheck(L_3);
		SciFiButtonScript_getProjectileNames_mB5530CC4D84424EAF09EDD6F83BCAA7FC7B427E2(L_3, NULL);
	}

IL_0037:
	{
		// if (Input.GetKeyDown(KeyCode.A))
		bool L_4;
		L_4 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(((int32_t)97), NULL);
		if (!L_4)
		{
			goto IL_0053;
		}
	}
	{
		// previousEffect();
		SciFiFireProjectile_previousEffect_m502F4A3D08CB0A577AF6681B6A22FACB1AED00AD(__this, NULL);
		// selectedProjectileButton.getProjectileNames();
		SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* L_5 = __this->___selectedProjectileButton_9;
		NullCheck(L_5);
		SciFiButtonScript_getProjectileNames_mB5530CC4D84424EAF09EDD6F83BCAA7FC7B427E2(L_5, NULL);
		goto IL_0070;
	}

IL_0053:
	{
		// else if (Input.GetKeyDown(KeyCode.LeftArrow))
		bool L_6;
		L_6 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(((int32_t)276), NULL);
		if (!L_6)
		{
			goto IL_0070;
		}
	}
	{
		// previousEffect();
		SciFiFireProjectile_previousEffect_m502F4A3D08CB0A577AF6681B6A22FACB1AED00AD(__this, NULL);
		// selectedProjectileButton.getProjectileNames();
		SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* L_7 = __this->___selectedProjectileButton_9;
		NullCheck(L_7);
		SciFiButtonScript_getProjectileNames_mB5530CC4D84424EAF09EDD6F83BCAA7FC7B427E2(L_7, NULL);
	}

IL_0070:
	{
		// if (Input.GetKeyDown(KeyCode.Mouse0))
		bool L_8;
		L_8 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(((int32_t)323), NULL);
		if (!L_8)
		{
			goto IL_0120;
		}
	}
	{
		// if (!selectedProjectileButton.overButton())
		SciFiButtonScript_tBDEDE48CDC6B298EF239CA13BB28C6A8DD526870* L_9 = __this->___selectedProjectileButton_9;
		NullCheck(L_9);
		bool L_10;
		L_10 = SciFiButtonScript_overButton_m20FB775D82D1508950C858477034292F63F75886(L_9, NULL);
		if (L_10)
		{
			goto IL_0120;
		}
	}
	{
		// if (Physics.Raycast(Camera.main.ScreenPointToRay(Input.mousePosition), out hit, 100f))
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_11;
		L_11 = Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		L_12 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		NullCheck(L_11);
		Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00 L_13;
		L_13 = Camera_ScreenPointToRay_m2887B9A49880B7AB670C57D66B67D6A6689FE315(L_11, L_12, NULL);
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_14 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&__this->___hit_4);
		bool L_15;
		L_15 = Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685(L_13, L_14, (100.0f), NULL);
		if (!L_15)
		{
			goto IL_0120;
		}
	}
	{
		// GameObject projectile = Instantiate(projectiles[currentProjectile], spawnPosition.position, Quaternion.identity) as GameObject;
		GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* L_16 = __this->___projectiles_5;
		int32_t L_17 = __this->___currentProjectile_7;
		NullCheck(L_16);
		int32_t L_18 = L_17;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_19 = (L_16)->GetAt(static_cast<il2cpp_array_size_t>(L_18));
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_20 = __this->___spawnPosition_6;
		NullCheck(L_20);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_21;
		L_21 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_20, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_22;
		L_22 = Quaternion_get_identity_m7E701AE095ED10FD5EA0B50ABCFDE2EEFF2173A5_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_23;
		L_23 = Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4(L_19, L_21, L_22, Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var);
		V_0 = L_23;
		// projectile.transform.LookAt(hit.point);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_24 = V_0;
		NullCheck(L_24);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_25;
		L_25 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_24, NULL);
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_26 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&__this->___hit_4);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_27;
		L_27 = RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39(L_26, NULL);
		NullCheck(L_25);
		Transform_LookAt_mFEF7353E4CAEB85D5F7CEEF9276C3B8D6E314C6C(L_25, L_27, NULL);
		// projectile.GetComponent<Rigidbody>().AddForce(projectile.transform.forward * speed);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_28 = V_0;
		NullCheck(L_28);
		Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* L_29;
		L_29 = GameObject_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m2D7F86C77ECF9B82AAC077B511F1004280571B90(L_28, GameObject_GetComponent_TisRigidbody_t268697F5A994213ED97393309870968BC1C7393C_m2D7F86C77ECF9B82AAC077B511F1004280571B90_RuntimeMethod_var);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_30 = V_0;
		NullCheck(L_30);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_31;
		L_31 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_30, NULL);
		NullCheck(L_31);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_32;
		L_32 = Transform_get_forward_mFCFACF7165FDAB21E80E384C494DF278386CEE2F(L_31, NULL);
		float L_33 = __this->___speed_8;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_34;
		L_34 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_32, L_33, NULL);
		NullCheck(L_29);
		Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198(L_29, L_34, NULL);
		// projectile.GetComponent<SciFiProjectileScript>().impactNormal = hit.normal;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_35 = V_0;
		NullCheck(L_35);
		SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25* L_36;
		L_36 = GameObject_GetComponent_TisSciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25_mBFE0AE078925AEB36508AEFAEC11E27428A77B11(L_35, GameObject_GetComponent_TisSciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25_mBFE0AE078925AEB36508AEFAEC11E27428A77B11_RuntimeMethod_var);
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_37 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&__this->___hit_4);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_38;
		L_38 = RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5(L_37, NULL);
		NullCheck(L_36);
		L_36->___impactNormal_8 = L_38;
	}

IL_0120:
	{
		// Debug.DrawRay(Camera.main.ScreenPointToRay(Input.mousePosition).origin, Camera.main.ScreenPointToRay(Input.mousePosition).direction*100, Color.yellow);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_39;
		L_39 = Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_40;
		L_40 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		NullCheck(L_39);
		Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00 L_41;
		L_41 = Camera_ScreenPointToRay_m2887B9A49880B7AB670C57D66B67D6A6689FE315(L_39, L_40, NULL);
		V_1 = L_41;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_42;
		L_42 = Ray_get_origin_m97604A8F180316A410DCD77B7D74D04522FA1BA6((&V_1), NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_43;
		L_43 = Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_44;
		L_44 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		NullCheck(L_43);
		Ray_t2B1742D7958DC05BDC3EFC7461D3593E1430DC00 L_45;
		L_45 = Camera_ScreenPointToRay_m2887B9A49880B7AB670C57D66B67D6A6689FE315(L_43, L_44, NULL);
		V_1 = L_45;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_46;
		L_46 = Ray_get_direction_m21C2D22D3BD4A683BD4DC191AB22DD05F5EC2086((&V_1), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_47;
		L_47 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_46, (100.0f), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_48;
		L_48 = Color_get_yellow_m66637FA14383E8D74F24AE256B577CE1D55D469F_inline(NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_DrawRay_mB172868181856F153732BB56C0BE1C58EE598F53(L_42, L_47, L_48, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiFireProjectile::nextEffect()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiFireProjectile_nextEffect_mB33DF89F8CDFA0E871CF6EB2239FA93756A47937 (SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* __this, const RuntimeMethod* method) 
{
	{
		// if (currentProjectile < projectiles.Length - 1)
		int32_t L_0 = __this->___currentProjectile_7;
		GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* L_1 = __this->___projectiles_5;
		NullCheck(L_1);
		if ((((int32_t)L_0) >= ((int32_t)((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_1)->max_length)), 1)))))
		{
			goto IL_0021;
		}
	}
	{
		// currentProjectile++;
		int32_t L_2 = __this->___currentProjectile_7;
		__this->___currentProjectile_7 = ((int32_t)il2cpp_codegen_add(L_2, 1));
		return;
	}

IL_0021:
	{
		// currentProjectile = 0;
		__this->___currentProjectile_7 = 0;
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiFireProjectile::previousEffect()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiFireProjectile_previousEffect_m502F4A3D08CB0A577AF6681B6A22FACB1AED00AD (SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* __this, const RuntimeMethod* method) 
{
	{
		// if (currentProjectile > 0)
		int32_t L_0 = __this->___currentProjectile_7;
		if ((((int32_t)L_0) <= ((int32_t)0)))
		{
			goto IL_0018;
		}
	}
	{
		// currentProjectile--;
		int32_t L_1 = __this->___currentProjectile_7;
		__this->___currentProjectile_7 = ((int32_t)il2cpp_codegen_subtract(L_1, 1));
		return;
	}

IL_0018:
	{
		// currentProjectile = projectiles.Length-1;
		GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* L_2 = __this->___projectiles_5;
		NullCheck(L_2);
		__this->___currentProjectile_7 = ((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_2)->max_length)), 1));
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiFireProjectile::AdjustSpeed(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiFireProjectile_AdjustSpeed_m55F1D39731D5914D191DDB32FC50EC36668529AE (SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* __this, float ___0_newSpeed, const RuntimeMethod* method) 
{
	{
		// speed = newSpeed;
		float L_0 = ___0_newSpeed;
		__this->___speed_8 = L_0;
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiFireProjectile::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiFireProjectile__ctor_m7DDEB4834E576AAA7B8F7A0743DD4948C1AC1941 (SciFiFireProjectile_tB61999D775E0F1C1B1531E42FBE8FE28DDD06D68* __this, const RuntimeMethod* method) 
{
	{
		// public float speed = 1000;
		__this->___speed_8 = (1000.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiProjectiles()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiProjectiles_m06A489A067B7214E20BBDD086A6DD76E4C45E042 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9984DEC0742753663EB35FB5277D667AB9814D55);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("scifi_projectiles");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral9984DEC0742753663EB35FB5277D667AB9814D55, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiBeamup()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiBeamup_mB5B2B13BEC68C6C59D0AA9A30BD5CCF4DC3FEBB9 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8508A0A813BD56B9159332EE6373A855AC767E04);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene("scifi_beamup");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral8508A0A813BD56B9159332EE6373A855AC767E04, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiBuff()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiBuff_m49D0F3DECE99A360A95D64F579DA27357FDB86C8 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral036E8DC7673BD2F2DB58497EB247496785DB4972);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene("scifi_buff");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral036E8DC7673BD2F2DB58497EB247496785DB4972, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiFlamethrowers2()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiFlamethrowers2_m55A9C0C1B4ACB2D39DDDA39372AAFD1E13D38DC2 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7F0C56B69B17BB7182854EBA5EF0642800198091);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("scifi_flamethrowers");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral7F0C56B69B17BB7182854EBA5EF0642800198091, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiQuestZone()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiQuestZone_m7033290D4A618722F27A40ED26F6E2CB98F2F000 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral54D0575E6058572F9374DC53FF19D2213EC18B3E);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("scifi_hexagonzone");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral54D0575E6058572F9374DC53FF19D2213EC18B3E, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiLightjump()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiLightjump_m2304E081D12A68AE9834CEEF75B0C5BAE12E094D (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA89B817E70FE2E1839DC79CDD79D04CB9F485551);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("scifi_lightjump");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteralA89B817E70FE2E1839DC79CDD79D04CB9F485551, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiLoot()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiLoot_m88A740BAA56CAEA426D5AE171DD446D9D69CA725 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5DEA71BCADD074EB568D7812B9BE365919D7A81A);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("scifi_loot");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral5DEA71BCADD074EB568D7812B9BE365919D7A81A, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiBeams()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiBeams_m548D26B64C6F3DA51DC5B603E298E45B339725A3 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCE079A97B022AB27159197F1AE180B4DF5FEC4F1);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("scifi_beams");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteralCE079A97B022AB27159197F1AE180B4DF5FEC4F1, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiPortals()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiPortals_m5693997FF6FA793369483F0DAD0787D201FC4A69 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA378690B716D95393E276C02A666A54DA98B6669);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("scifi_portals");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteralA378690B716D95393E276C02A666A54DA98B6669, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiRegenerate()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiRegenerate_m7B3F98F40F55228D9CAB7D8EDC8AC8E17B42224A (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2BEA1E602FC6193FB49093F6DD05DD4B82ADE78C);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene("scifi_regenerate");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral2BEA1E602FC6193FB49093F6DD05DD4B82ADE78C, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiShields()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiShields_m21F31A1F2E24E6C868773ABBAE726742AB996379 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral887D652C804CEF2C355B202D1A6466BD1054AA16);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene("scifi_shields");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral887D652C804CEF2C355B202D1A6466BD1054AA16, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiSwirlyAura()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiSwirlyAura_m8048B346BCB5208B3364589A1CCC21CD791EB5DD (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7474356D4679C1D2CC31EB5473525F3B3D936C04);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene("scifi_swirlyaura");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral7474356D4679C1D2CC31EB5473525F3B3D936C04, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiWarpgates()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiWarpgates_m694CEDBF90FDF91A919C3C84FB4F08DC6431AB43 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7945576977EFC6979B25BEA4BAE0C2079D5ABCFB);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene("scifi_warpgates");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral7945576977EFC6979B25BEA4BAE0C2079D5ABCFB, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiJetflame()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiJetflame_m5BD4B1D616E47EBA84E7BDD44433DF6DE712E685 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFFD10011D1B2FBB6781371C70017213C9C9B088C);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene("scifi_jetflame");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteralFFD10011D1B2FBB6781371C70017213C9C9B088C, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiUltimateNova()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiUltimateNova_mBB0996B8889BE43CD02476E0F1128B3EC04EDABD (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral98411044963701D038055BC7ED6558A6DD29AA6A);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene("scifi_ultimatenova");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral98411044963701D038055BC7ED6558A6DD29AA6A, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::LoadSceneSciFiFire()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick_LoadSceneSciFiFire_m42850B5EEBA004893F0598189F5C39F5FD5EED24 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8D6CC7AB013518017089598FFA1163BC9239BFC9);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene("scifi_fire");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral8D6CC7AB013518017089598FFA1163BC9239BFC9, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick__ctor_mEAB047E021F46303D3D8569BDF92B68AF4563C51 (SciFiLoadSceneOnClick_t32AF1FB182C565B5B0BF104F3C16E58293626D51* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate1()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate1_m359F235BE8476207F7BD4CC8AD34859333A7E1EE (SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA395A49C38624688BE663C163BC147794EFFEAAF);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("update_scifi_1");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteralA395A49C38624688BE663C163BC147794EFFEAAF, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate2()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate2_mDA2B24C8992E153F5793F406534C52DE481B6640 (SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral77BBFACE7078076F9EC58DF14E9B5828E902DCD4);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("update_scifi_2");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral77BBFACE7078076F9EC58DF14E9B5828E902DCD4, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate3()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate3_m39BC3D49C6A78A4549749D780CE746C18B95E18A (SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0306C66ED2BF36EA4AD0D6D8C9A3F5DD04EE0D70);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("update_scifi_3");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral0306C66ED2BF36EA4AD0D6D8C9A3F5DD04EE0D70, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate4()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate4_mF312F797624B408DB1276BAB66647662EB6DFDE4 (SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1BF379A8C91717D27E696451740272046B579229);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("update_scifi_4");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral1BF379A8C91717D27E696451740272046B579229, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate5()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate5_m9DCA1881C6C5194712306541370F04BE0E38FAF1 (SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCEE2E98CD60A1A62ADCCCD95617FA86B26E57552);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("update_scifi_5");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteralCEE2E98CD60A1A62ADCCCD95617FA86B26E57552, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate6()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate6_mA55563510406D4AD2C7EEEFAF2ABB41C64C31E2A (SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7C3058D929226D26583035E7F24EE258388DBAD6);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("update_scifi_6");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral7C3058D929226D26583035E7F24EE258388DBAD6, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick2::LoadSceneSciFiUpdate7()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick2_LoadSceneSciFiUpdate7_m8CF7FE1BF1EDD402841F6AA38F0FEA4499C50037 (SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4250553F1A33BB11D6ED51EB8D957D9EB9EAB359);
		s_Il2CppMethodInitialized = true;
	}
	{
		// SceneManager.LoadScene ("update_scifi_7");
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(_stringLiteral4250553F1A33BB11D6ED51EB8D957D9EB9EAB359, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoadSceneOnClick2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoadSceneOnClick2__ctor_m34EBD6C0F555E3489FC5B685EB1817CDE2E38E34 (SciFiLoadSceneOnClick2_t8E6938CC70D495D6516CBC907B3A4197D6A3D319* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiLoopScript::Start()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoopScript_Start_m5CBA93B096050B75DD47E10E8F0E3631A86D1DAA (SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* __this, const RuntimeMethod* method) 
{
	{
		// PlayEffect();
		SciFiLoopScript_PlayEffect_m44618A0F38C4FF16D37CB07CD245B716BBCC8FBD(__this, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoopScript::PlayEffect()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoopScript_PlayEffect_m44618A0F38C4FF16D37CB07CD245B716BBCC8FBD (SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral92E9796AF13FEEF7EE0A3D7079F97259971BBF9B);
		s_Il2CppMethodInitialized = true;
	}
	{
		// StartCoroutine("EffectLoop");
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_0;
		L_0 = MonoBehaviour_StartCoroutine_m10C4B693B96175C42B0FD00911E072701C220DB4(__this, _stringLiteral92E9796AF13FEEF7EE0A3D7079F97259971BBF9B, NULL);
		// }
		return;
	}
}
// System.Collections.IEnumerator SciFiArsenal.SciFiLoopScript::EffectLoop()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* SciFiLoopScript_EffectLoop_m1E95FFB9EAEC85E38FF4624E5557E48B5ACE5D05 (SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547* L_0 = (U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547*)il2cpp_codegen_object_new(U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CEffectLoopU3Ed__4__ctor_m128D913002532BD9711F8B37A031CBE97A31D174(L_0, 0, NULL);
		U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this_2 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this_2), (void*)__this);
		return L_1;
	}
}
// System.Void SciFiArsenal.SciFiLoopScript::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLoopScript__ctor_mD7BB17EF963524AA062F440C49DA0A13BA88B5A1 (SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* __this, const RuntimeMethod* method) 
{
	{
		// public float loopTimeLimit = 2.0f;
		__this->___loopTimeLimit_5 = (2.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::.ctor(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CEffectLoopU3Ed__4__ctor_m128D913002532BD9711F8B37A031CBE97A31D174 (U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state_0 = L_0;
		return;
	}
}
// System.Void SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::System.IDisposable.Dispose()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CEffectLoopU3Ed__4_System_IDisposable_Dispose_m55CDF331009F3ED686BEBAFCACE296CB4E46FFA7 (U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
// System.Boolean SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::MoveNext()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CEffectLoopU3Ed__4_MoveNext_mB4E7DF11146B99CD88D210F31E4DDB656DCC89FD (U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state_0;
		V_0 = L_0;
		SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* L_1 = __this->___U3CU3E4__this_2;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_005f;
		}
	}
	{
		return (bool)0;
	}

IL_0017:
	{
		__this->___U3CU3E1__state_0 = (-1);
		// GameObject effectPlayer = (GameObject) Instantiate(chosenEffect, transform.position, transform.rotation);
		SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* L_4 = V_1;
		NullCheck(L_4);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_5 = L_4->___chosenEffect_4;
		SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* L_6 = V_1;
		NullCheck(L_6);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_7;
		L_7 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_6, NULL);
		NullCheck(L_7);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		L_8 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_7, NULL);
		SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* L_9 = V_1;
		NullCheck(L_9);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_10;
		L_10 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_9, NULL);
		NullCheck(L_10);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_11;
		L_11 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_10, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_12;
		L_12 = Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4(L_5, L_8, L_11, Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var);
		__this->___U3CeffectPlayerU3E5__2_3 = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CeffectPlayerU3E5__2_3), (void*)L_12);
		// yield return new WaitForSeconds(loopTimeLimit);
		SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* L_13 = V_1;
		NullCheck(L_13);
		float L_14 = L_13->___loopTimeLimit_5;
		WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* L_15 = (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3*)il2cpp_codegen_object_new(WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		NullCheck(L_15);
		WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC(L_15, L_14, NULL);
		__this->___U3CU3E2__current_1 = L_15;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current_1), (void*)L_15);
		__this->___U3CU3E1__state_0 = 1;
		return (bool)1;
	}

IL_005f:
	{
		__this->___U3CU3E1__state_0 = (-1);
		// Destroy (effectPlayer);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_16 = __this->___U3CeffectPlayerU3E5__2_3;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(L_16, NULL);
		// PlayEffect();
		SciFiLoopScript_t91CF6FC5C3B20BDFCA1284F7760AE4257D2F823D* L_17 = V_1;
		NullCheck(L_17);
		SciFiLoopScript_PlayEffect_m44618A0F38C4FF16D37CB07CD245B716BBCC8FBD(L_17, NULL);
		// }
		return (bool)0;
	}
}
// System.Object SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CEffectLoopU3Ed__4_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m23B23974755F3C283C2E478C293ECC131FDC0859 (U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current_1;
		return L_0;
	}
}
// System.Void SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::System.Collections.IEnumerator.Reset()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_Reset_m5B9934F58AB9DDEC6857EC2091B37ECCFFF734A0 (U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NullCheck(L_0);
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_Reset_m5B9934F58AB9DDEC6857EC2091B37ECCFFF734A0_RuntimeMethod_var)));
	}
}
// System.Object SciFiArsenal.SciFiLoopScript/<EffectLoop>d__4::System.Collections.IEnumerator.get_Current()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CEffectLoopU3Ed__4_System_Collections_IEnumerator_get_Current_mF2E23D94483DEC47F104F6A73F56BCE26A7F0CE6 (U3CEffectLoopU3Ed__4_t9B8663FA7B8FBDEBEF05E8A54E7253399BBE0547* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current_1;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiProjectileScript::Start()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiProjectileScript_Start_m666D73BFDB37D668E2D548E03ED8173DD9D89CE2 (SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// projectileParticle = Instantiate(projectileParticle, transform.position, transform.rotation) as GameObject;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0 = __this->___projectileParticle_5;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1;
		L_1 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_1);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_1, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3;
		L_3 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_3);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_4;
		L_4 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_3, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_5;
		L_5 = Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4(L_0, L_2, L_4, Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var);
		__this->___projectileParticle_5 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___projectileParticle_5), (void*)L_5);
		// projectileParticle.transform.parent = transform;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_6 = __this->___projectileParticle_5;
		NullCheck(L_6);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_7;
		L_7 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_6, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_8;
		L_8 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_7);
		Transform_set_parent_m9BD5E563B539DD5BEC342736B03F97B38A243234(L_7, L_8, NULL);
		// if (muzzleParticle)
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_9 = __this->___muzzleParticle_6;
		bool L_10;
		L_10 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_9, NULL);
		if (!L_10)
		{
			goto IL_00b5;
		}
	}
	{
		// muzzleParticle = Instantiate(muzzleParticle, transform.position, transform.rotation) as GameObject;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_11 = __this->___muzzleParticle_6;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_12;
		L_12 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_12);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_12, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_14;
		L_14 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_14);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_15;
		L_15 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_14, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_16;
		L_16 = Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4(L_11, L_13, L_15, Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var);
		__this->___muzzleParticle_6 = L_16;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___muzzleParticle_6), (void*)L_16);
		// muzzleParticle.transform.rotation = transform.rotation * Quaternion.Euler(180, 0, 0);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_17 = __this->___muzzleParticle_6;
		NullCheck(L_17);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_18;
		L_18 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_17, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_19;
		L_19 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_19);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_20;
		L_20 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_19, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_21;
		L_21 = Quaternion_Euler_m9262AB29E3E9CE94EF71051F38A28E82AEC73F90_inline((180.0f), (0.0f), (0.0f), NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_22;
		L_22 = Quaternion_op_Multiply_mCB375FCCC12A2EC8F9EB824A1BFB4453B58C2012_inline(L_20, L_21, NULL);
		NullCheck(L_18);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_18, L_22, NULL);
		// Destroy(muzzleParticle, 1.5f); // Lifetime of muzzle effect.
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_23 = __this->___muzzleParticle_6;
		Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436(L_23, (1.5f), NULL);
	}

IL_00b5:
	{
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiProjectileScript::OnCollisionEnter(UnityEngine.Collision)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiProjectileScript_OnCollisionEnter_mB7E768C851D4AD9275C75479379CB7364899BE71 (SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25* __this, Collision_tBCC6AEBD9A63E6DA2E50660DAC03CDCB1FF7A9B0* ___0_hit, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponentsInChildren_TisParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1_m4A6A34D7CF3ABDD3C27C0FB3017B5B0D05AF407D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral85AC1BD189D9C0F524186EC813E144C4547A4EA8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD8A460B57C708AAE5B3FE032970AB4EB08FDAB9B);
		s_Il2CppMethodInitialized = true;
	}
	ParticleSystemU5BU5D_tC5E33DA557C9C2064085128B3530C5F04D48F6E6* V_0 = NULL;
	GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* V_1 = NULL;
	int32_t V_2 = 0;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* V_3 = NULL;
	int32_t V_4 = 0;
	ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* V_5 = NULL;
	{
		// if (!hasCollided)
		bool L_0 = __this->___hasCollided_9;
		if (L_0)
		{
			goto IL_013a;
		}
	}
	{
		// hasCollided = true;
		__this->___hasCollided_9 = (bool)1;
		// impactParticle = Instantiate(impactParticle, transform.position, Quaternion.FromToRotation(Vector3.up, impactNormal)) as GameObject;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_1 = __this->___impactParticle_4;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_2;
		L_2 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_2);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_2, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = __this->___impactNormal_8;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_6;
		L_6 = Quaternion_FromToRotation_mCB3100F93637E72455388B901C36EF8A25DFDB9A(L_4, L_5, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_7;
		L_7 = Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4(L_1, L_3, L_6, Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m831D2F71DF2AA6C93AFDFEFA04CF2CFC5FBBCDB4_RuntimeMethod_var);
		__this->___impactParticle_4 = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___impactParticle_4), (void*)L_7);
		// if (hit.gameObject.tag == "Destructible") // Projectile will destroy objects tagged as Destructible
		Collision_tBCC6AEBD9A63E6DA2E50660DAC03CDCB1FF7A9B0* L_8 = ___0_hit;
		NullCheck(L_8);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_9;
		L_9 = Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E(L_8, NULL);
		NullCheck(L_9);
		String_t* L_10;
		L_10 = GameObject_get_tag_mEDD27BF795072834D656B286CBE51B2C99747805(L_9, NULL);
		bool L_11;
		L_11 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_10, _stringLiteral85AC1BD189D9C0F524186EC813E144C4547A4EA8, NULL);
		if (!L_11)
		{
			goto IL_0060;
		}
	}
	{
		// Destroy(hit.gameObject);
		Collision_tBCC6AEBD9A63E6DA2E50660DAC03CDCB1FF7A9B0* L_12 = ___0_hit;
		NullCheck(L_12);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_13;
		L_13 = Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E(L_12, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(L_13, NULL);
	}

IL_0060:
	{
		// foreach (GameObject trail in trailParticles)
		GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* L_14 = __this->___trailParticles_7;
		V_1 = L_14;
		V_2 = 0;
		goto IL_00b4;
	}

IL_006b:
	{
		// foreach (GameObject trail in trailParticles)
		GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* L_15 = V_1;
		int32_t L_16 = V_2;
		NullCheck(L_15);
		int32_t L_17 = L_16;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_18 = (L_15)->GetAt(static_cast<il2cpp_array_size_t>(L_17));
		V_3 = L_18;
		// GameObject curTrail = transform.Find(projectileParticle.name + "/" + trail.name).gameObject;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_19;
		L_19 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_20 = __this->___projectileParticle_5;
		NullCheck(L_20);
		String_t* L_21;
		L_21 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_20, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_22 = V_3;
		NullCheck(L_22);
		String_t* L_23;
		L_23 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_22, NULL);
		String_t* L_24;
		L_24 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(L_21, _stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1, L_23, NULL);
		NullCheck(L_19);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_25;
		L_25 = Transform_Find_m3087032B0E1C5B96A2D2C27020BAEAE2DA08F932(L_19, L_24, NULL);
		NullCheck(L_25);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_26;
		L_26 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_25, NULL);
		// curTrail.transform.parent = null;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_27 = L_26;
		NullCheck(L_27);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_28;
		L_28 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_27, NULL);
		NullCheck(L_28);
		Transform_set_parent_m9BD5E563B539DD5BEC342736B03F97B38A243234(L_28, (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1*)NULL, NULL);
		// Destroy(curTrail, 3f);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436(L_27, (3.0f), NULL);
		int32_t L_29 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_29, 1));
	}

IL_00b4:
	{
		// foreach (GameObject trail in trailParticles)
		int32_t L_30 = V_2;
		GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* L_31 = V_1;
		NullCheck(L_31);
		if ((((int32_t)L_30) < ((int32_t)((int32_t)(((RuntimeArray*)L_31)->max_length)))))
		{
			goto IL_006b;
		}
	}
	{
		// Destroy(projectileParticle, 3f);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_32 = __this->___projectileParticle_5;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436(L_32, (3.0f), NULL);
		// Destroy(impactParticle, 5f);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_33 = __this->___impactParticle_4;
		Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436(L_33, (5.0f), NULL);
		// Destroy(gameObject);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_34;
		L_34 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(L_34, NULL);
		// ParticleSystem[] trails = GetComponentsInChildren<ParticleSystem>();
		ParticleSystemU5BU5D_tC5E33DA557C9C2064085128B3530C5F04D48F6E6* L_35;
		L_35 = Component_GetComponentsInChildren_TisParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1_m4A6A34D7CF3ABDD3C27C0FB3017B5B0D05AF407D(__this, Component_GetComponentsInChildren_TisParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1_m4A6A34D7CF3ABDD3C27C0FB3017B5B0D05AF407D_RuntimeMethod_var);
		V_0 = L_35;
		// for (int i = 1; i < trails.Length; i++)
		V_4 = 1;
		goto IL_0133;
	}

IL_00f1:
	{
		// ParticleSystem trail = trails[i];
		ParticleSystemU5BU5D_tC5E33DA557C9C2064085128B3530C5F04D48F6E6* L_36 = V_0;
		int32_t L_37 = V_4;
		NullCheck(L_36);
		int32_t L_38 = L_37;
		ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* L_39 = (L_36)->GetAt(static_cast<il2cpp_array_size_t>(L_38));
		V_5 = L_39;
		// if (trail.gameObject.name.Contains("Trail"))
		ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* L_40 = V_5;
		NullCheck(L_40);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_41;
		L_41 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_40, NULL);
		NullCheck(L_41);
		String_t* L_42;
		L_42 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_41, NULL);
		NullCheck(L_42);
		bool L_43;
		L_43 = String_Contains_m6D77B121FADA7CA5F397C0FABB65DA62DF03B6C3(L_42, _stringLiteralD8A460B57C708AAE5B3FE032970AB4EB08FDAB9B, NULL);
		if (!L_43)
		{
			goto IL_012d;
		}
	}
	{
		// trail.transform.SetParent(null);
		ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* L_44 = V_5;
		NullCheck(L_44);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_45;
		L_45 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_44, NULL);
		NullCheck(L_45);
		Transform_SetParent_m6677538B60246D958DD91F931C50F969CCBB5250(L_45, (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1*)NULL, NULL);
		// Destroy(trail.gameObject, 2f);
		ParticleSystem_tB19986EE308BD63D36FB6025EEEAFBEDB97C67C1* L_46 = V_5;
		NullCheck(L_46);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_47;
		L_47 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_46, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436(L_47, (2.0f), NULL);
	}

IL_012d:
	{
		// for (int i = 1; i < trails.Length; i++)
		int32_t L_48 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_48, 1));
	}

IL_0133:
	{
		// for (int i = 1; i < trails.Length; i++)
		int32_t L_49 = V_4;
		ParticleSystemU5BU5D_tC5E33DA557C9C2064085128B3530C5F04D48F6E6* L_50 = V_0;
		NullCheck(L_50);
		if ((((int32_t)L_49) < ((int32_t)((int32_t)(((RuntimeArray*)L_50)->max_length)))))
		{
			goto IL_00f1;
		}
	}

IL_013a:
	{
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiProjectileScript::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiProjectileScript__ctor_mE6921C7403DD189B576C5F60ED142E343E78528D (SciFiProjectileScript_t0114784C7FC916A2B391E1A4FA31BD0090DF7B25* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiLightFade::Start()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLightFade_Start_m7C95658CFADACD8BF2B9897BCABE2BAC71D92022 (SciFiLightFade_tC9C3F15AB49D681770068C068E92DDEA5BB6470E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral40F46EE1E9B8B2D497BB832A96B0800F6A5E4082);
		s_Il2CppMethodInitialized = true;
	}
	{
		// if (gameObject.GetComponent<Light>())
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_0);
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_1;
		L_1 = GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E(L_0, GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_1, NULL);
		if (!L_2)
		{
			goto IL_0035;
		}
	}
	{
		// li = gameObject.GetComponent<Light>();
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3;
		L_3 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_3);
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_4;
		L_4 = GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E(L_3, GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E_RuntimeMethod_var);
		__this->___li_6 = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___li_6), (void*)L_4);
		// initIntensity = li.intensity;
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_5 = __this->___li_6;
		NullCheck(L_5);
		float L_6;
		L_6 = Light_get_intensity_m8FA28D515853068A93FA68B2148809BBEE4E710F(L_5, NULL);
		__this->___initIntensity_7 = L_6;
		return;
	}

IL_0035:
	{
		// print("No light object found on " + gameObject.name);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_7;
		L_7 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_7);
		String_t* L_8;
		L_8 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_7, NULL);
		String_t* L_9;
		L_9 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteral40F46EE1E9B8B2D497BB832A96B0800F6A5E4082, L_8, NULL);
		MonoBehaviour_print_m9E6FF71C673B651F35DD418C293CFC50C46803B6(L_9, NULL);
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLightFade::Update()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLightFade_Update_m055AD3A2F8E3FA07BEC79762A6A311B0909227DE (SciFiLightFade_tC9C3F15AB49D681770068C068E92DDEA5BB6470E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// if (gameObject.GetComponent<Light>())
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_0);
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_1;
		L_1 = GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E(L_0, GameObject_GetComponent_TisLight_t1E68479B7782AF2050FAA02A5DC612FD034F18F3_m360B68036A4201772041F6229CE3B2EB21B0C91E_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_1, NULL);
		if (!L_2)
		{
			goto IL_005c;
		}
	}
	{
		// li.intensity -= initIntensity * (Time.deltaTime / life);
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_3 = __this->___li_6;
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_4 = L_3;
		NullCheck(L_4);
		float L_5;
		L_5 = Light_get_intensity_m8FA28D515853068A93FA68B2148809BBEE4E710F(L_4, NULL);
		float L_6 = __this->___initIntensity_7;
		float L_7;
		L_7 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_8 = __this->___life_4;
		NullCheck(L_4);
		Light_set_intensity_mE4820C7F39F490B92ED5EA0C3AADA7C0775BE854(L_4, ((float)il2cpp_codegen_subtract(L_5, ((float)il2cpp_codegen_multiply(L_6, ((float)(L_7/L_8)))))), NULL);
		// if (killAfterLife && li.intensity <= 0)
		bool L_9 = __this->___killAfterLife_5;
		if (!L_9)
		{
			goto IL_005c;
		}
	}
	{
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_10 = __this->___li_6;
		NullCheck(L_10);
		float L_11;
		L_11 = Light_get_intensity_m8FA28D515853068A93FA68B2148809BBEE4E710F(L_10, NULL);
		if ((!(((float)L_11) <= ((float)(0.0f)))))
		{
			goto IL_005c;
		}
	}
	{
		// Destroy(gameObject);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_12;
		L_12 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(L_12, NULL);
	}

IL_005c:
	{
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiLightFade::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiLightFade__ctor_m6D38AB8415ED9F3EF89B6054FD2F3034EC2F9BF2 (SciFiLightFade_tC9C3F15AB49D681770068C068E92DDEA5BB6470E* __this, const RuntimeMethod* method) 
{
	{
		// public float life = 0.2f;
		__this->___life_4 = (0.200000003f);
		// public bool killAfterLife = true;
		__this->___killAfterLife_5 = (bool)1;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void SciFiArsenal.SciFiRotation::Start()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiRotation_Start_m51B068991ED8C0873121B57A7EF5D0312697D439 (SciFiRotation_t1F3E04A6A67FD04C3BD1CB92A53EBE7D1A5F53E3* __this, const RuntimeMethod* method) 
{
	{
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiRotation::Update()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiRotation_Update_m778041A2A1664C07382C2E9BD3DC925777F61397 (SciFiRotation_t1F3E04A6A67FD04C3BD1CB92A53EBE7D1A5F53E3* __this, const RuntimeMethod* method) 
{
	{
		// if (rotateSpace == spaceEnum.Local)
		int32_t L_0 = __this->___rotateSpace_5;
		if (L_0)
		{
			goto IL_0023;
		}
	}
	{
		// transform.Rotate(rotateVector * Time.deltaTime);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1;
		L_1 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = __this->___rotateVector_4;
		float L_3;
		L_3 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_2, L_3, NULL);
		NullCheck(L_1);
		Transform_Rotate_m2A308205498AFEEA3DF784B1C86E4F7C126CA2EE(L_1, L_4, NULL);
	}

IL_0023:
	{
		// if (rotateSpace == spaceEnum.World)
		int32_t L_5 = __this->___rotateSpace_5;
		if ((!(((uint32_t)L_5) == ((uint32_t)1))))
		{
			goto IL_0048;
		}
	}
	{
		// transform.Rotate(rotateVector * Time.deltaTime, Space.World);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_6;
		L_6 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = __this->___rotateVector_4;
		float L_8;
		L_8 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		L_9 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_7, L_8, NULL);
		NullCheck(L_6);
		Transform_Rotate_mAE711E1B1F639FDBA7B456E1E1B35DB90EEB737A(L_6, L_9, 0, NULL);
	}

IL_0048:
	{
		// }
		return;
	}
}
// System.Void SciFiArsenal.SciFiRotation::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SciFiRotation__ctor_m9F169F92F0435667506D5FB35BA180929F35DC58 (SciFiRotation_t1F3E04A6A67FD04C3BD1CB92A53EBE7D1A5F53E3* __this, const RuntimeMethod* method) 
{
	{
		// public Vector3 rotateVector = Vector3.zero;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0;
		L_0 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		__this->___rotateVector_4 = L_0;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x_2 = L_0;
		float L_1 = ___1_y;
		__this->___y_3 = L_1;
		float L_2 = ___2_z;
		__this->___z_4 = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x_2;
		V_0 = ((float)il2cpp_codegen_subtract(L_1, L_3));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y_3;
		V_1 = ((float)il2cpp_codegen_subtract(L_5, L_7));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z_4;
		V_2 = ((float)il2cpp_codegen_subtract(L_9, L_11));
		float L_12 = V_0;
		float L_13 = V_0;
		float L_14 = V_1;
		float L_15 = V_1;
		float L_16 = V_2;
		float L_17 = V_2;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_18;
		L_18 = sqrt(((double)((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_12, L_13)), ((float)il2cpp_codegen_multiply(L_14, L_15)))), ((float)il2cpp_codegen_multiply(L_16, L_17))))));
		V_3 = ((float)L_18);
		goto IL_0040;
	}

IL_0040:
	{
		float L_19 = V_3;
		return L_19;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Inequality_m9F170CDFBF1E490E559DA5D06D6547501A402BBF_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) 
{
	bool V_0 = false;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_lhs;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___1_rhs;
		bool L_2;
		L_2 = Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline(L_0, L_1, NULL);
		V_0 = (bool)((((int32_t)L_2) == ((int32_t)0))? 1 : 0);
		goto IL_000e;
	}

IL_000e:
	{
		bool L_3 = V_0;
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void WaypointMover_ChangeWaypointMoverSpeed_m5A78628E9ACA8F9076662C4F69EA401E8D175620_inline (WaypointMover_t437A55C187B85E04203CD57E012E9697DB69A273* __this, float ___0_newSpeed, const RuntimeMethod* method) 
{
	{
		// initialMovementSpeed = newSpeed;
		float L_0 = ___0_newSpeed;
		__this->___initialMovementSpeed_26 = L_0;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), ((float)il2cpp_codegen_subtract(L_9, L_11)), /*hidden argument*/NULL);
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___forwardVector_11;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x_2;
		float L_2 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_a;
		float L_4 = L_3.___y_3;
		float L_5 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_a;
		float L_7 = L_6.___z_4;
		float L_8 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), /*hidden argument*/NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___upVector_7;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_a;
		float L_3 = L_2.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		memset((&L_6), 0, sizeof(L_6));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_6), ((-L_1)), ((-L_3)), ((-L_5)), /*hidden argument*/NULL);
		V_0 = L_6;
		goto IL_001e;
	}

IL_001e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_MoveTowards_m0363264647799F3173AC37F8E819F98298249B08_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_current, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_target, float ___2_maxDistanceDelta, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	bool V_5 = false;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_6;
	memset((&V_6), 0, sizeof(V_6));
	int32_t G_B4_0 = 0;
	int32_t G_B6_0 = 0;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___1_target;
		float L_1 = L_0.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_current;
		float L_3 = L_2.___x_2;
		V_0 = ((float)il2cpp_codegen_subtract(L_1, L_3));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___1_target;
		float L_5 = L_4.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_current;
		float L_7 = L_6.___y_3;
		V_1 = ((float)il2cpp_codegen_subtract(L_5, L_7));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___1_target;
		float L_9 = L_8.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___0_current;
		float L_11 = L_10.___z_4;
		V_2 = ((float)il2cpp_codegen_subtract(L_9, L_11));
		float L_12 = V_0;
		float L_13 = V_0;
		float L_14 = V_1;
		float L_15 = V_1;
		float L_16 = V_2;
		float L_17 = V_2;
		V_3 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_12, L_13)), ((float)il2cpp_codegen_multiply(L_14, L_15)))), ((float)il2cpp_codegen_multiply(L_16, L_17))));
		float L_18 = V_3;
		if ((((float)L_18) == ((float)(0.0f))))
		{
			goto IL_0055;
		}
	}
	{
		float L_19 = ___2_maxDistanceDelta;
		if ((!(((float)L_19) >= ((float)(0.0f)))))
		{
			goto IL_0052;
		}
	}
	{
		float L_20 = V_3;
		float L_21 = ___2_maxDistanceDelta;
		float L_22 = ___2_maxDistanceDelta;
		G_B4_0 = ((((int32_t)((!(((float)L_20) <= ((float)((float)il2cpp_codegen_multiply(L_21, L_22)))))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_0053;
	}

IL_0052:
	{
		G_B4_0 = 0;
	}

IL_0053:
	{
		G_B6_0 = G_B4_0;
		goto IL_0056;
	}

IL_0055:
	{
		G_B6_0 = 1;
	}

IL_0056:
	{
		V_5 = (bool)G_B6_0;
		bool L_23 = V_5;
		if (!L_23)
		{
			goto IL_0061;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_24 = ___1_target;
		V_6 = L_24;
		goto IL_009b;
	}

IL_0061:
	{
		float L_25 = V_3;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_26;
		L_26 = sqrt(((double)L_25));
		V_4 = ((float)L_26);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_27 = ___0_current;
		float L_28 = L_27.___x_2;
		float L_29 = V_0;
		float L_30 = V_4;
		float L_31 = ___2_maxDistanceDelta;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_32 = ___0_current;
		float L_33 = L_32.___y_3;
		float L_34 = V_1;
		float L_35 = V_4;
		float L_36 = ___2_maxDistanceDelta;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_37 = ___0_current;
		float L_38 = L_37.___z_4;
		float L_39 = V_2;
		float L_40 = V_4;
		float L_41 = ___2_maxDistanceDelta;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_42;
		memset((&L_42), 0, sizeof(L_42));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_42), ((float)il2cpp_codegen_add(L_28, ((float)il2cpp_codegen_multiply(((float)(L_29/L_30)), L_31)))), ((float)il2cpp_codegen_add(L_33, ((float)il2cpp_codegen_multiply(((float)(L_34/L_35)), L_36)))), ((float)il2cpp_codegen_add(L_38, ((float)il2cpp_codegen_multiply(((float)(L_39/L_40)), L_41)))), /*hidden argument*/NULL);
		V_6 = L_42;
		goto IL_009b;
	}

IL_009b:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_43 = V_6;
		return L_43;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_SmoothDamp_mF673AC30464B7DF671A0556140EB6E9DD75827ED_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_current, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_target, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___2_currentVelocity, float ___3_smoothTime, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		float L_0;
		L_0 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		V_0 = L_0;
		V_1 = (std::numeric_limits<float>::infinity());
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_current;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_3 = ___2_currentVelocity;
		float L_4 = ___3_smoothTime;
		float L_5 = V_1;
		float L_6 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Vector3_SmoothDamp_mAF61EA22D4906BF87DD00A91FB4F6AC0C54C495A(L_1, L_2, L_3, L_4, L_5, L_6, NULL);
		V_2 = L_7;
		goto IL_001b;
	}

IL_001b:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = V_2;
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_v, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_v;
		float L_1 = L_0.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_v;
		float L_3 = L_2.___y_3;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4;
		memset((&L_4), 0, sizeof(L_4));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_4), L_1, L_3, /*hidden argument*/NULL);
		V_0 = L_4;
		goto IL_0015;
	}

IL_0015:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_5 = V_0;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___rightVector_10;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Angle_mB16906B482814C140FE5BA9D041D2DC11E42A68D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_from, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_to, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	bool V_2 = false;
	float V_3 = 0.0f;
	{
		float L_0;
		L_0 = Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline((&___0_from), NULL);
		float L_1;
		L_1 = Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline((&___1_to), NULL);
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_2;
		L_2 = sqrt(((double)((float)il2cpp_codegen_multiply(L_0, L_1))));
		V_0 = ((float)L_2);
		float L_3 = V_0;
		V_2 = (bool)((((float)L_3) < ((float)(1.0E-15f)))? 1 : 0);
		bool L_4 = V_2;
		if (!L_4)
		{
			goto IL_002c;
		}
	}
	{
		V_3 = (0.0f);
		goto IL_0056;
	}

IL_002c:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = ___0_from;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_to;
		float L_7;
		L_7 = Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline(L_5, L_6, NULL);
		float L_8 = V_0;
		float L_9;
		L_9 = Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF_inline(((float)(L_7/L_8)), (-1.0f), (1.0f), NULL);
		V_1 = L_9;
		float L_10 = V_1;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_11;
		L_11 = acos(((double)L_10));
		V_3 = ((float)il2cpp_codegen_multiply(((float)L_11), (57.2957802f)));
		goto IL_0056;
	}

IL_0056:
	{
		float L_12 = V_3;
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_0 = (*(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)__this);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Quaternion_Internal_ToEulerRad_m5BD0EEC543120C320DC77FCCDFD2CE2E6BD3F1A8(L_0, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_1, (57.2957802f), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Quaternion_Internal_MakePositive_m73E2D01920CB0DFE661A55022C129E8617F0C9A8(L_2, NULL);
		V_0 = L_3;
		goto IL_001e;
	}

IL_001e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline (float ___0_a, float ___1_b, float ___2_t, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = ___0_a;
		float L_1 = ___1_b;
		float L_2 = ___0_a;
		float L_3 = ___2_t;
		float L_4;
		L_4 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_3, NULL);
		V_0 = ((float)il2cpp_codegen_add(L_0, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_1, L_2)), L_4))));
		goto IL_0010;
	}

IL_0010:
	{
		float L_5 = V_0;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_v, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_v;
		float L_1 = L_0.___x_0;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_2 = ___0_v;
		float L_3 = L_2.___y_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		memset((&L_4), 0, sizeof(L_4));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_4), L_1, L_3, (0.0f), /*hidden argument*/NULL);
		V_0 = L_4;
		goto IL_001a;
	}

IL_001a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = V_0;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_LerpAngle_m0653422E15193C2E4A4E5AF05236B6315C789C23_inline (float ___0_a, float ___1_b, float ___2_t, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	bool V_1 = false;
	float V_2 = 0.0f;
	{
		float L_0 = ___1_b;
		float L_1 = ___0_a;
		float L_2;
		L_2 = Mathf_Repeat_m6F1560A163481BB311D685294E1B463C3E4EB3BA_inline(((float)il2cpp_codegen_subtract(L_0, L_1)), (360.0f), NULL);
		V_0 = L_2;
		float L_3 = V_0;
		V_1 = (bool)((((float)L_3) > ((float)(180.0f)))? 1 : 0);
		bool L_4 = V_1;
		if (!L_4)
		{
			goto IL_0023;
		}
	}
	{
		float L_5 = V_0;
		V_0 = ((float)il2cpp_codegen_subtract(L_5, (360.0f)));
	}

IL_0023:
	{
		float L_6 = ___0_a;
		float L_7 = V_0;
		float L_8 = ___2_t;
		float L_9;
		L_9 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_8, NULL);
		V_2 = ((float)il2cpp_codegen_add(L_6, ((float)il2cpp_codegen_multiply(L_7, L_9))));
		goto IL_0030;
	}

IL_0030:
	{
		float L_10 = V_2;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___zeroVector_5;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_r;
		__this->___r_0 = L_0;
		float L_1 = ___1_g;
		__this->___g_1 = L_1;
		float L_2 = ___2_b;
		__this->___b_2 = L_2;
		float L_3 = ___3_a;
		__this->___a_3 = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), ((float)il2cpp_codegen_add(L_9, L_11)), /*hidden argument*/NULL);
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_InverseLerp_mBD7EC6A7173CE082226077E1557D5BC2D2AE0D9D_inline (float ___0_a, float ___1_b, float ___2_value, const RuntimeMethod* method) 
{
	bool V_0 = false;
	float V_1 = 0.0f;
	{
		float L_0 = ___0_a;
		float L_1 = ___1_b;
		V_0 = (bool)((((int32_t)((((float)L_0) == ((float)L_1))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_001b;
		}
	}
	{
		float L_3 = ___2_value;
		float L_4 = ___0_a;
		float L_5 = ___1_b;
		float L_6 = ___0_a;
		float L_7;
		L_7 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(((float)(((float)il2cpp_codegen_subtract(L_3, L_4))/((float)il2cpp_codegen_subtract(L_5, L_6)))), NULL);
		V_1 = L_7;
		goto IL_0023;
	}

IL_001b:
	{
		V_1 = (0.0f);
		goto IL_0023;
	}

IL_0023:
	{
		float L_8 = V_1;
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline (float ___0_d, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_a, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___1_a;
		float L_1 = L_0.___x_2;
		float L_2 = ___0_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___1_a;
		float L_4 = L_3.___y_3;
		float L_5 = ___0_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_a;
		float L_7 = L_6.___z_4;
		float L_8 = ___0_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), /*hidden argument*/NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x_0 = L_0;
		float L_1 = ___1_y;
		__this->___y_1 = L_1;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Euler_m9262AB29E3E9CE94EF71051F38A28E82AEC73F90_inline (float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float L_0 = ___0_x;
		float L_1 = ___1_y;
		float L_2 = ___2_z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		memset((&L_3), 0, sizeof(L_3));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_3), L_0, L_1, L_2, /*hidden argument*/NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_3, (0.0174532924f), NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_5;
		L_5 = Quaternion_Internal_FromEulerRad_m66D4475341F53949471E6870FB5C5E4A5E9BA93E(L_4, NULL);
		V_0 = L_5;
		goto IL_001b;
	}

IL_001b:
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF_inline (float ___0_value, float ___1_min, float ___2_max, const RuntimeMethod* method) 
{
	bool V_0 = false;
	bool V_1 = false;
	float V_2 = 0.0f;
	{
		float L_0 = ___0_value;
		float L_1 = ___1_min;
		V_0 = (bool)((((float)L_0) < ((float)L_1))? 1 : 0);
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_000e;
		}
	}
	{
		float L_3 = ___1_min;
		___0_value = L_3;
		goto IL_0019;
	}

IL_000e:
	{
		float L_4 = ___0_value;
		float L_5 = ___2_max;
		V_1 = (bool)((((float)L_4) > ((float)L_5))? 1 : 0);
		bool L_6 = V_1;
		if (!L_6)
		{
			goto IL_0019;
		}
	}
	{
		float L_7 = ___2_max;
		___0_value = L_7;
	}

IL_0019:
	{
		float L_8 = ___0_value;
		V_2 = L_8;
		goto IL_001d;
	}

IL_001d:
	{
		float L_9 = V_2;
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_get_identity_m7E701AE095ED10FD5EA0B50ABCFDE2EEFF2173A5_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_0 = ((Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_StaticFields*)il2cpp_codegen_static_fields_for(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var))->___identityQuaternion_4;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_yellow_m66637FA14383E8D74F24AE256B577CE1D55D469F_inline (const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (1.0f), (0.921568632f), (0.0156862754f), (1.0f), /*hidden argument*/NULL);
		V_0 = L_0;
		goto IL_001d;
	}

IL_001d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_op_Multiply_mCB375FCCC12A2EC8F9EB824A1BFB4453B58C2012_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_lhs, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___1_rhs, const RuntimeMethod* method) 
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_0 = ___0_lhs;
		float L_1 = L_0.___w_3;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_2 = ___1_rhs;
		float L_3 = L_2.___x_0;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_4 = ___0_lhs;
		float L_5 = L_4.___x_0;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_6 = ___1_rhs;
		float L_7 = L_6.___w_3;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_8 = ___0_lhs;
		float L_9 = L_8.___y_1;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_10 = ___1_rhs;
		float L_11 = L_10.___z_2;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_12 = ___0_lhs;
		float L_13 = L_12.___z_2;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_14 = ___1_rhs;
		float L_15 = L_14.___y_1;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_16 = ___0_lhs;
		float L_17 = L_16.___w_3;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_18 = ___1_rhs;
		float L_19 = L_18.___y_1;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_20 = ___0_lhs;
		float L_21 = L_20.___y_1;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_22 = ___1_rhs;
		float L_23 = L_22.___w_3;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_24 = ___0_lhs;
		float L_25 = L_24.___z_2;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_26 = ___1_rhs;
		float L_27 = L_26.___x_0;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_28 = ___0_lhs;
		float L_29 = L_28.___x_0;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_30 = ___1_rhs;
		float L_31 = L_30.___z_2;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_32 = ___0_lhs;
		float L_33 = L_32.___w_3;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_34 = ___1_rhs;
		float L_35 = L_34.___z_2;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_36 = ___0_lhs;
		float L_37 = L_36.___z_2;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_38 = ___1_rhs;
		float L_39 = L_38.___w_3;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_40 = ___0_lhs;
		float L_41 = L_40.___x_0;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_42 = ___1_rhs;
		float L_43 = L_42.___y_1;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_44 = ___0_lhs;
		float L_45 = L_44.___y_1;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_46 = ___1_rhs;
		float L_47 = L_46.___x_0;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_48 = ___0_lhs;
		float L_49 = L_48.___w_3;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_50 = ___1_rhs;
		float L_51 = L_50.___w_3;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_52 = ___0_lhs;
		float L_53 = L_52.___x_0;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_54 = ___1_rhs;
		float L_55 = L_54.___x_0;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_56 = ___0_lhs;
		float L_57 = L_56.___y_1;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_58 = ___1_rhs;
		float L_59 = L_58.___y_1;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_60 = ___0_lhs;
		float L_61 = L_60.___z_2;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_62 = ___1_rhs;
		float L_63 = L_62.___z_2;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_64;
		memset((&L_64), 0, sizeof(L_64));
		Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8_inline((&L_64), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11)))), ((float)il2cpp_codegen_multiply(L_13, L_15)))), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_17, L_19)), ((float)il2cpp_codegen_multiply(L_21, L_23)))), ((float)il2cpp_codegen_multiply(L_25, L_27)))), ((float)il2cpp_codegen_multiply(L_29, L_31)))), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_33, L_35)), ((float)il2cpp_codegen_multiply(L_37, L_39)))), ((float)il2cpp_codegen_multiply(L_41, L_43)))), ((float)il2cpp_codegen_multiply(L_45, L_47)))), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(L_49, L_51)), ((float)il2cpp_codegen_multiply(L_53, L_55)))), ((float)il2cpp_codegen_multiply(L_57, L_59)))), ((float)il2cpp_codegen_multiply(L_61, L_63)))), /*hidden argument*/NULL);
		V_0 = L_64;
		goto IL_00e5;
	}

IL_00e5:
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_65 = V_0;
		return L_65;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size_2;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version_3;
		__this->____version_3 = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->____items_1;
		V_0 = L_1;
		int32_t L_2 = __this->____size_2;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size_2 = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		int32_t L_7 = V_1;
		RuntimeObject* L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		return;
	}

IL_0034:
	{
		RuntimeObject* L_9 = ___0_item;
		((  void (*) (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D*, RuntimeObject*, const RuntimeMethod*))il2cpp_codegen_get_method_pointer(il2cpp_rgctx_method(method->klass->rgctx_data, 11)))(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 11));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	bool V_4 = false;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_lhs;
		float L_1 = L_0.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_rhs;
		float L_3 = L_2.___x_2;
		V_0 = ((float)il2cpp_codegen_subtract(L_1, L_3));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_lhs;
		float L_5 = L_4.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_rhs;
		float L_7 = L_6.___y_3;
		V_1 = ((float)il2cpp_codegen_subtract(L_5, L_7));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_lhs;
		float L_9 = L_8.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_rhs;
		float L_11 = L_10.___z_4;
		V_2 = ((float)il2cpp_codegen_subtract(L_9, L_11));
		float L_12 = V_0;
		float L_13 = V_0;
		float L_14 = V_1;
		float L_15 = V_1;
		float L_16 = V_2;
		float L_17 = V_2;
		V_3 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_12, L_13)), ((float)il2cpp_codegen_multiply(L_14, L_15)))), ((float)il2cpp_codegen_multiply(L_16, L_17))));
		float L_18 = V_3;
		V_4 = (bool)((((float)L_18) < ((float)(9.99999944E-11f)))? 1 : 0);
		goto IL_0043;
	}

IL_0043:
	{
		bool L_19 = V_4;
		return L_19;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___x_2;
		float L_1 = __this->___x_2;
		float L_2 = __this->___y_3;
		float L_3 = __this->___y_3;
		float L_4 = __this->___z_4;
		float L_5 = __this->___z_4;
		V_0 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, L_1)), ((float)il2cpp_codegen_multiply(L_2, L_3)))), ((float)il2cpp_codegen_multiply(L_4, L_5))));
		goto IL_002d;
	}

IL_002d:
	{
		float L_6 = V_0;
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_lhs;
		float L_1 = L_0.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_rhs;
		float L_3 = L_2.___x_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_lhs;
		float L_5 = L_4.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_rhs;
		float L_7 = L_6.___y_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_lhs;
		float L_9 = L_8.___z_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_rhs;
		float L_11 = L_10.___z_4;
		V_0 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11))));
		goto IL_002d;
	}

IL_002d:
	{
		float L_12 = V_0;
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) 
{
	bool V_0 = false;
	float V_1 = 0.0f;
	bool V_2 = false;
	{
		float L_0 = ___0_value;
		V_0 = (bool)((((float)L_0) < ((float)(0.0f)))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		V_1 = (0.0f);
		goto IL_002d;
	}

IL_0015:
	{
		float L_2 = ___0_value;
		V_2 = (bool)((((float)L_2) > ((float)(1.0f)))? 1 : 0);
		bool L_3 = V_2;
		if (!L_3)
		{
			goto IL_0029;
		}
	}
	{
		V_1 = (1.0f);
		goto IL_002d;
	}

IL_0029:
	{
		float L_4 = ___0_value;
		V_1 = L_4;
		goto IL_002d;
	}

IL_002d:
	{
		float L_5 = V_1;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Repeat_m6F1560A163481BB311D685294E1B463C3E4EB3BA_inline (float ___0_t, float ___1_length, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = ___0_t;
		float L_1 = ___0_t;
		float L_2 = ___1_length;
		float L_3;
		L_3 = floorf(((float)(L_1/L_2)));
		float L_4 = ___1_length;
		float L_5 = ___1_length;
		float L_6;
		L_6 = Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF_inline(((float)il2cpp_codegen_subtract(L_0, ((float)il2cpp_codegen_multiply(L_3, L_4)))), (0.0f), L_5, NULL);
		V_0 = L_6;
		goto IL_001b;
	}

IL_001b:
	{
		float L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x_0 = L_0;
		float L_1 = ___1_y;
		__this->___y_1 = L_1;
		float L_2 = ___2_z;
		__this->___z_2 = L_2;
		float L_3 = ___3_w;
		__this->___w_3 = L_3;
		return;
	}
}
