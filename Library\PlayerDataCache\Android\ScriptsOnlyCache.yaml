ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp-firstpass.dll:
    - DG.Tweening.DOTweenAnimation
    Assembly-CSharp.dll:
    - Birdtriiger
    - Cubemovement
    - DistanceFromPlayer
    - GameOptimizer
    - GrassCubeprocessormachine
    - LivingOcean
    - RCC_Camera
    - RCC_CarControllerV3
    - RC<PERSON>_ChangableWheels
    - RCC_CinematicCamera
    - RCC_ColorPickerBySliders
    - RCC_CustomizerExample
    - RCC_DashboardColors
    - RCC_DashboardInputs
    - RCC_Demo
    - RCC_DemoMaterials
    - RCC_DemoVehicles
    - RCC_Exhaust
    - RCC_FOVForCinematicCamera
    - RCC_GroundMaterials
    - RCC_HoodCamera
    - RCC_InfoLabel
    - RCC_InitialSettings
    - RCC_Light
    - RCC_Mirror
    - RCC_MobileButtons
    - RCC_MobileUIDrag
    - RCC_PoliceSiren
    - RCC_Records
    - RCC_SceneManager
    - RCC_Settings
    - RCC_Skidmarks
    - RCC_SkidmarksManager
    - RCC_Telemetry
    - RCC_TruckTrailer
    - RC<PERSON>_UIController
    - RC<PERSON>_UIDashboardButton
    - RCC_UIDashboardDisplay
    - RCC_UIJoystick
    - RCC_UISliderTextReader
    - RCC_UISteeringWheelController
    - RCC_UI_Canvas_Modification
    - RCC_UI_Color
    - RCC_UI_MobileDrag
    - RCC_UI_Siren
    - RCC_UI_Spoiler
    - RCC_UI_Upgrade
    - RCC_UI_Wheel
    - RCC_Useless
    - RCC_VehicleUpgrade_Brake
    - RCC_VehicleUpgrade_Engine
    - RCC_VehicleUpgrade_Handling
    - RCC_VehicleUpgrade_Paint
    - RCC_VehicleUpgrade_PaintManager
    - RCC_VehicleUpgrade_Siren
    - RCC_VehicleUpgrade_SirenManager
    - RCC_VehicleUpgrade_Spoiler
    - RCC_VehicleUpgrade_SpoilerManager
    - RCC_VehicleUpgrade_UpgradeManager
    - RCC_VehicleUpgrade_WheelManager
    - RCC_WheelCollider
    - ScrollUV
    - TerrainPainter
    - TrollycubepickSystem
    - Tructorlinkcontroll
    - WPC
    - WPC_Waypoint
    - WaypointWalker
    DOTween.dll:
    - DG.Tweening.Core.DOTweenSettings
    GoogleMobileAds.Editor.dll:
    - GoogleMobileAds.Editor.GoogleMobileAdsSettings
    Unity.InputSystem.dll:
    - UnityEngine.InputSystem.InputActionAsset
    - UnityEngine.InputSystem.InputActionReference
    - UnityEngine.InputSystem.InputSettings
    - UnityEngine.InputSystem.InputSystemObject
    - UnityEngine.InputSystem.RemoteInputPlayerConnection
    - UnityEngine.InputSystem.UI.InputSystemUIInputModule
    Unity.RenderPipelines.Core.Runtime.dll:
    - UnityEngine.Rendering.UI.DebugUIHandlerBitField
    - UnityEngine.Rendering.UI.DebugUIHandlerButton
    - UnityEngine.Rendering.UI.DebugUIHandlerCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerColor
    - UnityEngine.Rendering.UI.DebugUIHandlerContainer
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumField
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerFoldout
    - UnityEngine.Rendering.UI.DebugUIHandlerGroup
    - UnityEngine.Rendering.UI.DebugUIHandlerHBox
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerMessageBox
    - UnityEngine.Rendering.UI.DebugUIHandlerPanel
    - UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerRow
    - UnityEngine.Rendering.UI.DebugUIHandlerToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerVBox
    - UnityEngine.Rendering.UI.DebugUIHandlerValue
    - UnityEngine.Rendering.UI.DebugUIHandlerVector2
    - UnityEngine.Rendering.UI.DebugUIHandlerVector3
    - UnityEngine.Rendering.UI.DebugUIHandlerVector4
    - UnityEngine.Rendering.UI.UIFoldout
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.Timeline.Editor.dll:
    - TimelinePreferences
    - UnityEditor.Timeline.SequenceHierarchy
    - UnityEditor.Timeline.TimelineWindow
    Unity.Timeline.dll:
    - UnityEngine.Timeline.AnimationTrack
    - UnityEngine.Timeline.TimelineAsset
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.EventTrigger
    - UnityEngine.EventSystems.StandaloneInputModule
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.ContentSizeFitter
    - UnityEngine.UI.Dropdown
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.InputField
    - UnityEngine.UI.LayoutElement
    - UnityEngine.UI.Mask
    - UnityEngine.UI.Outline
    - UnityEngine.UI.RawImage
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Shadow
    - UnityEngine.UI.Slider
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
    - UnityEngine.UI.VerticalLayoutGroup
  serializedClasses:
    Assembly-CSharp:
    - RCC_Camera/CameraTarget
    - RCC_CarControllerV3/Gear
    - RCC_ChangableWheels/ChangableWheels
    - RCC_Damage
    - RCC_GroundMaterials/GroundMaterialFrictions
    - RCC_GroundMaterials/TerrainFrictions
    - RCC_GroundMaterials/TerrainFrictions/SplatmapIndexes
    - RCC_Inputs
    - RCC_Settings/BehaviorType
    - RCC_TruckTrailer/TrailerWheel
    DOTween:
    - DG.Tweening.Core.DOTweenSettings/ModulesSetup
    - DG.Tweening.Core.DOTweenSettings/SafeModeOptions
    Unity.InputSystem:
    - UnityEngine.InputSystem.InputAction
    - UnityEngine.InputSystem.InputActionMap
    - UnityEngine.InputSystem.InputBinding
    - UnityEngine.InputSystem.InputControlScheme
    - UnityEngine.InputSystem.InputControlScheme/DeviceRequirement
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.UI.DebugUIPrefabBundle
    Unity.Timeline:
    - UnityEngine.Timeline.MarkerList
    - UnityEngine.Timeline.TimelineAsset/EditorSettings
    UnityEngine.CoreModule:
    - UnityEngine.Events.ArgumentCache
    - UnityEngine.Events.PersistentCallGroup
    - UnityEngine.Events.PersistentListenerMode
    - UnityEngine.Events.UnityEvent
    - UnityEngine.RectOffset
    UnityEngine.IMGUIModule:
    - UnityEngine.GUISettings
    - UnityEngine.GUIStyle
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventTrigger/Entry
    - UnityEngine.EventSystems.EventTrigger/TriggerEvent
    - UnityEngine.UI.AnimationTriggers
    - UnityEngine.UI.Button/ButtonClickedEvent
    - UnityEngine.UI.ColorBlock
    - UnityEngine.UI.Dropdown/DropdownEvent
    - UnityEngine.UI.Dropdown/OptionData
    - UnityEngine.UI.Dropdown/OptionDataList
    - UnityEngine.UI.FontData
    - UnityEngine.UI.InputField/EndEditEvent
    - UnityEngine.UI.InputField/OnChangeEvent
    - UnityEngine.UI.InputField/SubmitEvent
    - UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
    - UnityEngine.UI.Navigation
    - UnityEngine.UI.ScrollRect/ScrollRectEvent
    - UnityEngine.UI.Scrollbar/ScrollEvent
    - UnityEngine.UI.Slider/SliderEvent
    - UnityEngine.UI.SpriteState
    - UnityEngine.UI.Toggle/ToggleEvent
  methodsToPreserve:
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRearCambersBySlider
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSmokeColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetHeadlightColorByColorPicker
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: ChangeWheelsBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetTurboByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSH
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetNOSByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetCounterSteeringByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetFrontSuspensionsSpringForceBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetExhaustFlameByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRearSuspensionsSpringDamperBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetTCS
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRearSuspensionDistancesBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSmokeColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSmokeColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetClutchThresholdBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetESP
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetFrontSuspensionDistancesBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: TogglePreviewExhaustFlameByToggle
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetGearShiftingThresholdBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetTransmission
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetHeadlightColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: TogglePreviewSmokeByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetFrontCambersBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetFrontSuspensionsSpringDamperBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRearSuspensionsSpringForceBySlider
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetABS
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRevLimiterByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetHeadlightColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSHStrength
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerButton
    methodName: OnAction
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: ResetDebugManager
  sceneClasses:
    Assets/Game scene/Farming mod.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 90
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 96
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 111
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 788}
    - Class: 114
      Script: {instanceID: 16214}
    - Class: 114
      Script: {instanceID: 16216}
    - Class: 114
      Script: {instanceID: 16218}
    - Class: 114
      Script: {instanceID: 16220}
    - Class: 114
      Script: {instanceID: 16222}
    - Class: 114
      Script: {instanceID: 16224}
    - Class: 114
      Script: {instanceID: 16228}
    - Class: 114
      Script: {instanceID: 16406}
    - Class: 114
      Script: {instanceID: 16512}
    - Class: 114
      Script: {instanceID: 16594}
    - Class: 114
      Script: {instanceID: 16644}
    - Class: 114
      Script: {instanceID: 16654}
    - Class: 114
      Script: {instanceID: 16662}
    - Class: 114
      Script: {instanceID: 16664}
    - Class: 114
      Script: {instanceID: 16818}
    - Class: 114
      Script: {instanceID: 16950}
    - Class: 114
      Script: {instanceID: 17020}
    - Class: 114
      Script: {instanceID: 17034}
    - Class: 114
      Script: {instanceID: 17114}
    - Class: 114
      Script: {instanceID: 17280}
    - Class: 114
      Script: {instanceID: 17364}
    - Class: 114
      Script: {instanceID: 17384}
    - Class: 114
      Script: {instanceID: 17418}
    - Class: 114
      Script: {instanceID: 17430}
    - Class: 114
      Script: {instanceID: 17446}
    - Class: 114
      Script: {instanceID: 17474}
    - Class: 114
      Script: {instanceID: 17570}
    - Class: 114
      Script: {instanceID: 17588}
    - Class: 114
      Script: {instanceID: 17610}
    - Class: 114
      Script: {instanceID: 17636}
    - Class: 114
      Script: {instanceID: 17670}
    - Class: 114
      Script: {instanceID: 17672}
    - Class: 114
      Script: {instanceID: 17736}
    - Class: 114
      Script: {instanceID: 17930}
    - Class: 114
      Script: {instanceID: 18054}
    - Class: 114
      Script: {instanceID: 18078}
    - Class: 114
      Script: {instanceID: 18126}
    - Class: 114
      Script: {instanceID: 18138}
    - Class: 114
      Script: {instanceID: 18238}
    - Class: 114
      Script: {instanceID: 18256}
    - Class: 114
      Script: {instanceID: 18302}
    - Class: 114
      Script: {instanceID: 18344}
    - Class: 114
      Script: {instanceID: 18354}
    - Class: 114
      Script: {instanceID: 18374}
    - Class: 114
      Script: {instanceID: 18474}
    - Class: 114
      Script: {instanceID: 18476}
    - Class: 114
      Script: {instanceID: 18498}
    - Class: 114
      Script: {instanceID: 18672}
    - Class: 114
      Script: {instanceID: 18736}
    - Class: 114
      Script: {instanceID: 18806}
    - Class: 114
      Script: {instanceID: 18812}
    - Class: 114
      Script: {instanceID: 18900}
    - Class: 114
      Script: {instanceID: 19030}
    - Class: 114
      Script: {instanceID: 19130}
    - Class: 114
      Script: {instanceID: 19232}
    - Class: 114
      Script: {instanceID: 19236}
    - Class: 114
      Script: {instanceID: 19240}
    - Class: 114
      Script: {instanceID: 19346}
    - Class: 114
      Script: {instanceID: 19362}
    - Class: 114
      Script: {instanceID: 19578}
    - Class: 114
      Script: {instanceID: 19858}
    - Class: 114
      Script: {instanceID: 19866}
    - Class: 114
      Script: {instanceID: 19944}
    - Class: 114
      Script: {instanceID: 19960}
    - Class: 114
      Script: {instanceID: 20016}
    - Class: 114
      Script: {instanceID: 20036}
    - Class: 114
      Script: {instanceID: 20044}
    - Class: 114
      Script: {instanceID: 20048}
    - Class: 114
      Script: {instanceID: 20102}
    - Class: 114
      Script: {instanceID: 20218}
    - Class: 114
      Script: {instanceID: 20256}
    - Class: 114
      Script: {instanceID: 20262}
    - Class: 114
      Script: {instanceID: 20312}
    - Class: 114
      Script: {instanceID: 20314}
    - Class: 114
      Script: {instanceID: 20320}
    - Class: 114
      Script: {instanceID: 20342}
    - Class: 114
      Script: {instanceID: 20350}
    - Class: 114
      Script: {instanceID: 20442}
    - Class: 114
      Script: {instanceID: 20498}
    - Class: 114
      Script: {instanceID: 20636}
    - Class: 114
      Script: {instanceID: 20798}
    - Class: 114
      Script: {instanceID: 20812}
    - Class: 114
      Script: {instanceID: 20934}
    - Class: 114
      Script: {instanceID: 20982}
    - Class: 114
      Script: {instanceID: 20992}
    - Class: 114
      Script: {instanceID: 20998}
    - Class: 114
      Script: {instanceID: 21032}
    - Class: 114
      Script: {instanceID: 21038}
    - Class: 114
      Script: {instanceID: 21156}
    - Class: 114
      Script: {instanceID: 21196}
    - Class: 114
      Script: {instanceID: 21198}
    - Class: 114
      Script: {instanceID: 21250}
    - Class: 114
      Script: {instanceID: 21312}
    - Class: 114
      Script: {instanceID: 21324}
    - Class: 114
      Script: {instanceID: 21408}
    - Class: 114
      Script: {instanceID: 21448}
    - Class: 114
      Script: {instanceID: 21644}
    - Class: 114
      Script: {instanceID: 21674}
    - Class: 114
      Script: {instanceID: 21746}
    - Class: 114
      Script: {instanceID: 21766}
    - Class: 114
      Script: {instanceID: 21784}
    - Class: 114
      Script: {instanceID: 21838}
    - Class: 114
      Script: {instanceID: 21910}
    - Class: 114
      Script: {instanceID: 22004}
    - Class: 114
      Script: {instanceID: 22040}
    - Class: 114
      Script: {instanceID: 22084}
    - Class: 114
      Script: {instanceID: 22124}
    - Class: 114
      Script: {instanceID: 22160}
    - Class: 114
      Script: {instanceID: 22188}
    - Class: 114
      Script: {instanceID: 22220}
    - Class: 114
      Script: {instanceID: 22290}
    - Class: 114
      Script: {instanceID: 22544}
    - Class: 114
      Script: {instanceID: 22550}
    - Class: 114
      Script: {instanceID: 22618}
    - Class: 114
      Script: {instanceID: 22694}
    - Class: 114
      Script: {instanceID: 22746}
    - Class: 114
      Script: {instanceID: 22812}
    - Class: 114
      Script: {instanceID: 22870}
    - Class: 114
      Script: {instanceID: 22922}
    - Class: 114
      Script: {instanceID: 23056}
    - Class: 114
      Script: {instanceID: 23164}
    - Class: 114
      Script: {instanceID: 23324}
    - Class: 114
      Script: {instanceID: 23396}
    - Class: 114
      Script: {instanceID: 23456}
    - Class: 114
      Script: {instanceID: 23474}
    - Class: 114
      Script: {instanceID: 618734}
    - Class: 114
      Script: {instanceID: 618742}
    - Class: 114
      Script: {instanceID: 618744}
    - Class: 114
      Script: {instanceID: 618746}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 121
      Script: {instanceID: 0}
    - Class: 123
      Script: {instanceID: 0}
    - Class: 124
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 134
      Script: {instanceID: 0}
    - Class: 136
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 138
      Script: {instanceID: 0}
    - Class: 146
      Script: {instanceID: 0}
    - Class: 153
      Script: {instanceID: 0}
    - Class: 154
      Script: {instanceID: 0}
    - Class: 156
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 205
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 215
      Script: {instanceID: 0}
    - Class: 218
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 225
      Script: {instanceID: 0}
    - Class: 241
      Script: {instanceID: 0}
    - Class: 243
      Script: {instanceID: 0}
    - Class: 245
      Script: {instanceID: 0}
    - Class: 320
      Script: {instanceID: 0}
    - Class: 850595691
      Script: {instanceID: 0}
    - Class: 1953259897
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: 68587ea39bb503ea8d65053e93c31d06
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: 7bcb35908a60cac26edee0ff51816ada
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: a9d9d285d996b592f1ccf674fa017099
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: 8e0c8b499f64b686c0ec50f43e94d3af
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: 8165edc0ea71078aadd02b6c3ce945a1
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: a1926869a1b71ceb204692733af622c6
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: cb8c9f2edb0d1bd5413e57087e78fdad
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector2
  - hash:
      serializedVersion: 2
      Hash: c1058004017c4d95e55a1c77d955e000
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: 671ed8f352abdd8bc0a27669d0370a2f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CustomizationTrigger
  - hash:
      serializedVersion: 2
      Hash: 25fcf8376601657d17838a1b724fb8e2
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: PlayerInput
  - hash:
      serializedVersion: 2
      Hash: a106667e49fe46f6fa38808b669906cb
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AutoTypeText
  - hash:
      serializedVersion: 2
      Hash: cf5df2ecad7eeff2a1d1d1142a5818e8
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: FreeCamera
  - hash:
      serializedVersion: 2
      Hash: a506b72c53f139c380ebe7f3581a434b
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: f810935f76d28048a0a4be4479a0b8c9
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineExternalCamera
  - hash:
      serializedVersion: 2
      Hash: da05abcc878171591b25804eef377360
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: d5dbba1e095217e88ae7faf82e4e2dbe
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: ac51533c95676e7af6ba886a0ed52d0d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFreeLook
  - hash:
      serializedVersion: 2
      Hash: 7a0b07b1892f2a5c214ff6d300f598c7
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTriggerAction
  - hash:
      serializedVersion: 2
      Hash: 7cea3daad0f112a7159c571a80f7af7b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCCOrbitReset
  - hash:
      serializedVersion: 2
      Hash: bdddf1488100f2b05e78d1640001a071
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.HID.Editor
    className: HIDDescriptorWindow
  - hash:
      serializedVersion: 2
      Hash: 89fdfe9459ae7f55578ea8284fefe4ed
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: VirtualMouseInput
  - hash:
      serializedVersion: 2
      Hash: b6919327bcca55e4e56b20b361fa8ee4
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: 66ae6dfd00d882b548b0f6848a4b8b38
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GDPRScript
  - hash:
      serializedVersion: 2
      Hash: bded33e439631f087d3612601816819f
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineCameraOffset
  - hash:
      serializedVersion: 2
      Hash: 7e7135d8d5174d2df8271beee14cd1c6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_DemoMaterials
  - hash:
      serializedVersion: 2
      Hash: 23a3c4121b82249c48f28f7c51cb8027
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_FixedCamera
  - hash:
      serializedVersion: 2
      Hash: 7c452fbf2d21a6ff3c4e80d869adcab1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UISliderTextReader
  - hash:
      serializedVersion: 2
      Hash: 74177df3eaaf089041860685bdb8ea15
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_PoliceSiren
  - hash:
      serializedVersion: 2
      Hash: b4d605e4ba7198403b5d69f9d937bfee
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: TerrainPainter
  - hash:
      serializedVersion: 2
      Hash: aac02b243ae1d890ed3c26d566fd6f4e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UI_Canvas_Modification
  - hash:
      serializedVersion: 2
      Hash: 112bc40a456c5b0bfe768c00a7a89ade
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LevelLock
  - hash:
      serializedVersion: 2
      Hash: 700cc65c5d61eca10bcacb25a2d31a1d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GoogleMobileAdsConsentController
  - hash:
      serializedVersion: 2
      Hash: 192bcb00de0b6dc327b080f43a033895
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineCollider
  - hash:
      serializedVersion: 2
      Hash: 7de7fec41819cf6d8f2fecf9cb14039e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: player
  - hash:
      serializedVersion: 2
      Hash: 38e7a5b163c4394727bc53d6d2090f88
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBlenderSettings
  - hash:
      serializedVersion: 2
      Hash: 57bea7b906ad65baac71705c8e084574
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_SkidmarksManager
  - hash:
      serializedVersion: 2
      Hash: 319f66ea1036bda420351a9a4ac0fde3
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: playermain
  - hash:
      serializedVersion: 2
      Hash: 9605a0475669ece0189f675c4d408e5f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: 7471b47de5eed6733786db82f287874d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_FeedVehicleExample
  - hash:
      serializedVersion: 2
      Hash: ce7d8e656ace251f308b0d622ae254b1
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: AdvancedDropdownWindow
  - hash:
      serializedVersion: 2
      Hash: baf218a9799098962f3ddb5d79364282
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner
    className: PlaymodeTestsController
  - hash:
      serializedVersion: 2
      Hash: ffe5ff027e9e2cfbff98a21aa35b5c76
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerMessageBox
  - hash:
      serializedVersion: 2
      Hash: fbc58186bcba82f31a01311018e75a24
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RECtangleAD
  - hash:
      serializedVersion: 2
      Hash: 8c09365a3ed8ce93346e2bee07695fc0
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering
    className: ProbeVolumePerSceneData
  - hash:
      serializedVersion: 2
      Hash: b8c62e6fbbd49b0adf47ca1dfd4b7754
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: 4823115591fc3fc95f57fe4f1ec012d3
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggle
  - hash:
      serializedVersion: 2
      Hash: 883e7228d183a3b9860af13a21ff117c
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerGroup
  - hash:
      serializedVersion: 2
      Hash: d4352cb5583b70809429a4d1ea810f24
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CharacterController
  - hash:
      serializedVersion: 2
      Hash: 1a954480ab8a39b6fb0a62f6d1592ba3
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiLoadSceneOnClick
  - hash:
      serializedVersion: 2
      Hash: 41c4885c7d69a42c336db4ac4d290676
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: LensFlareComponentSRP
  - hash:
      serializedVersion: 2
      Hash: 62af398c2bd0b57dc331d526dc0fe649
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_SetLayer
  - hash:
      serializedVersion: 2
      Hash: e060f19d4f072a9672e7cdac8b10cbf1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: vfxController
  - hash:
      serializedVersion: 2
      Hash: 920baae432b90c6d57c529040346e4e4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_Handling
  - hash:
      serializedVersion: 2
      Hash: d7acbd6ad1b391d94a914128f69d9325
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineVirtualCamera
  - hash:
      serializedVersion: 2
      Hash: 72a11fb010453981333c87f2fd79962c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: IgnoreCollisions
  - hash:
      serializedVersion: 2
      Hash: 80514a4aad8eb11350a6a1aa6025b6d4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WC15PRO
  - hash:
      serializedVersion: 2
      Hash: ef2a760dc8c0bb333363120518871762
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: fe9dd235f0d12e79744be18347cb632a
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiLoadSceneOnClick2
  - hash:
      serializedVersion: 2
      Hash: 46f8815aaefa696d509049c348ad6805
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: c3220f7afd647b11dae09800190b3898
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: a152f4bbfcc1c203222264e9469e6cd6
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: cdeabb5f77f9ec8f804242ddf7c2e306
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PluginManager
  - hash:
      serializedVersion: 2
      Hash: 703ddfbc436d7aee1117bc2332a27e8f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SciFiLightFlicker
  - hash:
      serializedVersion: 2
      Hash: c3c486a460102377f15a39f28cc66a34
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_TruckTrailer
  - hash:
      serializedVersion: 2
      Hash: ede167ceff9e5f1ce8b59212dcb0e526
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_MobileUIDrag
  - hash:
      serializedVersion: 2
      Hash: 6e93352154a8edfd62ff0a0bea9c005e
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder.Shapes
    className: ProBuilderShape
  - hash:
      serializedVersion: 2
      Hash: 3d02ff497fa903158a32fa89adf3810b
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePipeline
  - hash:
      serializedVersion: 2
      Hash: 2fa043fcdd0fd0fcbab6697e2bab1948
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_WheelCollider
  - hash:
      serializedVersion: 2
      Hash: c895614a6ad484093b565c53e1aca8ba
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_DashboardColors
  - hash:
      serializedVersion: 2
      Hash: fd5618094d1ec353bc50abe24baaba92
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineHardLookAt
  - hash:
      serializedVersion: 2
      Hash: 68b4edb102499623f0792d864422fda2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AdmobManager
  - hash:
      serializedVersion: 2
      Hash: 771ac73dfe7943d5313596f384ce5ca2
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering
    className: ProbeReferenceVolumeProfile
  - hash:
      serializedVersion: 2
      Hash: 3d5ae75421b07ac89c365f645f8584f2
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerWidget
  - hash:
      serializedVersion: 2
      Hash: 83ab2eee401291ff0aeaae7adfa8dcb4
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: ColorPalette
  - hash:
      serializedVersion: 2
      Hash: 84c295144a2fce36bf4afcb7fb81265f
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestRunner.Utils
    className: TestRunCallbackListener
  - hash:
      serializedVersion: 2
      Hash: df6eab954ec864d23f72bf3587a64b76
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: fb12fb3e27a19ef346c82620c984d706
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Tructorlinkcontroll
  - hash:
      serializedVersion: 2
      Hash: e334ba8ceb0eb18fbce4c1d9f35143df
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: InAppReview
  - hash:
      serializedVersion: 2
      Hash: 9e8898d1705d409ec4c988108072c8af
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: 68d5dec1c60c68c2dfc1f02723e0b735
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: bcb9b7a93c09c207eb313c02affe0d60
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: 4fe8fb4702199b29b5c8460a7bf81c5f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_MobileButtons
  - hash:
      serializedVersion: 2
      Hash: 8c5ab079dbc06dd660a4f3f0f25c099e
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: TestResultRendererCallback
  - hash:
      serializedVersion: 2
      Hash: 803e1991f819d19d6fcc5728dedcf475
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineInputProvider
  - hash:
      serializedVersion: 2
      Hash: 00f69a420ad82a22eddcc0f7fc344576
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Cubemovement
  - hash:
      serializedVersion: 2
      Hash: cf439fe8eb6bdf602e473453f4c73e36
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WPC
  - hash:
      serializedVersion: 2
      Hash: ed2094e12f032ec673dcde8d41537bd5
    assemblyName: GoogleMobileAds.Common.dll
    namespaceName: GoogleMobileAds.Common
    className: AppStateEventClient
  - hash:
      serializedVersion: 2
      Hash: df356894f021f8fa9b65818185a6de20
    assemblyName: GoogleMobileAds.Common.dll
    namespaceName: GoogleMobileAds.Common
    className: MobileAdsEventExecutor
  - hash:
      serializedVersion: 2
      Hash: d2ab0b299147f65834573ffd942ba546
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineTrack
  - hash:
      serializedVersion: 2
      Hash: 3529e0d1a6624a67d99f5a912b71a493
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AIBrakeZone
  - hash:
      serializedVersion: 2
      Hash: 18489aba93650f0e6326bf36f0c1b922
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: Cinemachine3rdPersonFollow
  - hash:
      serializedVersion: 2
      Hash: 4ace94bcd82a8892b2c26e2635218fa1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: FirebaseAnalyticsHandler
  - hash:
      serializedVersion: 2
      Hash: 671b4f3a131008e260da546dd4dec5e9
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_GroundMaterials
  - hash:
      serializedVersion: 2
      Hash: 220e801681412245aece5c38add147b8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SceneLoad
  - hash:
      serializedVersion: 2
      Hash: ea10b700f0d223aab296caf9b84ea302
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: BezierShape
  - hash:
      serializedVersion: 2
      Hash: cb9a815224f0f2005346ceaa3ad15318
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenButton
  - hash:
      serializedVersion: 2
      Hash: 4a3351e209ecf0eff6ab268e49d7f341
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: d3b117887d664b796e08789f8a61806f
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineMixingCamera
  - hash:
      serializedVersion: 2
      Hash: 52f7ee253aba09630d5f15e7fbc84038
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: MintegralRoas
  - hash:
      serializedVersion: 2
      Hash: 162fb5976245c757ff1e6ddd077ea595
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Skidmarks
  - hash:
      serializedVersion: 2
      Hash: 0905c3f75f9d18b61a8de31af05f89c1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CrashShredder
  - hash:
      serializedVersion: 2
      Hash: 5b73dfa7ead37d059de3031423565849
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Rotator
  - hash:
      serializedVersion: 2
      Hash: 2f9a56e247f7c20b2b3ac987fd06a151
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Core
  - hash:
      serializedVersion: 2
      Hash: 8e42f311d4e6bc1e4f95a4bed15253aa
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumField
  - hash:
      serializedVersion: 2
      Hash: 42f3d612fa6b74e88ddb99569024244d
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiRotation
  - hash:
      serializedVersion: 2
      Hash: a778f0b63dcf4d9df48972789d1e49a6
    assemblyName: Assembly-CSharp-firstpass.dll
    namespaceName: DG.Tweening
    className: DOTweenAnimation
  - hash:
      serializedVersion: 2
      Hash: dbf1dfa253e20667829f0817c5b6d684
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineSmoothPath
  - hash:
      serializedVersion: 2
      Hash: c6d7eebeace00b1615341882742e98af
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionImporterEditor
  - hash:
      serializedVersion: 2
      Hash: 60dac5e8b3c87c7e809d4947f05f2a83
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AIBrakeZonesContainer
  - hash:
      serializedVersion: 2
      Hash: dda852df7868289b03e5e09293f0c48a
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiProjectileScript
  - hash:
      serializedVersion: 2
      Hash: 056b914d1bb0fadfa2b3abeb1e769be5
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_InitialSettings
  - hash:
      serializedVersion: 2
      Hash: e059b9a4953958227fe05b6eb9999a6e
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerContainer
  - hash:
      serializedVersion: 2
      Hash: ebd0409eb7f02c30d237d55591f7b5a2
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePixelPerfect
  - hash:
      serializedVersion: 2
      Hash: bd4016a2045f75e945497f07c5b80503
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayerQuitHandler
  - hash:
      serializedVersion: 2
      Hash: 51f10e7961758dd62a1ec2f7863be045
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenComponent
  - hash:
      serializedVersion: 2
      Hash: bf75fa8cb4f20aa58bc51646b041fb2b
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenSettings
  - hash:
      serializedVersion: 2
      Hash: f87a82bdc1582c5f70b70ca85233d7f2
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: 3e24fbf7a2b4276bf8f378419a70065b
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVBox
  - hash:
      serializedVersion: 2
      Hash: 535f226b42413a750ae27d52859dc953
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: MainMenu
  - hash:
      serializedVersion: 2
      Hash: 4fe30b079bc8419c58089caa901d9184
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFramingTransposer
  - hash:
      serializedVersion: 2
      Hash: ba669a82de932d6eb54004566e0bb9b6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: FireBaseInitializer
  - hash:
      serializedVersion: 2
      Hash: acbb5b301203f6c0e9a4e0429d9f93dc
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Useless
  - hash:
      serializedVersion: 2
      Hash: b309f0b8b517f50a2601fbb7a2f9f705
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: 769abea9aa79f49a2ee1a4facd0a61d5
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: e9398ae5eb79edab34f47e4c6b21b45d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CrashPress
  - hash:
      serializedVersion: 2
      Hash: b76208a5482dbb774fee876b1db983dd
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UI_MobileDrag
  - hash:
      serializedVersion: 2
      Hash: 353508f5a25c03e0205c4b6da3dfda91
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggleHistory
  - hash:
      serializedVersion: 2
      Hash: 2c7335239058ee7106af5c639248d4a2
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputActionAsset
  - hash:
      serializedVersion: 2
      Hash: 9804bf9b4e94d9e0e5a4fb43422b2d07
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WPC_Waypoint
  - hash:
      serializedVersion: 2
      Hash: f87d793e9b38c2c31438dbe67b980464
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_Siren
  - hash:
      serializedVersion: 2
      Hash: 3c3e5980c88a14a86741564a7fd08aaa
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 614c1a43e0f3bbf140358b35cf9aa13a
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineIndependentImpulseListener
  - hash:
      serializedVersion: 2
      Hash: f9ae240b9b17ce4bb73a247498c010d1
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: PolyShape
  - hash:
      serializedVersion: 2
      Hash: 238ea53fa78c4727fec017c6481aaca0
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LoadingLogic
  - hash:
      serializedVersion: 2
      Hash: 4b9ef41da59c739e39a0e1cd34f1ddd8
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 2bd783b5b85429caff317fd8ece19300
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: f27dbaaa7f025997ec9fca2ef101f9cc
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 15f833455ba334a3b6ca548b31eac8f6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UIDashboardButton
  - hash:
      serializedVersion: 2
      Hash: 81edb5e973a40cf9ff08f4c26f68a9a0
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineGroupComposer
  - hash:
      serializedVersion: 2
      Hash: 5102ffa1370d4d486db510e3c2fc3e83
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_ColorPickerBySliders
  - hash:
      serializedVersion: 2
      Hash: 302b4df1c734d4959a9573a82662c66c
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPersistentCanvas
  - hash:
      serializedVersion: 2
      Hash: 6f4c38ee1b06ec5021a35b43d6edd1fd
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputStateWindow
  - hash:
      serializedVersion: 2
      Hash: 5db90ba1538b8e285d9a4cf769c2f07e
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: TrackedDeviceRaycaster
  - hash:
      serializedVersion: 2
      Hash: f612f6956389e86994c43050be53d1d0
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_APIExample
  - hash:
      serializedVersion: 2
      Hash: 21f4f1fec12583058b4c5843abeefdbb
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: 6a9516770b070951680a95390b9098a5
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: 4a0457e2e8e1314798dc96306d452939
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: wheelAi
  - hash:
      serializedVersion: 2
      Hash: 6fea02e0a8b3cb3b15440b3ff3fce29b
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: 61ecff97faeda7a425442ed5cff86ee1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_Brake
  - hash:
      serializedVersion: 2
      Hash: 82a62cf22b046020275a0176dca812e2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Waypoint
  - hash:
      serializedVersion: 2
      Hash: 8088a6e6266ae3e4922a5d6c505f6a7e
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineTouchInputMapper
  - hash:
      serializedVersion: 2
      Hash: b8035b84df1d28a4c0ee0b0a41440520
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: MaxMediation
  - hash:
      serializedVersion: 2
      Hash: 621258cbf131e999213abbde65c03985
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: dc3d5271f3271de34751f74fb838e919
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: Cinemachine3rdPersonAim
  - hash:
      serializedVersion: 2
      Hash: 03116c959005bf565efe4a31f8be265e
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: f8b9e92afe401bbed668543d4da3751f
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: 
    className: SceneRenderPipeline
  - hash:
      serializedVersion: 2
      Hash: 9b20bb37fe547a38e67e865c7074ed8f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: 3d58a5fe1892e5295f1cf87c032ae18c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WaypointsHolder
  - hash:
      serializedVersion: 2
      Hash: 8cb73aaac1c2c1ee9b4978ca0df73efb
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CustomizationDemo
  - hash:
      serializedVersion: 2
      Hash: 68532aae19bfea276c5777ea5ee81f3e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AIWaypointsContainer
  - hash:
      serializedVersion: 2
      Hash: b187c202ddcafd4c52781696e213aad2
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineHardLockToTarget
  - hash:
      serializedVersion: 2
      Hash: 1da9ff3f79c8e23199e2b4eb6abf9568
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_Paint
  - hash:
      serializedVersion: 2
      Hash: 1d1e664e0d8337c7cb2af559a14457c6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Customization
  - hash:
      serializedVersion: 2
      Hash: b51c2cb4416f46fd59809ff697c7b6d3
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AlertMessage
  - hash:
      serializedVersion: 2
      Hash: 5d77ddbaa8a9ea3eb8abf85398c27d56
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: GroupWeightManipulator
  - hash:
      serializedVersion: 2
      Hash: 83f39d6aef5423b963dbd7666758231b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Waypoint
  - hash:
      serializedVersion: 2
      Hash: 1808bedf05234f59aad67504dc589413
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTargetGroup
  - hash:
      serializedVersion: 2
      Hash: 9ed9e421d17369f1952e18cc1edadbcb
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: 7fd51685af477c236128295edc0b15e0
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumHistory
  - hash:
      serializedVersion: 2
      Hash: 5f14f78bc41036bae00594751d994b33
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_LevelLoader
  - hash:
      serializedVersion: 2
      Hash: 3acbd07fe034e5e58071c1e2d114ede4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: IAP_Controller
  - hash:
      serializedVersion: 2
      Hash: 1534b4a602edfd14c31fbe82c417b786
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_LOD
  - hash:
      serializedVersion: 2
      Hash: 9cef675c00efb98bcf527a1d4ff9fed8
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenStick
  - hash:
      serializedVersion: 2
      Hash: 6e431357dfdc2e425029a004e2cb3f65
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: TrollycubepickSystem
  - hash:
      serializedVersion: 2
      Hash: d8b0ea311ade16e0ef339de287a68d43
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Exhaust
  - hash:
      serializedVersion: 2
      Hash: 0653a3a0d81ae6a68c5e38d498a6fe49
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UI_Spoiler
  - hash:
      serializedVersion: 2
      Hash: 7219ebb0b4a64fd19236e423b34046f6
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: cb6e436fad8ad205794db196b22d97a2
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineCollisionImpulseSource
  - hash:
      serializedVersion: 2
      Hash: 772da381ab107246ab796edb479ef9e9
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_UpgradeManager
  - hash:
      serializedVersion: 2
      Hash: 8e35fe2655309f23b1e0b22231b6ab57
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePOV
  - hash:
      serializedVersion: 2
      Hash: e2eb090b2d42285e9e7ab90254368cfa
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UI_Wheel
  - hash:
      serializedVersion: 2
      Hash: 265c0392a7bafc0e300e5c5f6c3f9aad
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_DashboardObjects
  - hash:
      serializedVersion: 2
      Hash: 59e5743e7ec6da179942737a5217fb2c
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDeviceDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 9cd0152ee05f44c7a0421afc21da4418
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputSystemObject
  - hash:
      serializedVersion: 2
      Hash: 4746018e4e190bd2a86e7603d97607cb
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: CameraSwitcher
  - hash:
      serializedVersion: 2
      Hash: cda94de9b147df6d528100b4951eff3f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: targetcam
  - hash:
      serializedVersion: 2
      Hash: 82aafca91c0718ffcf931c70fdd09fcf
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: FirebaseRemoteConfigHandler
  - hash:
      serializedVersion: 2
      Hash: 9dc87b1d1b125f7d5b1cf7e2e9732e5c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: UnityAdsManager
  - hash:
      serializedVersion: 2
      Hash: 419d187f4a6de5833aaf3838ac2a3e36
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_SceneManager
  - hash:
      serializedVersion: 2
      Hash: 999c449ad95117c75b6263e062d2e58e
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b9484dc0a83a5260f8b27b6c3efe7f6b
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineComposer
  - hash:
      serializedVersion: 2
      Hash: 5a027759c00a61ba61a56a66e14e887e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SciFiBeamScript
  - hash:
      serializedVersion: 2
      Hash: d7b947190b978e5a6fdf2073ec7aed53
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AttPermissionRequest
  - hash:
      serializedVersion: 2
      Hash: 9f8efa23f04cda3535b08edc0ec458a0
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: ProBuilderMesh
  - hash:
      serializedVersion: 2
      Hash: 78fa8c70bef8c6d96ce76e6d539a1987
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 1bf7a0a2c93bd37dcb73a76d31519170
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: RemoteInputPlayerConnection
  - hash:
      serializedVersion: 2
      Hash: 056e06b22d42feb19ff3807a35d6efa1
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBlendListCamera
  - hash:
      serializedVersion: 2
      Hash: 43cc9e552cbae588a31fd8f1fcf75d25
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiLightFade
  - hash:
      serializedVersion: 2
      Hash: 067f6ba9d6063b307f995a8e9a978584
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SMGGameManager
  - hash:
      serializedVersion: 2
      Hash: 480e2a56c81cbe122ffc7c3fe7336a07
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 8205ccb80293b69bb9fd3d75144cd92c
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: VolumeProfile
  - hash:
      serializedVersion: 2
      Hash: b3b53420f0d10576e33674d9cccd900c
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: BaseInputOverride
  - hash:
      serializedVersion: 2
      Hash: e0890e75ccd62f2157ee5b6bce528699
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: ColliderBehaviour
  - hash:
      serializedVersion: 2
      Hash: 70c1b21b00314bcc18820019e550c423
    assemblyName: GoogleMobileAds.Editor.dll
    namespaceName: GoogleMobileAds.Editor
    className: GoogleMobileAdsSettings
  - hash:
      serializedVersion: 2
      Hash: 130a5d5758ac32316bedf3f8c758b7f3
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CarSelectionExample
  - hash:
      serializedVersion: 2
      Hash: ab515f173cfc0d80084a065efe56a44f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Caliper
  - hash:
      serializedVersion: 2
      Hash: a87e99b75dfaa0c6f8e7646e7bca3ca8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_FOVForCinematicCamera
  - hash:
      serializedVersion: 2
      Hash: e4f8e992bd8551aef082a903aad96f64
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePath
  - hash:
      serializedVersion: 2
      Hash: 89b081f67b76cf30c49d88a2ca6967a7
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine.PostFX
    className: CinemachineVolumeSettings
  - hash:
      serializedVersion: 2
      Hash: e688ed407bbfb11c21058187316ed44e
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: f64cef5e050a62f416f4a5e6d49d6fe5
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: PreferenceDictionary
  - hash:
      serializedVersion: 2
      Hash: 904c4a057fa1d1128e86dca533841d47
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_WheelManager
  - hash:
      serializedVersion: 2
      Hash: 10b44983f0a7578909bb4d68fa771902
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 49c8a1de997b38f462023bb8d7442419
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Tractorsense
  - hash:
      serializedVersion: 2
      Hash: 57a255c0ca22daf718d55ec02240ce8b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SettingAD
  - hash:
      serializedVersion: 2
      Hash: 3e8a67702e7670d23688421f8f8f61a8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: InAppUpdate
  - hash:
      serializedVersion: 2
      Hash: b46a27dc8fe542927657974694fc29aa
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 87a477c997c2f91d9c0b88c63927e543
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiLoopScript
  - hash:
      serializedVersion: 2
      Hash: 2380288dfe403a7f4b2182b575687cb8
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 3f77690e5bd526a9668b24016fc5c0bd
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_Spoiler
  - hash:
      serializedVersion: 2
      Hash: c5805f619ce05cb26c6933a1050dc799
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector3
  - hash:
      serializedVersion: 2
      Hash: b34c61c6f1509ce111a79002fc10e5a3
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiButtonScript
  - hash:
      serializedVersion: 2
      Hash: e78f6985cfa9077afa7bb6c3ee81529f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: edf106070be7ba652b75a88b6a334263
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectToggle
  - hash:
      serializedVersion: 2
      Hash: 9862c0808438e0279c49e5f500d63cb2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CustomizationApplier
  - hash:
      serializedVersion: 2
      Hash: d95e47c7038bd218cf8ccad141e1adcf
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: FirebaseAnalytics
  - hash:
      serializedVersion: 2
      Hash: 5f1b631f94952e4a3139021660a234d4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CustomizerExample
  - hash:
      serializedVersion: 2
      Hash: c95041e8eb90bc36ff802d71287e4725
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Teleporter
  - hash:
      serializedVersion: 2
      Hash: ff061b5c555cc59cb164e14b959dc387
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_DashboardInputs
  - hash:
      serializedVersion: 2
      Hash: e8df42a528d079875a4c898060b86414
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UIController
  - hash:
      serializedVersion: 2
      Hash: 0ad97cee779e6fd41aeffc0841c85850
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering
    className: ProbeVolumeAsset
  - hash:
      serializedVersion: 2
      Hash: 5d2eca6f6cfbab49b6fa5ee0d302f2f0
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: 43eb77ddd4b0f9c453f66eb0d9daee69
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerUIntField
  - hash:
      serializedVersion: 2
      Hash: 6ae7a7e2440625ba72f28586b3aed308
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFloatField
  - hash:
      serializedVersion: 2
      Hash: eaa9973d190f4fdb98db68ee59821eeb
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Birdtriiger
  - hash:
      serializedVersion: 2
      Hash: 2be854dee95084c1537e4d6bb482ca5d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineDollyCart
  - hash:
      serializedVersion: 2
      Hash: bfcc61e961dee25cef9c7d7ba35b4fe4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SliderRelease
  - hash:
      serializedVersion: 2
      Hash: 43b9d383cd12a2b17594e5b47f5f5644
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GameOptimizer
  - hash:
      serializedVersion: 2
      Hash: ce29c624b4e215f90c5fc5df651c5363
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: f62e218c9d6617bc82239d7acb0b9437
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPanel
  - hash:
      serializedVersion: 2
      Hash: 73db20b19a7ddd3e8a0823ce974766a1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_SuspensionArm
  - hash:
      serializedVersion: 2
      Hash: cd72280b7aeae3ad3c5c84174bad3936
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LevelLockImages
  - hash:
      serializedVersion: 2
      Hash: cdd1cd2371920ac0808450139031539d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_ChangableWheels
  - hash:
      serializedVersion: 2
      Hash: 5b62a6e927b562a7958dde4cd7cae6a1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WaypointWalker
  - hash:
      serializedVersion: 2
      Hash: 761962d9b61a6c489f533d5d6b307813
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: VolumeComponent
  - hash:
      serializedVersion: 2
      Hash: 39230f691ce4f712b5eb4e70922e506a
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionEditorWindow
  - hash:
      serializedVersion: 2
      Hash: 006e9a6d9526e39e04ab3b71ae3ac7b8
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine.PostFX
    className: CinemachinePostProcessing
  - hash:
      serializedVersion: 2
      Hash: f0089fe7211502a04bee2a61606b37ad
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputManagerEditor
  - hash:
      serializedVersion: 2
      Hash: 1544cedbd70586f52825fae58f995a6d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineOrbitalTransposer
  - hash:
      serializedVersion: 2
      Hash: f2cf443dfe4f7bb39cbe44eaca3c5086
    assemblyName: DemiLib.dll
    namespaceName: DG.DemiLib.External
    className: DeHierarchyComponent
  - hash:
      serializedVersion: 2
      Hash: 70f2d4180e0c617c181af9225d783eb4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: starFxControllerMy
  - hash:
      serializedVersion: 2
      Hash: dc7aabbde86f4d371290927e4762a9ed
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: UIFoldout
  - hash:
      serializedVersion: 2
      Hash: 5a17edc0c9cded335dbde56ff6c83b5d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: brake
  - hash:
      serializedVersion: 2
      Hash: eb17a75a770e21172667a14ff95a2247
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PauseAd
  - hash:
      serializedVersion: 2
      Hash: 74126a587e1d067b1fa7f63be3c11891
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_RepairStation
  - hash:
      serializedVersion: 2
      Hash: d78158674167b3da1fad56b0208ae288
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBrain
  - hash:
      serializedVersion: 2
      Hash: 940f185a7114db76938f52f483172434
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: ObjectRangeManager
  - hash:
      serializedVersion: 2
      Hash: e8864c11d09292c85014396a9a6526af
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SenseTraffic
  - hash:
      serializedVersion: 2
      Hash: 15e2174438103ba23087ee5cc6c0e351
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineConfiner
  - hash:
      serializedVersion: 2
      Hash: 0e321e09912fc665b76788dc31b0abfe
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineSameAsFollowTarget
  - hash:
      serializedVersion: 2
      Hash: e1bab3aa7e65be2fce43cc7d84ef1431
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UI_Upgrade
  - hash:
      serializedVersion: 2
      Hash: ba9b3062380d2ae0c7a454e22bbd1903
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UI_Color
  - hash:
      serializedVersion: 2
      Hash: 5aa4fc9c586bfc1f7d56918fab9e667e
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineConfiner2D
  - hash:
      serializedVersion: 2
      Hash: d13609b6d89635cbc29df61da760371b
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineClearShot
  - hash:
      serializedVersion: 2
      Hash: 0ab78f031b53dd5d55f964f3cd1c223e
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: 01ab0d8cf6662bc9c82b0f8fd96c3f53
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValue
  - hash:
      serializedVersion: 2
      Hash: ed034cff83e1d608e66691a37102630c
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: 448673bdf08983813306c834f27377f3
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_ShadowRotConst
  - hash:
      serializedVersion: 2
      Hash: 7b85f3c2acaad1d181ece00c595c89fc
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AIO
  - hash:
      serializedVersion: 2
      Hash: 4bcac0545b8ecc4aa77d06b222ce018e
    assemblyName: Assembly-CSharp.dll
    namespaceName: VisCircle
    className: PowerUpAnimation
  - hash:
      serializedVersion: 2
      Hash: a5e2bf54433abebd77cadf6fd7975d16
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LivingOcean
  - hash:
      serializedVersion: 2
      Hash: bb625ca5427f419e16ddc7db09bca863
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_InfoLabel
  - hash:
      serializedVersion: 2
      Hash: 5036a44ed8fda752a85add5acc8996f6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_HoodCamera
  - hash:
      serializedVersion: 2
      Hash: de267d5b93171be5ef4488b9cf716b92
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_InputManager
  - hash:
      serializedVersion: 2
      Hash: 6dda83aced86b730317e2d2fa48f2881
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerHBox
  - hash:
      serializedVersion: 2
      Hash: f8691b71a3f477369996804f54ed352d
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIntField
  - hash:
      serializedVersion: 2
      Hash: c457864c18c9ac0b151a723e4fae4ea4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: BannerAD
  - hash:
      serializedVersion: 2
      Hash: f3c786a638a8ef1a862495ece8a0acb6
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector4
  - hash:
      serializedVersion: 2
      Hash: 56484752efc49aaf5720e7c166075e4d
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: Entity
  - hash:
      serializedVersion: 2
      Hash: 72b2957d418a489374d3a07fceca9367
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Records
  - hash:
      serializedVersion: 2
      Hash: 60687eab5e4f654df0b45918d71ebf99
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectFloatField
  - hash:
      serializedVersion: 2
      Hash: d075cb06fc54aae9ed273f948cc3a2f5
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerBitField
  - hash:
      serializedVersion: 2
      Hash: 551fc332ca1d34a147749dd8966d5bc4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LightAnimation
  - hash:
      serializedVersion: 2
      Hash: cc3b5ae79c47866d69978c15d9a5075b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AICarController
  - hash:
      serializedVersion: 2
      Hash: 9690f760294698c29b1bee7d9b7bd452
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: a273edb1f10a7eafb870721bc4bddaca
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: 0ffacc03a651624ad5bb72bc787275f7
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: 6a92b2e660dc4d1eda27032865cbf385
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Spawner
  - hash:
      serializedVersion: 2
      Hash: ceb4961d39f347996fda1404ac174d05
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: cde977fb11d1f6ae6783d1b69745d58a
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineImpulseSource
  - hash:
      serializedVersion: 2
      Hash: 6691dd18200d133e8dbae24b6281d1cf
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools
    className: BeforeAfterTestCommandState
  - hash:
      serializedVersion: 2
      Hash: 39921e4b272e731c762bfa7230c3b636
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GameAnalyticsInitializer
  - hash:
      serializedVersion: 2
      Hash: b47fe65fd0354256d650ff167b8a1e66
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GrassCubeprocessormachine
  - hash:
      serializedVersion: 2
      Hash: dce5b66613be781b5459af45db50e03b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RewardReadyCheck
  - hash:
      serializedVersion: 2
      Hash: 8da3bad80dac0f757130dbaa9ceae075
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: DebugUpdater
  - hash:
      serializedVersion: 2
      Hash: c1933688180ee845ee0ae3bc5e3971e6
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineImpulseListener
  - hash:
      serializedVersion: 2
      Hash: 1ebc9667141d23d69e27d98cbf599a47
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerCanvas
  - hash:
      serializedVersion: 2
      Hash: 9fc14b809d7da3f0438dad852aaea6b0
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WeatherSystem
  - hash:
      serializedVersion: 2
      Hash: 0616b13f6c62e299153c6a68aaaec148
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.EnhancedTouch
    className: TouchSimulation
  - hash:
      serializedVersion: 2
      Hash: 5718db4fbfa13593401516b78a98b6e2
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputEditor
  - hash:
      serializedVersion: 2
      Hash: c722b438a375a028ec8b5c5f8c33cbce
    assemblyName: DOTweenPro.dll
    namespaceName: DG.Tweening
    className: DOTweenPath
  - hash:
      serializedVersion: 2
      Hash: 5e8d44b28cc321823122fb9b9f6cf30e
    assemblyName: DOTweenPro.dll
    namespaceName: DG.Tweening
    className: DOTweenVisualManager
  - hash:
      serializedVersion: 2
      Hash: 752f100eaca2a00eb5e13b1e2745ba1d
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: InputSystemUIInputModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 33351225ad07d388c6f33d76ccfe9ba6
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: c1f3d85aacdd221eebc8efdca44b1e74
    assemblyName: Firebase.Platform.dll
    namespaceName: Firebase.Platform
    className: FirebaseMonoBehaviour
  - hash:
      serializedVersion: 2
      Hash: e4c58e3b6491d44e8cd0a7a86c4b7014
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: TriggerBehaviour
  - hash:
      serializedVersion: 2
      Hash: d2368e02f10c90876ea326e2e6a839d4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CrashHammer
  - hash:
      serializedVersion: 2
      Hash: bd220c51cc2d0b552b30d57d97e359c5
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerColor
  - hash:
      serializedVersion: 2
      Hash: de6911de94590817a2c9b1375e4e041e
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionAssetEditor
  - hash:
      serializedVersion: 2
      Hash: 17df78ad7cc29bd75f94172342133611
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WeatherManager
  - hash:
      serializedVersion: 2
      Hash: 50096ec6de83fcf81331535bbc8e7157
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: DistanceFromPlayer
  - hash:
      serializedVersion: 2
      Hash: b906acd878baea9f22b8e04ea1ee81a1
    assemblyName: Unity.InputSystem.dll
    namespaceName: 
    className: DownloadableSample
  - hash:
      serializedVersion: 2
      Hash: 2fbd6c28e1a03872d56abf53df14d753
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Animationtexture
  - hash:
      serializedVersion: 2
      Hash: c775193bec472a91767f76dccfe603e7
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: d12a5d3cc62a1922e8894604591471af
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: ButtonChildToggle
  - hash:
      serializedVersion: 2
      Hash: 076ef579adc96603271dcafdcd3f0605
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerRow
  - hash:
      serializedVersion: 2
      Hash: fd005ae1f5a717637a43c3076bad5085
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputActionReference
  - hash:
      serializedVersion: 2
      Hash: 69c17fe89a5bc026c3929ded1a505ee3
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Recorder
  - hash:
      serializedVersion: 2
      Hash: 5dd80437a0058459586b1a3f7117ab60
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: MeshCombiner
  - hash:
      serializedVersion: 2
      Hash: 01ed6b5a5b180949d99e82a8b99cbde3
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: LensFlareDataSRP
  - hash:
      serializedVersion: 2
      Hash: 678e1b1d2ead89a22d454421d06258e4
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 60848472e362199ae5eac55c8892dcbe
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 2f1f3f1d4e3094c2995f034fec18ca91
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering
    className: ProbeVolume
  - hash:
      serializedVersion: 2
      Hash: 693a704080e97409a6aad060aa1be540
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: wheelrotator
  - hash:
      serializedVersion: 2
      Hash: 3d852ed0e8c9b38616d90b775f92a20e
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFixedSignal
  - hash:
      serializedVersion: 2
      Hash: 07e76c7d202c7975fd60819bf4b1eaf7
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Light
  - hash:
      serializedVersion: 2
      Hash: d301a4a7f75108c30e3d5c25aceea2d8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: COMPAD
  - hash:
      serializedVersion: 2
      Hash: 111d589e9cb42750fdacf7d6d13c61a5
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: NoiseSettings
  - hash:
      serializedVersion: 2
      Hash: 466329aa1430edd5d9cc35262ee17096
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: f9786a3dca6228a6818ec4a0134d9212
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayModeRunnerCallback
  - hash:
      serializedVersion: 2
      Hash: f387854b2eced4b901f4e63e7cbd7246
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_DemoVehicles
  - hash:
      serializedVersion: 2
      Hash: 805c62fb3df38a31dfa854c4f1d54dfa
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: 8fa503d8daa52f116c88f5561a646847
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_SirenManager
  - hash:
      serializedVersion: 2
      Hash: c05e73c4535140d8b52eabb410ae089c
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: c1d888aba02eab4d72c41701c96e6cee
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UISteeringWheelController
  - hash:
      serializedVersion: 2
      Hash: d71d9c16bdf050b8c33e1f3664cf8d1b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: mode2lock
  - hash:
      serializedVersion: 2
      Hash: 459334ca57e17e388588a5fb8a5d4c44
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTransposer
  - hash:
      serializedVersion: 2
      Hash: 421bffbee2ac47b81d60c69e2a3d5ac7
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: cronspray
  - hash:
      serializedVersion: 2
      Hash: dac1033fcebddf6e553faf4cd30cc51a
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: ScrollUV
  - hash:
      serializedVersion: 2
      Hash: 9fa5630af1eae1eddfb3d63d5fc08f3f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: DontDestroyOnLoad
  - hash:
      serializedVersion: 2
      Hash: 03537dd42b6fc73a601799c77ceb0b9c
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: RemoteTestResultSender
  - hash:
      serializedVersion: 2
      Hash: 453c286dc9c9c693626ccfeab876ea44
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: 0bd403cafe5bfb0c5bbda285dc190598
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineShot
  - hash:
      serializedVersion: 2
      Hash: b08d1d91c083f76044c6b090597bf7e5
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputSettings
  - hash:
      serializedVersion: 2
      Hash: 9a81ed6e856550f8086a6614cb8184ae
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CinematicCamera
  - hash:
      serializedVersion: 2
      Hash: 74101b1189e7f911aef3b5ca95b2db19
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: 3876765ccd20be5147b73524d55b66ef
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_ShowroomCamera
  - hash:
      serializedVersion: 2
      Hash: 551e99ad30b702f2eb620c73d347b985
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WPC_Editor
  - hash:
      serializedVersion: 2
      Hash: 933cf14e162bdaa406cbe03b3457a52d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LoadingAD
  - hash:
      serializedVersion: 2
      Hash: 32e43b20e01fab16051102e3b3053943
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_FuelStation
  - hash:
      serializedVersion: 2
      Hash: 9fec09927a72e8b48d04a59cd56a880c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: starFxController
  - hash:
      serializedVersion: 2
      Hash: 020d025a5a655dc2fbcbd87d350ed007
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Telemetry
  - hash:
      serializedVersion: 2
      Hash: 55ac714ecd14f17465c0e21a680e5b69
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: d3669877414759386a730a648c50f255
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTrackedDolly
  - hash:
      serializedVersion: 2
      Hash: 8cc55f419e2c40357c3b217ea762cd7c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CameraCarSelection
  - hash:
      serializedVersion: 2
      Hash: 6491ae22a68b6e3614fa6c982a527bfc
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine
    className: LightAnchor
  - hash:
      serializedVersion: 2
      Hash: 281111590cca9ff7b2b5cbc973d0ba1a
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: DemoController
  - hash:
      serializedVersion: 2
      Hash: 1ff76b7a82b2b0fd6d84eed67370a649
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: fbd3135687c248dda5118b8516726e6c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_TrailerAttachPoint
  - hash:
      serializedVersion: 2
      Hash: 2c53d8453894e67a944103c05c2373ac
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Settings
  - hash:
      serializedVersion: 2
      Hash: b54f4203bc5cb280d0758c27343b40a8
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: InputSystemUIInputModule
  - hash:
      serializedVersion: 2
      Hash: 94c43ba6793bb0742e16f89b83004190
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_PaintManager
  - hash:
      serializedVersion: 2
      Hash: 74d10f1ce9336a3e675e041537da79e0
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineStoryboard
  - hash:
      serializedVersion: 2
      Hash: 99e345688e823804d80e19d868be9f49
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_WheelCamera
  - hash:
      serializedVersion: 2
      Hash: 4ea7de30cb668cc5a4a70a803205db1d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineStateDrivenCamera
  - hash:
      serializedVersion: 2
      Hash: 16034359a335a1a9a2f4ccec24905f90
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_SpoilerManager
  - hash:
      serializedVersion: 2
      Hash: b2f38a35d6d6d225625e7c825760210f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Testingads
  - hash:
      serializedVersion: 2
      Hash: e9bc04f439c5f185c9ebd236fa9d990b
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiDragMouseOrbit
  - hash:
      serializedVersion: 2
      Hash: f84f4f411f591b4dcbabc8e99e1bd330
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UIDashboardDisplay
  - hash:
      serializedVersion: 2
      Hash: 8e4f99c1f9019b681acd5188f5fcfeb4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UIJoystick
  - hash:
      serializedVersion: 2
      Hash: f80ec5faf2372bddd6907d63028ba945
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: TimerSeconds
  - hash:
      serializedVersion: 2
      Hash: c795458f5fdb20c84a5f4224b195692a
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: MultiplayerEventSystem
  - hash:
      serializedVersion: 2
      Hash: 1221243affeb7b51593399d574153062
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.XR
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: d6fedb9690d964bfaaab31bf73a7a2a6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UI_Siren
  - hash:
      serializedVersion: 2
      Hash: cbdd15c00e527b842c7e8874f25cbfd9
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WaypointMover
  - hash:
      serializedVersion: 2
      Hash: ddb4481606a34c99382b812c001817a2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Fail
  - hash:
      serializedVersion: 2
      Hash: c3e81a21a5824449dd9e98a97b177bfd
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerButton
  - hash:
      serializedVersion: 2
      Hash: c3b807cff5e87026c412c5328a434827
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CustomizationManager
  - hash:
      serializedVersion: 2
      Hash: c3409785540d7aefc3e15014e19268a3
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiFireProjectile
  - hash:
      serializedVersion: 2
      Hash: cd5e4b671a6f3e47fe319d5913a5513e
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: cc6ba68260ea5e6304249d5ea33f06e5
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SaveButtonController
  - hash:
      serializedVersion: 2
      Hash: 6b3d159ebab64a2fd2ec2c2a3f666869
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Camera
  - hash:
      serializedVersion: 2
      Hash: 506a03d4644966d4cbbad2072683b26e
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: Volume
  - hash:
      serializedVersion: 2
      Hash: a49f31917d44a804be83a7e958ff7709
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineRecomposer
  - hash:
      serializedVersion: 2
      Hash: 31d0d66962989f5f311b2a1d2ed736c4
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBasicMultiChannelPerlin
  - hash:
      serializedVersion: 2
      Hash: 4bc979e0b3568ef5ba629fb284c47424
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFoldout
  - hash:
      serializedVersion: 2
      Hash: 594a9bf252b579ddcda1f1ab94e15608
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AdsController
  - hash:
      serializedVersion: 2
      Hash: af3c7bb5f1a72685c8bff2aeb7ca8d1b
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: 087270dae29f8e16c4d04b8a5007b275
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: manger2
  - hash:
      serializedVersion: 2
      Hash: 1e4094f9889dbb405020b5a600d7717f
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: PlayerInputManager
  - hash:
      serializedVersion: 2
      Hash: f332c8831039fd86633f45667842947f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Demo
  - hash:
      serializedVersion: 2
      Hash: 75f58323157f9b4e13d2528e58640a72
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFollowZoom
  - hash:
      serializedVersion: 2
      Hash: 829a3f3825b2b9d72365ef92d8177711
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 5f68dcc0f65b614af89d71a92353a00d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_DetachablePart
  - hash:
      serializedVersion: 2
      Hash: 0528873fc974e4c59f837d1d4ecf0115
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: ImageArrayDisplay
  - hash:
      serializedVersion: 2
      Hash: f396d61d546ad997f425780e41dbe981
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_VehicleUpgrade_Engine
  - hash:
      serializedVersion: 2
      Hash: f91b1270ec14fd46b12f8fe28b3f1a39
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_SpeedLimiter
  - hash:
      serializedVersion: 2
      Hash: 01584e5b6d82fd214425fbc0450fff17
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: startscene
  - hash:
      serializedVersion: 2
      Hash: 4399236925123c492081e93db6a8606b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WalkerCollisionHandler
  - hash:
      serializedVersion: 2
      Hash: 2eedad2de7a2cdac7488349860a0dae2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Mirror
  - hash:
      serializedVersion: 2
      Hash: ce5e181782707e5426b33811583454a6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CarControllerV3
  platform: 13
  scenePathNames:
  - Assets/Game scene/Farming mod.unity
  playerPath: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator
    Cargo/Apk/Tractor simulator FARMING MOD 3.apk
