﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void Microsoft.CodeAnalysis.EmbeddedAttribute::.ctor()
extern void EmbeddedAttribute__ctor_m3FF623BFEE0DB7F6FD99E1EAFE20C5581224D314 (void);
// 0x00000002 System.Void System.Runtime.CompilerServices.IsReadOnlyAttribute::.ctor()
extern void IsReadOnlyAttribute__ctor_m7C0ECD764E09B041BA7629AC5C020D9972AC8697 (void);
// 0x00000003 System.String UnityEngine.XR.InputFeatureUsage::get_name()
extern void InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085 (void);
// 0x00000004 UnityEngine.XR.InputFeatureType UnityEngine.XR.InputFeatureUsage::get_internalType()
extern void InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810 (void);
// 0x00000005 System.Boolean UnityEngine.XR.InputFeatureUsage::Equals(System.Object)
extern void InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0 (void);
// 0x00000006 System.Boolean UnityEngine.XR.InputFeatureUsage::Equals(UnityEngine.XR.InputFeatureUsage)
extern void InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D (void);
// 0x00000007 System.Int32 UnityEngine.XR.InputFeatureUsage::GetHashCode()
extern void InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D (void);
// 0x00000008 System.Void UnityEngine.XR.InputDevice::.ctor(System.UInt64)
extern void InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34 (void);
// 0x00000009 System.UInt64 UnityEngine.XR.InputDevice::get_deviceId()
extern void InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19 (void);
// 0x0000000A System.Boolean UnityEngine.XR.InputDevice::Equals(System.Object)
extern void InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC (void);
// 0x0000000B System.Boolean UnityEngine.XR.InputDevice::Equals(UnityEngine.XR.InputDevice)
extern void InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC (void);
// 0x0000000C System.Int32 UnityEngine.XR.InputDevice::GetHashCode()
extern void InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D (void);
// 0x0000000D System.UInt64 UnityEngine.XR.Hand::get_deviceId()
extern void Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A (void);
// 0x0000000E System.UInt32 UnityEngine.XR.Hand::get_featureIndex()
extern void Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775 (void);
// 0x0000000F System.Boolean UnityEngine.XR.Hand::Equals(System.Object)
extern void Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06 (void);
// 0x00000010 System.Boolean UnityEngine.XR.Hand::Equals(UnityEngine.XR.Hand)
extern void Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864 (void);
// 0x00000011 System.Int32 UnityEngine.XR.Hand::GetHashCode()
extern void Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE (void);
// 0x00000012 System.UInt64 UnityEngine.XR.Eyes::get_deviceId()
extern void Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD (void);
// 0x00000013 System.UInt32 UnityEngine.XR.Eyes::get_featureIndex()
extern void Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8 (void);
// 0x00000014 System.Boolean UnityEngine.XR.Eyes::Equals(System.Object)
extern void Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D (void);
// 0x00000015 System.Boolean UnityEngine.XR.Eyes::Equals(UnityEngine.XR.Eyes)
extern void Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969 (void);
// 0x00000016 System.Int32 UnityEngine.XR.Eyes::GetHashCode()
extern void Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8 (void);
// 0x00000017 System.UInt64 UnityEngine.XR.Bone::get_deviceId()
extern void Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1 (void);
// 0x00000018 System.UInt32 UnityEngine.XR.Bone::get_featureIndex()
extern void Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE (void);
// 0x00000019 System.Boolean UnityEngine.XR.Bone::Equals(System.Object)
extern void Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7 (void);
// 0x0000001A System.Boolean UnityEngine.XR.Bone::Equals(UnityEngine.XR.Bone)
extern void Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F (void);
// 0x0000001B System.Int32 UnityEngine.XR.Bone::GetHashCode()
extern void Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82 (void);
// 0x0000001C System.Void UnityEngine.XR.InputDevices::InvokeConnectionEvent(System.UInt64,UnityEngine.XR.ConnectionChangeType)
extern void InputDevices_InvokeConnectionEvent_m10F62F8E2E197247E88668345C22114268233B1A (void);
// 0x0000001D System.Void UnityEngine.XR.InputTracking::InvokeTrackingEvent(UnityEngine.XR.InputTracking/TrackingStateEventType,UnityEngine.XR.XRNode,System.Int64,System.Boolean)
extern void InputTracking_InvokeTrackingEvent_mA218CBE5D81A639B9C9A084A5360FEAD4625C42C (void);
// 0x0000001E System.Void UnityEngine.XR.XRNodeState::set_uniqueID(System.UInt64)
extern void XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F (void);
// 0x0000001F System.Void UnityEngine.XR.XRNodeState::set_nodeType(UnityEngine.XR.XRNode)
extern void XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4 (void);
// 0x00000020 System.Void UnityEngine.XR.XRNodeState::set_tracked(System.Boolean)
extern void XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45 (void);
// 0x00000021 System.Void UnityEngine.XR.XRMeshSubsystemDescriptor::.ctor()
extern void XRMeshSubsystemDescriptor__ctor_mFD056F69A8BECE56819411D4CD84653B3B735A1B (void);
// 0x00000022 System.String UnityEngine.XR.MeshId::ToString()
extern void MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4 (void);
// 0x00000023 System.Int32 UnityEngine.XR.MeshId::GetHashCode()
extern void MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0 (void);
// 0x00000024 System.Boolean UnityEngine.XR.MeshId::Equals(System.Object)
extern void MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9 (void);
// 0x00000025 System.Boolean UnityEngine.XR.MeshId::Equals(UnityEngine.XR.MeshId)
extern void MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818 (void);
// 0x00000026 System.Void UnityEngine.XR.MeshId::.cctor()
extern void MeshId__cctor_mE4556EF31E7F96397E4C9E7C3DF80A3C129D431D (void);
// 0x00000027 System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32)
extern void HashCodeHelper_Combine_m2A63E4964D06338CC6F9DE17F5EFCCC348A6A1D7 (void);
// 0x00000028 System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_mEDE733EC1ABDEA82040BBC18551FB975F3FAC322 (void);
// 0x00000029 System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_mF867D2FAB545AD0A4832679FC73F7A2B289A37F7 (void);
// 0x0000002A System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_mBE52BFDECDDA219A4A334421E8706078A4BE6680 (void);
// 0x0000002B System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_m429AA1346C5A7ACFA6EC9A5D26250CEA4A1BAF89 (void);
// 0x0000002C System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_m110C54CCBA83DD580295D7BA248976C50F6BF606 (void);
// 0x0000002D System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeHelper_Combine_mBF383AC565B49ACFCB9A0046504C40A8997BAEFD (void);
// 0x0000002E UnityEngine.XR.MeshId UnityEngine.XR.MeshGenerationResult::get_MeshId()
extern void MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D (void);
// 0x0000002F UnityEngine.Mesh UnityEngine.XR.MeshGenerationResult::get_Mesh()
extern void MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9 (void);
// 0x00000030 UnityEngine.MeshCollider UnityEngine.XR.MeshGenerationResult::get_MeshCollider()
extern void MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180 (void);
// 0x00000031 UnityEngine.XR.MeshGenerationStatus UnityEngine.XR.MeshGenerationResult::get_Status()
extern void MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0 (void);
// 0x00000032 UnityEngine.XR.MeshVertexAttributes UnityEngine.XR.MeshGenerationResult::get_Attributes()
extern void MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B (void);
// 0x00000033 UnityEngine.Vector3 UnityEngine.XR.MeshGenerationResult::get_Position()
extern void MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F (void);
// 0x00000034 UnityEngine.Quaternion UnityEngine.XR.MeshGenerationResult::get_Rotation()
extern void MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3 (void);
// 0x00000035 UnityEngine.Vector3 UnityEngine.XR.MeshGenerationResult::get_Scale()
extern void MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3 (void);
// 0x00000036 System.Boolean UnityEngine.XR.MeshGenerationResult::Equals(System.Object)
extern void MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F (void);
// 0x00000037 System.Boolean UnityEngine.XR.MeshGenerationResult::Equals(UnityEngine.XR.MeshGenerationResult)
extern void MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC (void);
// 0x00000038 System.Int32 UnityEngine.XR.MeshGenerationResult::GetHashCode()
extern void MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC (void);
// 0x00000039 System.Void UnityEngine.XR.XRMeshSubsystem::InvokeMeshReadyDelegate(UnityEngine.XR.MeshGenerationResult,System.Action`1<UnityEngine.XR.MeshGenerationResult>)
extern void XRMeshSubsystem_InvokeMeshReadyDelegate_m495A42DE7C44B760CB7D41244A9314F860EA6C53 (void);
// 0x0000003A System.Void UnityEngine.XR.XRMeshSubsystem::.ctor()
extern void XRMeshSubsystem__ctor_mA9C27A31A690B9023F70B628A9D9F5E3F5ED2AEA (void);
// 0x0000003B System.Void UnityEngine.XR.XRMeshSubsystem/MeshTransformList::Dispose()
extern void MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636 (void);
// 0x0000003C System.Void UnityEngine.XR.XRMeshSubsystem/MeshTransformList::Dispose(System.IntPtr)
extern void MeshTransformList_Dispose_m7655ACDE6BC605B30EF2BC387A7B9D0F4D9EED19 (void);
// 0x0000003D System.Void UnityEngine.XR.XRInputSubsystemDescriptor::.ctor()
extern void XRInputSubsystemDescriptor__ctor_m7DFAE8F8670A5721F02B0AE27BB47389BA0F8DFB (void);
// 0x0000003E System.Void UnityEngine.XR.XRInputSubsystem::InvokeTrackingOriginUpdatedEvent(System.IntPtr)
extern void XRInputSubsystem_InvokeTrackingOriginUpdatedEvent_mD9F93C1B2F0BCDE37190DC500F9D93B273362EEB (void);
// 0x0000003F System.Void UnityEngine.XR.XRInputSubsystem::InvokeBoundaryChangedEvent(System.IntPtr)
extern void XRInputSubsystem_InvokeBoundaryChangedEvent_m5BAE59235BADE518D0E32B8D420A0572B63C68C2 (void);
// 0x00000040 System.Void UnityEngine.XR.XRInputSubsystem::.ctor()
extern void XRInputSubsystem__ctor_mD0260427CD99745155B171BB6D03862B3CE303E4 (void);
// 0x00000041 System.Void UnityEngine.XR.XRDisplaySubsystemDescriptor::.ctor()
extern void XRDisplaySubsystemDescriptor__ctor_mB9B2993D74FFC580731C03B390C764260458FAA6 (void);
// 0x00000042 System.Void UnityEngine.XR.XRDisplaySubsystem::InvokeDisplayFocusChanged(System.Boolean)
extern void XRDisplaySubsystem_InvokeDisplayFocusChanged_m57036DB43BB9F6BF12AADC268AC47D190378BE56 (void);
// 0x00000043 System.Void UnityEngine.XR.XRDisplaySubsystem::.ctor()
extern void XRDisplaySubsystem__ctor_m5DA92849F107C6A802BF584D5E328FF2DB971B01 (void);
static Il2CppMethodPointer s_methodPointers[67] = 
{
	EmbeddedAttribute__ctor_m3FF623BFEE0DB7F6FD99E1EAFE20C5581224D314,
	IsReadOnlyAttribute__ctor_m7C0ECD764E09B041BA7629AC5C020D9972AC8697,
	InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085,
	InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810,
	InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0,
	InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D,
	InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D,
	InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34,
	InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19,
	InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC,
	InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC,
	InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D,
	Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A,
	Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775,
	Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06,
	Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864,
	Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE,
	Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD,
	Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8,
	Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D,
	Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969,
	Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8,
	Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1,
	Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE,
	Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7,
	Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F,
	Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82,
	InputDevices_InvokeConnectionEvent_m10F62F8E2E197247E88668345C22114268233B1A,
	InputTracking_InvokeTrackingEvent_mA218CBE5D81A639B9C9A084A5360FEAD4625C42C,
	XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F,
	XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4,
	XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45,
	XRMeshSubsystemDescriptor__ctor_mFD056F69A8BECE56819411D4CD84653B3B735A1B,
	MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4,
	MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0,
	MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9,
	MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818,
	MeshId__cctor_mE4556EF31E7F96397E4C9E7C3DF80A3C129D431D,
	HashCodeHelper_Combine_m2A63E4964D06338CC6F9DE17F5EFCCC348A6A1D7,
	HashCodeHelper_Combine_mEDE733EC1ABDEA82040BBC18551FB975F3FAC322,
	HashCodeHelper_Combine_mF867D2FAB545AD0A4832679FC73F7A2B289A37F7,
	HashCodeHelper_Combine_mBE52BFDECDDA219A4A334421E8706078A4BE6680,
	HashCodeHelper_Combine_m429AA1346C5A7ACFA6EC9A5D26250CEA4A1BAF89,
	HashCodeHelper_Combine_m110C54CCBA83DD580295D7BA248976C50F6BF606,
	HashCodeHelper_Combine_mBF383AC565B49ACFCB9A0046504C40A8997BAEFD,
	MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D,
	MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9,
	MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180,
	MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0,
	MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B,
	MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F,
	MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3,
	MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3,
	MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F,
	MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC,
	MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC,
	XRMeshSubsystem_InvokeMeshReadyDelegate_m495A42DE7C44B760CB7D41244A9314F860EA6C53,
	XRMeshSubsystem__ctor_mA9C27A31A690B9023F70B628A9D9F5E3F5ED2AEA,
	MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636,
	MeshTransformList_Dispose_m7655ACDE6BC605B30EF2BC387A7B9D0F4D9EED19,
	XRInputSubsystemDescriptor__ctor_m7DFAE8F8670A5721F02B0AE27BB47389BA0F8DFB,
	XRInputSubsystem_InvokeTrackingOriginUpdatedEvent_mD9F93C1B2F0BCDE37190DC500F9D93B273362EEB,
	XRInputSubsystem_InvokeBoundaryChangedEvent_m5BAE59235BADE518D0E32B8D420A0572B63C68C2,
	XRInputSubsystem__ctor_mD0260427CD99745155B171BB6D03862B3CE303E4,
	XRDisplaySubsystemDescriptor__ctor_mB9B2993D74FFC580731C03B390C764260458FAA6,
	XRDisplaySubsystem_InvokeDisplayFocusChanged_m57036DB43BB9F6BF12AADC268AC47D190378BE56,
	XRDisplaySubsystem__ctor_m5DA92849F107C6A802BF584D5E328FF2DB971B01,
};
extern void InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085_AdjustorThunk (void);
extern void InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810_AdjustorThunk (void);
extern void InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0_AdjustorThunk (void);
extern void InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D_AdjustorThunk (void);
extern void InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D_AdjustorThunk (void);
extern void InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34_AdjustorThunk (void);
extern void InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19_AdjustorThunk (void);
extern void InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC_AdjustorThunk (void);
extern void InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC_AdjustorThunk (void);
extern void InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D_AdjustorThunk (void);
extern void Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A_AdjustorThunk (void);
extern void Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775_AdjustorThunk (void);
extern void Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06_AdjustorThunk (void);
extern void Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864_AdjustorThunk (void);
extern void Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE_AdjustorThunk (void);
extern void Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD_AdjustorThunk (void);
extern void Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8_AdjustorThunk (void);
extern void Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D_AdjustorThunk (void);
extern void Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969_AdjustorThunk (void);
extern void Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8_AdjustorThunk (void);
extern void Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1_AdjustorThunk (void);
extern void Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE_AdjustorThunk (void);
extern void Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7_AdjustorThunk (void);
extern void Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F_AdjustorThunk (void);
extern void Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82_AdjustorThunk (void);
extern void XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F_AdjustorThunk (void);
extern void XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4_AdjustorThunk (void);
extern void XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45_AdjustorThunk (void);
extern void MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4_AdjustorThunk (void);
extern void MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0_AdjustorThunk (void);
extern void MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9_AdjustorThunk (void);
extern void MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818_AdjustorThunk (void);
extern void MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D_AdjustorThunk (void);
extern void MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9_AdjustorThunk (void);
extern void MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180_AdjustorThunk (void);
extern void MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0_AdjustorThunk (void);
extern void MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B_AdjustorThunk (void);
extern void MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F_AdjustorThunk (void);
extern void MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3_AdjustorThunk (void);
extern void MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3_AdjustorThunk (void);
extern void MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F_AdjustorThunk (void);
extern void MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC_AdjustorThunk (void);
extern void MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC_AdjustorThunk (void);
extern void MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[44] = 
{
	{ 0x06000003, InputFeatureUsage_get_name_mA51C7B42F66E87B3527CBD061D1E6944058FE085_AdjustorThunk },
	{ 0x06000004, InputFeatureUsage_get_internalType_m39EE9801AA983980676998D910C3A906191F8810_AdjustorThunk },
	{ 0x06000005, InputFeatureUsage_Equals_m490288B62FC8FB23746E66937C85D8600BC150C0_AdjustorThunk },
	{ 0x06000006, InputFeatureUsage_Equals_m77BD3037164E49DCCFE7C912FD67B14D9A0A621D_AdjustorThunk },
	{ 0x06000007, InputFeatureUsage_GetHashCode_m823C86783F2394534F08BF9F56F4A23656D8E34D_AdjustorThunk },
	{ 0x06000008, InputDevice__ctor_mC7506750471891883425541E9DE3EB23D8FE4E34_AdjustorThunk },
	{ 0x06000009, InputDevice_get_deviceId_mF3FFF56432109EF58AC2EE178FA343261E950A19_AdjustorThunk },
	{ 0x0600000A, InputDevice_Equals_m206193EDB0C7A69C0BDFC821CE87B887C7BE0CAC_AdjustorThunk },
	{ 0x0600000B, InputDevice_Equals_mD6717F0B7B8605BB9F616947C1A3289C52ED41FC_AdjustorThunk },
	{ 0x0600000C, InputDevice_GetHashCode_m4F8831F85A8646E52B12B5431BEC004AD1BAAD9D_AdjustorThunk },
	{ 0x0600000D, Hand_get_deviceId_m3FD7DC0BD8EBE55A433CBDB6F3D9486D6491714A_AdjustorThunk },
	{ 0x0600000E, Hand_get_featureIndex_mD4AFCDFAEE8471B59400FF25F84328FA17229775_AdjustorThunk },
	{ 0x0600000F, Hand_Equals_m2BEF7EF009B595519DA0ADBE1912C197CEAD2E06_AdjustorThunk },
	{ 0x06000010, Hand_Equals_mA5FFEB3581A5E053717C0718AD68E596E801C864_AdjustorThunk },
	{ 0x06000011, Hand_GetHashCode_mC1EA0B4473AFA966324B32BAC552ADB9BA9D24EE_AdjustorThunk },
	{ 0x06000012, Eyes_get_deviceId_m673FE292832DCAB360282083381923052944EBFD_AdjustorThunk },
	{ 0x06000013, Eyes_get_featureIndex_mC4D34E37C0C008A69E75136C501F8907BC4F2EE8_AdjustorThunk },
	{ 0x06000014, Eyes_Equals_m4DC0E274FA64B59ED26E913BE0C95554476F228D_AdjustorThunk },
	{ 0x06000015, Eyes_Equals_mE463ABC100B9BDB2790A18804BAD09FA1F289969_AdjustorThunk },
	{ 0x06000016, Eyes_GetHashCode_m754EAF148F82A79A7DD39D5F0D294EC4ABC763C8_AdjustorThunk },
	{ 0x06000017, Bone_get_deviceId_mA75E5FA9A3288C5F5E4172D7632FE228487BE0F1_AdjustorThunk },
	{ 0x06000018, Bone_get_featureIndex_mF108AEAE50FFD1B908846717BBD95C59665925BE_AdjustorThunk },
	{ 0x06000019, Bone_Equals_mDC77AF51237B5937D83C048DF2734EB7162620F7_AdjustorThunk },
	{ 0x0600001A, Bone_Equals_mB8585697810AA19D6DD257F9479F01993739020F_AdjustorThunk },
	{ 0x0600001B, Bone_GetHashCode_mF14BB3AB7B7F52290CDF5D6A7C63AE11B3294F82_AdjustorThunk },
	{ 0x0600001E, XRNodeState_set_uniqueID_m1C42BE763CEB5BE66EECE54288DE28D30CCB085F_AdjustorThunk },
	{ 0x0600001F, XRNodeState_set_nodeType_m17D747D9C558277596BD29F59FD8FDEE1A892FF4_AdjustorThunk },
	{ 0x06000020, XRNodeState_set_tracked_m5B448272E0E14001A16A150EE7B602ADE1D85A45_AdjustorThunk },
	{ 0x06000022, MeshId_ToString_m8D2D9206A924A86FF28CF6499B7C66941FD0E7D4_AdjustorThunk },
	{ 0x06000023, MeshId_GetHashCode_mC6B6428EB5856505023C0D48879B92C2E3F062B0_AdjustorThunk },
	{ 0x06000024, MeshId_Equals_m1106B9A800E87442FE36125FD3EC3CB12FD778E9_AdjustorThunk },
	{ 0x06000025, MeshId_Equals_m3D0AC754FDC327819342797C38CF20C5F3D25818_AdjustorThunk },
	{ 0x0600002E, MeshGenerationResult_get_MeshId_m2260BF3F7EB6ED4F2CF0A8F6BEFDD2CD82B6751D_AdjustorThunk },
	{ 0x0600002F, MeshGenerationResult_get_Mesh_m95A022CCC5DDD8D0E8B9CEB3A2D3E06A0E7210F9_AdjustorThunk },
	{ 0x06000030, MeshGenerationResult_get_MeshCollider_m07210E3B1723B5BA535250E2012755AAC46FB180_AdjustorThunk },
	{ 0x06000031, MeshGenerationResult_get_Status_m392754927B1E28DB2E6078A212F8FCC013AC10C0_AdjustorThunk },
	{ 0x06000032, MeshGenerationResult_get_Attributes_m62E3E9EC74240C37D818481B1243B3EB40424C6B_AdjustorThunk },
	{ 0x06000033, MeshGenerationResult_get_Position_m72AE25CA3412AE3C653251095067B496CEC1617F_AdjustorThunk },
	{ 0x06000034, MeshGenerationResult_get_Rotation_mC27F07EC357E8B3EED94DBEE5365B33154941EE3_AdjustorThunk },
	{ 0x06000035, MeshGenerationResult_get_Scale_mA3CEB3EA848509F5B1A5525F40A45AA9CB8BD5D3_AdjustorThunk },
	{ 0x06000036, MeshGenerationResult_Equals_m7A6C6A94553E313F74FB2B44326CF32FC94E122F_AdjustorThunk },
	{ 0x06000037, MeshGenerationResult_Equals_mE32D058EBA520E8CC77A08119D82AE735CE0ACAC_AdjustorThunk },
	{ 0x06000038, MeshGenerationResult_GetHashCode_mA542C935FE2ABB8EC0A3CE001267176095F7DCBC_AdjustorThunk },
	{ 0x0600003B, MeshTransformList_Dispose_m0F0B77B84E50F02EB1A48751831808F9C69E0636_AdjustorThunk },
};
static const int32_t s_InvokerIndices[67] = 
{
	6037,
	6037,
	5913,
	6023,
	3413,
	3382,
	5887,
	4905,
	6024,
	3413,
	3378,
	5887,
	6024,
	6023,
	3413,
	3370,
	5887,
	6024,
	6023,
	3413,
	3352,
	5887,
	6024,
	6023,
	3413,
	3318,
	5887,
	8405,
	7182,
	4905,
	4785,
	4715,
	6037,
	5913,
	5887,
	3413,
	3407,
	9162,
	8062,
	7405,
	6866,
	6509,
	6374,
	6299,
	6256,
	5907,
	5913,
	5913,
	5887,
	5887,
	6028,
	5931,
	6028,
	3413,
	3406,
	5887,
	2684,
	6037,
	6037,
	8971,
	6037,
	8971,
	8971,
	6037,
	6037,
	4715,
	6037,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule = 
{
	"UnityEngine.XRModule.dll",
	67,
	s_methodPointers,
	44,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
