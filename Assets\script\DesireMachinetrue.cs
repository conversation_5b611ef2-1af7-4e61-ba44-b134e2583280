using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class DesireMachinetrue : MonoBehaviour
{
    public Button Grasscutter, grasspicker, flater, seedmachine, plough;
    public GameObject grasscutter, grasspicker1, flater1, seedmachine1, plough1;

    void Start()
    {
        // Add button click listeners
        Grasscutter.onClick.AddListener(() => ActivateMachine(grasscutter));
        grasspicker.onClick.AddListener(() => ActivateMachine(grasspicker1));
        flater.onClick.AddListener(() => ActivateMachine(flater1));
        seedmachine.onClick.AddListener(() => ActivateMachine(seedmachine1));
        plough.onClick.AddListener(() => ActivateMachine(plough1));
    }

    void ActivateMachine(GameObject selectedMachine)
    {
        // Deactivate all machines first
        grasscutter.SetActive(false);
        grasspicker1.SetActive(false);
        flater1.SetActive(false);
        seedmachine1.SetActive(false);
        plough1.SetActive(false);

        // Activate only the selected machine
        selectedMachine.SetActive(true);
    }
    public void Deattach()
    {
        RCC_TruckTrailer.instance.attached = false;
    }
}
