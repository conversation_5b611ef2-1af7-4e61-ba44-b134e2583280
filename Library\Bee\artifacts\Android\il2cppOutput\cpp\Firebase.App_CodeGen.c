﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* FutureVoid_SWIG_CompletionDispatcher_mE9933C19D489F4E5B7B8F99C087A7A1C95681554_RuntimeMethod_var;
extern const RuntimeMethod* LogUtil_LogMessageFromCallback_m3EA336850B4BE115C393BA3AD71981D1AA654307_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingApplicationException_m7FE3B7ADC198F4ED5A180BC5ECD18CC371444591_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArgumentException_m82CC529F5355DF173784D29CDB197BC3AAA353BC_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArgumentNullException_m1A239C193A01B3E73BD763718FB528ED933847A0_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m1F079CDB1AC454648BEFF38716F88AFE6FA8F926_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArithmeticException_m5DA562871B81FA3E688FD12D78E82882F5ADC315_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingDivideByZeroException_m7AE515E72B8E23D18919432B5B7BF0F06CCD18E7_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingIOException_mA50448F1AA4CA664C39B8AB78EF912F18E0DDF50_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingIndexOutOfRangeException_mCD203C03B85ADB38206622594E5DEECA14C1CA7E_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingInvalidCastException_m64057305E28A3122C79BFF5A8C441D72B04C6E5B_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingInvalidOperationException_m4CE89FA918E3D9CA7C6391147792F8226CF6BA07_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingNullReferenceException_m0CE8D326228371436AB3BBCE9AA7464619030A35_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingOutOfMemoryException_mB4209DD263A50C83F1E9CE39A85ADDAE18F51759_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingOverflowException_m366DEFCF657EFE4CBABD2ADCD7D09BD6144E25B0_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingSystemException_mE317DA81F256CF3BD75CAC264E25961D7A536191_RuntimeMethod_var;
extern const RuntimeMethod* SWIGStringHelper_CreateString_m929C4B0922610C0571E685F27F79757BF669C77F_RuntimeMethod_var;



// 0x00000001 System.Void Firebase.FirebaseException::.ctor(System.Int32,System.String)
extern void FirebaseException__ctor_m18D67DA955D2B4EA2BC58BCE0E96AC0A177DD70F (void);
// 0x00000002 System.Void Firebase.FirebaseException::set_ErrorCode(System.Int32)
extern void FirebaseException_set_ErrorCode_m65B2880424E85063D56405A009DAA13E3B106465 (void);
// 0x00000003 Firebase.InitResult Firebase.InitializationException::get_InitResult()
extern void InitializationException_get_InitResult_mC611CCB6BD3529EFD841FCF4BC099532973F75F2 (void);
// 0x00000004 System.Void Firebase.InitializationException::set_InitResult(Firebase.InitResult)
extern void InitializationException_set_InitResult_m94032AD57F63718F6F20625FDB98958766C9D764 (void);
// 0x00000005 System.Void Firebase.InitializationException::.ctor(Firebase.InitResult,System.String)
extern void InitializationException__ctor_mC48C74EE90B137CDEA82068C2E1695D81974C5BF (void);
// 0x00000006 System.Void Firebase.InitializationException::.ctor(Firebase.InitResult,System.String,System.Exception)
extern void InitializationException__ctor_m1384021A3E1B7B0E372257380559D926BD6200BF (void);
// 0x00000007 System.String Firebase.ErrorMessages::get_DependencyNotFoundErrorMessage()
extern void ErrorMessages_get_DependencyNotFoundErrorMessage_mA71EBFCD6E5CC0C61BD0E3624738175EADBCC0F7 (void);
// 0x00000008 System.String Firebase.ErrorMessages::get_DllNotFoundExceptionErrorMessage()
extern void ErrorMessages_get_DllNotFoundExceptionErrorMessage_m0B273BB2A0E048AACEA44918DBBBBACB38B579F3 (void);
// 0x00000009 System.Void Firebase.ErrorMessages::.cctor()
extern void ErrorMessages__cctor_m15AA44253303AB0779074729761A927C52A9DD82 (void);
// 0x0000000A System.Void Firebase.LogUtil::.cctor()
extern void LogUtil__cctor_m65D0A76AA61474FFF64D462091D3620818923C9E (void);
// 0x0000000B System.Void Firebase.LogUtil::InitializeLogging()
extern void LogUtil_InitializeLogging_mC8B6DCC4B1E24F42B676EA58E1AD2EBCDF2967CE (void);
// 0x0000000C Firebase.Platform.PlatformLogLevel Firebase.LogUtil::ConvertLogLevel(Firebase.LogLevel)
extern void LogUtil_ConvertLogLevel_mE58CCE065A1D6EBEDDDDA2CDE76AFEA71E474216 (void);
// 0x0000000D System.Void Firebase.LogUtil::LogMessage(Firebase.LogLevel,System.String)
extern void LogUtil_LogMessage_mA96CEACFEBC0F9B08D7F282A4E55685F6E803A49 (void);
// 0x0000000E System.Void Firebase.LogUtil::LogMessageFromCallback(Firebase.LogLevel,System.String)
extern void LogUtil_LogMessageFromCallback_m3EA336850B4BE115C393BA3AD71981D1AA654307 (void);
// 0x0000000F System.Void Firebase.LogUtil::.ctor()
extern void LogUtil__ctor_mFE64F3E0CAE4C8D317093D419552825F2187F3EA (void);
// 0x00000010 System.Void Firebase.LogUtil::Finalize()
extern void LogUtil_Finalize_mA58D6095B47CD414CEED5AB924C2D53F34FF9D55 (void);
// 0x00000011 System.Void Firebase.LogUtil::Dispose()
extern void LogUtil_Dispose_m69B36B965145091F6023543E577B1D882AAD3F31 (void);
// 0x00000012 System.Void Firebase.LogUtil::Dispose(System.Boolean)
extern void LogUtil_Dispose_m97EA8C366043F8F98301F73F488901880DA431CB (void);
// 0x00000013 System.Void Firebase.LogUtil::<.ctor>b__9_0(System.Object,System.EventArgs)
extern void LogUtil_U3C_ctorU3Eb__9_0_m057EE72CCDA8877817C356F04A3FB0403BDC8268 (void);
// 0x00000014 System.Void Firebase.LogUtil/LogMessageDelegate::.ctor(System.Object,System.IntPtr)
extern void LogMessageDelegate__ctor_mB6AACCCEAE43E818C4B0DFCF6388FF4CC7200F10 (void);
// 0x00000015 System.Void Firebase.LogUtil/LogMessageDelegate::Invoke(Firebase.LogLevel,System.String)
extern void LogMessageDelegate_Invoke_m93848481738EC2A03FD8F5600C132464290BDAC8 (void);
// 0x00000016 System.Void Firebase.MonoPInvokeCallbackAttribute::.ctor(System.Type)
extern void MonoPInvokeCallbackAttribute__ctor_m4AE84268E5E69C1E4E1E8CD7AF145EF3C73DDA02 (void);
// 0x00000017 System.Void Firebase.FutureBase::.ctor(System.IntPtr,System.Boolean)
extern void FutureBase__ctor_m98C8AE4F030730C1CEE7E0B4A1816C623F2B9BE0 (void);
// 0x00000018 System.Void Firebase.FutureBase::Finalize()
extern void FutureBase_Finalize_m9CD99D25C0199A337732E16288ABCE051A4D5CB7 (void);
// 0x00000019 System.Void Firebase.FutureBase::Dispose()
extern void FutureBase_Dispose_m32193D02DE4608C6C3EDF42F3D0495707DA4D15E (void);
// 0x0000001A System.Void Firebase.FutureBase::Dispose(System.Boolean)
extern void FutureBase_Dispose_m17D716EFFAF752B7DBF402C73D757D02C34457EB (void);
// 0x0000001B Firebase.FutureStatus Firebase.FutureBase::status()
extern void FutureBase_status_mC75FD35438B176F95462D3A5D7D9194629211902 (void);
// 0x0000001C System.Int32 Firebase.FutureBase::error()
extern void FutureBase_error_m47E3B5E0A43B4C19510A77B3658EE5D7D10B6030 (void);
// 0x0000001D System.String Firebase.FutureBase::error_message()
extern void FutureBase_error_message_m5CC18319253B1ECC3C8AC675B213A08B1755D527 (void);
// 0x0000001E System.Void Firebase.StringStringMap::.ctor(System.IntPtr,System.Boolean)
extern void StringStringMap__ctor_m493F3867E24E87A4D890A56366DAE5D3E2172E35 (void);
// 0x0000001F System.Runtime.InteropServices.HandleRef Firebase.StringStringMap::getCPtr(Firebase.StringStringMap)
extern void StringStringMap_getCPtr_m9E30BAD269827991D469F743D10098904502616D (void);
// 0x00000020 System.Void Firebase.StringStringMap::Finalize()
extern void StringStringMap_Finalize_mE24B29EBA8476775366BE1E56D51757FF34412D6 (void);
// 0x00000021 System.Void Firebase.StringStringMap::Dispose()
extern void StringStringMap_Dispose_mFECCAB7DCE0572DDE5BAFE9999616BBAD5B42D12 (void);
// 0x00000022 System.Void Firebase.StringStringMap::Dispose(System.Boolean)
extern void StringStringMap_Dispose_m88AC30342C42C0575CC7029859A48F77BCCA4AC0 (void);
// 0x00000023 System.String Firebase.StringStringMap::get_Item(System.String)
extern void StringStringMap_get_Item_m01061069FC7C194E45C518987A14FA5918806BE1 (void);
// 0x00000024 System.Void Firebase.StringStringMap::set_Item(System.String,System.String)
extern void StringStringMap_set_Item_m975DA3FC714B74CB4E7D4CAAE0482D7B669D186F (void);
// 0x00000025 System.Boolean Firebase.StringStringMap::TryGetValue(System.String,System.String&)
extern void StringStringMap_TryGetValue_mEF4B761217F202E2F25001244A02516D4B85263D (void);
// 0x00000026 System.Int32 Firebase.StringStringMap::get_Count()
extern void StringStringMap_get_Count_m2B11AF48BF1530FCB3ED130712C6B5BADC76A848 (void);
// 0x00000027 System.Boolean Firebase.StringStringMap::get_IsReadOnly()
extern void StringStringMap_get_IsReadOnly_m679F53D527AD174BC0D08D0F86998D53FDA6F481 (void);
// 0x00000028 System.Collections.Generic.ICollection`1<System.String> Firebase.StringStringMap::get_Keys()
extern void StringStringMap_get_Keys_m558C6C1516539080580AB4D6F8B2905B4B604AC4 (void);
// 0x00000029 System.Void Firebase.StringStringMap::Add(System.Collections.Generic.KeyValuePair`2<System.String,System.String>)
extern void StringStringMap_Add_mA5E43086723E81409FB93BD34211779B2B95B466 (void);
// 0x0000002A System.Boolean Firebase.StringStringMap::Remove(System.Collections.Generic.KeyValuePair`2<System.String,System.String>)
extern void StringStringMap_Remove_m403C3C6E00AF3F626AFF1EF753E5A69AC4D4C06A (void);
// 0x0000002B System.Boolean Firebase.StringStringMap::Contains(System.Collections.Generic.KeyValuePair`2<System.String,System.String>)
extern void StringStringMap_Contains_m5F1544CEFA19C9797BD02C53E7DD9EB2C9097916 (void);
// 0x0000002C System.Void Firebase.StringStringMap::CopyTo(System.Collections.Generic.KeyValuePair`2<System.String,System.String>[],System.Int32)
extern void StringStringMap_CopyTo_m5D78F9003BE6B16285A5C22504731EA6D14B6454 (void);
// 0x0000002D System.Collections.Generic.IEnumerator`1<System.Collections.Generic.KeyValuePair`2<System.String,System.String>> Firebase.StringStringMap::global::System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<System.String,System.String>>.GetEnumerator()
extern void StringStringMap_globalU3AU3ASystem_Collections_Generic_IEnumerableU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CSystem_StringU3EU3E_GetEnumerator_m4B458CA8BEE64D77627CBEF5804F460379D3CCEC (void);
// 0x0000002E System.Collections.IEnumerator Firebase.StringStringMap::global::System.Collections.IEnumerable.GetEnumerator()
extern void StringStringMap_globalU3AU3ASystem_Collections_IEnumerable_GetEnumerator_m9A35129110F7B5928202096310FDD2CCEBFC9CAF (void);
// 0x0000002F System.Void Firebase.StringStringMap::.ctor()
extern void StringStringMap__ctor_m5295C0F5394545250F06A5F0A53C9ABB48D67794 (void);
// 0x00000030 System.UInt32 Firebase.StringStringMap::size()
extern void StringStringMap_size_m9C20BFD104A8B49600AD587CC29C4A748F408DC4 (void);
// 0x00000031 System.Void Firebase.StringStringMap::Clear()
extern void StringStringMap_Clear_mC33CC5FAC952437E4A1844D77F07682AB4A440A8 (void);
// 0x00000032 System.String Firebase.StringStringMap::getitem(System.String)
extern void StringStringMap_getitem_m8DADD76F6CD52B4B98611DD4292910963C079C1B (void);
// 0x00000033 System.Void Firebase.StringStringMap::setitem(System.String,System.String)
extern void StringStringMap_setitem_mC5A0170C20E03D926C187A4B7AC6B0B96FB18C00 (void);
// 0x00000034 System.Boolean Firebase.StringStringMap::ContainsKey(System.String)
extern void StringStringMap_ContainsKey_mD13F26BD8A08E581ADB303D38074819105C605A5 (void);
// 0x00000035 System.Void Firebase.StringStringMap::Add(System.String,System.String)
extern void StringStringMap_Add_m01048CFD777D82B2F693B6D71A4D452FAF7AEAC1 (void);
// 0x00000036 System.Boolean Firebase.StringStringMap::Remove(System.String)
extern void StringStringMap_Remove_m986E61004827D7F62831AEE307E6020383C3CB4D (void);
// 0x00000037 System.IntPtr Firebase.StringStringMap::create_iterator_begin()
extern void StringStringMap_create_iterator_begin_mF1C81519248E3646D376EC9F288C1C6A2065630D (void);
// 0x00000038 System.String Firebase.StringStringMap::get_next_key(System.IntPtr)
extern void StringStringMap_get_next_key_m92C94637FF8B9A00B08BFCAD08CD41705AEAEE9B (void);
// 0x00000039 System.Void Firebase.StringStringMap::destroy_iterator(System.IntPtr)
extern void StringStringMap_destroy_iterator_m75AD241AD5316B8B09070BF04C571918FE34BA04 (void);
// 0x0000003A System.Void Firebase.StringStringMap/StringStringMapEnumerator::.ctor(Firebase.StringStringMap)
extern void StringStringMapEnumerator__ctor_m1659D491782A6E753AC1792C39802A79860F75B3 (void);
// 0x0000003B System.Collections.Generic.KeyValuePair`2<System.String,System.String> Firebase.StringStringMap/StringStringMapEnumerator::get_Current()
extern void StringStringMapEnumerator_get_Current_mE221D98D4E0B07220825ED8752B9714AADBCB04E (void);
// 0x0000003C System.Object Firebase.StringStringMap/StringStringMapEnumerator::global::System.Collections.IEnumerator.get_Current()
extern void StringStringMapEnumerator_globalU3AU3ASystem_Collections_IEnumerator_get_Current_mF90B634B9E830DB2302FBCBC3F6DC9625AB41FBF (void);
// 0x0000003D System.Boolean Firebase.StringStringMap/StringStringMapEnumerator::MoveNext()
extern void StringStringMapEnumerator_MoveNext_m19D42D8E29467683964512499AABEF93BD9830E1 (void);
// 0x0000003E System.Void Firebase.StringStringMap/StringStringMapEnumerator::Reset()
extern void StringStringMapEnumerator_Reset_mFE28DCE2D21F6639E75B255AAF56D34BFA7A151A (void);
// 0x0000003F System.Void Firebase.StringStringMap/StringStringMapEnumerator::Dispose()
extern void StringStringMapEnumerator_Dispose_mDB957DDBEF7D7D2207BEF3C8EF98D4AF2357B9E8 (void);
// 0x00000040 System.Void Firebase.FutureVoid::.ctor(System.IntPtr,System.Boolean)
extern void FutureVoid__ctor_m6E0B9CCC4BD6777C7369CA92E236C003CCCBC043 (void);
// 0x00000041 System.Void Firebase.FutureVoid::Dispose(System.Boolean)
extern void FutureVoid_Dispose_mE53AC3550993F046EC655040D365FA932C784DE0 (void);
// 0x00000042 System.Threading.Tasks.Task Firebase.FutureVoid::GetTask(Firebase.FutureVoid)
extern void FutureVoid_GetTask_m68B083F4868870F64B15374FE3D1343543C863DF (void);
// 0x00000043 System.Void Firebase.FutureVoid::ThrowIfDisposed()
extern void FutureVoid_ThrowIfDisposed_m58FAFA498227E36B2075EDAF83F346CD70BD9739 (void);
// 0x00000044 System.Void Firebase.FutureVoid::SetOnCompletionCallback(Firebase.FutureVoid/Action)
extern void FutureVoid_SetOnCompletionCallback_m917E3BBFE7A06872EEA6F4FC13F557E5F2B6652D (void);
// 0x00000045 System.Void Firebase.FutureVoid::SetCompletionData(System.IntPtr)
extern void FutureVoid_SetCompletionData_mB5B6BB51BB7E9B0D64631B560A2D6E947B369D45 (void);
// 0x00000046 System.Void Firebase.FutureVoid::SWIG_CompletionDispatcher(System.Int32)
extern void FutureVoid_SWIG_CompletionDispatcher_mE9933C19D489F4E5B7B8F99C087A7A1C95681554 (void);
// 0x00000047 System.IntPtr Firebase.FutureVoid::SWIG_OnCompletion(Firebase.FutureVoid/SWIG_CompletionDelegate,System.Int32)
extern void FutureVoid_SWIG_OnCompletion_mD6E8C709E067EC3FCCC1DD9F8F7C664D7479B634 (void);
// 0x00000048 System.Void Firebase.FutureVoid::SWIG_FreeCompletionData(System.IntPtr)
extern void FutureVoid_SWIG_FreeCompletionData_m9BCF84FC8090543CD5ED95C85C1179837895DA55 (void);
// 0x00000049 System.Void Firebase.FutureVoid::.cctor()
extern void FutureVoid__cctor_mE3C6FE56F8AED90EE6D795624E8E4D84CB6F0944 (void);
// 0x0000004A System.Void Firebase.FutureVoid/Action::.ctor(System.Object,System.IntPtr)
extern void Action__ctor_mED144299E578968B25C10C34315DFE3EF08B445E (void);
// 0x0000004B System.Void Firebase.FutureVoid/Action::Invoke()
extern void Action_Invoke_mB0C4867EC8469233A3647955D2408E97D6107F91 (void);
// 0x0000004C System.Void Firebase.FutureVoid/SWIG_CompletionDelegate::.ctor(System.Object,System.IntPtr)
extern void SWIG_CompletionDelegate__ctor_mE4580033D452974357EA9E700C2AB1D7FBABA5E5 (void);
// 0x0000004D System.Void Firebase.FutureVoid/SWIG_CompletionDelegate::Invoke(System.Int32)
extern void SWIG_CompletionDelegate_Invoke_mB8239A488193E3DC4DB9EE3276065CBDBA72D73B (void);
// 0x0000004E System.Void Firebase.FutureVoid/<>c__DisplayClass5_0::.ctor()
extern void U3CU3Ec__DisplayClass5_0__ctor_m89776A34BECBA50695D3E5F098200BF8227A9FE9 (void);
// 0x0000004F System.Void Firebase.FutureVoid/<>c__DisplayClass5_0::<GetTask>b__0()
extern void U3CU3Ec__DisplayClass5_0_U3CGetTaskU3Eb__0_m3C63FB12C6FC4D1551D9D48B32F123DF7EAA4AE0 (void);
// 0x00000050 System.Void Firebase.FirebaseApp::.ctor(System.IntPtr,System.Boolean)
extern void FirebaseApp__ctor_mC539AF748C2E16CD3B7820D6039B9A29DBDF908C (void);
// 0x00000051 System.Runtime.InteropServices.HandleRef Firebase.FirebaseApp::getCPtr(Firebase.FirebaseApp)
extern void FirebaseApp_getCPtr_m54B5DAC73BA43E79771E0181BEFD846BBE06C84F (void);
// 0x00000052 System.Void Firebase.FirebaseApp::Finalize()
extern void FirebaseApp_Finalize_mF8DA91BE30AF031A390E068301053AEF3D6B5A98 (void);
// 0x00000053 System.Void Firebase.FirebaseApp::Dispose()
extern void FirebaseApp_Dispose_mC1965A7AE8BAB834DB652BF0BACF377F3D45192B (void);
// 0x00000054 System.Void Firebase.FirebaseApp::Dispose(System.Boolean)
extern void FirebaseApp_Dispose_m7AA869727509B99D04399B9BA7F1FEEC0251974A (void);
// 0x00000055 System.Void Firebase.FirebaseApp::.cctor()
extern void FirebaseApp__cctor_m91B5E844644438D93858FE54C4DF15D53358F31B (void);
// 0x00000056 System.Void Firebase.FirebaseApp::TranslateDllNotFoundException(System.Action)
extern void FirebaseApp_TranslateDllNotFoundException_m8A53BF93797E69E0A396E5D387C8BE2FAC5A887E (void);
// 0x00000057 Firebase.FirebaseApp Firebase.FirebaseApp::get_DefaultInstance()
extern void FirebaseApp_get_DefaultInstance_m2387909BEFA7CA8F51D87B62700EAE8DA6FC13A0 (void);
// 0x00000058 Firebase.FirebaseApp Firebase.FirebaseApp::GetInstance(System.String)
extern void FirebaseApp_GetInstance_m9BAC597B32771401771C8915446DA531E7B66EC5 (void);
// 0x00000059 Firebase.FirebaseApp Firebase.FirebaseApp::Create()
extern void FirebaseApp_Create_mB737A2508FF5A06F35D01D4A8CD4AEF1F8944512 (void);
// 0x0000005A System.String Firebase.FirebaseApp::get_Name()
extern void FirebaseApp_get_Name_m89C11F96726C8E4FD3CCAE04A5DC3129F7CD975E (void);
// 0x0000005B Firebase.LogLevel Firebase.FirebaseApp::get_LogLevel()
extern void FirebaseApp_get_LogLevel_m64B54EED8CF1B5F8EA074612CF09E58026D23603 (void);
// 0x0000005C System.Void Firebase.FirebaseApp::AddReference()
extern void FirebaseApp_AddReference_m562BA6DFE00568AC30B15C36D8BB848F14EDED95 (void);
// 0x0000005D System.Void Firebase.FirebaseApp::RemoveReference()
extern void FirebaseApp_RemoveReference_m3C28724EDB5D9F20A2A4924E517A8FF79C7E3425 (void);
// 0x0000005E System.Void Firebase.FirebaseApp::ThrowIfNull()
extern void FirebaseApp_ThrowIfNull_mEBB4A7F4A0E30B8F6969C68C340AF30D44491B20 (void);
// 0x0000005F System.Void Firebase.FirebaseApp::InitializeAppUtilCallbacks()
extern void FirebaseApp_InitializeAppUtilCallbacks_m69A50FD352AE820F31C0DBA793A462BC774F4B20 (void);
// 0x00000060 System.Void Firebase.FirebaseApp::OnAllAppsDestroyed()
extern void FirebaseApp_OnAllAppsDestroyed_m19CF36FB1A2439786994BBB55F1E405B7B43CAAB (void);
// 0x00000061 System.Boolean Firebase.FirebaseApp::InitializeCrashlyticsIfPresent()
extern void FirebaseApp_InitializeCrashlyticsIfPresent_m543A1327364F796F96120C0CD4D7805B412529B6 (void);
// 0x00000062 Firebase.FirebaseApp Firebase.FirebaseApp::CreateAndTrack(Firebase.FirebaseApp/CreateDelegate,Firebase.FirebaseApp)
extern void FirebaseApp_CreateAndTrack_m3EFC2B1BFE4FF3BE069B04DE63E93A257CD65B3E (void);
// 0x00000063 System.Void Firebase.FirebaseApp::SetCheckDependenciesThread(System.Int32)
extern void FirebaseApp_SetCheckDependenciesThread_mFE57ACFA06A8CBAAD19379B430621763999C3563 (void);
// 0x00000064 System.Void Firebase.FirebaseApp::ThrowIfCheckDependenciesRunning()
extern void FirebaseApp_ThrowIfCheckDependenciesRunning_mCC374EEFD25964DB6A38DA14EC987792DE119D9B (void);
// 0x00000065 System.Boolean Firebase.FirebaseApp::IsCheckDependenciesRunning()
extern void FirebaseApp_IsCheckDependenciesRunning_m14D0D3293F91F52A79382CB3B8B2C93EBE3FDB59 (void);
// 0x00000066 System.Threading.Tasks.Task`1<Firebase.DependencyStatus> Firebase.FirebaseApp::CheckDependenciesAsync()
extern void FirebaseApp_CheckDependenciesAsync_m75E00BB4F43A6D11511A55F146E0D9DD94405BCD (void);
// 0x00000067 System.Threading.Tasks.Task`1<Firebase.DependencyStatus> Firebase.FirebaseApp::CheckAndFixDependenciesAsync()
extern void FirebaseApp_CheckAndFixDependenciesAsync_mB21D0BA4D3C2F6C975D72DB0F2CEFF46A4361557 (void);
// 0x00000068 Firebase.DependencyStatus Firebase.FirebaseApp::CheckDependencies()
extern void FirebaseApp_CheckDependencies_mF21910BC5E3CEF3AD82DE4055B05FA2CFF07CBA1 (void);
// 0x00000069 Firebase.DependencyStatus Firebase.FirebaseApp::CheckDependenciesInternal()
extern void FirebaseApp_CheckDependenciesInternal_m738CFE3533F41FC660E43B2EFCCD44BF6EF0AFE5 (void);
// 0x0000006A System.Threading.Tasks.Task Firebase.FirebaseApp::FixDependenciesAsync()
extern void FirebaseApp_FixDependenciesAsync_m3E5A198309832981F2E4C516D60078247E1607C3 (void);
// 0x0000006B System.Void Firebase.FirebaseApp::ResetDefaultAppCPtr()
extern void FirebaseApp_ResetDefaultAppCPtr_m1D38FAF9DA97DFA0C3DB217134369DEA8DA86035 (void);
// 0x0000006C System.String Firebase.FirebaseApp::get_NameInternal()
extern void FirebaseApp_get_NameInternal_m493D9AEC87709D1197A1997C7560AFEBB107FBCE (void);
// 0x0000006D Firebase.FirebaseApp Firebase.FirebaseApp::CreateInternal()
extern void FirebaseApp_CreateInternal_m63EB3F64189DA8E6C5B2E1C95B11D63EF7F7BC4B (void);
// 0x0000006E System.Void Firebase.FirebaseApp::ReleaseReferenceInternal(Firebase.FirebaseApp)
extern void FirebaseApp_ReleaseReferenceInternal_mA281FCA13BCF136D9D9B4311C40925B99614D63D (void);
// 0x0000006F System.Void Firebase.FirebaseApp::RegisterLibrariesInternal(Firebase.StringStringMap)
extern void FirebaseApp_RegisterLibrariesInternal_m03B7487A8CAECC33FFFFFC110445811D0DDB35F2 (void);
// 0x00000070 System.Void Firebase.FirebaseApp::LogHeartbeatInternal(Firebase.FirebaseApp)
extern void FirebaseApp_LogHeartbeatInternal_m849C31C3F73680D986D5672C464BEBCA899FBEFC (void);
// 0x00000071 System.Void Firebase.FirebaseApp::AppSetDefaultConfigPath(System.String)
extern void FirebaseApp_AppSetDefaultConfigPath_m9712BA055777DA0F98524BD85A6C97FBFC5C6192 (void);
// 0x00000072 System.String Firebase.FirebaseApp::get_DefaultName()
extern void FirebaseApp_get_DefaultName_mE170961E3E149AB409453866F8FBEDE07E9C3714 (void);
// 0x00000073 System.String Firebase.FirebaseApp/EnableModuleParams::get_CppModuleName()
extern void EnableModuleParams_get_CppModuleName_mB91981F21F3F94D82CD64DD7BD810741CBB04E3A (void);
// 0x00000074 System.Void Firebase.FirebaseApp/EnableModuleParams::set_CppModuleName(System.String)
extern void EnableModuleParams_set_CppModuleName_mF1C3FE3BBE44DEDB23AF2879630075AEAC7106DF (void);
// 0x00000075 System.String Firebase.FirebaseApp/EnableModuleParams::get_CSharpClassName()
extern void EnableModuleParams_get_CSharpClassName_m04AD392AA82FCE1E6636F812672C77F294AC16EC (void);
// 0x00000076 System.Void Firebase.FirebaseApp/EnableModuleParams::set_CSharpClassName(System.String)
extern void EnableModuleParams_set_CSharpClassName_m9152635BDD8F608352C12F3447962C10F7DF4F43 (void);
// 0x00000077 System.Boolean Firebase.FirebaseApp/EnableModuleParams::get_AlwaysEnable()
extern void EnableModuleParams_get_AlwaysEnable_mC44F8EA7A9EDCD493C6B8E04E3B3CF00D09FDEA6 (void);
// 0x00000078 System.Void Firebase.FirebaseApp/EnableModuleParams::set_AlwaysEnable(System.Boolean)
extern void EnableModuleParams_set_AlwaysEnable_m3F7638041BDA0CC3669AD7119C68ABD2B6F7C482 (void);
// 0x00000079 System.Void Firebase.FirebaseApp/EnableModuleParams::.ctor(System.String,System.String,System.Boolean)
extern void EnableModuleParams__ctor_m448B394AF46BBC2CE9C3301F732850625F6B37EF (void);
// 0x0000007A System.Void Firebase.FirebaseApp/CreateDelegate::.ctor(System.Object,System.IntPtr)
extern void CreateDelegate__ctor_m966C39812E422F82DD3AACF101F012749B1F9E12 (void);
// 0x0000007B Firebase.FirebaseApp Firebase.FirebaseApp/CreateDelegate::Invoke()
extern void CreateDelegate_Invoke_m3C05F10053C0FD938376079571835049ADDD6186 (void);
// 0x0000007C System.Void Firebase.FirebaseApp/<>c::.cctor()
extern void U3CU3Ec__cctor_m829BBCDF9C61D6D694ABAB6DAC91B8EF322B6D0E (void);
// 0x0000007D System.Void Firebase.FirebaseApp/<>c::.ctor()
extern void U3CU3Ec__ctor_m463FBDE085153371662615419AFD8228F4704F75 (void);
// 0x0000007E Firebase.FirebaseApp Firebase.FirebaseApp/<>c::<Create>b__15_0()
extern void U3CU3Ec_U3CCreateU3Eb__15_0_mF43BDAE5875C0C407791D7735DC43BB00EB29F32 (void);
// 0x0000007F System.Boolean Firebase.FirebaseApp/<>c::<CreateAndTrack>b__48_0()
extern void U3CU3Ec_U3CCreateAndTrackU3Eb__48_0_m353C5F1E7C6BDE8601757A37801E17C89CA49AC2 (void);
// 0x00000080 Firebase.DependencyStatus Firebase.FirebaseApp/<>c::<CheckDependenciesAsync>b__56_0()
extern void U3CU3Ec_U3CCheckDependenciesAsyncU3Eb__56_0_m636488EA2B782D4C9144E642E528E6E85F8C2129 (void);
// 0x00000081 System.Threading.Tasks.Task`1<Firebase.DependencyStatus> Firebase.FirebaseApp/<>c::<CheckAndFixDependenciesAsync>b__57_0(System.Threading.Tasks.Task`1<Firebase.DependencyStatus>)
extern void U3CU3Ec_U3CCheckAndFixDependenciesAsyncU3Eb__57_0_m9404F3BF637CC9AE29CC521159638C07E55E9513 (void);
// 0x00000082 System.Threading.Tasks.Task`1<Firebase.DependencyStatus> Firebase.FirebaseApp/<>c::<CheckAndFixDependenciesAsync>b__57_1(System.Threading.Tasks.Task)
extern void U3CU3Ec_U3CCheckAndFixDependenciesAsyncU3Eb__57_1_m910DECA8F467A4E6A55360C8962CB5C85EB2B8ED (void);
// 0x00000083 System.Void Firebase.FirebaseApp/<>c::<FixDependenciesAsync>b__60_1(System.Threading.Tasks.Task)
extern void U3CU3Ec_U3CFixDependenciesAsyncU3Eb__60_1_mE24B887D7A05989A5250E16C95370FEF0A6920EF (void);
// 0x00000084 System.Void Firebase.FirebaseApp/<>c__DisplayClass58_0::.ctor()
extern void U3CU3Ec__DisplayClass58_0__ctor_m7192D3B51ED821A760AF82992CBBFC8652897AA5 (void);
// 0x00000085 System.Void Firebase.FirebaseApp/<>c__DisplayClass58_0::<CheckDependencies>b__0()
extern void U3CU3Ec__DisplayClass58_0_U3CCheckDependenciesU3Eb__0_m4CEFE0A719711F3C0563FD9C9FDDF2F652553AF6 (void);
// 0x00000086 System.Void Firebase.FirebaseApp/<>c__DisplayClass60_0::.ctor()
extern void U3CU3Ec__DisplayClass60_0__ctor_m14A111BEE6E3DE63EDF0086569D3D9C9D5D708B5 (void);
// 0x00000087 System.Void Firebase.FirebaseApp/<>c__DisplayClass60_0::<FixDependenciesAsync>b__0()
extern void U3CU3Ec__DisplayClass60_0_U3CFixDependenciesAsyncU3Eb__0_mA216F8EE30AAE8DB85F3BD26CFA937C99363D009 (void);
// 0x00000088 System.Void Firebase.AppUtilPINVOKE::.cctor()
extern void AppUtilPINVOKE__cctor_m8F7249FF9A16A52D144B2223D3CB9EA2195CE6DF (void);
// 0x00000089 System.Void Firebase.AppUtilPINVOKE::delete_FutureBase(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_delete_FutureBase_mBA1AC9D7BE7977A080996ED289972DF8449F7BC5 (void);
// 0x0000008A System.Int32 Firebase.AppUtilPINVOKE::FutureBase_status(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FutureBase_status_m3056FA61BBB6B002A5BEE4F34F75DCD0858BC27C (void);
// 0x0000008B System.Int32 Firebase.AppUtilPINVOKE::FutureBase_error(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FutureBase_error_mDB0C901628E9EE247400F57E7E697743B69906E3 (void);
// 0x0000008C System.String Firebase.AppUtilPINVOKE::FutureBase_error_message(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FutureBase_error_message_mDB4089A47D35BF6D3457750C5355B1908C4BE46B (void);
// 0x0000008D System.IntPtr Firebase.AppUtilPINVOKE::new_StringStringMap__SWIG_0()
extern void AppUtilPINVOKE_new_StringStringMap__SWIG_0_m84D097C2772CA3A9AC5F931078FB79C3D3C1AF5E (void);
// 0x0000008E System.UInt32 Firebase.AppUtilPINVOKE::StringStringMap_size(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_StringStringMap_size_mD8D9155E1FE3473C2D52EF30DB99A8054B8036C0 (void);
// 0x0000008F System.Void Firebase.AppUtilPINVOKE::StringStringMap_Clear(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_StringStringMap_Clear_m8D2936F5AC4C42D9E10C8A296765929E80819E6A (void);
// 0x00000090 System.String Firebase.AppUtilPINVOKE::StringStringMap_getitem(System.Runtime.InteropServices.HandleRef,System.String)
extern void AppUtilPINVOKE_StringStringMap_getitem_m9D4E9FAA3CA338A73368072EEA97E99D4BA5FC35 (void);
// 0x00000091 System.Void Firebase.AppUtilPINVOKE::StringStringMap_setitem(System.Runtime.InteropServices.HandleRef,System.String,System.String)
extern void AppUtilPINVOKE_StringStringMap_setitem_mF52AA2B0DD2B1C82C96641B73D967C5AA0AA3F04 (void);
// 0x00000092 System.Boolean Firebase.AppUtilPINVOKE::StringStringMap_ContainsKey(System.Runtime.InteropServices.HandleRef,System.String)
extern void AppUtilPINVOKE_StringStringMap_ContainsKey_mD1DB16B8E96A716AA7D6E9EF19565228BE89314A (void);
// 0x00000093 System.Void Firebase.AppUtilPINVOKE::StringStringMap_Add(System.Runtime.InteropServices.HandleRef,System.String,System.String)
extern void AppUtilPINVOKE_StringStringMap_Add_mB5BC4CE692924D407020A29EB1D91EA5FD4DACDA (void);
// 0x00000094 System.Boolean Firebase.AppUtilPINVOKE::StringStringMap_Remove(System.Runtime.InteropServices.HandleRef,System.String)
extern void AppUtilPINVOKE_StringStringMap_Remove_mC01EAA294015A6D9D894D19B73DE6455110EBDA8 (void);
// 0x00000095 System.IntPtr Firebase.AppUtilPINVOKE::StringStringMap_create_iterator_begin(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_StringStringMap_create_iterator_begin_mBBC01E5599A704C943471C9CEFF97810AC4054A1 (void);
// 0x00000096 System.String Firebase.AppUtilPINVOKE::StringStringMap_get_next_key(System.Runtime.InteropServices.HandleRef,System.IntPtr)
extern void AppUtilPINVOKE_StringStringMap_get_next_key_mDE2CD543826A393C6BEF7562D4A81B66872826DB (void);
// 0x00000097 System.Void Firebase.AppUtilPINVOKE::StringStringMap_destroy_iterator(System.Runtime.InteropServices.HandleRef,System.IntPtr)
extern void AppUtilPINVOKE_StringStringMap_destroy_iterator_mD19027EE722AC2BD93E8F886D93AC6D0C3B56209 (void);
// 0x00000098 System.Void Firebase.AppUtilPINVOKE::delete_StringStringMap(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_delete_StringStringMap_m9ACE5D4FA3CBE366F2925D4328DBE077A95E7196 (void);
// 0x00000099 System.IntPtr Firebase.AppUtilPINVOKE::FutureVoid_SWIG_OnCompletion(System.Runtime.InteropServices.HandleRef,Firebase.FutureVoid/SWIG_CompletionDelegate,System.Int32)
extern void AppUtilPINVOKE_FutureVoid_SWIG_OnCompletion_mFD32977A7A2E4A35F4DF9D05E4890FEF115BACB9 (void);
// 0x0000009A System.Void Firebase.AppUtilPINVOKE::FutureVoid_SWIG_FreeCompletionData(System.IntPtr)
extern void AppUtilPINVOKE_FutureVoid_SWIG_FreeCompletionData_m2A3F6D97610E37684611807E9F8471619FA7C22F (void);
// 0x0000009B System.Void Firebase.AppUtilPINVOKE::delete_FutureVoid(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_delete_FutureVoid_m9F8202898737C91464B6621C899505CE987A6393 (void);
// 0x0000009C System.String Firebase.AppUtilPINVOKE::FirebaseApp_NameInternal_get(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FirebaseApp_NameInternal_get_mE4931CA287567E2C7F91442E07F30AE96AE91172 (void);
// 0x0000009D System.IntPtr Firebase.AppUtilPINVOKE::FirebaseApp_CreateInternal__SWIG_0()
extern void AppUtilPINVOKE_FirebaseApp_CreateInternal__SWIG_0_m8C61ECBE5407D4A591827E10595AE28EFE8C6CE5 (void);
// 0x0000009E System.Void Firebase.AppUtilPINVOKE::FirebaseApp_ReleaseReferenceInternal(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FirebaseApp_ReleaseReferenceInternal_m5A184BBB9E09539CDDB771120995526D0328439B (void);
// 0x0000009F System.Int32 Firebase.AppUtilPINVOKE::FirebaseApp_GetLogLevelInternal()
extern void AppUtilPINVOKE_FirebaseApp_GetLogLevelInternal_mAF74BAD43730ACCFFB25EFBDCB0F40CA30CE1940 (void);
// 0x000000A0 System.Void Firebase.AppUtilPINVOKE::FirebaseApp_RegisterLibrariesInternal(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FirebaseApp_RegisterLibrariesInternal_m6A299F866D396117D75C2243115E25F2B8A6F00E (void);
// 0x000000A1 System.Void Firebase.AppUtilPINVOKE::FirebaseApp_LogHeartbeatInternal(System.Runtime.InteropServices.HandleRef)
extern void AppUtilPINVOKE_FirebaseApp_LogHeartbeatInternal_m6BA2A1959A135C6D953FC965A4C09D80DA62C838 (void);
// 0x000000A2 System.Void Firebase.AppUtilPINVOKE::FirebaseApp_AppSetDefaultConfigPath(System.String)
extern void AppUtilPINVOKE_FirebaseApp_AppSetDefaultConfigPath_m461EBC1DCEA9353F3C927FAEEB1590BA130E69DB (void);
// 0x000000A3 System.String Firebase.AppUtilPINVOKE::FirebaseApp_DefaultName_get()
extern void AppUtilPINVOKE_FirebaseApp_DefaultName_get_m7561CF63339BB772F6A9690B9E07D9E21BA70D2A (void);
// 0x000000A4 System.Void Firebase.AppUtilPINVOKE::PollCallbacks()
extern void AppUtilPINVOKE_PollCallbacks_mD2FF7C60A52AF22E9AC028564A06E0F3974B6D56 (void);
// 0x000000A5 System.Void Firebase.AppUtilPINVOKE::AppEnableLogCallback(System.Boolean)
extern void AppUtilPINVOKE_AppEnableLogCallback_m5BB69B725FD3DF1FE26C20DE516F14E02E82BDB3 (void);
// 0x000000A6 System.Void Firebase.AppUtilPINVOKE::SetEnabledAllAppCallbacks(System.Boolean)
extern void AppUtilPINVOKE_SetEnabledAllAppCallbacks_m6A273BFF682F24C5D1F66273B0AA3AF975B29019 (void);
// 0x000000A7 System.Void Firebase.AppUtilPINVOKE::SetEnabledAppCallbackByName(System.String,System.Boolean)
extern void AppUtilPINVOKE_SetEnabledAppCallbackByName_m4705ADB8109C59533C8D2117C87E6336EEB54A05 (void);
// 0x000000A8 System.Boolean Firebase.AppUtilPINVOKE::GetEnabledAppCallbackByName(System.String)
extern void AppUtilPINVOKE_GetEnabledAppCallbackByName_m97E56E8BCA68A8391B677BC814B1E3584FD985C5 (void);
// 0x000000A9 System.Void Firebase.AppUtilPINVOKE::SetLogFunction(Firebase.LogUtil/LogMessageDelegate)
extern void AppUtilPINVOKE_SetLogFunction_m76775D9FA055C83D4C65B6E6E7192E941A433EAE (void);
// 0x000000AA System.Int32 Firebase.AppUtilPINVOKE::CheckAndroidDependencies()
extern void AppUtilPINVOKE_CheckAndroidDependencies_m4FCC7A6957631074F898E119E993E423A6EB6C48 (void);
// 0x000000AB System.IntPtr Firebase.AppUtilPINVOKE::FixAndroidDependencies()
extern void AppUtilPINVOKE_FixAndroidDependencies_m1C8B8699F37268F8755608458DEAE04014F9D7C0 (void);
// 0x000000AC System.Void Firebase.AppUtilPINVOKE::InitializePlayServicesInternal()
extern void AppUtilPINVOKE_InitializePlayServicesInternal_m0027EC41EE00DCF8A530695B979F8AC1EBCEF814 (void);
// 0x000000AD System.Void Firebase.AppUtilPINVOKE::TerminatePlayServicesInternal()
extern void AppUtilPINVOKE_TerminatePlayServicesInternal_mABD31836A1A4B753A27CFD9472F2513746ED4499 (void);
// 0x000000AE System.IntPtr Firebase.AppUtilPINVOKE::FutureVoid_SWIGUpcast(System.IntPtr)
extern void AppUtilPINVOKE_FutureVoid_SWIGUpcast_m8D1EB93463FFA3A9F14A49FD1459CBE45E8957CC (void);
// 0x000000AF System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SWIGRegisterExceptionCallbacks_AppUtil(Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate)
extern void SWIGExceptionHelper_SWIGRegisterExceptionCallbacks_AppUtil_m37C58735D4F8200BA3896F46FD46006F467A604D (void);
// 0x000000B0 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SWIGRegisterExceptionCallbacksArgument_AppUtil(Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate,Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate)
extern void SWIGExceptionHelper_SWIGRegisterExceptionCallbacksArgument_AppUtil_m20CAE98AF0AFEDCADBBEAA94148843C8C671F8D5 (void);
// 0x000000B1 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingApplicationException(System.String)
extern void SWIGExceptionHelper_SetPendingApplicationException_m7FE3B7ADC198F4ED5A180BC5ECD18CC371444591 (void);
// 0x000000B2 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingArithmeticException(System.String)
extern void SWIGExceptionHelper_SetPendingArithmeticException_m5DA562871B81FA3E688FD12D78E82882F5ADC315 (void);
// 0x000000B3 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingDivideByZeroException(System.String)
extern void SWIGExceptionHelper_SetPendingDivideByZeroException_m7AE515E72B8E23D18919432B5B7BF0F06CCD18E7 (void);
// 0x000000B4 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingIndexOutOfRangeException(System.String)
extern void SWIGExceptionHelper_SetPendingIndexOutOfRangeException_mCD203C03B85ADB38206622594E5DEECA14C1CA7E (void);
// 0x000000B5 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingInvalidCastException(System.String)
extern void SWIGExceptionHelper_SetPendingInvalidCastException_m64057305E28A3122C79BFF5A8C441D72B04C6E5B (void);
// 0x000000B6 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingInvalidOperationException(System.String)
extern void SWIGExceptionHelper_SetPendingInvalidOperationException_m4CE89FA918E3D9CA7C6391147792F8226CF6BA07 (void);
// 0x000000B7 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingIOException(System.String)
extern void SWIGExceptionHelper_SetPendingIOException_mA50448F1AA4CA664C39B8AB78EF912F18E0DDF50 (void);
// 0x000000B8 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingNullReferenceException(System.String)
extern void SWIGExceptionHelper_SetPendingNullReferenceException_m0CE8D326228371436AB3BBCE9AA7464619030A35 (void);
// 0x000000B9 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingOutOfMemoryException(System.String)
extern void SWIGExceptionHelper_SetPendingOutOfMemoryException_mB4209DD263A50C83F1E9CE39A85ADDAE18F51759 (void);
// 0x000000BA System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingOverflowException(System.String)
extern void SWIGExceptionHelper_SetPendingOverflowException_m366DEFCF657EFE4CBABD2ADCD7D09BD6144E25B0 (void);
// 0x000000BB System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingSystemException(System.String)
extern void SWIGExceptionHelper_SetPendingSystemException_mE317DA81F256CF3BD75CAC264E25961D7A536191 (void);
// 0x000000BC System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingArgumentException(System.String,System.String)
extern void SWIGExceptionHelper_SetPendingArgumentException_m82CC529F5355DF173784D29CDB197BC3AAA353BC (void);
// 0x000000BD System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingArgumentNullException(System.String,System.String)
extern void SWIGExceptionHelper_SetPendingArgumentNullException_m1A239C193A01B3E73BD763718FB528ED933847A0 (void);
// 0x000000BE System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::SetPendingArgumentOutOfRangeException(System.String,System.String)
extern void SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m1F079CDB1AC454648BEFF38716F88AFE6FA8F926 (void);
// 0x000000BF System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::.cctor()
extern void SWIGExceptionHelper__cctor_m2E9064FCCB3E37EAE10ED204AF7A72C0F1F78F8B (void);
// 0x000000C0 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper::.ctor()
extern void SWIGExceptionHelper__ctor_m4DB6794D8CB5F1A9740C37B0C257B69982C013B9 (void);
// 0x000000C1 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate::.ctor(System.Object,System.IntPtr)
extern void ExceptionDelegate__ctor_m49AB94CEC8E6544CE0D7B1E2300735728EE336D8 (void);
// 0x000000C2 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionDelegate::Invoke(System.String)
extern void ExceptionDelegate_Invoke_mE04E9A1D96F5AE159E3D7878E87706B91A149B25 (void);
// 0x000000C3 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate::.ctor(System.Object,System.IntPtr)
extern void ExceptionArgumentDelegate__ctor_m45E4EFAE5F14FFEC5843A00ABEF4D0E1F0854629 (void);
// 0x000000C4 System.Void Firebase.AppUtilPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate::Invoke(System.String,System.String)
extern void ExceptionArgumentDelegate_Invoke_m46BEB8B17B239BFB498C54B89EB06352BD1948F8 (void);
// 0x000000C5 System.Boolean Firebase.AppUtilPINVOKE/SWIGPendingException::get_Pending()
extern void SWIGPendingException_get_Pending_mFF0B94BEAD2C48E3CD3BB7EFB5EB23A0B6CF55EE (void);
// 0x000000C6 System.Void Firebase.AppUtilPINVOKE/SWIGPendingException::Set(System.Exception)
extern void SWIGPendingException_Set_m7EB577A3129E190D2FBAF8CB9C9CAB7F87DA2642 (void);
// 0x000000C7 System.Exception Firebase.AppUtilPINVOKE/SWIGPendingException::Retrieve()
extern void SWIGPendingException_Retrieve_m5AF2B72B0C255B87F0044137B357F55F50101D51 (void);
// 0x000000C8 System.Void Firebase.AppUtilPINVOKE/SWIGPendingException::.cctor()
extern void SWIGPendingException__cctor_m171494E914FABB4580D25BA675FC206C6C83B6ED (void);
// 0x000000C9 System.Void Firebase.AppUtilPINVOKE/SWIGStringHelper::SWIGRegisterStringCallback_AppUtil(Firebase.AppUtilPINVOKE/SWIGStringHelper/SWIGStringDelegate)
extern void SWIGStringHelper_SWIGRegisterStringCallback_AppUtil_m6DC4FCA4611C8A5521BE0E65F5CCE26D000E4DD1 (void);
// 0x000000CA System.String Firebase.AppUtilPINVOKE/SWIGStringHelper::CreateString(System.String)
extern void SWIGStringHelper_CreateString_m929C4B0922610C0571E685F27F79757BF669C77F (void);
// 0x000000CB System.Void Firebase.AppUtilPINVOKE/SWIGStringHelper::.cctor()
extern void SWIGStringHelper__cctor_mAEBC8A14986139AE4856DA02EC5F3748CACE1971 (void);
// 0x000000CC System.Void Firebase.AppUtilPINVOKE/SWIGStringHelper::.ctor()
extern void SWIGStringHelper__ctor_mF5EBABDC102D937A919B6A6CCA3690E2244ECE85 (void);
// 0x000000CD System.Void Firebase.AppUtilPINVOKE/SWIGStringHelper/SWIGStringDelegate::.ctor(System.Object,System.IntPtr)
extern void SWIGStringDelegate__ctor_m4D5B167B33345B58192AD3B50D1F8901A18F4F4D (void);
// 0x000000CE System.String Firebase.AppUtilPINVOKE/SWIGStringHelper/SWIGStringDelegate::Invoke(System.String)
extern void SWIGStringDelegate_Invoke_m9831BD87E0EBFECFF48B4CA3FFEB118280C2ABCB (void);
// 0x000000CF System.Void Firebase.AppUtil::PollCallbacks()
extern void AppUtil_PollCallbacks_mB25BC1FD5126974F80860A05910301AAF7589BFC (void);
// 0x000000D0 System.Void Firebase.AppUtil::AppEnableLogCallback(System.Boolean)
extern void AppUtil_AppEnableLogCallback_m9FE8159D116019E4E918F4B7CEC39687DD64B2EA (void);
// 0x000000D1 System.Void Firebase.AppUtil::SetEnabledAllAppCallbacks(System.Boolean)
extern void AppUtil_SetEnabledAllAppCallbacks_m0C41A4271764464915ACA461AE8A309AEDAFA6AC (void);
// 0x000000D2 System.Void Firebase.AppUtil::SetEnabledAppCallbackByName(System.String,System.Boolean)
extern void AppUtil_SetEnabledAppCallbackByName_m59C6CB27D92D72FD0841031C207D86C2F684F366 (void);
// 0x000000D3 System.Boolean Firebase.AppUtil::GetEnabledAppCallbackByName(System.String)
extern void AppUtil_GetEnabledAppCallbackByName_m979A86ABDBC2257B697ABDE5015AA76DE5D04F70 (void);
// 0x000000D4 System.Void Firebase.AppUtil::SetLogFunction(Firebase.LogUtil/LogMessageDelegate)
extern void AppUtil_SetLogFunction_m1C535B70AA8069AC3CE81CB05882946F841DACCB (void);
// 0x000000D5 Firebase.GooglePlayServicesAvailability Firebase.AppUtil::CheckAndroidDependencies()
extern void AppUtil_CheckAndroidDependencies_m0D39BD88D8B8246E40B4F874956BE9884C8E0C93 (void);
// 0x000000D6 System.Threading.Tasks.Task Firebase.AppUtil::FixAndroidDependenciesAsync()
extern void AppUtil_FixAndroidDependenciesAsync_m27A782E22791F6CE0E354216AA5A652683BDDAD7 (void);
// 0x000000D7 System.Void Firebase.AppUtil::InitializePlayServicesInternal()
extern void AppUtil_InitializePlayServicesInternal_m89751D7035A1D3816E68F8FDCB4B5B2F8D2F23D2 (void);
// 0x000000D8 System.Void Firebase.AppUtil::TerminatePlayServicesInternal()
extern void AppUtil_TerminatePlayServicesInternal_mED5F11E95B315C4014E0680574592ED30EBFF772 (void);
// 0x000000D9 System.String Firebase.VersionInfo::get_SdkVersion()
extern void VersionInfo_get_SdkVersion_mC32BFBE632414898F8525BD5AED76B512BA0E266 (void);
// 0x000000DA System.String Firebase.VersionInfo::get_BuildSource()
extern void VersionInfo_get_BuildSource_mFEB9E963780C505D73965545BFED5EB50EA7BAD5 (void);
// 0x000000DB Firebase.Platform.FirebaseAppUtils Firebase.Platform.FirebaseAppUtils::get_Instance()
extern void FirebaseAppUtils_get_Instance_m844818D4028B3E8866E21AD1CB6E559038CEEC41 (void);
// 0x000000DC System.Void Firebase.Platform.FirebaseAppUtils::TranslateDllNotFoundException(System.Action)
extern void FirebaseAppUtils_TranslateDllNotFoundException_m8D9620D2F9B093C4DBF14AD9803923F0763955B8 (void);
// 0x000000DD System.Void Firebase.Platform.FirebaseAppUtils::PollCallbacks()
extern void FirebaseAppUtils_PollCallbacks_m94AC1FCAA3602F030E6AA26C1FD6CB03E0F7155C (void);
// 0x000000DE Firebase.Platform.PlatformLogLevel Firebase.Platform.FirebaseAppUtils::GetLogLevel()
extern void FirebaseAppUtils_GetLogLevel_m420F7E6140E65C5494538339E1322E33F3661105 (void);
// 0x000000DF System.Void Firebase.Platform.FirebaseAppUtils::.ctor()
extern void FirebaseAppUtils__ctor_m77E9C2ADF611B1553A685AC953C5508DFD636CD4 (void);
// 0x000000E0 System.Void Firebase.Platform.FirebaseAppUtils::.cctor()
extern void FirebaseAppUtils__cctor_m5444BBFA10C503F9659FF92D02A028B94DBB2204 (void);
static Il2CppMethodPointer s_methodPointers[224] = 
{
	FirebaseException__ctor_m18D67DA955D2B4EA2BC58BCE0E96AC0A177DD70F,
	FirebaseException_set_ErrorCode_m65B2880424E85063D56405A009DAA13E3B106465,
	InitializationException_get_InitResult_mC611CCB6BD3529EFD841FCF4BC099532973F75F2,
	InitializationException_set_InitResult_m94032AD57F63718F6F20625FDB98958766C9D764,
	InitializationException__ctor_mC48C74EE90B137CDEA82068C2E1695D81974C5BF,
	InitializationException__ctor_m1384021A3E1B7B0E372257380559D926BD6200BF,
	ErrorMessages_get_DependencyNotFoundErrorMessage_mA71EBFCD6E5CC0C61BD0E3624738175EADBCC0F7,
	ErrorMessages_get_DllNotFoundExceptionErrorMessage_m0B273BB2A0E048AACEA44918DBBBBACB38B579F3,
	ErrorMessages__cctor_m15AA44253303AB0779074729761A927C52A9DD82,
	LogUtil__cctor_m65D0A76AA61474FFF64D462091D3620818923C9E,
	LogUtil_InitializeLogging_mC8B6DCC4B1E24F42B676EA58E1AD2EBCDF2967CE,
	LogUtil_ConvertLogLevel_mE58CCE065A1D6EBEDDDDA2CDE76AFEA71E474216,
	LogUtil_LogMessage_mA96CEACFEBC0F9B08D7F282A4E55685F6E803A49,
	LogUtil_LogMessageFromCallback_m3EA336850B4BE115C393BA3AD71981D1AA654307,
	LogUtil__ctor_mFE64F3E0CAE4C8D317093D419552825F2187F3EA,
	LogUtil_Finalize_mA58D6095B47CD414CEED5AB924C2D53F34FF9D55,
	LogUtil_Dispose_m69B36B965145091F6023543E577B1D882AAD3F31,
	LogUtil_Dispose_m97EA8C366043F8F98301F73F488901880DA431CB,
	LogUtil_U3C_ctorU3Eb__9_0_m057EE72CCDA8877817C356F04A3FB0403BDC8268,
	LogMessageDelegate__ctor_mB6AACCCEAE43E818C4B0DFCF6388FF4CC7200F10,
	LogMessageDelegate_Invoke_m93848481738EC2A03FD8F5600C132464290BDAC8,
	MonoPInvokeCallbackAttribute__ctor_m4AE84268E5E69C1E4E1E8CD7AF145EF3C73DDA02,
	FutureBase__ctor_m98C8AE4F030730C1CEE7E0B4A1816C623F2B9BE0,
	FutureBase_Finalize_m9CD99D25C0199A337732E16288ABCE051A4D5CB7,
	FutureBase_Dispose_m32193D02DE4608C6C3EDF42F3D0495707DA4D15E,
	FutureBase_Dispose_m17D716EFFAF752B7DBF402C73D757D02C34457EB,
	FutureBase_status_mC75FD35438B176F95462D3A5D7D9194629211902,
	FutureBase_error_m47E3B5E0A43B4C19510A77B3658EE5D7D10B6030,
	FutureBase_error_message_m5CC18319253B1ECC3C8AC675B213A08B1755D527,
	StringStringMap__ctor_m493F3867E24E87A4D890A56366DAE5D3E2172E35,
	StringStringMap_getCPtr_m9E30BAD269827991D469F743D10098904502616D,
	StringStringMap_Finalize_mE24B29EBA8476775366BE1E56D51757FF34412D6,
	StringStringMap_Dispose_mFECCAB7DCE0572DDE5BAFE9999616BBAD5B42D12,
	StringStringMap_Dispose_m88AC30342C42C0575CC7029859A48F77BCCA4AC0,
	StringStringMap_get_Item_m01061069FC7C194E45C518987A14FA5918806BE1,
	StringStringMap_set_Item_m975DA3FC714B74CB4E7D4CAAE0482D7B669D186F,
	StringStringMap_TryGetValue_mEF4B761217F202E2F25001244A02516D4B85263D,
	StringStringMap_get_Count_m2B11AF48BF1530FCB3ED130712C6B5BADC76A848,
	StringStringMap_get_IsReadOnly_m679F53D527AD174BC0D08D0F86998D53FDA6F481,
	StringStringMap_get_Keys_m558C6C1516539080580AB4D6F8B2905B4B604AC4,
	StringStringMap_Add_mA5E43086723E81409FB93BD34211779B2B95B466,
	StringStringMap_Remove_m403C3C6E00AF3F626AFF1EF753E5A69AC4D4C06A,
	StringStringMap_Contains_m5F1544CEFA19C9797BD02C53E7DD9EB2C9097916,
	StringStringMap_CopyTo_m5D78F9003BE6B16285A5C22504731EA6D14B6454,
	StringStringMap_globalU3AU3ASystem_Collections_Generic_IEnumerableU3CSystem_Collections_Generic_KeyValuePairU3CSystem_StringU2CSystem_StringU3EU3E_GetEnumerator_m4B458CA8BEE64D77627CBEF5804F460379D3CCEC,
	StringStringMap_globalU3AU3ASystem_Collections_IEnumerable_GetEnumerator_m9A35129110F7B5928202096310FDD2CCEBFC9CAF,
	StringStringMap__ctor_m5295C0F5394545250F06A5F0A53C9ABB48D67794,
	StringStringMap_size_m9C20BFD104A8B49600AD587CC29C4A748F408DC4,
	StringStringMap_Clear_mC33CC5FAC952437E4A1844D77F07682AB4A440A8,
	StringStringMap_getitem_m8DADD76F6CD52B4B98611DD4292910963C079C1B,
	StringStringMap_setitem_mC5A0170C20E03D926C187A4B7AC6B0B96FB18C00,
	StringStringMap_ContainsKey_mD13F26BD8A08E581ADB303D38074819105C605A5,
	StringStringMap_Add_m01048CFD777D82B2F693B6D71A4D452FAF7AEAC1,
	StringStringMap_Remove_m986E61004827D7F62831AEE307E6020383C3CB4D,
	StringStringMap_create_iterator_begin_mF1C81519248E3646D376EC9F288C1C6A2065630D,
	StringStringMap_get_next_key_m92C94637FF8B9A00B08BFCAD08CD41705AEAEE9B,
	StringStringMap_destroy_iterator_m75AD241AD5316B8B09070BF04C571918FE34BA04,
	StringStringMapEnumerator__ctor_m1659D491782A6E753AC1792C39802A79860F75B3,
	StringStringMapEnumerator_get_Current_mE221D98D4E0B07220825ED8752B9714AADBCB04E,
	StringStringMapEnumerator_globalU3AU3ASystem_Collections_IEnumerator_get_Current_mF90B634B9E830DB2302FBCBC3F6DC9625AB41FBF,
	StringStringMapEnumerator_MoveNext_m19D42D8E29467683964512499AABEF93BD9830E1,
	StringStringMapEnumerator_Reset_mFE28DCE2D21F6639E75B255AAF56D34BFA7A151A,
	StringStringMapEnumerator_Dispose_mDB957DDBEF7D7D2207BEF3C8EF98D4AF2357B9E8,
	FutureVoid__ctor_m6E0B9CCC4BD6777C7369CA92E236C003CCCBC043,
	FutureVoid_Dispose_mE53AC3550993F046EC655040D365FA932C784DE0,
	FutureVoid_GetTask_m68B083F4868870F64B15374FE3D1343543C863DF,
	FutureVoid_ThrowIfDisposed_m58FAFA498227E36B2075EDAF83F346CD70BD9739,
	FutureVoid_SetOnCompletionCallback_m917E3BBFE7A06872EEA6F4FC13F557E5F2B6652D,
	FutureVoid_SetCompletionData_mB5B6BB51BB7E9B0D64631B560A2D6E947B369D45,
	FutureVoid_SWIG_CompletionDispatcher_mE9933C19D489F4E5B7B8F99C087A7A1C95681554,
	FutureVoid_SWIG_OnCompletion_mD6E8C709E067EC3FCCC1DD9F8F7C664D7479B634,
	FutureVoid_SWIG_FreeCompletionData_m9BCF84FC8090543CD5ED95C85C1179837895DA55,
	FutureVoid__cctor_mE3C6FE56F8AED90EE6D795624E8E4D84CB6F0944,
	Action__ctor_mED144299E578968B25C10C34315DFE3EF08B445E,
	Action_Invoke_mB0C4867EC8469233A3647955D2408E97D6107F91,
	SWIG_CompletionDelegate__ctor_mE4580033D452974357EA9E700C2AB1D7FBABA5E5,
	SWIG_CompletionDelegate_Invoke_mB8239A488193E3DC4DB9EE3276065CBDBA72D73B,
	U3CU3Ec__DisplayClass5_0__ctor_m89776A34BECBA50695D3E5F098200BF8227A9FE9,
	U3CU3Ec__DisplayClass5_0_U3CGetTaskU3Eb__0_m3C63FB12C6FC4D1551D9D48B32F123DF7EAA4AE0,
	FirebaseApp__ctor_mC539AF748C2E16CD3B7820D6039B9A29DBDF908C,
	FirebaseApp_getCPtr_m54B5DAC73BA43E79771E0181BEFD846BBE06C84F,
	FirebaseApp_Finalize_mF8DA91BE30AF031A390E068301053AEF3D6B5A98,
	FirebaseApp_Dispose_mC1965A7AE8BAB834DB652BF0BACF377F3D45192B,
	FirebaseApp_Dispose_m7AA869727509B99D04399B9BA7F1FEEC0251974A,
	FirebaseApp__cctor_m91B5E844644438D93858FE54C4DF15D53358F31B,
	FirebaseApp_TranslateDllNotFoundException_m8A53BF93797E69E0A396E5D387C8BE2FAC5A887E,
	FirebaseApp_get_DefaultInstance_m2387909BEFA7CA8F51D87B62700EAE8DA6FC13A0,
	FirebaseApp_GetInstance_m9BAC597B32771401771C8915446DA531E7B66EC5,
	FirebaseApp_Create_mB737A2508FF5A06F35D01D4A8CD4AEF1F8944512,
	FirebaseApp_get_Name_m89C11F96726C8E4FD3CCAE04A5DC3129F7CD975E,
	FirebaseApp_get_LogLevel_m64B54EED8CF1B5F8EA074612CF09E58026D23603,
	FirebaseApp_AddReference_m562BA6DFE00568AC30B15C36D8BB848F14EDED95,
	FirebaseApp_RemoveReference_m3C28724EDB5D9F20A2A4924E517A8FF79C7E3425,
	FirebaseApp_ThrowIfNull_mEBB4A7F4A0E30B8F6969C68C340AF30D44491B20,
	FirebaseApp_InitializeAppUtilCallbacks_m69A50FD352AE820F31C0DBA793A462BC774F4B20,
	FirebaseApp_OnAllAppsDestroyed_m19CF36FB1A2439786994BBB55F1E405B7B43CAAB,
	FirebaseApp_InitializeCrashlyticsIfPresent_m543A1327364F796F96120C0CD4D7805B412529B6,
	FirebaseApp_CreateAndTrack_m3EFC2B1BFE4FF3BE069B04DE63E93A257CD65B3E,
	FirebaseApp_SetCheckDependenciesThread_mFE57ACFA06A8CBAAD19379B430621763999C3563,
	FirebaseApp_ThrowIfCheckDependenciesRunning_mCC374EEFD25964DB6A38DA14EC987792DE119D9B,
	FirebaseApp_IsCheckDependenciesRunning_m14D0D3293F91F52A79382CB3B8B2C93EBE3FDB59,
	FirebaseApp_CheckDependenciesAsync_m75E00BB4F43A6D11511A55F146E0D9DD94405BCD,
	FirebaseApp_CheckAndFixDependenciesAsync_mB21D0BA4D3C2F6C975D72DB0F2CEFF46A4361557,
	FirebaseApp_CheckDependencies_mF21910BC5E3CEF3AD82DE4055B05FA2CFF07CBA1,
	FirebaseApp_CheckDependenciesInternal_m738CFE3533F41FC660E43B2EFCCD44BF6EF0AFE5,
	FirebaseApp_FixDependenciesAsync_m3E5A198309832981F2E4C516D60078247E1607C3,
	FirebaseApp_ResetDefaultAppCPtr_m1D38FAF9DA97DFA0C3DB217134369DEA8DA86035,
	FirebaseApp_get_NameInternal_m493D9AEC87709D1197A1997C7560AFEBB107FBCE,
	FirebaseApp_CreateInternal_m63EB3F64189DA8E6C5B2E1C95B11D63EF7F7BC4B,
	FirebaseApp_ReleaseReferenceInternal_mA281FCA13BCF136D9D9B4311C40925B99614D63D,
	FirebaseApp_RegisterLibrariesInternal_m03B7487A8CAECC33FFFFFC110445811D0DDB35F2,
	FirebaseApp_LogHeartbeatInternal_m849C31C3F73680D986D5672C464BEBCA899FBEFC,
	FirebaseApp_AppSetDefaultConfigPath_m9712BA055777DA0F98524BD85A6C97FBFC5C6192,
	FirebaseApp_get_DefaultName_mE170961E3E149AB409453866F8FBEDE07E9C3714,
	EnableModuleParams_get_CppModuleName_mB91981F21F3F94D82CD64DD7BD810741CBB04E3A,
	EnableModuleParams_set_CppModuleName_mF1C3FE3BBE44DEDB23AF2879630075AEAC7106DF,
	EnableModuleParams_get_CSharpClassName_m04AD392AA82FCE1E6636F812672C77F294AC16EC,
	EnableModuleParams_set_CSharpClassName_m9152635BDD8F608352C12F3447962C10F7DF4F43,
	EnableModuleParams_get_AlwaysEnable_mC44F8EA7A9EDCD493C6B8E04E3B3CF00D09FDEA6,
	EnableModuleParams_set_AlwaysEnable_m3F7638041BDA0CC3669AD7119C68ABD2B6F7C482,
	EnableModuleParams__ctor_m448B394AF46BBC2CE9C3301F732850625F6B37EF,
	CreateDelegate__ctor_m966C39812E422F82DD3AACF101F012749B1F9E12,
	CreateDelegate_Invoke_m3C05F10053C0FD938376079571835049ADDD6186,
	U3CU3Ec__cctor_m829BBCDF9C61D6D694ABAB6DAC91B8EF322B6D0E,
	U3CU3Ec__ctor_m463FBDE085153371662615419AFD8228F4704F75,
	U3CU3Ec_U3CCreateU3Eb__15_0_mF43BDAE5875C0C407791D7735DC43BB00EB29F32,
	U3CU3Ec_U3CCreateAndTrackU3Eb__48_0_m353C5F1E7C6BDE8601757A37801E17C89CA49AC2,
	U3CU3Ec_U3CCheckDependenciesAsyncU3Eb__56_0_m636488EA2B782D4C9144E642E528E6E85F8C2129,
	U3CU3Ec_U3CCheckAndFixDependenciesAsyncU3Eb__57_0_m9404F3BF637CC9AE29CC521159638C07E55E9513,
	U3CU3Ec_U3CCheckAndFixDependenciesAsyncU3Eb__57_1_m910DECA8F467A4E6A55360C8962CB5C85EB2B8ED,
	U3CU3Ec_U3CFixDependenciesAsyncU3Eb__60_1_mE24B887D7A05989A5250E16C95370FEF0A6920EF,
	U3CU3Ec__DisplayClass58_0__ctor_m7192D3B51ED821A760AF82992CBBFC8652897AA5,
	U3CU3Ec__DisplayClass58_0_U3CCheckDependenciesU3Eb__0_m4CEFE0A719711F3C0563FD9C9FDDF2F652553AF6,
	U3CU3Ec__DisplayClass60_0__ctor_m14A111BEE6E3DE63EDF0086569D3D9C9D5D708B5,
	U3CU3Ec__DisplayClass60_0_U3CFixDependenciesAsyncU3Eb__0_mA216F8EE30AAE8DB85F3BD26CFA937C99363D009,
	AppUtilPINVOKE__cctor_m8F7249FF9A16A52D144B2223D3CB9EA2195CE6DF,
	AppUtilPINVOKE_delete_FutureBase_mBA1AC9D7BE7977A080996ED289972DF8449F7BC5,
	AppUtilPINVOKE_FutureBase_status_m3056FA61BBB6B002A5BEE4F34F75DCD0858BC27C,
	AppUtilPINVOKE_FutureBase_error_mDB0C901628E9EE247400F57E7E697743B69906E3,
	AppUtilPINVOKE_FutureBase_error_message_mDB4089A47D35BF6D3457750C5355B1908C4BE46B,
	AppUtilPINVOKE_new_StringStringMap__SWIG_0_m84D097C2772CA3A9AC5F931078FB79C3D3C1AF5E,
	AppUtilPINVOKE_StringStringMap_size_mD8D9155E1FE3473C2D52EF30DB99A8054B8036C0,
	AppUtilPINVOKE_StringStringMap_Clear_m8D2936F5AC4C42D9E10C8A296765929E80819E6A,
	AppUtilPINVOKE_StringStringMap_getitem_m9D4E9FAA3CA338A73368072EEA97E99D4BA5FC35,
	AppUtilPINVOKE_StringStringMap_setitem_mF52AA2B0DD2B1C82C96641B73D967C5AA0AA3F04,
	AppUtilPINVOKE_StringStringMap_ContainsKey_mD1DB16B8E96A716AA7D6E9EF19565228BE89314A,
	AppUtilPINVOKE_StringStringMap_Add_mB5BC4CE692924D407020A29EB1D91EA5FD4DACDA,
	AppUtilPINVOKE_StringStringMap_Remove_mC01EAA294015A6D9D894D19B73DE6455110EBDA8,
	AppUtilPINVOKE_StringStringMap_create_iterator_begin_mBBC01E5599A704C943471C9CEFF97810AC4054A1,
	AppUtilPINVOKE_StringStringMap_get_next_key_mDE2CD543826A393C6BEF7562D4A81B66872826DB,
	AppUtilPINVOKE_StringStringMap_destroy_iterator_mD19027EE722AC2BD93E8F886D93AC6D0C3B56209,
	AppUtilPINVOKE_delete_StringStringMap_m9ACE5D4FA3CBE366F2925D4328DBE077A95E7196,
	AppUtilPINVOKE_FutureVoid_SWIG_OnCompletion_mFD32977A7A2E4A35F4DF9D05E4890FEF115BACB9,
	AppUtilPINVOKE_FutureVoid_SWIG_FreeCompletionData_m2A3F6D97610E37684611807E9F8471619FA7C22F,
	AppUtilPINVOKE_delete_FutureVoid_m9F8202898737C91464B6621C899505CE987A6393,
	AppUtilPINVOKE_FirebaseApp_NameInternal_get_mE4931CA287567E2C7F91442E07F30AE96AE91172,
	AppUtilPINVOKE_FirebaseApp_CreateInternal__SWIG_0_m8C61ECBE5407D4A591827E10595AE28EFE8C6CE5,
	AppUtilPINVOKE_FirebaseApp_ReleaseReferenceInternal_m5A184BBB9E09539CDDB771120995526D0328439B,
	AppUtilPINVOKE_FirebaseApp_GetLogLevelInternal_mAF74BAD43730ACCFFB25EFBDCB0F40CA30CE1940,
	AppUtilPINVOKE_FirebaseApp_RegisterLibrariesInternal_m6A299F866D396117D75C2243115E25F2B8A6F00E,
	AppUtilPINVOKE_FirebaseApp_LogHeartbeatInternal_m6BA2A1959A135C6D953FC965A4C09D80DA62C838,
	AppUtilPINVOKE_FirebaseApp_AppSetDefaultConfigPath_m461EBC1DCEA9353F3C927FAEEB1590BA130E69DB,
	AppUtilPINVOKE_FirebaseApp_DefaultName_get_m7561CF63339BB772F6A9690B9E07D9E21BA70D2A,
	AppUtilPINVOKE_PollCallbacks_mD2FF7C60A52AF22E9AC028564A06E0F3974B6D56,
	AppUtilPINVOKE_AppEnableLogCallback_m5BB69B725FD3DF1FE26C20DE516F14E02E82BDB3,
	AppUtilPINVOKE_SetEnabledAllAppCallbacks_m6A273BFF682F24C5D1F66273B0AA3AF975B29019,
	AppUtilPINVOKE_SetEnabledAppCallbackByName_m4705ADB8109C59533C8D2117C87E6336EEB54A05,
	AppUtilPINVOKE_GetEnabledAppCallbackByName_m97E56E8BCA68A8391B677BC814B1E3584FD985C5,
	AppUtilPINVOKE_SetLogFunction_m76775D9FA055C83D4C65B6E6E7192E941A433EAE,
	AppUtilPINVOKE_CheckAndroidDependencies_m4FCC7A6957631074F898E119E993E423A6EB6C48,
	AppUtilPINVOKE_FixAndroidDependencies_m1C8B8699F37268F8755608458DEAE04014F9D7C0,
	AppUtilPINVOKE_InitializePlayServicesInternal_m0027EC41EE00DCF8A530695B979F8AC1EBCEF814,
	AppUtilPINVOKE_TerminatePlayServicesInternal_mABD31836A1A4B753A27CFD9472F2513746ED4499,
	AppUtilPINVOKE_FutureVoid_SWIGUpcast_m8D1EB93463FFA3A9F14A49FD1459CBE45E8957CC,
	SWIGExceptionHelper_SWIGRegisterExceptionCallbacks_AppUtil_m37C58735D4F8200BA3896F46FD46006F467A604D,
	SWIGExceptionHelper_SWIGRegisterExceptionCallbacksArgument_AppUtil_m20CAE98AF0AFEDCADBBEAA94148843C8C671F8D5,
	SWIGExceptionHelper_SetPendingApplicationException_m7FE3B7ADC198F4ED5A180BC5ECD18CC371444591,
	SWIGExceptionHelper_SetPendingArithmeticException_m5DA562871B81FA3E688FD12D78E82882F5ADC315,
	SWIGExceptionHelper_SetPendingDivideByZeroException_m7AE515E72B8E23D18919432B5B7BF0F06CCD18E7,
	SWIGExceptionHelper_SetPendingIndexOutOfRangeException_mCD203C03B85ADB38206622594E5DEECA14C1CA7E,
	SWIGExceptionHelper_SetPendingInvalidCastException_m64057305E28A3122C79BFF5A8C441D72B04C6E5B,
	SWIGExceptionHelper_SetPendingInvalidOperationException_m4CE89FA918E3D9CA7C6391147792F8226CF6BA07,
	SWIGExceptionHelper_SetPendingIOException_mA50448F1AA4CA664C39B8AB78EF912F18E0DDF50,
	SWIGExceptionHelper_SetPendingNullReferenceException_m0CE8D326228371436AB3BBCE9AA7464619030A35,
	SWIGExceptionHelper_SetPendingOutOfMemoryException_mB4209DD263A50C83F1E9CE39A85ADDAE18F51759,
	SWIGExceptionHelper_SetPendingOverflowException_m366DEFCF657EFE4CBABD2ADCD7D09BD6144E25B0,
	SWIGExceptionHelper_SetPendingSystemException_mE317DA81F256CF3BD75CAC264E25961D7A536191,
	SWIGExceptionHelper_SetPendingArgumentException_m82CC529F5355DF173784D29CDB197BC3AAA353BC,
	SWIGExceptionHelper_SetPendingArgumentNullException_m1A239C193A01B3E73BD763718FB528ED933847A0,
	SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m1F079CDB1AC454648BEFF38716F88AFE6FA8F926,
	SWIGExceptionHelper__cctor_m2E9064FCCB3E37EAE10ED204AF7A72C0F1F78F8B,
	SWIGExceptionHelper__ctor_m4DB6794D8CB5F1A9740C37B0C257B69982C013B9,
	ExceptionDelegate__ctor_m49AB94CEC8E6544CE0D7B1E2300735728EE336D8,
	ExceptionDelegate_Invoke_mE04E9A1D96F5AE159E3D7878E87706B91A149B25,
	ExceptionArgumentDelegate__ctor_m45E4EFAE5F14FFEC5843A00ABEF4D0E1F0854629,
	ExceptionArgumentDelegate_Invoke_m46BEB8B17B239BFB498C54B89EB06352BD1948F8,
	SWIGPendingException_get_Pending_mFF0B94BEAD2C48E3CD3BB7EFB5EB23A0B6CF55EE,
	SWIGPendingException_Set_m7EB577A3129E190D2FBAF8CB9C9CAB7F87DA2642,
	SWIGPendingException_Retrieve_m5AF2B72B0C255B87F0044137B357F55F50101D51,
	SWIGPendingException__cctor_m171494E914FABB4580D25BA675FC206C6C83B6ED,
	SWIGStringHelper_SWIGRegisterStringCallback_AppUtil_m6DC4FCA4611C8A5521BE0E65F5CCE26D000E4DD1,
	SWIGStringHelper_CreateString_m929C4B0922610C0571E685F27F79757BF669C77F,
	SWIGStringHelper__cctor_mAEBC8A14986139AE4856DA02EC5F3748CACE1971,
	SWIGStringHelper__ctor_mF5EBABDC102D937A919B6A6CCA3690E2244ECE85,
	SWIGStringDelegate__ctor_m4D5B167B33345B58192AD3B50D1F8901A18F4F4D,
	SWIGStringDelegate_Invoke_m9831BD87E0EBFECFF48B4CA3FFEB118280C2ABCB,
	AppUtil_PollCallbacks_mB25BC1FD5126974F80860A05910301AAF7589BFC,
	AppUtil_AppEnableLogCallback_m9FE8159D116019E4E918F4B7CEC39687DD64B2EA,
	AppUtil_SetEnabledAllAppCallbacks_m0C41A4271764464915ACA461AE8A309AEDAFA6AC,
	AppUtil_SetEnabledAppCallbackByName_m59C6CB27D92D72FD0841031C207D86C2F684F366,
	AppUtil_GetEnabledAppCallbackByName_m979A86ABDBC2257B697ABDE5015AA76DE5D04F70,
	AppUtil_SetLogFunction_m1C535B70AA8069AC3CE81CB05882946F841DACCB,
	AppUtil_CheckAndroidDependencies_m0D39BD88D8B8246E40B4F874956BE9884C8E0C93,
	AppUtil_FixAndroidDependenciesAsync_m27A782E22791F6CE0E354216AA5A652683BDDAD7,
	AppUtil_InitializePlayServicesInternal_m89751D7035A1D3816E68F8FDCB4B5B2F8D2F23D2,
	AppUtil_TerminatePlayServicesInternal_mED5F11E95B315C4014E0680574592ED30EBFF772,
	VersionInfo_get_SdkVersion_mC32BFBE632414898F8525BD5AED76B512BA0E266,
	VersionInfo_get_BuildSource_mFEB9E963780C505D73965545BFED5EB50EA7BAD5,
	FirebaseAppUtils_get_Instance_m844818D4028B3E8866E21AD1CB6E559038CEEC41,
	FirebaseAppUtils_TranslateDllNotFoundException_m8D9620D2F9B093C4DBF14AD9803923F0763955B8,
	FirebaseAppUtils_PollCallbacks_m94AC1FCAA3602F030E6AA26C1FD6CB03E0F7155C,
	FirebaseAppUtils_GetLogLevel_m420F7E6140E65C5494538339E1322E33F3661105,
	FirebaseAppUtils__ctor_m77E9C2ADF611B1553A685AC953C5508DFD636CD4,
	FirebaseAppUtils__cctor_m5444BBFA10C503F9659FF92D02A028B94DBB2204,
};
static const int32_t s_InvokerIndices[224] = 
{
	2471,
	4785,
	5887,
	4785,
	2471,
	1359,
	9116,
	9116,
	9162,
	9162,
	9162,
	8665,
	8342,
	8342,
	6037,
	6037,
	6037,
	4715,
	2708,
	2704,
	2471,
	4809,
	2673,
	6037,
	6037,
	4715,
	5887,
	5887,
	5913,
	2673,
	8633,
	6037,
	6037,
	4715,
	4254,
	2708,
	1601,
	5887,
	5818,
	5913,
	4591,
	3190,
	3190,
	2702,
	5913,
	5913,
	6037,
	6023,
	6037,
	4254,
	2708,
	3413,
	2708,
	3413,
	5889,
	4249,
	4787,
	4809,
	5698,
	5913,
	5818,
	6037,
	6037,
	2673,
	4715,
	8761,
	6037,
	4809,
	4787,
	8969,
	2049,
	8971,
	9162,
	2704,
	6037,
	2704,
	4785,
	6037,
	6037,
	2673,
	8633,
	6037,
	6037,
	4715,
	9162,
	8973,
	9116,
	8761,
	9116,
	5913,
	9108,
	6037,
	6037,
	6037,
	9162,
	9162,
	9083,
	8149,
	8969,
	9162,
	9083,
	9116,
	9116,
	9108,
	9108,
	9116,
	9162,
	5913,
	9116,
	8973,
	8973,
	8973,
	8973,
	9116,
	5913,
	4809,
	5913,
	4809,
	5818,
	4715,
	1410,
	2704,
	5913,
	9162,
	6037,
	5913,
	5818,
	5887,
	4254,
	4254,
	4809,
	6037,
	6037,
	6037,
	6037,
	9162,
	8965,
	8661,
	8661,
	8754,
	9110,
	8915,
	8965,
	8127,
	7676,
	7881,
	7676,
	7881,
	8697,
	8126,
	8335,
	8965,
	7446,
	8971,
	8965,
	8754,
	9110,
	8965,
	9108,
	8965,
	8965,
	8973,
	9116,
	9162,
	8961,
	8961,
	8358,
	8564,
	8973,
	9108,
	9110,
	9162,
	9162,
	8700,
	6211,
	7742,
	8973,
	8973,
	8973,
	8973,
	8973,
	8973,
	8973,
	8973,
	8973,
	8973,
	8973,
	8373,
	8373,
	8373,
	9162,
	6037,
	2704,
	4809,
	2704,
	2708,
	9083,
	8973,
	9116,
	9162,
	8973,
	8761,
	9162,
	6037,
	2704,
	4254,
	9162,
	8961,
	8961,
	8358,
	8564,
	8973,
	9108,
	9116,
	9162,
	9162,
	9116,
	9116,
	9116,
	4809,
	6037,
	5887,
	6037,
	9162,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[17] = 
{
	{ 0x0600000E, 2,  (void**)&LogUtil_LogMessageFromCallback_m3EA336850B4BE115C393BA3AD71981D1AA654307_RuntimeMethod_var, 0 },
	{ 0x06000046, 1,  (void**)&FutureVoid_SWIG_CompletionDispatcher_mE9933C19D489F4E5B7B8F99C087A7A1C95681554_RuntimeMethod_var, 0 },
	{ 0x060000B1, 4,  (void**)&SWIGExceptionHelper_SetPendingApplicationException_m7FE3B7ADC198F4ED5A180BC5ECD18CC371444591_RuntimeMethod_var, 0 },
	{ 0x060000B2, 8,  (void**)&SWIGExceptionHelper_SetPendingArithmeticException_m5DA562871B81FA3E688FD12D78E82882F5ADC315_RuntimeMethod_var, 0 },
	{ 0x060000B3, 9,  (void**)&SWIGExceptionHelper_SetPendingDivideByZeroException_m7AE515E72B8E23D18919432B5B7BF0F06CCD18E7_RuntimeMethod_var, 0 },
	{ 0x060000B4, 11,  (void**)&SWIGExceptionHelper_SetPendingIndexOutOfRangeException_mCD203C03B85ADB38206622594E5DEECA14C1CA7E_RuntimeMethod_var, 0 },
	{ 0x060000B5, 12,  (void**)&SWIGExceptionHelper_SetPendingInvalidCastException_m64057305E28A3122C79BFF5A8C441D72B04C6E5B_RuntimeMethod_var, 0 },
	{ 0x060000B6, 13,  (void**)&SWIGExceptionHelper_SetPendingInvalidOperationException_m4CE89FA918E3D9CA7C6391147792F8226CF6BA07_RuntimeMethod_var, 0 },
	{ 0x060000B7, 10,  (void**)&SWIGExceptionHelper_SetPendingIOException_mA50448F1AA4CA664C39B8AB78EF912F18E0DDF50_RuntimeMethod_var, 0 },
	{ 0x060000B8, 14,  (void**)&SWIGExceptionHelper_SetPendingNullReferenceException_m0CE8D326228371436AB3BBCE9AA7464619030A35_RuntimeMethod_var, 0 },
	{ 0x060000B9, 15,  (void**)&SWIGExceptionHelper_SetPendingOutOfMemoryException_mB4209DD263A50C83F1E9CE39A85ADDAE18F51759_RuntimeMethod_var, 0 },
	{ 0x060000BA, 16,  (void**)&SWIGExceptionHelper_SetPendingOverflowException_m366DEFCF657EFE4CBABD2ADCD7D09BD6144E25B0_RuntimeMethod_var, 0 },
	{ 0x060000BB, 17,  (void**)&SWIGExceptionHelper_SetPendingSystemException_mE317DA81F256CF3BD75CAC264E25961D7A536191_RuntimeMethod_var, 0 },
	{ 0x060000BC, 5,  (void**)&SWIGExceptionHelper_SetPendingArgumentException_m82CC529F5355DF173784D29CDB197BC3AAA353BC_RuntimeMethod_var, 0 },
	{ 0x060000BD, 6,  (void**)&SWIGExceptionHelper_SetPendingArgumentNullException_m1A239C193A01B3E73BD763718FB528ED933847A0_RuntimeMethod_var, 0 },
	{ 0x060000BE, 7,  (void**)&SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m1F079CDB1AC454648BEFF38716F88AFE6FA8F926_RuntimeMethod_var, 0 },
	{ 0x060000CA, 18,  (void**)&SWIGStringHelper_CreateString_m929C4B0922610C0571E685F27F79757BF669C77F_RuntimeMethod_var, 0 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Firebase_App_CodeGenModule;
const Il2CppCodeGenModule g_Firebase_App_CodeGenModule = 
{
	"Firebase.App.dll",
	224,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	17,
	s_reversePInvokeIndices,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
