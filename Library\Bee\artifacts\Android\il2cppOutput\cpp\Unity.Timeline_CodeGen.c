﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 UnityEngine.Playables.ScriptPlayable`1<UnityEngine.Timeline.ActivationMixerPlayable> UnityEngine.Timeline.ActivationMixerPlayable::Create(UnityEngine.Playables.PlayableGraph,System.Int32)
extern void ActivationMixerPlayable_Create_mB03A5A5C425D4F901ED4B55E89799A5D207EEC47 (void);
// 0x00000002 UnityEngine.Timeline.ActivationTrack/PostPlaybackState UnityEngine.Timeline.ActivationMixerPlayable::get_postPlaybackState()
extern void ActivationMixerPlayable_get_postPlaybackState_mF0384E7535C7AEA8617EADC8D9832D8C5CC46D46 (void);
// 0x00000003 System.Void UnityEngine.Timeline.ActivationMixerPlayable::set_postPlaybackState(UnityEngine.Timeline.ActivationTrack/PostPlaybackState)
extern void ActivationMixerPlayable_set_postPlaybackState_m5FDC121E23D90F62C75843C5A363DE97E7DD2EB3 (void);
// 0x00000004 System.Void UnityEngine.Timeline.ActivationMixerPlayable::OnPlayableDestroy(UnityEngine.Playables.Playable)
extern void ActivationMixerPlayable_OnPlayableDestroy_mE80DBAA8FAFBA511669B4E78B55751AA5898B074 (void);
// 0x00000005 System.Void UnityEngine.Timeline.ActivationMixerPlayable::ProcessFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData,System.Object)
extern void ActivationMixerPlayable_ProcessFrame_mDCC3B223FA8AB796778E2E8B979A227BE8DB588C (void);
// 0x00000006 System.Void UnityEngine.Timeline.ActivationMixerPlayable::.ctor()
extern void ActivationMixerPlayable__ctor_m513A8FE0373D3B5A4FFC829ED4DB38735128582E (void);
// 0x00000007 UnityEngine.Timeline.ClipCaps UnityEngine.Timeline.ActivationPlayableAsset::get_clipCaps()
extern void ActivationPlayableAsset_get_clipCaps_mF49360399D36AEA60F85A01905F9CC9176F32FA2 (void);
// 0x00000008 UnityEngine.Playables.Playable UnityEngine.Timeline.ActivationPlayableAsset::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject)
extern void ActivationPlayableAsset_CreatePlayable_m5A7D557E44615A717158ADB9CAFAD5C1FA70279A (void);
// 0x00000009 System.Void UnityEngine.Timeline.ActivationPlayableAsset::.ctor()
extern void ActivationPlayableAsset__ctor_m367911F439EC602657B306B2F180B46D959A87B3 (void);
// 0x0000000A System.Boolean UnityEngine.Timeline.ActivationTrack::CanCompileClips()
extern void ActivationTrack_CanCompileClips_mA606603F95C9516112052DD4B17136499DE42193 (void);
// 0x0000000B UnityEngine.Timeline.ActivationTrack/PostPlaybackState UnityEngine.Timeline.ActivationTrack::get_postPlaybackState()
extern void ActivationTrack_get_postPlaybackState_m1294D74ED475B43256FC9AEB2AB2DEDC2019FB82 (void);
// 0x0000000C System.Void UnityEngine.Timeline.ActivationTrack::set_postPlaybackState(UnityEngine.Timeline.ActivationTrack/PostPlaybackState)
extern void ActivationTrack_set_postPlaybackState_m25320A2912F8EF46DEDDE08963D837CA3B1E55CE (void);
// 0x0000000D UnityEngine.Playables.Playable UnityEngine.Timeline.ActivationTrack::CreateTrackMixer(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,System.Int32)
extern void ActivationTrack_CreateTrackMixer_mD0C559021DDE8B870EE86BCA9BE59DF1FB54C38F (void);
// 0x0000000E System.Void UnityEngine.Timeline.ActivationTrack::UpdateTrackMode()
extern void ActivationTrack_UpdateTrackMode_mD01BE7C19739DBC651C645F5AD11FB6DAF5EBB1E (void);
// 0x0000000F System.Void UnityEngine.Timeline.ActivationTrack::GatherProperties(UnityEngine.Playables.PlayableDirector,UnityEngine.Timeline.IPropertyCollector)
extern void ActivationTrack_GatherProperties_m3808D2F7F5C1FAE282906617F42A80448A33F1FA (void);
// 0x00000010 System.Void UnityEngine.Timeline.ActivationTrack::OnCreateClip(UnityEngine.Timeline.TimelineClip)
extern void ActivationTrack_OnCreateClip_m14768AEC5F93456C8BB50050D14D09DF68EDD703 (void);
// 0x00000011 System.Void UnityEngine.Timeline.ActivationTrack::.ctor()
extern void ActivationTrack__ctor_mC854FF5136E9975AF73188924C9A939FA1B973C4 (void);
// 0x00000012 System.Void UnityEngine.Timeline.AnimationOutputWeightProcessor::.ctor(UnityEngine.Animations.AnimationPlayableOutput)
extern void AnimationOutputWeightProcessor__ctor_m474D5FD59685D4A13DE5307630091A0414E91E0F (void);
// 0x00000013 System.Void UnityEngine.Timeline.AnimationOutputWeightProcessor::FindMixers()
extern void AnimationOutputWeightProcessor_FindMixers_m82DA2CCB4F1B0AA24E3B8CFAB6DDEC8176EE2F65 (void);
// 0x00000014 System.Void UnityEngine.Timeline.AnimationOutputWeightProcessor::FindMixers(UnityEngine.Playables.Playable,System.Int32,UnityEngine.Playables.Playable)
extern void AnimationOutputWeightProcessor_FindMixers_mF9681DE5BB91778AFCF883E4732DA6BE1A31A43D (void);
// 0x00000015 System.Void UnityEngine.Timeline.AnimationOutputWeightProcessor::Evaluate()
extern void AnimationOutputWeightProcessor_Evaluate_m3C0568570541A5BD6B6405B49B72CCE50714BB17 (void);
// 0x00000016 UnityEngine.Vector3 UnityEngine.Timeline.AnimationPlayableAsset::get_position()
extern void AnimationPlayableAsset_get_position_mC75DAA87C62AE6BC462492531727C2E45EC150F0 (void);
// 0x00000017 System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_position(UnityEngine.Vector3)
extern void AnimationPlayableAsset_set_position_mF1539D8E2BCDC4BFABF2D1683EAD9AE7A91B4DE3 (void);
// 0x00000018 UnityEngine.Quaternion UnityEngine.Timeline.AnimationPlayableAsset::get_rotation()
extern void AnimationPlayableAsset_get_rotation_m1DBB59B5F15442C5CEA8F76CA8B23398459D32D8 (void);
// 0x00000019 System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_rotation(UnityEngine.Quaternion)
extern void AnimationPlayableAsset_set_rotation_mEE464480D66C9C564AC035507554C340E013EB8C (void);
// 0x0000001A UnityEngine.Vector3 UnityEngine.Timeline.AnimationPlayableAsset::get_eulerAngles()
extern void AnimationPlayableAsset_get_eulerAngles_m1FAFD32D61627D433AC648EDB670431434372123 (void);
// 0x0000001B System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_eulerAngles(UnityEngine.Vector3)
extern void AnimationPlayableAsset_set_eulerAngles_mEFBE1E1450804D8A5C2C44D276178B01CBA74F8A (void);
// 0x0000001C System.Boolean UnityEngine.Timeline.AnimationPlayableAsset::get_useTrackMatchFields()
extern void AnimationPlayableAsset_get_useTrackMatchFields_m3CA7C4CD0E3AE6B315028847FD86485139BB1DB2 (void);
// 0x0000001D System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_useTrackMatchFields(System.Boolean)
extern void AnimationPlayableAsset_set_useTrackMatchFields_m020978DC261F7FBA5BE26BF2E3CCFFE79A7DA27C (void);
// 0x0000001E UnityEngine.Timeline.MatchTargetFields UnityEngine.Timeline.AnimationPlayableAsset::get_matchTargetFields()
extern void AnimationPlayableAsset_get_matchTargetFields_m65CDF95B5D21B931336CDD663309ADC226C666F6 (void);
// 0x0000001F System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_matchTargetFields(UnityEngine.Timeline.MatchTargetFields)
extern void AnimationPlayableAsset_set_matchTargetFields_mCF18FDE6E3EA660995C822B5678EBB98D374FDB7 (void);
// 0x00000020 System.Boolean UnityEngine.Timeline.AnimationPlayableAsset::get_removeStartOffset()
extern void AnimationPlayableAsset_get_removeStartOffset_m38878C3AEB0FF2C0ABAEC4CF6ACA7E640CF896F9 (void);
// 0x00000021 System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_removeStartOffset(System.Boolean)
extern void AnimationPlayableAsset_set_removeStartOffset_mB16879678AA070E3D8A4C58FBA1267867E20B420 (void);
// 0x00000022 System.Boolean UnityEngine.Timeline.AnimationPlayableAsset::get_applyFootIK()
extern void AnimationPlayableAsset_get_applyFootIK_m3D85EDBC855694E23CA22044F83F3EA9918FF6A7 (void);
// 0x00000023 System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_applyFootIK(System.Boolean)
extern void AnimationPlayableAsset_set_applyFootIK_m2894B7F3E32B0CB12961AEC079B464F5F3195006 (void);
// 0x00000024 UnityEngine.Timeline.AnimationPlayableAsset/LoopMode UnityEngine.Timeline.AnimationPlayableAsset::get_loop()
extern void AnimationPlayableAsset_get_loop_mFB0E127EC34FDCB9E706B864733FB45580281A81 (void);
// 0x00000025 System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_loop(UnityEngine.Timeline.AnimationPlayableAsset/LoopMode)
extern void AnimationPlayableAsset_set_loop_mC3AF990F0EDBFF52B6D0C17234144B1999471B6E (void);
// 0x00000026 System.Boolean UnityEngine.Timeline.AnimationPlayableAsset::get_hasRootTransforms()
extern void AnimationPlayableAsset_get_hasRootTransforms_m74DF6202F2F64346F199101D528591FF02AFE48C (void);
// 0x00000027 UnityEngine.Timeline.AppliedOffsetMode UnityEngine.Timeline.AnimationPlayableAsset::get_appliedOffsetMode()
extern void AnimationPlayableAsset_get_appliedOffsetMode_m6A5F22FBF4719ADE8C78BB1A13397E5BA186F908 (void);
// 0x00000028 System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_appliedOffsetMode(UnityEngine.Timeline.AppliedOffsetMode)
extern void AnimationPlayableAsset_set_appliedOffsetMode_m389C789650423968CF01A0830766D2346F2202D1 (void);
// 0x00000029 UnityEngine.AnimationClip UnityEngine.Timeline.AnimationPlayableAsset::get_clip()
extern void AnimationPlayableAsset_get_clip_m0170771CEBEA44040F1857332846B5E12183AF8E (void);
// 0x0000002A System.Void UnityEngine.Timeline.AnimationPlayableAsset::set_clip(UnityEngine.AnimationClip)
extern void AnimationPlayableAsset_set_clip_mF2E641EB15E80990B4587625F199F681FBFAE752 (void);
// 0x0000002B System.Double UnityEngine.Timeline.AnimationPlayableAsset::get_duration()
extern void AnimationPlayableAsset_get_duration_m7FC1BA9D27EAFC31E19C71EB0AF485A955D8E615 (void);
// 0x0000002C System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.AnimationPlayableAsset::get_outputs()
extern void AnimationPlayableAsset_get_outputs_mD795879A17D0640EDFB055B39B7C832136E216DA (void);
// 0x0000002D UnityEngine.Playables.Playable UnityEngine.Timeline.AnimationPlayableAsset::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject)
extern void AnimationPlayableAsset_CreatePlayable_mAC380311FD71AB8C793327307A1BCBE8CF152F29 (void);
// 0x0000002E UnityEngine.Playables.Playable UnityEngine.Timeline.AnimationPlayableAsset::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.AnimationClip,UnityEngine.Vector3,UnityEngine.Vector3,System.Boolean,UnityEngine.Timeline.AppliedOffsetMode,System.Boolean,UnityEngine.Timeline.AnimationPlayableAsset/LoopMode)
extern void AnimationPlayableAsset_CreatePlayable_m8B11A8EAC431FCD5E878FABCB683A4F9B74A6998 (void);
// 0x0000002F System.Boolean UnityEngine.Timeline.AnimationPlayableAsset::ShouldApplyOffset(UnityEngine.Timeline.AppliedOffsetMode,UnityEngine.AnimationClip)
extern void AnimationPlayableAsset_ShouldApplyOffset_m7D958577429B51630CAD52246A957F9BD850119D (void);
// 0x00000030 System.Boolean UnityEngine.Timeline.AnimationPlayableAsset::ShouldApplyScaleRemove(UnityEngine.Timeline.AppliedOffsetMode)
extern void AnimationPlayableAsset_ShouldApplyScaleRemove_m99FB3C3471F9B6C7A5E595308EF4ABCDB2E63841 (void);
// 0x00000031 UnityEngine.Timeline.ClipCaps UnityEngine.Timeline.AnimationPlayableAsset::get_clipCaps()
extern void AnimationPlayableAsset_get_clipCaps_m2BAF77E319A9EE382FF20AB13A56907312C064F8 (void);
// 0x00000032 System.Void UnityEngine.Timeline.AnimationPlayableAsset::ResetOffsets()
extern void AnimationPlayableAsset_ResetOffsets_m8DC6D73742DA551BB81E5BDAAC2F186D045D0EC4 (void);
// 0x00000033 System.Void UnityEngine.Timeline.AnimationPlayableAsset::GatherProperties(UnityEngine.Playables.PlayableDirector,UnityEngine.Timeline.IPropertyCollector)
extern void AnimationPlayableAsset_GatherProperties_mB16CF0B019B7CF063D76CA4B69A51C227EB24EF0 (void);
// 0x00000034 System.Boolean UnityEngine.Timeline.AnimationPlayableAsset::HasRootTransforms(UnityEngine.AnimationClip)
extern void AnimationPlayableAsset_HasRootTransforms_mF8339B890A9F6875852E02AA6FE7EBA7F2CE8161 (void);
// 0x00000035 System.Void UnityEngine.Timeline.AnimationPlayableAsset::UnityEngine.ISerializationCallbackReceiver.OnBeforeSerialize()
extern void AnimationPlayableAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mE7288B1EA8E582C2661277C75FDE80D8B6CFEBC1 (void);
// 0x00000036 System.Void UnityEngine.Timeline.AnimationPlayableAsset::UnityEngine.ISerializationCallbackReceiver.OnAfterDeserialize()
extern void AnimationPlayableAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mA584D2BD183C657C1DB990C055CE79E3880C88E2 (void);
// 0x00000037 System.Void UnityEngine.Timeline.AnimationPlayableAsset::OnUpgradeFromVersion(System.Int32)
extern void AnimationPlayableAsset_OnUpgradeFromVersion_mE19EC4FA01C0D93E8FAC4E4D56FC3B5DBB2C2354 (void);
// 0x00000038 System.Void UnityEngine.Timeline.AnimationPlayableAsset::.ctor()
extern void AnimationPlayableAsset__ctor_m6F298BFCD42F32F57484C3329B147586ECBF141B (void);
// 0x00000039 System.Void UnityEngine.Timeline.AnimationPlayableAsset::.cctor()
extern void AnimationPlayableAsset__cctor_m1748B5EB189D81582B97A7BBD370D03960F1FD7D (void);
// 0x0000003A System.Void UnityEngine.Timeline.AnimationPlayableAsset/AnimationPlayableAssetUpgrade::ConvertRotationToEuler(UnityEngine.Timeline.AnimationPlayableAsset)
extern void AnimationPlayableAssetUpgrade_ConvertRotationToEuler_mB851991DE0D6624E9C49487DFA8630FA6AC08491 (void);
// 0x0000003B System.Void UnityEngine.Timeline.AnimationPlayableAsset/<get_outputs>d__45::.ctor(System.Int32)
extern void U3Cget_outputsU3Ed__45__ctor_m0E2DA3AD05663EA90B980D7150DDAD589F0336DA (void);
// 0x0000003C System.Void UnityEngine.Timeline.AnimationPlayableAsset/<get_outputs>d__45::System.IDisposable.Dispose()
extern void U3Cget_outputsU3Ed__45_System_IDisposable_Dispose_mA7F31F3D37807CE6BA6B290B62C93BEE16B0A6D7 (void);
// 0x0000003D System.Boolean UnityEngine.Timeline.AnimationPlayableAsset/<get_outputs>d__45::MoveNext()
extern void U3Cget_outputsU3Ed__45_MoveNext_mF982F762F9D3069822C9C69545FA6E439F6F1DC8 (void);
// 0x0000003E UnityEngine.Playables.PlayableBinding UnityEngine.Timeline.AnimationPlayableAsset/<get_outputs>d__45::System.Collections.Generic.IEnumerator<UnityEngine.Playables.PlayableBinding>.get_Current()
extern void U3Cget_outputsU3Ed__45_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m7E523BBDF4F714C6737AC3D2709BB75FCF0ED93E (void);
// 0x0000003F System.Void UnityEngine.Timeline.AnimationPlayableAsset/<get_outputs>d__45::System.Collections.IEnumerator.Reset()
extern void U3Cget_outputsU3Ed__45_System_Collections_IEnumerator_Reset_mC8FF472CBE1E59FE4E740083CA6D09ACF0354C0F (void);
// 0x00000040 System.Object UnityEngine.Timeline.AnimationPlayableAsset/<get_outputs>d__45::System.Collections.IEnumerator.get_Current()
extern void U3Cget_outputsU3Ed__45_System_Collections_IEnumerator_get_Current_mBFDCDFF0341D56EEF2ACE33B8504A53CECE1F7AE (void);
// 0x00000041 System.Collections.Generic.IEnumerator`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.AnimationPlayableAsset/<get_outputs>d__45::System.Collections.Generic.IEnumerable<UnityEngine.Playables.PlayableBinding>.GetEnumerator()
extern void U3Cget_outputsU3Ed__45_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m89F51E97B14897204A10EB33B1DF48AD64260492 (void);
// 0x00000042 System.Collections.IEnumerator UnityEngine.Timeline.AnimationPlayableAsset/<get_outputs>d__45::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_outputsU3Ed__45_System_Collections_IEnumerable_GetEnumerator_m571AD1499FE97BA88AC1996D6DBCB5FB81EA6B64 (void);
// 0x00000043 System.Void UnityEngine.Timeline.AnimationPreviewUpdateCallback::.ctor(UnityEngine.Animations.AnimationPlayableOutput)
extern void AnimationPreviewUpdateCallback__ctor_m9A81A41A73B25FFB193B05DB41AAD480B5019302 (void);
// 0x00000044 System.Void UnityEngine.Timeline.AnimationPreviewUpdateCallback::Evaluate()
extern void AnimationPreviewUpdateCallback_Evaluate_m4859735BDA333DF67BA53C5157909003103DC4FF (void);
// 0x00000045 System.Void UnityEngine.Timeline.AnimationPreviewUpdateCallback::FetchPreviewComponents()
extern void AnimationPreviewUpdateCallback_FetchPreviewComponents_mF1C66A832544073C8DFA760DB6F1910DED0BE2D2 (void);
// 0x00000046 System.Boolean UnityEngine.Timeline.MatchTargetFieldConstants::HasAny(UnityEngine.Timeline.MatchTargetFields,UnityEngine.Timeline.MatchTargetFields)
extern void MatchTargetFieldConstants_HasAny_m73744E801BA5F758254854BF5283EE4A8BFA13B4 (void);
// 0x00000047 UnityEngine.Timeline.MatchTargetFields UnityEngine.Timeline.MatchTargetFieldConstants::Toggle(UnityEngine.Timeline.MatchTargetFields,UnityEngine.Timeline.MatchTargetFields)
extern void MatchTargetFieldConstants_Toggle_m649C32A5427F1B9959B3D58468956DF2FBE237DD (void);
// 0x00000048 System.Void UnityEngine.Timeline.MatchTargetFieldConstants::.cctor()
extern void MatchTargetFieldConstants__cctor_mC761D8CE72745FF29695B219E52D3AF62D2DC5BC (void);
// 0x00000049 UnityEngine.Vector3 UnityEngine.Timeline.AnimationTrack::get_position()
extern void AnimationTrack_get_position_mBF745D40410A1F530116319EDA2ED612851C863E (void);
// 0x0000004A System.Void UnityEngine.Timeline.AnimationTrack::set_position(UnityEngine.Vector3)
extern void AnimationTrack_set_position_m58583D926C865297D1D248FF3DB33E9A101115F6 (void);
// 0x0000004B UnityEngine.Quaternion UnityEngine.Timeline.AnimationTrack::get_rotation()
extern void AnimationTrack_get_rotation_m5A3EC08F5E1AD62FC72BA69294001BAC42C15251 (void);
// 0x0000004C System.Void UnityEngine.Timeline.AnimationTrack::set_rotation(UnityEngine.Quaternion)
extern void AnimationTrack_set_rotation_m372BEF805EE2FEA03E5766C853CD934FADAF6FFD (void);
// 0x0000004D UnityEngine.Vector3 UnityEngine.Timeline.AnimationTrack::get_eulerAngles()
extern void AnimationTrack_get_eulerAngles_m9EDE81D8FD7675470DF4CFA620D911019B56C768 (void);
// 0x0000004E System.Void UnityEngine.Timeline.AnimationTrack::set_eulerAngles(UnityEngine.Vector3)
extern void AnimationTrack_set_eulerAngles_mCF06238E5C41BE05161163DCA33ED817BF1E24CD (void);
// 0x0000004F System.Boolean UnityEngine.Timeline.AnimationTrack::get_applyOffsets()
extern void AnimationTrack_get_applyOffsets_m10D70F0C85FB9C462B4659813EDB37BF84D86B9A (void);
// 0x00000050 System.Void UnityEngine.Timeline.AnimationTrack::set_applyOffsets(System.Boolean)
extern void AnimationTrack_set_applyOffsets_mF644A614EB6FB30B8604D917BA8F4A710983A9DE (void);
// 0x00000051 UnityEngine.Timeline.TrackOffset UnityEngine.Timeline.AnimationTrack::get_trackOffset()
extern void AnimationTrack_get_trackOffset_m8D91B82F85D98F276E45FB805628232278AF2D2D (void);
// 0x00000052 System.Void UnityEngine.Timeline.AnimationTrack::set_trackOffset(UnityEngine.Timeline.TrackOffset)
extern void AnimationTrack_set_trackOffset_m66B617D407D7D8CE17EDAD849DEC34FD1E0B1CE5 (void);
// 0x00000053 UnityEngine.Timeline.MatchTargetFields UnityEngine.Timeline.AnimationTrack::get_matchTargetFields()
extern void AnimationTrack_get_matchTargetFields_m130ABF7AA0D2F38D40127029CA02993A5FA998F3 (void);
// 0x00000054 System.Void UnityEngine.Timeline.AnimationTrack::set_matchTargetFields(UnityEngine.Timeline.MatchTargetFields)
extern void AnimationTrack_set_matchTargetFields_m14ED03793080CDD4BDF4D1B91418C430853D9330 (void);
// 0x00000055 UnityEngine.AnimationClip UnityEngine.Timeline.AnimationTrack::get_infiniteClip()
extern void AnimationTrack_get_infiniteClip_mE55BF71562803B5C849A335584AE526A2747E5B2 (void);
// 0x00000056 System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClip(UnityEngine.AnimationClip)
extern void AnimationTrack_set_infiniteClip_mA700EED208399A4DCA3314E3F848BBE698DAB6C4 (void);
// 0x00000057 System.Boolean UnityEngine.Timeline.AnimationTrack::get_infiniteClipRemoveOffset()
extern void AnimationTrack_get_infiniteClipRemoveOffset_m18DFC76B9AFEC35654AF299A62264E925202146B (void);
// 0x00000058 System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClipRemoveOffset(System.Boolean)
extern void AnimationTrack_set_infiniteClipRemoveOffset_m7999F05C17EE0524CCC197732CADB8927A0D5454 (void);
// 0x00000059 UnityEngine.AvatarMask UnityEngine.Timeline.AnimationTrack::get_avatarMask()
extern void AnimationTrack_get_avatarMask_m4E6BD1DAC9D92FD6CD663DADEE525A158CFE4F01 (void);
// 0x0000005A System.Void UnityEngine.Timeline.AnimationTrack::set_avatarMask(UnityEngine.AvatarMask)
extern void AnimationTrack_set_avatarMask_m97BFF1B75D7E891C249CCE0917F1CBCB886FD30D (void);
// 0x0000005B System.Boolean UnityEngine.Timeline.AnimationTrack::get_applyAvatarMask()
extern void AnimationTrack_get_applyAvatarMask_m2FCA8FA363B449E4420E8492F92E5270A2D4FA7E (void);
// 0x0000005C System.Void UnityEngine.Timeline.AnimationTrack::set_applyAvatarMask(System.Boolean)
extern void AnimationTrack_set_applyAvatarMask_mEAEA18A18FB74A33D808B94F20C01A2D880A8606 (void);
// 0x0000005D System.Boolean UnityEngine.Timeline.AnimationTrack::CanCompileClips()
extern void AnimationTrack_CanCompileClips_m67F41F2F20E51CB999957ACD3D9C31B799EC3026 (void);
// 0x0000005E System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.AnimationTrack::get_outputs()
extern void AnimationTrack_get_outputs_mF3776BF93312A616ADC9D18F061AD972626F0EB7 (void);
// 0x0000005F System.Boolean UnityEngine.Timeline.AnimationTrack::get_inClipMode()
extern void AnimationTrack_get_inClipMode_m0559987652D8B45851CF6EE37AAE259156007271 (void);
// 0x00000060 UnityEngine.Vector3 UnityEngine.Timeline.AnimationTrack::get_infiniteClipOffsetPosition()
extern void AnimationTrack_get_infiniteClipOffsetPosition_m23355191742B9EF9877193DCD3C77758D9435666 (void);
// 0x00000061 System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClipOffsetPosition(UnityEngine.Vector3)
extern void AnimationTrack_set_infiniteClipOffsetPosition_m74A78994C38BE22C6AD33C5EF4CD710CCAFDD166 (void);
// 0x00000062 UnityEngine.Quaternion UnityEngine.Timeline.AnimationTrack::get_infiniteClipOffsetRotation()
extern void AnimationTrack_get_infiniteClipOffsetRotation_m3A152B45D0F99F716EEFA25BDF0A932FACB02052 (void);
// 0x00000063 System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClipOffsetRotation(UnityEngine.Quaternion)
extern void AnimationTrack_set_infiniteClipOffsetRotation_m0B8FF5AB8D6F4EE92C022D40510C5C41AD3896C2 (void);
// 0x00000064 UnityEngine.Vector3 UnityEngine.Timeline.AnimationTrack::get_infiniteClipOffsetEulerAngles()
extern void AnimationTrack_get_infiniteClipOffsetEulerAngles_m0EF73054860F1F705E07F5729C9CB1CE0D4AA6B9 (void);
// 0x00000065 System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClipOffsetEulerAngles(UnityEngine.Vector3)
extern void AnimationTrack_set_infiniteClipOffsetEulerAngles_m958891CA74BDFD73A39E2427084FA3777939C29E (void);
// 0x00000066 System.Boolean UnityEngine.Timeline.AnimationTrack::get_infiniteClipApplyFootIK()
extern void AnimationTrack_get_infiniteClipApplyFootIK_m813FEE3250C60BFEF060210215BD68E3C5CC29CB (void);
// 0x00000067 System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClipApplyFootIK(System.Boolean)
extern void AnimationTrack_set_infiniteClipApplyFootIK_mCC194F75CD06FA7E9F0EE890E7A2AADA9C387BAB (void);
// 0x00000068 System.Double UnityEngine.Timeline.AnimationTrack::get_infiniteClipTimeOffset()
extern void AnimationTrack_get_infiniteClipTimeOffset_mC4376105C76EAB9E8139C426D3BD218ED21B4A7C (void);
// 0x00000069 System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClipTimeOffset(System.Double)
extern void AnimationTrack_set_infiniteClipTimeOffset_m93F31C8AEFE3DE5FE275F7EB250FA98F5978AD3A (void);
// 0x0000006A UnityEngine.Timeline.TimelineClip/ClipExtrapolation UnityEngine.Timeline.AnimationTrack::get_infiniteClipPreExtrapolation()
extern void AnimationTrack_get_infiniteClipPreExtrapolation_m4B4906080C16E2D7C07B409787363735F8C8AB85 (void);
// 0x0000006B System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClipPreExtrapolation(UnityEngine.Timeline.TimelineClip/ClipExtrapolation)
extern void AnimationTrack_set_infiniteClipPreExtrapolation_m5A1D2B6511FB01EEDF0405DB5914F43D1289EDE3 (void);
// 0x0000006C UnityEngine.Timeline.TimelineClip/ClipExtrapolation UnityEngine.Timeline.AnimationTrack::get_infiniteClipPostExtrapolation()
extern void AnimationTrack_get_infiniteClipPostExtrapolation_mE68867202ACB337F11C12FD75C196278FFEA34D9 (void);
// 0x0000006D System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClipPostExtrapolation(UnityEngine.Timeline.TimelineClip/ClipExtrapolation)
extern void AnimationTrack_set_infiniteClipPostExtrapolation_m8197868300D197E73BFB72D95EEC0CA3F3E69D6E (void);
// 0x0000006E UnityEngine.Timeline.AnimationPlayableAsset/LoopMode UnityEngine.Timeline.AnimationTrack::get_infiniteClipLoop()
extern void AnimationTrack_get_infiniteClipLoop_m0076C92D7935227835C007059EC228D6ACA5205C (void);
// 0x0000006F System.Void UnityEngine.Timeline.AnimationTrack::set_infiniteClipLoop(UnityEngine.Timeline.AnimationPlayableAsset/LoopMode)
extern void AnimationTrack_set_infiniteClipLoop_mE52F35E3A552CD510D873A9A1865D474974B2750 (void);
// 0x00000070 System.Void UnityEngine.Timeline.AnimationTrack::ResetOffsets()
extern void AnimationTrack_ResetOffsets_m9CA8762D3880419B7CA71E966228FF801A0F47A4 (void);
// 0x00000071 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.AnimationTrack::CreateClip(UnityEngine.AnimationClip)
extern void AnimationTrack_CreateClip_mC2014488815D2C34B95F739D82F9A93A195FD919 (void);
// 0x00000072 System.Void UnityEngine.Timeline.AnimationTrack::CreateInfiniteClip(System.String)
extern void AnimationTrack_CreateInfiniteClip_mC2EB76F9F2462F3AED209E90018A86DE5D36843B (void);
// 0x00000073 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.AnimationTrack::CreateRecordableClip(System.String)
extern void AnimationTrack_CreateRecordableClip_m2F49C361C87418375F803A4760EDF011A9D960C7 (void);
// 0x00000074 System.Void UnityEngine.Timeline.AnimationTrack::OnCreateClip(UnityEngine.Timeline.TimelineClip)
extern void AnimationTrack_OnCreateClip_m5A7462347F4CAA6EC0B248F534F45C89B8F38786 (void);
// 0x00000075 System.Int32 UnityEngine.Timeline.AnimationTrack::CalculateItemsHash()
extern void AnimationTrack_CalculateItemsHash_m69D9B8299B8E4085DE184A3FC278BB33C6CECF70 (void);
// 0x00000076 System.Void UnityEngine.Timeline.AnimationTrack::UpdateClipOffsets()
extern void AnimationTrack_UpdateClipOffsets_m0DE8CEBC4F96E1F40669DC8732F2E04EBADA1331 (void);
// 0x00000077 UnityEngine.Playables.Playable UnityEngine.Timeline.AnimationTrack::CompileTrackPlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.Timeline.AnimationTrack,UnityEngine.GameObject,UnityEngine.Timeline.IntervalTree`1<UnityEngine.Timeline.RuntimeElement>,UnityEngine.Timeline.AppliedOffsetMode)
extern void AnimationTrack_CompileTrackPlayable_m8AEA486513BF42BC4682217FCB37A61218B68094 (void);
// 0x00000078 UnityEngine.Playables.Playable UnityEngine.Timeline.AnimationTrack::UnityEngine.Timeline.ILayerable.CreateLayerMixer(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,System.Int32)
extern void AnimationTrack_UnityEngine_Timeline_ILayerable_CreateLayerMixer_m2C90244DFA117930E0911876688435A62CF9C628 (void);
// 0x00000079 UnityEngine.Playables.Playable UnityEngine.Timeline.AnimationTrack::CreateMixerPlayableGraph(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,UnityEngine.Timeline.IntervalTree`1<UnityEngine.Timeline.RuntimeElement>)
extern void AnimationTrack_CreateMixerPlayableGraph_mC3352DAB3A0AF3D277C78B94D5DC2DB763DFD3F7 (void);
// 0x0000007A System.Int32 UnityEngine.Timeline.AnimationTrack::GetDefaultBlendCount()
extern void AnimationTrack_GetDefaultBlendCount_m34C24E878EB89D3D5B56AECED494D8375E35A64D (void);
// 0x0000007B System.Void UnityEngine.Timeline.AnimationTrack::AttachDefaultBlend(UnityEngine.Playables.PlayableGraph,UnityEngine.Animations.AnimationLayerMixerPlayable,System.Boolean)
extern void AnimationTrack_AttachDefaultBlend_mAF67FD6982E9EAA8CAFCB0106C7F324547400DFC (void);
// 0x0000007C UnityEngine.Playables.Playable UnityEngine.Timeline.AnimationTrack::AttachOffsetPlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.Playables.Playable,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void AnimationTrack_AttachOffsetPlayable_m854FE97C64D490D94150A604554C01A9433813B8 (void);
// 0x0000007D System.Boolean UnityEngine.Timeline.AnimationTrack::RequiresMotionXPlayable(UnityEngine.Timeline.AppliedOffsetMode,UnityEngine.GameObject)
extern void AnimationTrack_RequiresMotionXPlayable_m8BF0F53DE406934214E0E402295F8E432E02744C (void);
// 0x0000007E System.Boolean UnityEngine.Timeline.AnimationTrack::UsesAbsoluteMotion(UnityEngine.Timeline.AppliedOffsetMode)
extern void AnimationTrack_UsesAbsoluteMotion_m305667FA27FD4DFC54406F5A21A4344A069EAF4C (void);
// 0x0000007F System.Boolean UnityEngine.Timeline.AnimationTrack::HasController(UnityEngine.GameObject)
extern void AnimationTrack_HasController_m9DA394CFD76B80E53A80EC26FEEC9F1769BA6684 (void);
// 0x00000080 UnityEngine.Animator UnityEngine.Timeline.AnimationTrack::GetBinding(UnityEngine.Playables.PlayableDirector)
extern void AnimationTrack_GetBinding_mFA6516C67603F256A7306024BB0DA86496F42B43 (void);
// 0x00000081 UnityEngine.Animations.AnimationLayerMixerPlayable UnityEngine.Timeline.AnimationTrack::CreateGroupMixer(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,System.Int32)
extern void AnimationTrack_CreateGroupMixer_mF2B248685103E2FBA973109155FE0D5153946B8A (void);
// 0x00000082 UnityEngine.Playables.Playable UnityEngine.Timeline.AnimationTrack::CreateInfiniteTrackPlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,UnityEngine.Timeline.IntervalTree`1<UnityEngine.Timeline.RuntimeElement>,UnityEngine.Timeline.AppliedOffsetMode)
extern void AnimationTrack_CreateInfiniteTrackPlayable_m7951EDDA36CE14693AFFF9C460C0FC3E5B732A88 (void);
// 0x00000083 UnityEngine.Playables.Playable UnityEngine.Timeline.AnimationTrack::ApplyTrackOffset(UnityEngine.Playables.PlayableGraph,UnityEngine.Playables.Playable,UnityEngine.GameObject,UnityEngine.Timeline.AppliedOffsetMode)
extern void AnimationTrack_ApplyTrackOffset_m7ACA50839B41B3AECEA66E60986AE71E9FA3F7A0 (void);
// 0x00000084 System.Void UnityEngine.Timeline.AnimationTrack::GetEvaluationTime(System.Double&,System.Double&)
extern void AnimationTrack_GetEvaluationTime_mE2271656B917685A4108E734F77ED34FC75EC3AD (void);
// 0x00000085 System.Void UnityEngine.Timeline.AnimationTrack::GetSequenceTime(System.Double&,System.Double&)
extern void AnimationTrack_GetSequenceTime_m366C5486DCF37C3A41612B08749F063F491ED593 (void);
// 0x00000086 System.Void UnityEngine.Timeline.AnimationTrack::AssignAnimationClip(UnityEngine.Timeline.TimelineClip,UnityEngine.AnimationClip)
extern void AnimationTrack_AssignAnimationClip_m26DB250244CE6240EF4F946A4D93F311D0D1F367 (void);
// 0x00000087 System.Void UnityEngine.Timeline.AnimationTrack::GatherProperties(UnityEngine.Playables.PlayableDirector,UnityEngine.Timeline.IPropertyCollector)
extern void AnimationTrack_GatherProperties_m5FE1175C9AE614E6878B582CEF3DE396837B54C2 (void);
// 0x00000088 System.Void UnityEngine.Timeline.AnimationTrack::GetAnimationClips(System.Collections.Generic.List`1<UnityEngine.AnimationClip>)
extern void AnimationTrack_GetAnimationClips_m7459AE8A7BE31D25B0625297A0C94173042A0207 (void);
// 0x00000089 UnityEngine.Timeline.AppliedOffsetMode UnityEngine.Timeline.AnimationTrack::GetOffsetMode(UnityEngine.GameObject,System.Boolean)
extern void AnimationTrack_GetOffsetMode_m8D2EA6334F72FE079612C5B71B745CAF91111C9D (void);
// 0x0000008A System.Boolean UnityEngine.Timeline.AnimationTrack::IsRootTransformDisabledByMask(UnityEngine.GameObject,UnityEngine.Transform)
extern void AnimationTrack_IsRootTransformDisabledByMask_m130CFADD77EB830E9E7EA6DBBE88EFEE73E1D0C7 (void);
// 0x0000008B UnityEngine.Transform UnityEngine.Timeline.AnimationTrack::GetGenericRootNode(UnityEngine.GameObject)
extern void AnimationTrack_GetGenericRootNode_m40F3B83DE0D88C67592549EBDFADCD0168D63C3A (void);
// 0x0000008C System.Boolean UnityEngine.Timeline.AnimationTrack::AnimatesRootTransform()
extern void AnimationTrack_AnimatesRootTransform_m2D168DCB100F72EFC128C2DEC6B068E2CC216F1F (void);
// 0x0000008D UnityEngine.Transform UnityEngine.Timeline.AnimationTrack::FindInHierarchyBreadthFirst(UnityEngine.Transform,System.String)
extern void AnimationTrack_FindInHierarchyBreadthFirst_m021FFC49FDED402415A469E33CC690FFCC7AD1CB (void);
// 0x0000008E UnityEngine.Vector3 UnityEngine.Timeline.AnimationTrack::get_openClipOffsetPosition()
extern void AnimationTrack_get_openClipOffsetPosition_m75E1CFC9D51546A9F24A0EB998B6E87794C3A705 (void);
// 0x0000008F System.Void UnityEngine.Timeline.AnimationTrack::set_openClipOffsetPosition(UnityEngine.Vector3)
extern void AnimationTrack_set_openClipOffsetPosition_m9752610280CE6027BAEE683C36C9D290B442A6DB (void);
// 0x00000090 UnityEngine.Quaternion UnityEngine.Timeline.AnimationTrack::get_openClipOffsetRotation()
extern void AnimationTrack_get_openClipOffsetRotation_m10B5894DD11A0AF08D99594D1FB459B6362B9303 (void);
// 0x00000091 System.Void UnityEngine.Timeline.AnimationTrack::set_openClipOffsetRotation(UnityEngine.Quaternion)
extern void AnimationTrack_set_openClipOffsetRotation_m1D1B9B56375DE34C8BEA905DCC0299B39BFC7B02 (void);
// 0x00000092 UnityEngine.Vector3 UnityEngine.Timeline.AnimationTrack::get_openClipOffsetEulerAngles()
extern void AnimationTrack_get_openClipOffsetEulerAngles_m355BD7957BCD76AC87769CED0927FD9728C3F2EA (void);
// 0x00000093 System.Void UnityEngine.Timeline.AnimationTrack::set_openClipOffsetEulerAngles(UnityEngine.Vector3)
extern void AnimationTrack_set_openClipOffsetEulerAngles_m562570438DFB431C65AB322D1753D6210DB62787 (void);
// 0x00000094 UnityEngine.Timeline.TimelineClip/ClipExtrapolation UnityEngine.Timeline.AnimationTrack::get_openClipPreExtrapolation()
extern void AnimationTrack_get_openClipPreExtrapolation_m27E1C2DE521D2F5B0AB5569695CC1C060416FDB4 (void);
// 0x00000095 System.Void UnityEngine.Timeline.AnimationTrack::set_openClipPreExtrapolation(UnityEngine.Timeline.TimelineClip/ClipExtrapolation)
extern void AnimationTrack_set_openClipPreExtrapolation_mC2D775230A229A66B1E98C131FAE66C2312E4BC3 (void);
// 0x00000096 UnityEngine.Timeline.TimelineClip/ClipExtrapolation UnityEngine.Timeline.AnimationTrack::get_openClipPostExtrapolation()
extern void AnimationTrack_get_openClipPostExtrapolation_m81FD4E8B992C6758D21790E77A57DAFB2CFB903A (void);
// 0x00000097 System.Void UnityEngine.Timeline.AnimationTrack::set_openClipPostExtrapolation(UnityEngine.Timeline.TimelineClip/ClipExtrapolation)
extern void AnimationTrack_set_openClipPostExtrapolation_m1C35F5DC4A86ADA4548AFAFA8323A118433B91FC (void);
// 0x00000098 System.Void UnityEngine.Timeline.AnimationTrack::OnUpgradeFromVersion(System.Int32)
extern void AnimationTrack_OnUpgradeFromVersion_mB41D3B6A330F4AA2FE59E61632E4B1F99C2866DF (void);
// 0x00000099 System.Void UnityEngine.Timeline.AnimationTrack::.ctor()
extern void AnimationTrack__ctor_mDB5857315630BBECBD1CD59097456E0F331B9264 (void);
// 0x0000009A System.Void UnityEngine.Timeline.AnimationTrack::.cctor()
extern void AnimationTrack__cctor_mF7E0C93F99D88D2E284763DB25C116DA566C1135 (void);
// 0x0000009B System.Void UnityEngine.Timeline.AnimationTrack/AnimationTrackUpgrade::ConvertRotationsToEuler(UnityEngine.Timeline.AnimationTrack)
extern void AnimationTrackUpgrade_ConvertRotationsToEuler_m4ACA9D918164FEEDBEC2881DE13C779094D253D5 (void);
// 0x0000009C System.Void UnityEngine.Timeline.AnimationTrack/AnimationTrackUpgrade::ConvertRootMotion(UnityEngine.Timeline.AnimationTrack)
extern void AnimationTrackUpgrade_ConvertRootMotion_m54DF7747856D06D2179EB95642D299E5D6A97A21 (void);
// 0x0000009D System.Void UnityEngine.Timeline.AnimationTrack/AnimationTrackUpgrade::ConvertInfiniteTrack(UnityEngine.Timeline.AnimationTrack)
extern void AnimationTrackUpgrade_ConvertInfiniteTrack_m4006E7259789D3B8829ABC5700DD2828C3EB2A6F (void);
// 0x0000009E System.Void UnityEngine.Timeline.AnimationTrack/<get_outputs>d__49::.ctor(System.Int32)
extern void U3Cget_outputsU3Ed__49__ctor_mD93688EDAA61ADD8474DC977257C4865A2EC6389 (void);
// 0x0000009F System.Void UnityEngine.Timeline.AnimationTrack/<get_outputs>d__49::System.IDisposable.Dispose()
extern void U3Cget_outputsU3Ed__49_System_IDisposable_Dispose_m607FCCC43C82ECC93F674CCB5415929336502C51 (void);
// 0x000000A0 System.Boolean UnityEngine.Timeline.AnimationTrack/<get_outputs>d__49::MoveNext()
extern void U3Cget_outputsU3Ed__49_MoveNext_m7BDA8087249FD461975437E56C9BFE7B80C8A099 (void);
// 0x000000A1 UnityEngine.Playables.PlayableBinding UnityEngine.Timeline.AnimationTrack/<get_outputs>d__49::System.Collections.Generic.IEnumerator<UnityEngine.Playables.PlayableBinding>.get_Current()
extern void U3Cget_outputsU3Ed__49_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m8665A050400E9DA6702260117B51F8609806ECBE (void);
// 0x000000A2 System.Void UnityEngine.Timeline.AnimationTrack/<get_outputs>d__49::System.Collections.IEnumerator.Reset()
extern void U3Cget_outputsU3Ed__49_System_Collections_IEnumerator_Reset_m5D524429CEE49B55F5EE0E3015806B835110C113 (void);
// 0x000000A3 System.Object UnityEngine.Timeline.AnimationTrack/<get_outputs>d__49::System.Collections.IEnumerator.get_Current()
extern void U3Cget_outputsU3Ed__49_System_Collections_IEnumerator_get_Current_mF8DC66EBBBD2A455C22F3B8B229EE3FC09ABC96F (void);
// 0x000000A4 System.Collections.Generic.IEnumerator`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.AnimationTrack/<get_outputs>d__49::System.Collections.Generic.IEnumerable<UnityEngine.Playables.PlayableBinding>.GetEnumerator()
extern void U3Cget_outputsU3Ed__49_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m3B62F5C3F8AF686D249000D59B0052197BECB161 (void);
// 0x000000A5 System.Collections.IEnumerator UnityEngine.Timeline.AnimationTrack/<get_outputs>d__49::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_outputsU3Ed__49_System_Collections_IEnumerable_GetEnumerator_mFC98EDB93155902AC6052BEAEBF678837BD62B2C (void);
// 0x000000A6 UnityEngine.AnimationClip UnityEngine.Timeline.ICurvesOwner::get_curves()
// 0x000000A7 System.Boolean UnityEngine.Timeline.ICurvesOwner::get_hasCurves()
// 0x000000A8 System.Double UnityEngine.Timeline.ICurvesOwner::get_duration()
// 0x000000A9 System.Void UnityEngine.Timeline.ICurvesOwner::CreateCurves(System.String)
// 0x000000AA System.String UnityEngine.Timeline.ICurvesOwner::get_defaultCurvesName()
// 0x000000AB UnityEngine.Object UnityEngine.Timeline.ICurvesOwner::get_asset()
// 0x000000AC UnityEngine.Object UnityEngine.Timeline.ICurvesOwner::get_assetOwner()
// 0x000000AD UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.ICurvesOwner::get_targetTrack()
// 0x000000AE System.Void UnityEngine.Timeline.TimelineClip::UpgradeToLatestVersion()
extern void TimelineClip_UpgradeToLatestVersion_m3C367DA0661298065465C583C9D33AC4B63ADF0D (void);
// 0x000000AF System.Void UnityEngine.Timeline.TimelineClip::.ctor(UnityEngine.Timeline.TrackAsset)
extern void TimelineClip__ctor_m69070FAC237B4E3DC873E07AD1C3902E5C031939 (void);
// 0x000000B0 System.Boolean UnityEngine.Timeline.TimelineClip::get_hasPreExtrapolation()
extern void TimelineClip_get_hasPreExtrapolation_m2D086ECAC0C10D46FE58D22AEAA9CC1EAE94C196 (void);
// 0x000000B1 System.Boolean UnityEngine.Timeline.TimelineClip::get_hasPostExtrapolation()
extern void TimelineClip_get_hasPostExtrapolation_mE6565EF983300E526B31AC25C57F8B583C6B7AC6 (void);
// 0x000000B2 System.Double UnityEngine.Timeline.TimelineClip::get_timeScale()
extern void TimelineClip_get_timeScale_mF76C0A2D5CDBF201F2CC01967928CA7FFC261474 (void);
// 0x000000B3 System.Void UnityEngine.Timeline.TimelineClip::set_timeScale(System.Double)
extern void TimelineClip_set_timeScale_m29FAE01EF4CF682E2C82169B9D6976289C2665D0 (void);
// 0x000000B4 System.Double UnityEngine.Timeline.TimelineClip::get_start()
extern void TimelineClip_get_start_m76BB53BEBD6B700D5A4197F72779A321DE55B296 (void);
// 0x000000B5 System.Void UnityEngine.Timeline.TimelineClip::set_start(System.Double)
extern void TimelineClip_set_start_m476586FED5274C2691B71BC75C76E3F471332BF5 (void);
// 0x000000B6 System.Double UnityEngine.Timeline.TimelineClip::get_duration()
extern void TimelineClip_get_duration_m4DC76F051723CC7427813C076B255BA8BB4366F7 (void);
// 0x000000B7 System.Void UnityEngine.Timeline.TimelineClip::set_duration(System.Double)
extern void TimelineClip_set_duration_m1904C6BD6C64F8CC7AD09256F559C8C62AB97001 (void);
// 0x000000B8 System.Double UnityEngine.Timeline.TimelineClip::get_end()
extern void TimelineClip_get_end_m4C3E4DF4B095A1D60694B379EA839A68E3C4217C (void);
// 0x000000B9 System.Double UnityEngine.Timeline.TimelineClip::get_clipIn()
extern void TimelineClip_get_clipIn_m0ABA66BE9CAD32C80313321C963C8FA9AB5FC1EB (void);
// 0x000000BA System.Void UnityEngine.Timeline.TimelineClip::set_clipIn(System.Double)
extern void TimelineClip_set_clipIn_m5ACCEA54D376384ED1264CE7F108BD64F725C67E (void);
// 0x000000BB System.String UnityEngine.Timeline.TimelineClip::get_displayName()
extern void TimelineClip_get_displayName_m61712CDBEA102FB64B5E3464E4420843E1FB7111 (void);
// 0x000000BC System.Void UnityEngine.Timeline.TimelineClip::set_displayName(System.String)
extern void TimelineClip_set_displayName_m5F725FB8B45340748ECFAA870D034C85352F54CD (void);
// 0x000000BD System.Double UnityEngine.Timeline.TimelineClip::get_clipAssetDuration()
extern void TimelineClip_get_clipAssetDuration_mC3509BB4EF5397A55A42E02BE6A6730998B5EAF5 (void);
// 0x000000BE UnityEngine.AnimationClip UnityEngine.Timeline.TimelineClip::get_curves()
extern void TimelineClip_get_curves_m7572F0ACC8D51E605271B6002685740F946FC761 (void);
// 0x000000BF System.Void UnityEngine.Timeline.TimelineClip::set_curves(UnityEngine.AnimationClip)
extern void TimelineClip_set_curves_mDE58D1A741554AF3E95F7BC53625E5D7F3BD212A (void);
// 0x000000C0 System.String UnityEngine.Timeline.TimelineClip::UnityEngine.Timeline.ICurvesOwner.get_defaultCurvesName()
extern void TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_defaultCurvesName_m65DD8FEFE6FC8AADA51EECD8906A9066C7EF6C18 (void);
// 0x000000C1 System.Boolean UnityEngine.Timeline.TimelineClip::get_hasCurves()
extern void TimelineClip_get_hasCurves_mDB6EF3ADD8FF4693992F7EA092F269A6F3631EFD (void);
// 0x000000C2 UnityEngine.Object UnityEngine.Timeline.TimelineClip::get_asset()
extern void TimelineClip_get_asset_m49BF68F5E0C41EBA5145FCA0C97D7146DF016120 (void);
// 0x000000C3 System.Void UnityEngine.Timeline.TimelineClip::set_asset(UnityEngine.Object)
extern void TimelineClip_set_asset_mF8539EA76B6C0F19E2AECBA025C70D605322195E (void);
// 0x000000C4 UnityEngine.Object UnityEngine.Timeline.TimelineClip::UnityEngine.Timeline.ICurvesOwner.get_assetOwner()
extern void TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_assetOwner_m542C93F1E1B0382CCA596359A1A96DAC55539D66 (void);
// 0x000000C5 UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.TimelineClip::UnityEngine.Timeline.ICurvesOwner.get_targetTrack()
extern void TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_targetTrack_m46FB66ACADD2B522822EB7691A4A5F1C976A368F (void);
// 0x000000C6 UnityEngine.Object UnityEngine.Timeline.TimelineClip::get_underlyingAsset()
extern void TimelineClip_get_underlyingAsset_m1DE5682815DA82061499CCF642ECDC9CAA9C1881 (void);
// 0x000000C7 System.Void UnityEngine.Timeline.TimelineClip::set_underlyingAsset(UnityEngine.Object)
extern void TimelineClip_set_underlyingAsset_m02D44F445992B5431831BF7C77466164CA527B85 (void);
// 0x000000C8 UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.TimelineClip::get_parentTrack()
extern void TimelineClip_get_parentTrack_m3E33146230902DCAFAEAAE034F5DCBB70BE03A8C (void);
// 0x000000C9 System.Void UnityEngine.Timeline.TimelineClip::set_parentTrack(UnityEngine.Timeline.TrackAsset)
extern void TimelineClip_set_parentTrack_mC3646EF3C4198254776CFA1E47CFC2636C1BA69E (void);
// 0x000000CA UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.TimelineClip::GetParentTrack()
extern void TimelineClip_GetParentTrack_m560CB13E873CE39EB7E3754B53B1594D2A58C37D (void);
// 0x000000CB System.Void UnityEngine.Timeline.TimelineClip::SetParentTrack_Internal(UnityEngine.Timeline.TrackAsset)
extern void TimelineClip_SetParentTrack_Internal_m39F7D49888E7EBFC36796EAE0C7BE6C263C0FB02 (void);
// 0x000000CC System.Double UnityEngine.Timeline.TimelineClip::get_easeInDuration()
extern void TimelineClip_get_easeInDuration_m171A5C1C8BA1392C4CBC7F5C17A90EAF915A2145 (void);
// 0x000000CD System.Void UnityEngine.Timeline.TimelineClip::set_easeInDuration(System.Double)
extern void TimelineClip_set_easeInDuration_mF310C781E91789DAA8AE5A593C9BA46FC173E01C (void);
// 0x000000CE System.Double UnityEngine.Timeline.TimelineClip::get_easeOutDuration()
extern void TimelineClip_get_easeOutDuration_mAAE6DEE05138F6902199D985A3EE6E26BCBB35EC (void);
// 0x000000CF System.Void UnityEngine.Timeline.TimelineClip::set_easeOutDuration(System.Double)
extern void TimelineClip_set_easeOutDuration_m4666F442BADEAFAC3C8E450BCF987E403D1E42ED (void);
// 0x000000D0 System.Double UnityEngine.Timeline.TimelineClip::get_eastOutTime()
extern void TimelineClip_get_eastOutTime_mE21671B32EE9CB06C994A5B91D5E86741F2BB30B (void);
// 0x000000D1 System.Double UnityEngine.Timeline.TimelineClip::get_easeOutTime()
extern void TimelineClip_get_easeOutTime_mC16D31D13CB3953A0ECE61CCD13381318B6AC115 (void);
// 0x000000D2 System.Double UnityEngine.Timeline.TimelineClip::get_blendInDuration()
extern void TimelineClip_get_blendInDuration_m3A18D7C0942B43C5A171422EDC1D41260C1269D7 (void);
// 0x000000D3 System.Void UnityEngine.Timeline.TimelineClip::set_blendInDuration(System.Double)
extern void TimelineClip_set_blendInDuration_m34F303216992A919EF47D01B874EEEB47C8470E3 (void);
// 0x000000D4 System.Double UnityEngine.Timeline.TimelineClip::get_blendOutDuration()
extern void TimelineClip_get_blendOutDuration_mA1BB945DFE74A6AB5EB112587ED3509CFAF43379 (void);
// 0x000000D5 System.Void UnityEngine.Timeline.TimelineClip::set_blendOutDuration(System.Double)
extern void TimelineClip_set_blendOutDuration_m5013A1CF4593F805143A0E665261EBB0B00298F0 (void);
// 0x000000D6 UnityEngine.Timeline.TimelineClip/BlendCurveMode UnityEngine.Timeline.TimelineClip::get_blendInCurveMode()
extern void TimelineClip_get_blendInCurveMode_m4C612BAD55BB180A24C9FA67A8F9D84C907E170C (void);
// 0x000000D7 System.Void UnityEngine.Timeline.TimelineClip::set_blendInCurveMode(UnityEngine.Timeline.TimelineClip/BlendCurveMode)
extern void TimelineClip_set_blendInCurveMode_m4138FBEEA1484C9DDAA0C859B505E8E20B4DFE8A (void);
// 0x000000D8 UnityEngine.Timeline.TimelineClip/BlendCurveMode UnityEngine.Timeline.TimelineClip::get_blendOutCurveMode()
extern void TimelineClip_get_blendOutCurveMode_m106D929CE951F07CB31BF9DEA7B0D2B1D830F093 (void);
// 0x000000D9 System.Void UnityEngine.Timeline.TimelineClip::set_blendOutCurveMode(UnityEngine.Timeline.TimelineClip/BlendCurveMode)
extern void TimelineClip_set_blendOutCurveMode_m5CBC0E1382775395757B85824BFBB39571241A9E (void);
// 0x000000DA System.Boolean UnityEngine.Timeline.TimelineClip::get_hasBlendIn()
extern void TimelineClip_get_hasBlendIn_m9B9C96AAADD801969D9C97D4130428EEB6B6D368 (void);
// 0x000000DB System.Boolean UnityEngine.Timeline.TimelineClip::get_hasBlendOut()
extern void TimelineClip_get_hasBlendOut_m8444D00A050FD627EA6A7DC834B5B14B2C845E51 (void);
// 0x000000DC UnityEngine.AnimationCurve UnityEngine.Timeline.TimelineClip::get_mixInCurve()
extern void TimelineClip_get_mixInCurve_mCF8C05F353EF6EF3E115DEC17FA10DB481724B45 (void);
// 0x000000DD System.Void UnityEngine.Timeline.TimelineClip::set_mixInCurve(UnityEngine.AnimationCurve)
extern void TimelineClip_set_mixInCurve_m53265562D7425F388E7DC93717D8707F816E65EF (void);
// 0x000000DE System.Single UnityEngine.Timeline.TimelineClip::get_mixInPercentage()
extern void TimelineClip_get_mixInPercentage_m9120693B790082B5ED9FC988B3C9B5F06C946B10 (void);
// 0x000000DF System.Double UnityEngine.Timeline.TimelineClip::get_mixInDuration()
extern void TimelineClip_get_mixInDuration_m754EA2369F50687A58E3A883DBA5DA79C090B243 (void);
// 0x000000E0 UnityEngine.AnimationCurve UnityEngine.Timeline.TimelineClip::get_mixOutCurve()
extern void TimelineClip_get_mixOutCurve_mAA470F75E97A1960E50BFA0B97E4BC2E0873D687 (void);
// 0x000000E1 System.Void UnityEngine.Timeline.TimelineClip::set_mixOutCurve(UnityEngine.AnimationCurve)
extern void TimelineClip_set_mixOutCurve_m93EA2EF70C31BC5625A419E6830AFB6DD594A729 (void);
// 0x000000E2 System.Double UnityEngine.Timeline.TimelineClip::get_mixOutTime()
extern void TimelineClip_get_mixOutTime_mD780BF14E8EE91F808CEB62BEE0A93B30C6233A6 (void);
// 0x000000E3 System.Double UnityEngine.Timeline.TimelineClip::get_mixOutDuration()
extern void TimelineClip_get_mixOutDuration_m19A9A249A1628E222E534662B0D4CB30F4DBCB0E (void);
// 0x000000E4 System.Single UnityEngine.Timeline.TimelineClip::get_mixOutPercentage()
extern void TimelineClip_get_mixOutPercentage_m4D9124E362272E0DA49CA228C72E5CEEF39BB3CB (void);
// 0x000000E5 System.Boolean UnityEngine.Timeline.TimelineClip::get_recordable()
extern void TimelineClip_get_recordable_m2BE0E30776FECD30BAE62D78399B6402682B2001 (void);
// 0x000000E6 System.Void UnityEngine.Timeline.TimelineClip::set_recordable(System.Boolean)
extern void TimelineClip_set_recordable_m38B678C027026275DD27636B8AF8345D94D9403A (void);
// 0x000000E7 System.Collections.Generic.List`1<System.String> UnityEngine.Timeline.TimelineClip::get_exposedParameters()
extern void TimelineClip_get_exposedParameters_mB96B6748027997A9DFEF943387DD86F74876B279 (void);
// 0x000000E8 UnityEngine.Timeline.ClipCaps UnityEngine.Timeline.TimelineClip::get_clipCaps()
extern void TimelineClip_get_clipCaps_m11FD6AE29AD801B99B64A71C6C76680FA213FC0A (void);
// 0x000000E9 System.Int32 UnityEngine.Timeline.TimelineClip::Hash()
extern void TimelineClip_Hash_mA9299778B1C618795049AB37ADD4A01A7E5331DE (void);
// 0x000000EA System.Single UnityEngine.Timeline.TimelineClip::EvaluateMixOut(System.Double)
extern void TimelineClip_EvaluateMixOut_mA36406D289DE5A9831B821AC5A9C51DF21B47C83 (void);
// 0x000000EB System.Single UnityEngine.Timeline.TimelineClip::EvaluateMixIn(System.Double)
extern void TimelineClip_EvaluateMixIn_m206FBF82C1AF116724AC429CAF7C4759E6D28D60 (void);
// 0x000000EC UnityEngine.AnimationCurve UnityEngine.Timeline.TimelineClip::GetDefaultMixInCurve()
extern void TimelineClip_GetDefaultMixInCurve_m90A35726DC5817536F73783D5B1028F64251EDE4 (void);
// 0x000000ED UnityEngine.AnimationCurve UnityEngine.Timeline.TimelineClip::GetDefaultMixOutCurve()
extern void TimelineClip_GetDefaultMixOutCurve_m07738400AEE1FB893C14562EF2EE11142E767AE6 (void);
// 0x000000EE System.Double UnityEngine.Timeline.TimelineClip::ToLocalTime(System.Double)
extern void TimelineClip_ToLocalTime_m975B84C7F3371F39F73AD8DA1F89C3C825D40E1E (void);
// 0x000000EF System.Double UnityEngine.Timeline.TimelineClip::ToLocalTimeUnbound(System.Double)
extern void TimelineClip_ToLocalTimeUnbound_m8E307B2FDD49E30A57137FD94BA001B9157DEA6F (void);
// 0x000000F0 System.Double UnityEngine.Timeline.TimelineClip::FromLocalTimeUnbound(System.Double)
extern void TimelineClip_FromLocalTimeUnbound_mBCE77F0E508F86A77DE09CE16623A6FB374700E1 (void);
// 0x000000F1 UnityEngine.AnimationClip UnityEngine.Timeline.TimelineClip::get_animationClip()
extern void TimelineClip_get_animationClip_mFDFBA4582868E85C0B56242660C9EF3B4442D07D (void);
// 0x000000F2 System.Double UnityEngine.Timeline.TimelineClip::SanitizeTimeValue(System.Double,System.Double)
extern void TimelineClip_SanitizeTimeValue_m3185743B561388628273A3FBD9AD1C7C4AD3A9DE (void);
// 0x000000F3 UnityEngine.Timeline.TimelineClip/ClipExtrapolation UnityEngine.Timeline.TimelineClip::get_postExtrapolationMode()
extern void TimelineClip_get_postExtrapolationMode_mDF06AB6BEE7906C373109C04775C0B3CA83D8685 (void);
// 0x000000F4 System.Void UnityEngine.Timeline.TimelineClip::set_postExtrapolationMode(UnityEngine.Timeline.TimelineClip/ClipExtrapolation)
extern void TimelineClip_set_postExtrapolationMode_mD2B003EB90D2C816050F5FF12B662401BB858092 (void);
// 0x000000F5 UnityEngine.Timeline.TimelineClip/ClipExtrapolation UnityEngine.Timeline.TimelineClip::get_preExtrapolationMode()
extern void TimelineClip_get_preExtrapolationMode_mC20AE951A26D337093ABC65FD2DBF9C5CBE8E3FA (void);
// 0x000000F6 System.Void UnityEngine.Timeline.TimelineClip::set_preExtrapolationMode(UnityEngine.Timeline.TimelineClip/ClipExtrapolation)
extern void TimelineClip_set_preExtrapolationMode_m26E6222B67AFA46CC6F1EE1730C8819B19D035AF (void);
// 0x000000F7 System.Void UnityEngine.Timeline.TimelineClip::SetPostExtrapolationTime(System.Double)
extern void TimelineClip_SetPostExtrapolationTime_m659389D7F8F07AE58FF2D1B4BCFAB9FF13691C35 (void);
// 0x000000F8 System.Void UnityEngine.Timeline.TimelineClip::SetPreExtrapolationTime(System.Double)
extern void TimelineClip_SetPreExtrapolationTime_mE9DAF291C193942B0703ADB10CA1BEF1B9D0C4DA (void);
// 0x000000F9 System.Boolean UnityEngine.Timeline.TimelineClip::IsExtrapolatedTime(System.Double)
extern void TimelineClip_IsExtrapolatedTime_m82974EEE2D3C650B90C2F5F5592FB31B7029AB6C (void);
// 0x000000FA System.Boolean UnityEngine.Timeline.TimelineClip::IsPreExtrapolatedTime(System.Double)
extern void TimelineClip_IsPreExtrapolatedTime_mCA79ABD6E8F0408DA16E8BB57C9509BB092CC753 (void);
// 0x000000FB System.Boolean UnityEngine.Timeline.TimelineClip::IsPostExtrapolatedTime(System.Double)
extern void TimelineClip_IsPostExtrapolatedTime_m2C8FFEF3F821D6CD45A7D8F5E06F3E7705C6B1FE (void);
// 0x000000FC System.Double UnityEngine.Timeline.TimelineClip::get_extrapolatedStart()
extern void TimelineClip_get_extrapolatedStart_m4092C97F1EB880583FA92B8FAAFC208D245630BF (void);
// 0x000000FD System.Double UnityEngine.Timeline.TimelineClip::get_extrapolatedDuration()
extern void TimelineClip_get_extrapolatedDuration_mFA3957094871DA9151A26AB04A894E771FFC1F21 (void);
// 0x000000FE System.Double UnityEngine.Timeline.TimelineClip::GetExtrapolatedTime(System.Double,UnityEngine.Timeline.TimelineClip/ClipExtrapolation,System.Double)
extern void TimelineClip_GetExtrapolatedTime_m10D665C5B1EC5CBCF3FC38E10BE6A2276D5E7405 (void);
// 0x000000FF System.Void UnityEngine.Timeline.TimelineClip::CreateCurves(System.String)
extern void TimelineClip_CreateCurves_m494AE0E2457ACD87387E039AF081F93442FACBB3 (void);
// 0x00000100 System.Void UnityEngine.Timeline.TimelineClip::UnityEngine.ISerializationCallbackReceiver.OnBeforeSerialize()
extern void TimelineClip_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mDC9EFB3125995E8EFA91EB3CF27E9B2784B60DE8 (void);
// 0x00000101 System.Void UnityEngine.Timeline.TimelineClip::UnityEngine.ISerializationCallbackReceiver.OnAfterDeserialize()
extern void TimelineClip_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mE0860037B34E43C2CB25567793EDB4F48B018D4E (void);
// 0x00000102 System.String UnityEngine.Timeline.TimelineClip::ToString()
extern void TimelineClip_ToString_m56D472178DA2F04872969618EF5CA3E142E9FD9A (void);
// 0x00000103 System.Void UnityEngine.Timeline.TimelineClip::ConformEaseValues()
extern void TimelineClip_ConformEaseValues_m6774B17BD4E1E6734FC21CB1F67C267DF92DD623 (void);
// 0x00000104 System.Double UnityEngine.Timeline.TimelineClip::CalculateEasingRatio(System.Double,System.Double)
extern void TimelineClip_CalculateEasingRatio_mE7960562253BE9447E532E1BA3709AF9B0834D75 (void);
// 0x00000105 System.Void UnityEngine.Timeline.TimelineClip::UpdateDirty(System.Double,System.Double)
extern void TimelineClip_UpdateDirty_m3F5A0077C88F2C9D481AE93CEF2E16DCD235EC08 (void);
// 0x00000106 System.Void UnityEngine.Timeline.TimelineClip::.cctor()
extern void TimelineClip__cctor_mD74B4E06C1F4905731E8C1E06F0BB24BCED88317 (void);
// 0x00000107 System.Void UnityEngine.Timeline.TimelineClip/TimelineClipUpgrade::UpgradeClipInFromGlobalToLocal(UnityEngine.Timeline.TimelineClip)
extern void TimelineClipUpgrade_UpgradeClipInFromGlobalToLocal_m59F498F944599E3F1BAF6B0D5AC37E5E38770E7C (void);
// 0x00000108 System.Void UnityEngine.Timeline.TimelineAsset::UpgradeToLatestVersion()
extern void TimelineAsset_UpgradeToLatestVersion_m34BA46E2D4643EFB179CBFB81706EC27785CBFA0 (void);
// 0x00000109 UnityEngine.Timeline.TimelineAsset/EditorSettings UnityEngine.Timeline.TimelineAsset::get_editorSettings()
extern void TimelineAsset_get_editorSettings_m3D5DEC0305D9E517E29CB898C789C3B42D3B1CB9 (void);
// 0x0000010A System.Double UnityEngine.Timeline.TimelineAsset::get_duration()
extern void TimelineAsset_get_duration_m22226DF293F2D489E63D881775112FC26518E297 (void);
// 0x0000010B System.Double UnityEngine.Timeline.TimelineAsset::get_fixedDuration()
extern void TimelineAsset_get_fixedDuration_mB9B8F8FBE5DCF918C17AC705E6D17A730DB9A69C (void);
// 0x0000010C System.Void UnityEngine.Timeline.TimelineAsset::set_fixedDuration(System.Double)
extern void TimelineAsset_set_fixedDuration_m10FF2CD4C12508C9F4938A17B2EF7E81A5C701AB (void);
// 0x0000010D UnityEngine.Timeline.TimelineAsset/DurationMode UnityEngine.Timeline.TimelineAsset::get_durationMode()
extern void TimelineAsset_get_durationMode_m58895CD3D78D4F4A912BD149FD810B3AE6AA0034 (void);
// 0x0000010E System.Void UnityEngine.Timeline.TimelineAsset::set_durationMode(UnityEngine.Timeline.TimelineAsset/DurationMode)
extern void TimelineAsset_set_durationMode_mB082ACE2157EF8472C2C30C07DAE8DDCC6E9008D (void);
// 0x0000010F System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.TimelineAsset::get_outputs()
extern void TimelineAsset_get_outputs_m232A9F7BCE2E4BCFB2115B9ABB26D53C7D8C478E (void);
// 0x00000110 UnityEngine.Timeline.ClipCaps UnityEngine.Timeline.TimelineAsset::get_clipCaps()
extern void TimelineAsset_get_clipCaps_m0F3ACD23FAE67D92F0F67A056F0AC64BEC0A3AA8 (void);
// 0x00000111 System.Int32 UnityEngine.Timeline.TimelineAsset::get_outputTrackCount()
extern void TimelineAsset_get_outputTrackCount_m07CED6FE6B99D62D0E5EB3C2A91863E21E1CB70B (void);
// 0x00000112 System.Int32 UnityEngine.Timeline.TimelineAsset::get_rootTrackCount()
extern void TimelineAsset_get_rootTrackCount_m89C66C83C874A1D60BE068E40C5CB94033F0A7ED (void);
// 0x00000113 System.Void UnityEngine.Timeline.TimelineAsset::OnValidate()
extern void TimelineAsset_OnValidate_m4D4626C6372A467AE9EB04DA835A8CD9EE75EDF0 (void);
// 0x00000114 UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.TimelineAsset::GetRootTrack(System.Int32)
extern void TimelineAsset_GetRootTrack_mC41A0CF4127692DDEEBAA8BA973BE4392C49B83A (void);
// 0x00000115 System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.TrackAsset> UnityEngine.Timeline.TimelineAsset::GetRootTracks()
extern void TimelineAsset_GetRootTracks_m6340C1C261F14F8FBAF7116F775780C75068F05F (void);
// 0x00000116 UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.TimelineAsset::GetOutputTrack(System.Int32)
extern void TimelineAsset_GetOutputTrack_m8002892E722FE6CA9C8B8EB7D7040BE1E47D672D (void);
// 0x00000117 System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.TrackAsset> UnityEngine.Timeline.TimelineAsset::GetOutputTracks()
extern void TimelineAsset_GetOutputTracks_m324315A337B30921D4B5E740E1FC2BE81563B26F (void);
// 0x00000118 System.Double UnityEngine.Timeline.TimelineAsset::GetValidFrameRate(System.Double)
extern void TimelineAsset_GetValidFrameRate_m5F0BAEC92EDFDBE539E1A0187DC2F185DA8D5E72 (void);
// 0x00000119 System.Void UnityEngine.Timeline.TimelineAsset::UpdateRootTrackCache()
extern void TimelineAsset_UpdateRootTrackCache_mADE060D9C1F785CFA83E43B4CB895D772EF74749 (void);
// 0x0000011A System.Void UnityEngine.Timeline.TimelineAsset::UpdateOutputTrackCache()
extern void TimelineAsset_UpdateOutputTrackCache_m4A4249728370EB77115CDCB08D2A08C4D3925818 (void);
// 0x0000011B UnityEngine.Timeline.TrackAsset[] UnityEngine.Timeline.TimelineAsset::get_flattenedTracks()
extern void TimelineAsset_get_flattenedTracks_m51B0499CC8CD43CB520F6B84D37FCD19C8FE4F18 (void);
// 0x0000011C UnityEngine.Timeline.MarkerTrack UnityEngine.Timeline.TimelineAsset::get_markerTrack()
extern void TimelineAsset_get_markerTrack_mA1522943FBA0FDFDB0765165F00B6F9373EB01A3 (void);
// 0x0000011D System.Collections.Generic.List`1<UnityEngine.ScriptableObject> UnityEngine.Timeline.TimelineAsset::get_trackObjects()
extern void TimelineAsset_get_trackObjects_mFE1A564170B932E65741C29D0A94B5DA55C7D676 (void);
// 0x0000011E System.Void UnityEngine.Timeline.TimelineAsset::AddTrackInternal(UnityEngine.Timeline.TrackAsset)
extern void TimelineAsset_AddTrackInternal_m1699C0B3168FF4D471509AA664AA92BCC3000A57 (void);
// 0x0000011F System.Void UnityEngine.Timeline.TimelineAsset::RemoveTrack(UnityEngine.Timeline.TrackAsset)
extern void TimelineAsset_RemoveTrack_m34E111A2A0D031C27F8EB42DD4CD014A5883ADC2 (void);
// 0x00000120 UnityEngine.Playables.Playable UnityEngine.Timeline.TimelineAsset::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject)
extern void TimelineAsset_CreatePlayable_m015AE0523BB6A93F3F0070BF0757ECA3199EB5FE (void);
// 0x00000121 System.Void UnityEngine.Timeline.TimelineAsset::UnityEngine.ISerializationCallbackReceiver.OnBeforeSerialize()
extern void TimelineAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m4ED0C35C1033C2177A683A17F97863B2F3A8982F (void);
// 0x00000122 System.Void UnityEngine.Timeline.TimelineAsset::UnityEngine.ISerializationCallbackReceiver.OnAfterDeserialize()
extern void TimelineAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m7C5218B558DC68FA00A3A7AC87FAE02F589C2808 (void);
// 0x00000123 System.Void UnityEngine.Timeline.TimelineAsset::__internalAwake()
extern void TimelineAsset___internalAwake_m3BDB3D8798B27D100140D4C189285ACCFD116F18 (void);
// 0x00000124 System.Void UnityEngine.Timeline.TimelineAsset::GatherProperties(UnityEngine.Playables.PlayableDirector,UnityEngine.Timeline.IPropertyCollector)
extern void TimelineAsset_GatherProperties_m966DA31A1B9D51FA370CB0DD4B3FC9036C230EEA (void);
// 0x00000125 System.Void UnityEngine.Timeline.TimelineAsset::CreateMarkerTrack()
extern void TimelineAsset_CreateMarkerTrack_m959223B301DE70DED79380138F93BF1565D2B586 (void);
// 0x00000126 System.Void UnityEngine.Timeline.TimelineAsset::Invalidate()
extern void TimelineAsset_Invalidate_m601FE157269B617EEB1D840439E36715DC81AFC6 (void);
// 0x00000127 System.Void UnityEngine.Timeline.TimelineAsset::UpdateFixedDurationWithItemsDuration()
extern void TimelineAsset_UpdateFixedDurationWithItemsDuration_mE79C311415955F474C4C885E4CF8B7124CBBE008 (void);
// 0x00000128 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.TimelineAsset::CalculateItemsDuration()
extern void TimelineAsset_CalculateItemsDuration_m2904F09544AB814380305EA97D6EB11AFDCFD0BC (void);
// 0x00000129 System.Void UnityEngine.Timeline.TimelineAsset::AddSubTracksRecursive(UnityEngine.Timeline.TrackAsset,System.Collections.Generic.List`1<UnityEngine.Timeline.TrackAsset>&)
extern void TimelineAsset_AddSubTracksRecursive_m03EBD254DE32A5C23CE13E80EFFC4BDC51AD960F (void);
// 0x0000012A UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.TimelineAsset::CreateTrack(System.Type,UnityEngine.Timeline.TrackAsset,System.String)
extern void TimelineAsset_CreateTrack_m327D088F33507A544DE566503CDF6593C024C1ED (void);
// 0x0000012B T UnityEngine.Timeline.TimelineAsset::CreateTrack(UnityEngine.Timeline.TrackAsset,System.String)
// 0x0000012C T UnityEngine.Timeline.TimelineAsset::CreateTrack(System.String)
// 0x0000012D T UnityEngine.Timeline.TimelineAsset::CreateTrack()
// 0x0000012E System.Boolean UnityEngine.Timeline.TimelineAsset::DeleteClip(UnityEngine.Timeline.TimelineClip)
extern void TimelineAsset_DeleteClip_m61EFBF21AF49C5FE55CD70060C19B81657170B43 (void);
// 0x0000012F System.Boolean UnityEngine.Timeline.TimelineAsset::DeleteTrack(UnityEngine.Timeline.TrackAsset)
extern void TimelineAsset_DeleteTrack_m80BEC8B29D94214E5663B0F64E725726B7EBAC08 (void);
// 0x00000130 System.Void UnityEngine.Timeline.TimelineAsset::MoveLastTrackBefore(UnityEngine.Timeline.TrackAsset)
extern void TimelineAsset_MoveLastTrackBefore_mB1C0BE93519C75B57FC07BCFBCBFA912A61841E4 (void);
// 0x00000131 UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.TimelineAsset::AllocateTrack(UnityEngine.Timeline.TrackAsset,System.String,System.Type)
extern void TimelineAsset_AllocateTrack_m8DFE21D5BC86310C82E23E43CE3D7D04759EA2F5 (void);
// 0x00000132 System.Void UnityEngine.Timeline.TimelineAsset::DeleteRecordedAnimation(UnityEngine.Timeline.TrackAsset)
extern void TimelineAsset_DeleteRecordedAnimation_m3FF55ADA0F576F30D7522D85D999E5A2A8824939 (void);
// 0x00000133 System.Void UnityEngine.Timeline.TimelineAsset::DeleteRecordedAnimation(UnityEngine.Timeline.TimelineClip)
extern void TimelineAsset_DeleteRecordedAnimation_mBE3F874CEAEFD795EC06C74E50F3E007890369C8 (void);
// 0x00000134 System.Void UnityEngine.Timeline.TimelineAsset::.ctor()
extern void TimelineAsset__ctor_m75D9A08991F60CBFEFDAD23DC01B6A49A4601E4C (void);
// 0x00000135 System.Single UnityEngine.Timeline.TimelineAsset/EditorSettings::get_fps()
extern void EditorSettings_get_fps_m2B7B48B6BBD590B690E91D5C761336A3454796A2 (void);
// 0x00000136 System.Void UnityEngine.Timeline.TimelineAsset/EditorSettings::set_fps(System.Single)
extern void EditorSettings_set_fps_mC963EB8F7CAFC6A54CBF37278ADFB0311161674F (void);
// 0x00000137 System.Double UnityEngine.Timeline.TimelineAsset/EditorSettings::get_frameRate()
extern void EditorSettings_get_frameRate_m699A6599212042DC13CB0DB91C7057B9644A0037 (void);
// 0x00000138 System.Void UnityEngine.Timeline.TimelineAsset/EditorSettings::set_frameRate(System.Double)
extern void EditorSettings_set_frameRate_mE8F1DFC8C2B4203B48865F22DB7C5309932EB32B (void);
// 0x00000139 System.Void UnityEngine.Timeline.TimelineAsset/EditorSettings::SetStandardFrameRate(UnityEngine.Timeline.StandardFrameRates)
extern void EditorSettings_SetStandardFrameRate_mA67A361C75A563095119DC1E6D20FD455ED1AC1A (void);
// 0x0000013A System.Boolean UnityEngine.Timeline.TimelineAsset/EditorSettings::get_scenePreview()
extern void EditorSettings_get_scenePreview_m1FF2AB573D0405B69314643070555FFAF997B3C0 (void);
// 0x0000013B System.Void UnityEngine.Timeline.TimelineAsset/EditorSettings::set_scenePreview(System.Boolean)
extern void EditorSettings_set_scenePreview_m7996AD908D830ED6DB8D9FF103FE848BA9AEF553 (void);
// 0x0000013C System.Void UnityEngine.Timeline.TimelineAsset/EditorSettings::.ctor()
extern void EditorSettings__ctor_m239B6DF8C75E8C21F4FCA53B668E9C7E64D1A8A3 (void);
// 0x0000013D System.Void UnityEngine.Timeline.TimelineAsset/EditorSettings::.cctor()
extern void EditorSettings__cctor_m4FE2EB891C4975DD9E721DDF925816B926040D89 (void);
// 0x0000013E System.Void UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::.ctor(System.Int32)
extern void U3Cget_outputsU3Ed__27__ctor_mBFD68070C9454051A90D0AA75FB4D2FF2F63466F (void);
// 0x0000013F System.Void UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::System.IDisposable.Dispose()
extern void U3Cget_outputsU3Ed__27_System_IDisposable_Dispose_m08F64F0AB9B4EFBB4D45A957869E4167332ACD41 (void);
// 0x00000140 System.Boolean UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::MoveNext()
extern void U3Cget_outputsU3Ed__27_MoveNext_mA380099DD3A55548271DBAD6F80E42AA990F778C (void);
// 0x00000141 System.Void UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::<>m__Finally1()
extern void U3Cget_outputsU3Ed__27_U3CU3Em__Finally1_m7E65569ADF800B81563544B8D264833E518A3C7C (void);
// 0x00000142 System.Void UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::<>m__Finally2()
extern void U3Cget_outputsU3Ed__27_U3CU3Em__Finally2_m85B6B1B2A51FA070A90244EC2076C5E22F7CD920 (void);
// 0x00000143 UnityEngine.Playables.PlayableBinding UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::System.Collections.Generic.IEnumerator<UnityEngine.Playables.PlayableBinding>.get_Current()
extern void U3Cget_outputsU3Ed__27_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m22E55FE2DC239357C1BEBBB551C332C97F221F31 (void);
// 0x00000144 System.Void UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::System.Collections.IEnumerator.Reset()
extern void U3Cget_outputsU3Ed__27_System_Collections_IEnumerator_Reset_m334D7D0581BE692B7764A7F29E4C53B6BD3D8350 (void);
// 0x00000145 System.Object UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::System.Collections.IEnumerator.get_Current()
extern void U3Cget_outputsU3Ed__27_System_Collections_IEnumerator_get_Current_m2F264D817E0F1C5AE475AD9282977DEC88875D14 (void);
// 0x00000146 System.Collections.Generic.IEnumerator`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::System.Collections.Generic.IEnumerable<UnityEngine.Playables.PlayableBinding>.GetEnumerator()
extern void U3Cget_outputsU3Ed__27_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m22938CACD8363F1D9EF4B4B7BCC37B3ECF0554C0 (void);
// 0x00000147 System.Collections.IEnumerator UnityEngine.Timeline.TimelineAsset/<get_outputs>d__27::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_outputsU3Ed__27_System_Collections_IEnumerable_GetEnumerator_m54F9DE0351DB259B654FB5C81A2B12FABB8DA0CE (void);
// 0x00000148 System.Void UnityEngine.Timeline.TrackAsset::OnBeforeTrackSerialize()
extern void TrackAsset_OnBeforeTrackSerialize_m5BCB0C082629BB807C2BC9F8E1623AA84EF3CDD8 (void);
// 0x00000149 System.Void UnityEngine.Timeline.TrackAsset::OnAfterTrackDeserialize()
extern void TrackAsset_OnAfterTrackDeserialize_mF383ADAF4C3070BFB81F0478237946621E4FC850 (void);
// 0x0000014A System.Void UnityEngine.Timeline.TrackAsset::OnUpgradeFromVersion(System.Int32)
extern void TrackAsset_OnUpgradeFromVersion_m5A0523EA6441B741A60768840C9A6493DB682D22 (void);
// 0x0000014B System.Void UnityEngine.Timeline.TrackAsset::UnityEngine.ISerializationCallbackReceiver.OnBeforeSerialize()
extern void TrackAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m965E10D67C9FEE0BDE0BB2887C4A24B6BDEC755B (void);
// 0x0000014C System.Void UnityEngine.Timeline.TrackAsset::UnityEngine.ISerializationCallbackReceiver.OnAfterDeserialize()
extern void TrackAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m6A13E9CC4CBECD81002067EAD4B9FF875071F0D1 (void);
// 0x0000014D System.Void UnityEngine.Timeline.TrackAsset::UpgradeToLatestVersion()
extern void TrackAsset_UpgradeToLatestVersion_m70FA58294D201156412BA790FD158D2029FDB505 (void);
// 0x0000014E System.Void UnityEngine.Timeline.TrackAsset::add_OnClipPlayableCreate(System.Action`3<UnityEngine.Timeline.TimelineClip,UnityEngine.GameObject,UnityEngine.Playables.Playable>)
extern void TrackAsset_add_OnClipPlayableCreate_m7E859C1C550253310D9D6B34427DDAF0325AC228 (void);
// 0x0000014F System.Void UnityEngine.Timeline.TrackAsset::remove_OnClipPlayableCreate(System.Action`3<UnityEngine.Timeline.TimelineClip,UnityEngine.GameObject,UnityEngine.Playables.Playable>)
extern void TrackAsset_remove_OnClipPlayableCreate_mE0AE4E40FC4B75040AFEB6AF08B55FFA6F72CAEE (void);
// 0x00000150 System.Void UnityEngine.Timeline.TrackAsset::add_OnTrackAnimationPlayableCreate(System.Action`3<UnityEngine.Timeline.TrackAsset,UnityEngine.GameObject,UnityEngine.Playables.Playable>)
extern void TrackAsset_add_OnTrackAnimationPlayableCreate_mCBC06DE4F00D3DFF308397D76B49D4F4C71058B0 (void);
// 0x00000151 System.Void UnityEngine.Timeline.TrackAsset::remove_OnTrackAnimationPlayableCreate(System.Action`3<UnityEngine.Timeline.TrackAsset,UnityEngine.GameObject,UnityEngine.Playables.Playable>)
extern void TrackAsset_remove_OnTrackAnimationPlayableCreate_mA022F7131A984ECB7D002069487AC63834290FA1 (void);
// 0x00000152 System.Double UnityEngine.Timeline.TrackAsset::get_start()
extern void TrackAsset_get_start_mA37EAE96A7EB745D0BC538509B78AEDEF05208E1 (void);
// 0x00000153 System.Double UnityEngine.Timeline.TrackAsset::get_end()
extern void TrackAsset_get_end_m283E3BCE09D393EFA56FB26D5AD68326EECFF312 (void);
// 0x00000154 System.Double UnityEngine.Timeline.TrackAsset::get_duration()
extern void TrackAsset_get_duration_m3A3B2F6AD1F1945549C459B432CD4E469FAAECEF (void);
// 0x00000155 System.Boolean UnityEngine.Timeline.TrackAsset::get_muted()
extern void TrackAsset_get_muted_mBA0D78639ED5132C019E69AC51A5BE56478C7A66 (void);
// 0x00000156 System.Void UnityEngine.Timeline.TrackAsset::set_muted(System.Boolean)
extern void TrackAsset_set_muted_m9FCF9772AA42FB22761AA468792BE272258E426E (void);
// 0x00000157 System.Boolean UnityEngine.Timeline.TrackAsset::get_mutedInHierarchy()
extern void TrackAsset_get_mutedInHierarchy_m14257BCE9CD51D5A3C086E465F035A7707D666F7 (void);
// 0x00000158 UnityEngine.Timeline.TimelineAsset UnityEngine.Timeline.TrackAsset::get_timelineAsset()
extern void TrackAsset_get_timelineAsset_m969726B43E66370FB81F1CCB6C012BCAD2B112A8 (void);
// 0x00000159 UnityEngine.Playables.PlayableAsset UnityEngine.Timeline.TrackAsset::get_parent()
extern void TrackAsset_get_parent_m4B5D7DF104388286053C4BE8D5ECB28615FE7DD9 (void);
// 0x0000015A System.Void UnityEngine.Timeline.TrackAsset::set_parent(UnityEngine.Playables.PlayableAsset)
extern void TrackAsset_set_parent_mE5000EE0C72D1A0E0A6B916B7E0B94F85192D138 (void);
// 0x0000015B System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.TimelineClip> UnityEngine.Timeline.TrackAsset::GetClips()
extern void TrackAsset_GetClips_m467A7BE887049F3CC0F411AB220F488D1230FA76 (void);
// 0x0000015C UnityEngine.Timeline.TimelineClip[] UnityEngine.Timeline.TrackAsset::get_clips()
extern void TrackAsset_get_clips_m033A1CF810A017C6BD6F52190421C1474648BEB8 (void);
// 0x0000015D System.Boolean UnityEngine.Timeline.TrackAsset::get_isEmpty()
extern void TrackAsset_get_isEmpty_m269392C25CDC0E0179FEFD5322476B8BEF7FC86B (void);
// 0x0000015E System.Boolean UnityEngine.Timeline.TrackAsset::get_hasClips()
extern void TrackAsset_get_hasClips_m552EDF37A12F12705B5A016D9351380BF08D25ED (void);
// 0x0000015F System.Boolean UnityEngine.Timeline.TrackAsset::get_hasCurves()
extern void TrackAsset_get_hasCurves_m0F0AF8869F6E78DB23C129134B11A39E9CA4208E (void);
// 0x00000160 System.Boolean UnityEngine.Timeline.TrackAsset::get_isSubTrack()
extern void TrackAsset_get_isSubTrack_mB3337FCA3D035259F1A6F5B9CFD6D9B6E2EDF05E (void);
// 0x00000161 System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.TrackAsset::get_outputs()
extern void TrackAsset_get_outputs_mF8F332F69DBE3F1DF5C24DF6AFEE25B14F6ED4F7 (void);
// 0x00000162 System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.TrackAsset> UnityEngine.Timeline.TrackAsset::GetChildTracks()
extern void TrackAsset_GetChildTracks_m34EE35A341030F99020F56267BAFC8FF4E98477C (void);
// 0x00000163 System.String UnityEngine.Timeline.TrackAsset::get_customPlayableTypename()
extern void TrackAsset_get_customPlayableTypename_m5E5465F06E569999A2839AAA5B29B6907C74718D (void);
// 0x00000164 System.Void UnityEngine.Timeline.TrackAsset::set_customPlayableTypename(System.String)
extern void TrackAsset_set_customPlayableTypename_m091B3CFE1EEBAFC3508726A51254C6090CF84A4A (void);
// 0x00000165 UnityEngine.AnimationClip UnityEngine.Timeline.TrackAsset::get_curves()
extern void TrackAsset_get_curves_m3068225198516361CFB374B26B71E266079D1031 (void);
// 0x00000166 System.Void UnityEngine.Timeline.TrackAsset::set_curves(UnityEngine.AnimationClip)
extern void TrackAsset_set_curves_m61933C531CDE903FD6DCB11ABB1DEA36F6C844B6 (void);
// 0x00000167 System.String UnityEngine.Timeline.TrackAsset::UnityEngine.Timeline.ICurvesOwner.get_defaultCurvesName()
extern void TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_defaultCurvesName_m56DD3624C90F3BDDD8AC804AD16A80B4D1D5D542 (void);
// 0x00000168 UnityEngine.Object UnityEngine.Timeline.TrackAsset::UnityEngine.Timeline.ICurvesOwner.get_asset()
extern void TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_asset_mCB4DB0FF33D5C0E1A701D53C1CA54386A1DAF291 (void);
// 0x00000169 UnityEngine.Object UnityEngine.Timeline.TrackAsset::UnityEngine.Timeline.ICurvesOwner.get_assetOwner()
extern void TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_assetOwner_m9E12631082B8FD221B0C93F5EA88E8926D629537 (void);
// 0x0000016A UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.TrackAsset::UnityEngine.Timeline.ICurvesOwner.get_targetTrack()
extern void TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_targetTrack_m34F7F32EC8BF3AA9219D1B02A6E8BE32D2B0BD50 (void);
// 0x0000016B System.Collections.Generic.List`1<UnityEngine.ScriptableObject> UnityEngine.Timeline.TrackAsset::get_subTracksObjects()
extern void TrackAsset_get_subTracksObjects_m18E7B9EEC20905CBE5D35805FAA819CB663ECC43 (void);
// 0x0000016C System.Boolean UnityEngine.Timeline.TrackAsset::get_locked()
extern void TrackAsset_get_locked_mCEBC35262FB276860A374085F2626DCE333FC466 (void);
// 0x0000016D System.Void UnityEngine.Timeline.TrackAsset::set_locked(System.Boolean)
extern void TrackAsset_set_locked_mA8190606E6AC618DCF1CF211AD648C15E67D0F87 (void);
// 0x0000016E System.Boolean UnityEngine.Timeline.TrackAsset::get_lockedInHierarchy()
extern void TrackAsset_get_lockedInHierarchy_mB8B7847141ACB7B33320D85B456F7B7FAD155298 (void);
// 0x0000016F System.Boolean UnityEngine.Timeline.TrackAsset::get_supportsNotifications()
extern void TrackAsset_get_supportsNotifications_mFAD0EDA9BAD3DDD341161C02D5DDC658F1184729 (void);
// 0x00000170 System.Void UnityEngine.Timeline.TrackAsset::__internalAwake()
extern void TrackAsset___internalAwake_mBBCB93A3A0959E0B82B795E298DCD8B3C5E67FAB (void);
// 0x00000171 System.Void UnityEngine.Timeline.TrackAsset::CreateCurves(System.String)
extern void TrackAsset_CreateCurves_m4EA9E9F12B65A9584E97158527FA7DC7BD11BAB2 (void);
// 0x00000172 UnityEngine.Playables.Playable UnityEngine.Timeline.TrackAsset::CreateTrackMixer(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,System.Int32)
extern void TrackAsset_CreateTrackMixer_m088F7E10980285D4DFD092CFDFB0A34B9AB78DAF (void);
// 0x00000173 UnityEngine.Playables.Playable UnityEngine.Timeline.TrackAsset::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject)
extern void TrackAsset_CreatePlayable_mD0CEA49410876109A48E365380586AD0E5F0CEBF (void);
// 0x00000174 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.TrackAsset::CreateDefaultClip()
extern void TrackAsset_CreateDefaultClip_m1AC3502758E2185D0747626A32D27B513AC85E0F (void);
// 0x00000175 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.TrackAsset::CreateClip()
// 0x00000176 System.Boolean UnityEngine.Timeline.TrackAsset::DeleteClip(UnityEngine.Timeline.TimelineClip)
extern void TrackAsset_DeleteClip_mA02A503841BACD54C29CCAF816A917F1018E1FAE (void);
// 0x00000177 UnityEngine.Timeline.IMarker UnityEngine.Timeline.TrackAsset::CreateMarker(System.Type,System.Double)
extern void TrackAsset_CreateMarker_mD4F5715387220B12D0EF244C7C02F83F6040638A (void);
// 0x00000178 T UnityEngine.Timeline.TrackAsset::CreateMarker(System.Double)
// 0x00000179 System.Boolean UnityEngine.Timeline.TrackAsset::DeleteMarker(UnityEngine.Timeline.IMarker)
extern void TrackAsset_DeleteMarker_mA491061D745EB9073AB46DABEDDA9B4BF8963347 (void);
// 0x0000017A System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.IMarker> UnityEngine.Timeline.TrackAsset::GetMarkers()
extern void TrackAsset_GetMarkers_m4FE387892A6434D0DDD3535BD974E276372B7ADA (void);
// 0x0000017B System.Int32 UnityEngine.Timeline.TrackAsset::GetMarkerCount()
extern void TrackAsset_GetMarkerCount_mE3D948E944448243CAC284D1D358C9A889FB44B7 (void);
// 0x0000017C UnityEngine.Timeline.IMarker UnityEngine.Timeline.TrackAsset::GetMarker(System.Int32)
extern void TrackAsset_GetMarker_m2CB75ED400AECE525785B9E58020CC8EF0669A0A (void);
// 0x0000017D UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.TrackAsset::CreateClip(System.Type)
extern void TrackAsset_CreateClip_mA7D1A7B6ACCF5CCF9FB416E86C483BD2EC31A45F (void);
// 0x0000017E UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.TrackAsset::CreateAndAddNewClipOfType(System.Type)
extern void TrackAsset_CreateAndAddNewClipOfType_mF1CA85A2B77A5CF003685CEBBF0A7551CCFA05E9 (void);
// 0x0000017F UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.TrackAsset::CreateClipOfType(System.Type)
extern void TrackAsset_CreateClipOfType_m62E140A7CBDF66DE0643A1D65601E6ECCC6DA0B1 (void);
// 0x00000180 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.TrackAsset::CreateClipFromPlayableAsset(UnityEngine.Playables.IPlayableAsset)
extern void TrackAsset_CreateClipFromPlayableAsset_mFCC78BCEE93E36BB92F1CC13A08CA7E11592CB44 (void);
// 0x00000181 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.TrackAsset::CreateClipFromAsset(UnityEngine.ScriptableObject)
extern void TrackAsset_CreateClipFromAsset_mC48C4A19A082D69011DE9A64D73F05160DD1BD55 (void);
// 0x00000182 System.Collections.Generic.IEnumerable`1<UnityEngine.ScriptableObject> UnityEngine.Timeline.TrackAsset::GetMarkersRaw()
extern void TrackAsset_GetMarkersRaw_m31260E1A58B86C26BD0E4EE80F5DC22FA64FBC14 (void);
// 0x00000183 System.Void UnityEngine.Timeline.TrackAsset::ClearMarkers()
extern void TrackAsset_ClearMarkers_m4F8DA7A63459F34E3561E03BB1D198D63B0435ED (void);
// 0x00000184 System.Void UnityEngine.Timeline.TrackAsset::AddMarker(UnityEngine.ScriptableObject)
extern void TrackAsset_AddMarker_m9B5E8B481DA0086DB46B0FE6219231F2B8FE63E8 (void);
// 0x00000185 System.Boolean UnityEngine.Timeline.TrackAsset::DeleteMarkerRaw(UnityEngine.ScriptableObject)
extern void TrackAsset_DeleteMarkerRaw_mC80F55242BE94323E2C826A8C9CD6E7C8ECA7540 (void);
// 0x00000186 System.Int32 UnityEngine.Timeline.TrackAsset::GetTimeRangeHash()
extern void TrackAsset_GetTimeRangeHash_m6A9D09CE4BE80DBBF10AD763E66944FBB37C33D4 (void);
// 0x00000187 System.Void UnityEngine.Timeline.TrackAsset::AddClip(UnityEngine.Timeline.TimelineClip)
extern void TrackAsset_AddClip_mC0AD6626726FE95A50EFEC2DAF26D9625210FE09 (void);
// 0x00000188 UnityEngine.Playables.Playable UnityEngine.Timeline.TrackAsset::CreateNotificationsPlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.Playables.Playable,UnityEngine.GameObject,UnityEngine.Playables.Playable)
extern void TrackAsset_CreateNotificationsPlayable_mA46A9A1F92F6DBB21E6AA080A0491AF04F326D9B (void);
// 0x00000189 UnityEngine.Playables.Playable UnityEngine.Timeline.TrackAsset::CreatePlayableGraph(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,UnityEngine.Timeline.IntervalTree`1<UnityEngine.Timeline.RuntimeElement>,UnityEngine.Playables.Playable)
extern void TrackAsset_CreatePlayableGraph_mB09B3F258784F140E7D3F33ED4029056EA220371 (void);
// 0x0000018A UnityEngine.Playables.Playable UnityEngine.Timeline.TrackAsset::CompileClips(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,System.Collections.Generic.IList`1<UnityEngine.Timeline.TimelineClip>,UnityEngine.Timeline.IntervalTree`1<UnityEngine.Timeline.RuntimeElement>)
extern void TrackAsset_CompileClips_mAC9B684ADF9F1E0AB784A283BF350D3B5AC3D7AA (void);
// 0x0000018B System.Void UnityEngine.Timeline.TrackAsset::GatherCompilableTracks(System.Collections.Generic.IList`1<UnityEngine.Timeline.TrackAsset>)
extern void TrackAsset_GatherCompilableTracks_m0494F6912D73193408CB13F43099A6D5F2665C14 (void);
// 0x0000018C System.Void UnityEngine.Timeline.TrackAsset::GatherNotifications(System.Collections.Generic.List`1<UnityEngine.Timeline.IMarker>)
extern void TrackAsset_GatherNotifications_mF5DBDCF22D9CD5ED6FE6D868583BB6D332AD6BFA (void);
// 0x0000018D UnityEngine.Playables.Playable UnityEngine.Timeline.TrackAsset::CreateMixerPlayableGraph(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,UnityEngine.Timeline.IntervalTree`1<UnityEngine.Timeline.RuntimeElement>)
extern void TrackAsset_CreateMixerPlayableGraph_m38CCE75D2966419D1FB327209C2320F36FB19222 (void);
// 0x0000018E System.Void UnityEngine.Timeline.TrackAsset::ConfigureTrackAnimation(UnityEngine.Timeline.IntervalTree`1<UnityEngine.Timeline.RuntimeElement>,UnityEngine.GameObject,UnityEngine.Playables.Playable)
extern void TrackAsset_ConfigureTrackAnimation_mF10DDD7B2768DEDFDB46752A7E7A43F7E149EBBC (void);
// 0x0000018F System.Void UnityEngine.Timeline.TrackAsset::SortClips()
extern void TrackAsset_SortClips_m4FE3C4820022ECF050815215CEB982DE373F85D2 (void);
// 0x00000190 System.Void UnityEngine.Timeline.TrackAsset::ClearClipsInternal()
extern void TrackAsset_ClearClipsInternal_m60A468FD1AF2A83A9C8BB65376464216878BE994 (void);
// 0x00000191 System.Void UnityEngine.Timeline.TrackAsset::ClearSubTracksInternal()
extern void TrackAsset_ClearSubTracksInternal_m8638EC88D91AC3AA5F80BF38C92E16BD67D994DE (void);
// 0x00000192 System.Void UnityEngine.Timeline.TrackAsset::OnClipMove()
extern void TrackAsset_OnClipMove_m9FB2E0FF1003CA992D3F6841D13418262C8BF341 (void);
// 0x00000193 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.TrackAsset::CreateNewClipContainerInternal()
extern void TrackAsset_CreateNewClipContainerInternal_m7DD56E4426DDBA57B25BF61EAF45938324FA9F2B (void);
// 0x00000194 System.Void UnityEngine.Timeline.TrackAsset::AddChild(UnityEngine.Timeline.TrackAsset)
extern void TrackAsset_AddChild_m208FDE1AB513103FCFA9F1C8174B976E9D1DAE76 (void);
// 0x00000195 System.Void UnityEngine.Timeline.TrackAsset::MoveLastTrackBefore(UnityEngine.Timeline.TrackAsset)
extern void TrackAsset_MoveLastTrackBefore_m4619211278763606F6FE3FC90665837417BCD966 (void);
// 0x00000196 System.Boolean UnityEngine.Timeline.TrackAsset::RemoveSubTrack(UnityEngine.Timeline.TrackAsset)
extern void TrackAsset_RemoveSubTrack_m7F68C7D6AF1FB79584D154B357F47ACDF29FDAA8 (void);
// 0x00000197 System.Void UnityEngine.Timeline.TrackAsset::RemoveClip(UnityEngine.Timeline.TimelineClip)
extern void TrackAsset_RemoveClip_m1D64D42648A148BAE62836E46FC57C25EC9D1A60 (void);
// 0x00000198 System.Void UnityEngine.Timeline.TrackAsset::GetEvaluationTime(System.Double&,System.Double&)
extern void TrackAsset_GetEvaluationTime_m30B94204B96340B1C2810E05607E34A4DB85067F (void);
// 0x00000199 System.Void UnityEngine.Timeline.TrackAsset::GetSequenceTime(System.Double&,System.Double&)
extern void TrackAsset_GetSequenceTime_m618B6BD38BE9B1136E8405B1BEDFBAFD898896A9 (void);
// 0x0000019A System.Void UnityEngine.Timeline.TrackAsset::GatherProperties(UnityEngine.Playables.PlayableDirector,UnityEngine.Timeline.IPropertyCollector)
extern void TrackAsset_GatherProperties_m09C1A335FCE1ABA158748583AF4A641FF2EBB09D (void);
// 0x0000019B UnityEngine.GameObject UnityEngine.Timeline.TrackAsset::GetGameObjectBinding(UnityEngine.Playables.PlayableDirector)
extern void TrackAsset_GetGameObjectBinding_mF2D645FA74007FD6EA6B337298F939FFB4A5B853 (void);
// 0x0000019C System.Boolean UnityEngine.Timeline.TrackAsset::ValidateClipType(System.Type)
extern void TrackAsset_ValidateClipType_mD794D7B040F112C3E8ACBF9FB601414D08B97A8F (void);
// 0x0000019D System.Void UnityEngine.Timeline.TrackAsset::OnCreateClip(UnityEngine.Timeline.TimelineClip)
extern void TrackAsset_OnCreateClip_m0FF04313EBF6CE614CAB3C124B1F4D11B0E3AF94 (void);
// 0x0000019E System.Void UnityEngine.Timeline.TrackAsset::UpdateDuration()
extern void TrackAsset_UpdateDuration_mCC46D1145F6BCB2B5DB28211C23E7E12702F7035 (void);
// 0x0000019F System.Int32 UnityEngine.Timeline.TrackAsset::CalculateItemsHash()
extern void TrackAsset_CalculateItemsHash_m2928153E88198C690E2CB8486AC483555276546E (void);
// 0x000001A0 UnityEngine.Playables.Playable UnityEngine.Timeline.TrackAsset::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,UnityEngine.Timeline.TimelineClip)
extern void TrackAsset_CreatePlayable_mDD98D897F11DDC7593DF7F3029ED7A1B63285D0C (void);
// 0x000001A1 System.Void UnityEngine.Timeline.TrackAsset::Invalidate()
extern void TrackAsset_Invalidate_m3CBA531307AEE6181C9938F174AE6D1A977115BF (void);
// 0x000001A2 System.Double UnityEngine.Timeline.TrackAsset::GetNotificationDuration()
extern void TrackAsset_GetNotificationDuration_m8F9FB2B13DC1C6CC88617A18535730D1E012FC13 (void);
// 0x000001A3 System.Boolean UnityEngine.Timeline.TrackAsset::CanCompileClips()
extern void TrackAsset_CanCompileClips_mB7B603BB1D6782D00C14752530F66C638C174505 (void);
// 0x000001A4 System.Boolean UnityEngine.Timeline.TrackAsset::CanCreateTrackMixer()
extern void TrackAsset_CanCreateTrackMixer_mA825C739B3AA34B9F0F36C7534AAC4E84073F261 (void);
// 0x000001A5 System.Boolean UnityEngine.Timeline.TrackAsset::IsCompilable()
extern void TrackAsset_IsCompilable_mD65DF730D54F4D1F0F0E13D42A009FDF8C59F284 (void);
// 0x000001A6 System.Void UnityEngine.Timeline.TrackAsset::UpdateChildTrackCache()
extern void TrackAsset_UpdateChildTrackCache_mDEEB1C096FDD5B0D035CD5C699119CD913BC6BB0 (void);
// 0x000001A7 System.Int32 UnityEngine.Timeline.TrackAsset::Hash()
extern void TrackAsset_Hash_m11C239AAFB9C1FFB1035A0B0DC5271A62CA3AD78 (void);
// 0x000001A8 System.Int32 UnityEngine.Timeline.TrackAsset::GetClipsHash()
extern void TrackAsset_GetClipsHash_mF0B665CDE32E5BEA60FD6EC88F7272CEA6A83115 (void);
// 0x000001A9 System.Int32 UnityEngine.Timeline.TrackAsset::GetAnimationClipHash(UnityEngine.AnimationClip)
extern void TrackAsset_GetAnimationClipHash_m9A2D605C95C224426E2263A8693E578B77633EE7 (void);
// 0x000001AA System.Boolean UnityEngine.Timeline.TrackAsset::HasNotifications()
extern void TrackAsset_HasNotifications_mE386A9FE718BC4C8D368E8BA5B0FFC3BC50C5E06 (void);
// 0x000001AB System.Boolean UnityEngine.Timeline.TrackAsset::CanCompileNotifications()
extern void TrackAsset_CanCompileNotifications_mEA94C527EED8871F7AB1CC74C3487ABC204C708A (void);
// 0x000001AC System.Boolean UnityEngine.Timeline.TrackAsset::CanCreateMixerRecursive()
extern void TrackAsset_CanCreateMixerRecursive_m31242CE6BCCB6DA4F24690457585789405645FFC (void);
// 0x000001AD System.Void UnityEngine.Timeline.TrackAsset::.ctor()
extern void TrackAsset__ctor_mC05CAAD737449BAF26721F82EA1972843F86FE9A (void);
// 0x000001AE System.Void UnityEngine.Timeline.TrackAsset::.cctor()
extern void TrackAsset__cctor_m9E9CC7F378B533931B19CC75ACD04B10793C18BA (void);
// 0x000001AF UnityEngine.Timeline.TrackAsset/TransientBuildData UnityEngine.Timeline.TrackAsset/TransientBuildData::Create()
extern void TransientBuildData_Create_m4A6E221013EDF3015DB6842AC1A51882A0E74F86 (void);
// 0x000001B0 System.Void UnityEngine.Timeline.TrackAsset/TransientBuildData::Clear()
extern void TransientBuildData_Clear_mA1B052EF3A05C723008A58559E4C77A989ED2FD3 (void);
// 0x000001B1 System.Void UnityEngine.Timeline.TrackAsset/<get_outputs>d__65::.ctor(System.Int32)
extern void U3Cget_outputsU3Ed__65__ctor_m36D55B6998402316E9C824384D2823CF2887FA99 (void);
// 0x000001B2 System.Void UnityEngine.Timeline.TrackAsset/<get_outputs>d__65::System.IDisposable.Dispose()
extern void U3Cget_outputsU3Ed__65_System_IDisposable_Dispose_m1E8207D4EAFDAEFB62061AD6D689431BD40999AB (void);
// 0x000001B3 System.Boolean UnityEngine.Timeline.TrackAsset/<get_outputs>d__65::MoveNext()
extern void U3Cget_outputsU3Ed__65_MoveNext_mB5F1A5655A42CB9A832A2B7B941A3DA555E65F36 (void);
// 0x000001B4 UnityEngine.Playables.PlayableBinding UnityEngine.Timeline.TrackAsset/<get_outputs>d__65::System.Collections.Generic.IEnumerator<UnityEngine.Playables.PlayableBinding>.get_Current()
extern void U3Cget_outputsU3Ed__65_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_mA6C35B64EBEC6BDA1221AC7F991E746D1729589D (void);
// 0x000001B5 System.Void UnityEngine.Timeline.TrackAsset/<get_outputs>d__65::System.Collections.IEnumerator.Reset()
extern void U3Cget_outputsU3Ed__65_System_Collections_IEnumerator_Reset_m322763B2D18B71E3A120A80829AB3CBE23709A03 (void);
// 0x000001B6 System.Object UnityEngine.Timeline.TrackAsset/<get_outputs>d__65::System.Collections.IEnumerator.get_Current()
extern void U3Cget_outputsU3Ed__65_System_Collections_IEnumerator_get_Current_mF3FCDDFE0515DC769DD6EF0A8D6ABADA7BEE9302 (void);
// 0x000001B7 System.Collections.Generic.IEnumerator`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.TrackAsset/<get_outputs>d__65::System.Collections.Generic.IEnumerable<UnityEngine.Playables.PlayableBinding>.GetEnumerator()
extern void U3Cget_outputsU3Ed__65_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m1EFEC630120231AFC3D898AFCDD0756F9561543D (void);
// 0x000001B8 System.Collections.IEnumerator UnityEngine.Timeline.TrackAsset/<get_outputs>d__65::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_outputsU3Ed__65_System_Collections_IEnumerable_GetEnumerator_mD5E8220BC3E60F92FA1CFDD991151AAE34C519B2 (void);
// 0x000001B9 System.Void UnityEngine.Timeline.TrackAsset/<>c::.cctor()
extern void U3CU3Ec__cctor_m4CF50A8244F0E8B9535D4C7B45D8D6B2D4C3F07A (void);
// 0x000001BA System.Void UnityEngine.Timeline.TrackAsset/<>c::.ctor()
extern void U3CU3Ec__ctor_m0071EE643C6662AFC30FD5F241BD4F6B05382B91 (void);
// 0x000001BB System.Int32 UnityEngine.Timeline.TrackAsset/<>c::<SortClips>b__121_0(UnityEngine.Timeline.TimelineClip,UnityEngine.Timeline.TimelineClip)
extern void U3CU3Ec_U3CSortClipsU3Eb__121_0_mBD715AA0E013972CC995FFB813CBACEAFD120E71 (void);
// 0x000001BC System.Void UnityEngine.Timeline.TimelineHelpURLAttribute::.ctor(System.Type)
extern void TimelineHelpURLAttribute__ctor_mEB91B85CCCBEC196457E5FCB18A6C9C13E48CEBC (void);
// 0x000001BD UnityEngine.Color UnityEngine.Timeline.TrackColorAttribute::get_color()
extern void TrackColorAttribute_get_color_m5AC7558C98A60D996F1C3A9F1EB6F180FB45FFE4 (void);
// 0x000001BE System.Void UnityEngine.Timeline.TrackColorAttribute::.ctor(System.Single,System.Single,System.Single)
extern void TrackColorAttribute__ctor_mBC50DD796426AAD66A6A8BB7BE46EBB7873AA006 (void);
// 0x000001BF System.Void UnityEngine.Timeline.AudioClipProperties::.ctor()
extern void AudioClipProperties__ctor_m044736544A12AD2A937225432C158B5BDA240B83 (void);
// 0x000001C0 System.Void UnityEngine.Timeline.AudioMixerProperties::PrepareFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void AudioMixerProperties_PrepareFrame_m84E02C27BA11487134C9360A3A3F2BF90A3CDAD8 (void);
// 0x000001C1 System.Void UnityEngine.Timeline.AudioMixerProperties::.ctor()
extern void AudioMixerProperties__ctor_mB4EC402EBE2B6C8F3CF268598319F789AF837592 (void);
// 0x000001C2 System.Single UnityEngine.Timeline.AudioPlayableAsset::get_bufferingTime()
extern void AudioPlayableAsset_get_bufferingTime_mE7FFA2B05A64D29C552930DFAA17A57CFE397031 (void);
// 0x000001C3 System.Void UnityEngine.Timeline.AudioPlayableAsset::set_bufferingTime(System.Single)
extern void AudioPlayableAsset_set_bufferingTime_mDA418332D841FE5667E21538BBAEC16041A07032 (void);
// 0x000001C4 UnityEngine.AudioClip UnityEngine.Timeline.AudioPlayableAsset::get_clip()
extern void AudioPlayableAsset_get_clip_m49311E74FD58B38CC0D8ED16A0D8869F4ADAB720 (void);
// 0x000001C5 System.Void UnityEngine.Timeline.AudioPlayableAsset::set_clip(UnityEngine.AudioClip)
extern void AudioPlayableAsset_set_clip_m2A3379A7D58655D8C6B0395F262B6829FDCAD6EB (void);
// 0x000001C6 System.Boolean UnityEngine.Timeline.AudioPlayableAsset::get_loop()
extern void AudioPlayableAsset_get_loop_m579753DC7E58ACCA36A44E217C28EBF40CC3C294 (void);
// 0x000001C7 System.Void UnityEngine.Timeline.AudioPlayableAsset::set_loop(System.Boolean)
extern void AudioPlayableAsset_set_loop_m594EA5EE80017E2F1C15FE170CAD6F22D01EE7DD (void);
// 0x000001C8 System.Double UnityEngine.Timeline.AudioPlayableAsset::get_duration()
extern void AudioPlayableAsset_get_duration_m44FF0A8D8526B9EFBA604BFCDF79DB380E5B76BB (void);
// 0x000001C9 System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.AudioPlayableAsset::get_outputs()
extern void AudioPlayableAsset_get_outputs_m40AC20DF1E9E352D9C2EA015742D42E7F9C73E6E (void);
// 0x000001CA UnityEngine.Playables.Playable UnityEngine.Timeline.AudioPlayableAsset::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject)
extern void AudioPlayableAsset_CreatePlayable_mBFEC2FDD97D15CA9376CAAF65FE217AAC6E854BB (void);
// 0x000001CB UnityEngine.Timeline.ClipCaps UnityEngine.Timeline.AudioPlayableAsset::get_clipCaps()
extern void AudioPlayableAsset_get_clipCaps_m7DF29DF049D9F0407E9AFD7D209AB825A8743F80 (void);
// 0x000001CC System.Void UnityEngine.Timeline.AudioPlayableAsset::.ctor()
extern void AudioPlayableAsset__ctor_mF10BFB16BB64A6F8D0FE62D7A0B8335ADB119A76 (void);
// 0x000001CD System.Void UnityEngine.Timeline.AudioPlayableAsset/<get_outputs>d__16::.ctor(System.Int32)
extern void U3Cget_outputsU3Ed__16__ctor_m61F54FD6956E98C4C644EC8189368AB432C0F0D4 (void);
// 0x000001CE System.Void UnityEngine.Timeline.AudioPlayableAsset/<get_outputs>d__16::System.IDisposable.Dispose()
extern void U3Cget_outputsU3Ed__16_System_IDisposable_Dispose_m72E1B27D3C90C82B4A415D51E3B2177CED2BE178 (void);
// 0x000001CF System.Boolean UnityEngine.Timeline.AudioPlayableAsset/<get_outputs>d__16::MoveNext()
extern void U3Cget_outputsU3Ed__16_MoveNext_m6D77A67836D44DDA516C67755527D4DD24E1F9A5 (void);
// 0x000001D0 UnityEngine.Playables.PlayableBinding UnityEngine.Timeline.AudioPlayableAsset/<get_outputs>d__16::System.Collections.Generic.IEnumerator<UnityEngine.Playables.PlayableBinding>.get_Current()
extern void U3Cget_outputsU3Ed__16_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_mBE6C905012CCD38CF8A2DD6A4815FDE3772370D2 (void);
// 0x000001D1 System.Void UnityEngine.Timeline.AudioPlayableAsset/<get_outputs>d__16::System.Collections.IEnumerator.Reset()
extern void U3Cget_outputsU3Ed__16_System_Collections_IEnumerator_Reset_m026B2E69F6C94660273E8386AC24B908344881CE (void);
// 0x000001D2 System.Object UnityEngine.Timeline.AudioPlayableAsset/<get_outputs>d__16::System.Collections.IEnumerator.get_Current()
extern void U3Cget_outputsU3Ed__16_System_Collections_IEnumerator_get_Current_m582D28A2EE588AF6E3ACADE39F5EE74A2B852D1B (void);
// 0x000001D3 System.Collections.Generic.IEnumerator`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.AudioPlayableAsset/<get_outputs>d__16::System.Collections.Generic.IEnumerable<UnityEngine.Playables.PlayableBinding>.GetEnumerator()
extern void U3Cget_outputsU3Ed__16_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m8038F14452BCFDB7F80F35661C0BAB7CB2201C4B (void);
// 0x000001D4 System.Collections.IEnumerator UnityEngine.Timeline.AudioPlayableAsset/<get_outputs>d__16::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_outputsU3Ed__16_System_Collections_IEnumerable_GetEnumerator_mD88200543F030A8F6F9ADFBCA257B6DB811ED84E (void);
// 0x000001D5 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.AudioTrack::CreateClip(UnityEngine.AudioClip)
extern void AudioTrack_CreateClip_m8CBE84BCC1FF99D6E93FD790F9BAD277F2BC6D9E (void);
// 0x000001D6 UnityEngine.Playables.Playable UnityEngine.Timeline.AudioTrack::CompileClips(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,System.Collections.Generic.IList`1<UnityEngine.Timeline.TimelineClip>,UnityEngine.Timeline.IntervalTree`1<UnityEngine.Timeline.RuntimeElement>)
extern void AudioTrack_CompileClips_m641F177F94C9BA21B517CCDBF43C25ABC605BF28 (void);
// 0x000001D7 System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.AudioTrack::get_outputs()
extern void AudioTrack_get_outputs_m8B9BE825525351885F322DCEBB5A8FA7D6AFEE89 (void);
// 0x000001D8 System.Void UnityEngine.Timeline.AudioTrack::OnValidate()
extern void AudioTrack_OnValidate_mB46DE622AEA6652C4248854BF0DB7607FA704960 (void);
// 0x000001D9 System.Void UnityEngine.Timeline.AudioTrack::.ctor()
extern void AudioTrack__ctor_m6EEC48668D6F9248F48B2B0686291560444B8F35 (void);
// 0x000001DA System.Void UnityEngine.Timeline.AudioTrack/<get_outputs>d__4::.ctor(System.Int32)
extern void U3Cget_outputsU3Ed__4__ctor_m190710D72768E977B81519D942515031DCF91C88 (void);
// 0x000001DB System.Void UnityEngine.Timeline.AudioTrack/<get_outputs>d__4::System.IDisposable.Dispose()
extern void U3Cget_outputsU3Ed__4_System_IDisposable_Dispose_m5824B5C4F4CA22007A3FEEE9AB75AA4BA9AEC83C (void);
// 0x000001DC System.Boolean UnityEngine.Timeline.AudioTrack/<get_outputs>d__4::MoveNext()
extern void U3Cget_outputsU3Ed__4_MoveNext_m636D185FBF8DAF5943220DF0FD5AE6FA2C7C7999 (void);
// 0x000001DD UnityEngine.Playables.PlayableBinding UnityEngine.Timeline.AudioTrack/<get_outputs>d__4::System.Collections.Generic.IEnumerator<UnityEngine.Playables.PlayableBinding>.get_Current()
extern void U3Cget_outputsU3Ed__4_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m75314CB64DC2DC82417162EFFB901A50D6E60C51 (void);
// 0x000001DE System.Void UnityEngine.Timeline.AudioTrack/<get_outputs>d__4::System.Collections.IEnumerator.Reset()
extern void U3Cget_outputsU3Ed__4_System_Collections_IEnumerator_Reset_mBE20566179B7912EC30C3BFFC5A8781486E25E58 (void);
// 0x000001DF System.Object UnityEngine.Timeline.AudioTrack/<get_outputs>d__4::System.Collections.IEnumerator.get_Current()
extern void U3Cget_outputsU3Ed__4_System_Collections_IEnumerator_get_Current_m2F2377F982B6CA15109F25D7E87D68030B432BA5 (void);
// 0x000001E0 System.Collections.Generic.IEnumerator`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.AudioTrack/<get_outputs>d__4::System.Collections.Generic.IEnumerable<UnityEngine.Playables.PlayableBinding>.GetEnumerator()
extern void U3Cget_outputsU3Ed__4_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m7C348C673AFB4692FE68B667BF09E0F8C8B3E6A1 (void);
// 0x000001E1 System.Collections.IEnumerator UnityEngine.Timeline.AudioTrack/<get_outputs>d__4::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_outputsU3Ed__4_System_Collections_IEnumerable_GetEnumerator_mD0AB6E0CA93EA531B030519FAB433F4F25D27CD0 (void);
// 0x000001E2 System.Boolean UnityEngine.Timeline.TimelineClipCapsExtensions::SupportsLooping(UnityEngine.Timeline.TimelineClip)
extern void TimelineClipCapsExtensions_SupportsLooping_m149B53ABA1636A092CD5BB849C19277D93D4B935 (void);
// 0x000001E3 System.Boolean UnityEngine.Timeline.TimelineClipCapsExtensions::SupportsExtrapolation(UnityEngine.Timeline.TimelineClip)
extern void TimelineClipCapsExtensions_SupportsExtrapolation_m3C0E25676F88C9DEEF4149DC2158A4A18711EE3A (void);
// 0x000001E4 System.Boolean UnityEngine.Timeline.TimelineClipCapsExtensions::SupportsClipIn(UnityEngine.Timeline.TimelineClip)
extern void TimelineClipCapsExtensions_SupportsClipIn_m318FDA5D82ED5E932A5E9C195668685F4571EEF9 (void);
// 0x000001E5 System.Boolean UnityEngine.Timeline.TimelineClipCapsExtensions::SupportsSpeedMultiplier(UnityEngine.Timeline.TimelineClip)
extern void TimelineClipCapsExtensions_SupportsSpeedMultiplier_mC1AAE95266F6201D78F17EBAB47D7923341BBD30 (void);
// 0x000001E6 System.Boolean UnityEngine.Timeline.TimelineClipCapsExtensions::SupportsBlending(UnityEngine.Timeline.TimelineClip)
extern void TimelineClipCapsExtensions_SupportsBlending_m569C3ED6D06142794DA7B1FD6C00062A209E7885 (void);
// 0x000001E7 System.Boolean UnityEngine.Timeline.TimelineClipCapsExtensions::HasAll(UnityEngine.Timeline.ClipCaps,UnityEngine.Timeline.ClipCaps)
extern void TimelineClipCapsExtensions_HasAll_m2596307B9954E6FCEFDDFD4A0BE87D936DE31BF9 (void);
// 0x000001E8 System.Boolean UnityEngine.Timeline.TimelineClipCapsExtensions::HasAny(UnityEngine.Timeline.ClipCaps,UnityEngine.Timeline.ClipCaps)
extern void TimelineClipCapsExtensions_HasAny_mA750DF1B0964785A2FC8DC2F9A05CF03D27F1670 (void);
// 0x000001E9 System.Boolean UnityEngine.Timeline.ControlPlayableAsset::get_controllingDirectors()
extern void ControlPlayableAsset_get_controllingDirectors_m0370250573BBDA45A8E8B086FFDE90ED5B1961CD (void);
// 0x000001EA System.Void UnityEngine.Timeline.ControlPlayableAsset::set_controllingDirectors(System.Boolean)
extern void ControlPlayableAsset_set_controllingDirectors_m8D263E063F860FDF4E832CC9C284C45D49FA9270 (void);
// 0x000001EB System.Boolean UnityEngine.Timeline.ControlPlayableAsset::get_controllingParticles()
extern void ControlPlayableAsset_get_controllingParticles_mEDF1CC5E356EA9B115EF4F35F3509ABB93F5D44A (void);
// 0x000001EC System.Void UnityEngine.Timeline.ControlPlayableAsset::set_controllingParticles(System.Boolean)
extern void ControlPlayableAsset_set_controllingParticles_m31709F8C09DB20C7551FDC488A0127295FD0B219 (void);
// 0x000001ED System.Void UnityEngine.Timeline.ControlPlayableAsset::OnEnable()
extern void ControlPlayableAsset_OnEnable_m63F67A81F41C02520284308515022DF19FA2FBE4 (void);
// 0x000001EE System.Double UnityEngine.Timeline.ControlPlayableAsset::get_duration()
extern void ControlPlayableAsset_get_duration_m0C2033B0C5C3E6DE7385797DE1B07F0263521878 (void);
// 0x000001EF UnityEngine.Timeline.ClipCaps UnityEngine.Timeline.ControlPlayableAsset::get_clipCaps()
extern void ControlPlayableAsset_get_clipCaps_m09BC8BAA1440D221483F653285DA52715346F22A (void);
// 0x000001F0 UnityEngine.Playables.Playable UnityEngine.Timeline.ControlPlayableAsset::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject)
extern void ControlPlayableAsset_CreatePlayable_m88A68ADB8119BC891B37E1B9CCA7FD26090914D8 (void);
// 0x000001F1 UnityEngine.Playables.Playable UnityEngine.Timeline.ControlPlayableAsset::ConnectPlayablesToMixer(UnityEngine.Playables.PlayableGraph,System.Collections.Generic.List`1<UnityEngine.Playables.Playable>)
extern void ControlPlayableAsset_ConnectPlayablesToMixer_m8020203CFDC98E5674106B99938017E8D9E798CC (void);
// 0x000001F2 System.Void UnityEngine.Timeline.ControlPlayableAsset::CreateActivationPlayable(UnityEngine.GameObject,UnityEngine.Playables.PlayableGraph,System.Collections.Generic.List`1<UnityEngine.Playables.Playable>)
extern void ControlPlayableAsset_CreateActivationPlayable_m8BC4706CA44F6216AE3C3169D03BEE15BC819111 (void);
// 0x000001F3 System.Void UnityEngine.Timeline.ControlPlayableAsset::SearchHierarchyAndConnectParticleSystem(System.Collections.Generic.IEnumerable`1<UnityEngine.ParticleSystem>,UnityEngine.Playables.PlayableGraph,System.Collections.Generic.List`1<UnityEngine.Playables.Playable>)
extern void ControlPlayableAsset_SearchHierarchyAndConnectParticleSystem_m6D97733D38AAE740196B5AFA9C75A45AB54114B4 (void);
// 0x000001F4 System.Void UnityEngine.Timeline.ControlPlayableAsset::SearchHierarchyAndConnectDirector(System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableDirector>,UnityEngine.Playables.PlayableGraph,System.Collections.Generic.List`1<UnityEngine.Playables.Playable>,System.Boolean)
extern void ControlPlayableAsset_SearchHierarchyAndConnectDirector_m9AD1670D0949F2D418DBF34152170810855A18AB (void);
// 0x000001F5 System.Void UnityEngine.Timeline.ControlPlayableAsset::SearchHierarchyAndConnectControlableScripts(System.Collections.Generic.IEnumerable`1<UnityEngine.MonoBehaviour>,UnityEngine.Playables.PlayableGraph,System.Collections.Generic.List`1<UnityEngine.Playables.Playable>)
extern void ControlPlayableAsset_SearchHierarchyAndConnectControlableScripts_m46A0974334C5CAB121787DC91ED6C1A9E07B33BC (void);
// 0x000001F6 System.Void UnityEngine.Timeline.ControlPlayableAsset::ConnectMixerAndPlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.Playables.Playable,UnityEngine.Playables.Playable,System.Int32)
extern void ControlPlayableAsset_ConnectMixerAndPlayable_m8727A31B27ECFA1AF0B7478B8514F3D7B27C8459 (void);
// 0x000001F7 System.Collections.Generic.IList`1<T> UnityEngine.Timeline.ControlPlayableAsset::GetComponent(UnityEngine.GameObject)
// 0x000001F8 System.Collections.Generic.IEnumerable`1<UnityEngine.MonoBehaviour> UnityEngine.Timeline.ControlPlayableAsset::GetControlableScripts(UnityEngine.GameObject)
extern void ControlPlayableAsset_GetControlableScripts_mDAC0BC709F7024BAC5F520AA31F565824AE542A2 (void);
// 0x000001F9 System.Void UnityEngine.Timeline.ControlPlayableAsset::UpdateDurationAndLoopFlag(System.Collections.Generic.IList`1<UnityEngine.Playables.PlayableDirector>,System.Collections.Generic.IList`1<UnityEngine.ParticleSystem>)
extern void ControlPlayableAsset_UpdateDurationAndLoopFlag_m3B3976CA44009F140D54203A5A696A341A815669 (void);
// 0x000001FA System.Collections.Generic.IList`1<UnityEngine.ParticleSystem> UnityEngine.Timeline.ControlPlayableAsset::GetControllableParticleSystems(UnityEngine.GameObject)
extern void ControlPlayableAsset_GetControllableParticleSystems_m182DE87B71BF287B5D828C7D48D099C0C3B4F85A (void);
// 0x000001FB System.Void UnityEngine.Timeline.ControlPlayableAsset::GetControllableParticleSystems(UnityEngine.Transform,System.Collections.Generic.ICollection`1<UnityEngine.ParticleSystem>,System.Collections.Generic.HashSet`1<UnityEngine.ParticleSystem>)
extern void ControlPlayableAsset_GetControllableParticleSystems_mB7DA5089B5C09BC5876DEA587D20550184A69BC4 (void);
// 0x000001FC System.Void UnityEngine.Timeline.ControlPlayableAsset::CacheSubEmitters(UnityEngine.ParticleSystem,System.Collections.Generic.HashSet`1<UnityEngine.ParticleSystem>)
extern void ControlPlayableAsset_CacheSubEmitters_m823886C0C0F1C8AA13129B3A9810CDA8AAF2EE7B (void);
// 0x000001FD System.Void UnityEngine.Timeline.ControlPlayableAsset::GatherProperties(UnityEngine.Playables.PlayableDirector,UnityEngine.Timeline.IPropertyCollector)
extern void ControlPlayableAsset_GatherProperties_mEB211D7180EBC912CAEE8C6521F67AE4AC240FDB (void);
// 0x000001FE System.Void UnityEngine.Timeline.ControlPlayableAsset::PreviewParticles(UnityEngine.Timeline.IPropertyCollector,System.Collections.Generic.IEnumerable`1<UnityEngine.ParticleSystem>)
extern void ControlPlayableAsset_PreviewParticles_m611AFD61ACF20FDD50C82D09230C32B7D0E3D94E (void);
// 0x000001FF System.Void UnityEngine.Timeline.ControlPlayableAsset::PreviewActivation(UnityEngine.Timeline.IPropertyCollector,System.Collections.Generic.IEnumerable`1<UnityEngine.GameObject>)
extern void ControlPlayableAsset_PreviewActivation_m0FAEA1926A9ACFC55CE3CF14992F5A4DE1F1368E (void);
// 0x00000200 System.Void UnityEngine.Timeline.ControlPlayableAsset::PreviewTimeControl(UnityEngine.Timeline.IPropertyCollector,UnityEngine.Playables.PlayableDirector,System.Collections.Generic.IEnumerable`1<UnityEngine.MonoBehaviour>)
extern void ControlPlayableAsset_PreviewTimeControl_m38FFD36A41ECA2BA874D7A7DA234E15EFAB2A043 (void);
// 0x00000201 System.Void UnityEngine.Timeline.ControlPlayableAsset::PreviewDirectors(UnityEngine.Timeline.IPropertyCollector,System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableDirector>)
extern void ControlPlayableAsset_PreviewDirectors_mA5DF7CF7A9F2ECFAE8C59FEDD944BDD4C25AE118 (void);
// 0x00000202 System.Void UnityEngine.Timeline.ControlPlayableAsset::.ctor()
extern void ControlPlayableAsset__ctor_mCB87B77766491D451517D1082079113798B8518A (void);
// 0x00000203 System.Void UnityEngine.Timeline.ControlPlayableAsset::.cctor()
extern void ControlPlayableAsset__cctor_m1343221BA07342B50EF52CA584CAFF1508E50BA5 (void);
// 0x00000204 System.Void UnityEngine.Timeline.ControlPlayableAsset/<GetControlableScripts>d__39::.ctor(System.Int32)
extern void U3CGetControlableScriptsU3Ed__39__ctor_m61DC7C12C95430EF9C16D27768169F05CC5784D0 (void);
// 0x00000205 System.Void UnityEngine.Timeline.ControlPlayableAsset/<GetControlableScripts>d__39::System.IDisposable.Dispose()
extern void U3CGetControlableScriptsU3Ed__39_System_IDisposable_Dispose_mF25100DBDEB7B203BC1DD6CCBE34A94797D68A46 (void);
// 0x00000206 System.Boolean UnityEngine.Timeline.ControlPlayableAsset/<GetControlableScripts>d__39::MoveNext()
extern void U3CGetControlableScriptsU3Ed__39_MoveNext_m0A28A750413795A869B753439AC20C1E8AC2E33A (void);
// 0x00000207 UnityEngine.MonoBehaviour UnityEngine.Timeline.ControlPlayableAsset/<GetControlableScripts>d__39::System.Collections.Generic.IEnumerator<UnityEngine.MonoBehaviour>.get_Current()
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_Generic_IEnumeratorU3CUnityEngine_MonoBehaviourU3E_get_Current_m288919711BCB540205566CDF9B9339EC8EBB2DB1 (void);
// 0x00000208 System.Void UnityEngine.Timeline.ControlPlayableAsset/<GetControlableScripts>d__39::System.Collections.IEnumerator.Reset()
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerator_Reset_mBCFCCD39392B14C63C54D9079BB6E3302DC858B0 (void);
// 0x00000209 System.Object UnityEngine.Timeline.ControlPlayableAsset/<GetControlableScripts>d__39::System.Collections.IEnumerator.get_Current()
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerator_get_Current_m0C00993614DC7FD44F070DD625C5325A84301309 (void);
// 0x0000020A System.Collections.Generic.IEnumerator`1<UnityEngine.MonoBehaviour> UnityEngine.Timeline.ControlPlayableAsset/<GetControlableScripts>d__39::System.Collections.Generic.IEnumerable<UnityEngine.MonoBehaviour>.GetEnumerator()
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_Generic_IEnumerableU3CUnityEngine_MonoBehaviourU3E_GetEnumerator_mAC5B005DF8CED15F0652A85E921526C4E9E245C9 (void);
// 0x0000020B System.Collections.IEnumerator UnityEngine.Timeline.ControlPlayableAsset/<GetControlableScripts>d__39::System.Collections.IEnumerable.GetEnumerator()
extern void U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerable_GetEnumerator_m51F55F60727D8A0C7F5CD7F862853E8B041F1910 (void);
// 0x0000020C System.Void UnityEngine.Timeline.ControlTrack::.ctor()
extern void ControlTrack__ctor_m0B4C0633844F60987B07ABC4467976B46AB0975D (void);
// 0x0000020D System.Double UnityEngine.Timeline.DiscreteTime::get_tickValue()
extern void DiscreteTime_get_tickValue_m0ACC9CCCEB39D170368ACD3FD094781F6D01EAFD (void);
// 0x0000020E System.Void UnityEngine.Timeline.DiscreteTime::.ctor(UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime__ctor_mA6613BBA0DEB0A0BCCED50838378D9D9971B9F62 (void);
// 0x0000020F System.Void UnityEngine.Timeline.DiscreteTime::.ctor(System.Int64)
extern void DiscreteTime__ctor_mF5CD806CC7273F8C5CC80053A78D09604DED0491 (void);
// 0x00000210 System.Void UnityEngine.Timeline.DiscreteTime::.ctor(System.Double)
extern void DiscreteTime__ctor_mF858F11921F841F599EF51C7975430BEA8EC3B52 (void);
// 0x00000211 System.Void UnityEngine.Timeline.DiscreteTime::.ctor(System.Single)
extern void DiscreteTime__ctor_m7D7C3098B16F74497C5FC907B364C50A667EB2C8 (void);
// 0x00000212 System.Void UnityEngine.Timeline.DiscreteTime::.ctor(System.Int32)
extern void DiscreteTime__ctor_mCD4A3BE5EB3142C4247190F7896378B68F428F69 (void);
// 0x00000213 System.Void UnityEngine.Timeline.DiscreteTime::.ctor(System.Int32,System.Double)
extern void DiscreteTime__ctor_m5C84429A52611671EE6F2D7BCD1399335D8242F5 (void);
// 0x00000214 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::OneTickBefore()
extern void DiscreteTime_OneTickBefore_m06F6D6A227D8356FCD3283CD59571DFA12943D1A (void);
// 0x00000215 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::OneTickAfter()
extern void DiscreteTime_OneTickAfter_mD2C58DF00885E44EA0D7AF8C9F56C14F77E53EB7 (void);
// 0x00000216 System.Int64 UnityEngine.Timeline.DiscreteTime::GetTick()
extern void DiscreteTime_GetTick_m52C7EE5F63087831CF4B21EF76A00655181349AA (void);
// 0x00000217 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::FromTicks(System.Int64)
extern void DiscreteTime_FromTicks_m39AD4CB20BF1DA744A07F93B9FCA9F0F75DE9BDD (void);
// 0x00000218 System.Int32 UnityEngine.Timeline.DiscreteTime::CompareTo(System.Object)
extern void DiscreteTime_CompareTo_m9A2C892A6E4097A5FBC0C2D5D666E01CE12427EA (void);
// 0x00000219 System.Boolean UnityEngine.Timeline.DiscreteTime::Equals(UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_Equals_m3D269E4164E6E705E6DD19B557F9DBFC2CB226FA (void);
// 0x0000021A System.Boolean UnityEngine.Timeline.DiscreteTime::Equals(System.Object)
extern void DiscreteTime_Equals_m42B27C6DDF993E36A0C3D20B708986277AE6B7FD (void);
// 0x0000021B System.Int64 UnityEngine.Timeline.DiscreteTime::DoubleToDiscreteTime(System.Double)
extern void DiscreteTime_DoubleToDiscreteTime_m8D83412A88CCAC93ABD5070209109957FCA85762 (void);
// 0x0000021C System.Int64 UnityEngine.Timeline.DiscreteTime::FloatToDiscreteTime(System.Single)
extern void DiscreteTime_FloatToDiscreteTime_m0F57E8699783D9B2A48381F34D2116E71F3EA559 (void);
// 0x0000021D System.Int64 UnityEngine.Timeline.DiscreteTime::IntToDiscreteTime(System.Int32)
extern void DiscreteTime_IntToDiscreteTime_m17C3BF4B229B41B91CCACB7F465628A9ACA4736D (void);
// 0x0000021E System.Double UnityEngine.Timeline.DiscreteTime::ToDouble(System.Int64)
extern void DiscreteTime_ToDouble_m319F0D5276BC26C56CDC124BC7878DB535351348 (void);
// 0x0000021F System.Single UnityEngine.Timeline.DiscreteTime::ToFloat(System.Int64)
extern void DiscreteTime_ToFloat_mA77F564AE95883062A631DAF86C4F401567CEB0E (void);
// 0x00000220 System.Double UnityEngine.Timeline.DiscreteTime::op_Explicit(UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_Explicit_m801A8089D31AA0C435943A08C89A83607FACD52B (void);
// 0x00000221 System.Single UnityEngine.Timeline.DiscreteTime::op_Explicit(UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_Explicit_m08D29220F020CB6A40CC9D59B5B4104BBA7599D0 (void);
// 0x00000222 System.Int64 UnityEngine.Timeline.DiscreteTime::op_Explicit(UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_Explicit_m7C11B47C356E41AB9F96ED46AE8BBFA05C1B3941 (void);
// 0x00000223 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::op_Explicit(System.Double)
extern void DiscreteTime_op_Explicit_mE186D14B06F947B0B372625D4E927566B0161874 (void);
// 0x00000224 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::op_Explicit(System.Single)
extern void DiscreteTime_op_Explicit_mCA9C09F74934D46B5635C42C9984C8C84589CD30 (void);
// 0x00000225 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::op_Implicit(System.Int32)
extern void DiscreteTime_op_Implicit_m8EEF2A52EFFBFC375CB1D8171DC439A22DC35ECB (void);
// 0x00000226 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::op_Explicit(System.Int64)
extern void DiscreteTime_op_Explicit_m42F903A62DF258BD11463C461991AA5DE89AA71D (void);
// 0x00000227 System.Boolean UnityEngine.Timeline.DiscreteTime::op_Equality(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_Equality_m55FD471B465FFDAC78BE2413701203194CFEADC4 (void);
// 0x00000228 System.Boolean UnityEngine.Timeline.DiscreteTime::op_Inequality(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_Inequality_m19BD9CC31D1A2FBDA0CC50BA96EA37463F404AF6 (void);
// 0x00000229 System.Boolean UnityEngine.Timeline.DiscreteTime::op_GreaterThan(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_GreaterThan_m3F023E3705B46C7E548730E10C543416EACEBC45 (void);
// 0x0000022A System.Boolean UnityEngine.Timeline.DiscreteTime::op_LessThan(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_LessThan_m3CD38C215D6813414BFB1AC88BAF5BF1796C49D1 (void);
// 0x0000022B System.Boolean UnityEngine.Timeline.DiscreteTime::op_LessThanOrEqual(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_LessThanOrEqual_m8E8286335E92963611D6C0F6598B20E61CBE2F14 (void);
// 0x0000022C System.Boolean UnityEngine.Timeline.DiscreteTime::op_GreaterThanOrEqual(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_GreaterThanOrEqual_m04A7C9DC1DACEBE2FF6201F7B81C5CE59518A57D (void);
// 0x0000022D UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::op_Addition(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_Addition_mCE0392D2D1FB0BD53AD56C9A287810D1672B2BC8 (void);
// 0x0000022E UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::op_Subtraction(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_op_Subtraction_mA27858E3E6CA4BBD578B1CE193504549AA916FC5 (void);
// 0x0000022F System.String UnityEngine.Timeline.DiscreteTime::ToString()
extern void DiscreteTime_ToString_mFE5EEC6255B3AA617F692DA9EB7DF4A5AD71FD83 (void);
// 0x00000230 System.Int32 UnityEngine.Timeline.DiscreteTime::GetHashCode()
extern void DiscreteTime_GetHashCode_m59E889384FD1F78DC2A1220C228EAB164F8995CE (void);
// 0x00000231 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::Min(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_Min_m6FF92F93C9C652F5794A44BCADF4C1F6D15A3140 (void);
// 0x00000232 UnityEngine.Timeline.DiscreteTime UnityEngine.Timeline.DiscreteTime::Max(UnityEngine.Timeline.DiscreteTime,UnityEngine.Timeline.DiscreteTime)
extern void DiscreteTime_Max_mE6741CA89EADA7D3EF47C29C8C9D0D57CC8597B6 (void);
// 0x00000233 System.Double UnityEngine.Timeline.DiscreteTime::SnapToNearestTick(System.Double)
extern void DiscreteTime_SnapToNearestTick_mBA4C390A09ED03D90BE0D8C0CD39949AAC76AF03 (void);
// 0x00000234 System.Single UnityEngine.Timeline.DiscreteTime::SnapToNearestTick(System.Single)
extern void DiscreteTime_SnapToNearestTick_m563A71A5A432C7676FC6692CF68DD81FF2020AB1 (void);
// 0x00000235 System.Int64 UnityEngine.Timeline.DiscreteTime::GetNearestTick(System.Double)
extern void DiscreteTime_GetNearestTick_mC4A04B29410685B1864597BCB83664F6B2F95367 (void);
// 0x00000236 System.Void UnityEngine.Timeline.DiscreteTime::.cctor()
extern void DiscreteTime__cctor_m23DF1F1B6C69F8785475607E6188362FE65E123F (void);
// 0x00000237 System.Void UnityEngine.Timeline.InfiniteRuntimeClip::.ctor(UnityEngine.Playables.Playable)
extern void InfiniteRuntimeClip__ctor_m807AE0572975FDC87883AC4877442F7E986B7812 (void);
// 0x00000238 System.Int64 UnityEngine.Timeline.InfiniteRuntimeClip::get_intervalStart()
extern void InfiniteRuntimeClip_get_intervalStart_m9F26CE71EA9F461A924BF1AF6892154B8077FF8D (void);
// 0x00000239 System.Int64 UnityEngine.Timeline.InfiniteRuntimeClip::get_intervalEnd()
extern void InfiniteRuntimeClip_get_intervalEnd_mB05A86E64F77260F1CC9EF60285E9985106A7D41 (void);
// 0x0000023A System.Void UnityEngine.Timeline.InfiniteRuntimeClip::set_enable(System.Boolean)
extern void InfiniteRuntimeClip_set_enable_m950C18673BCCA53BA644B8998BE3A00FC37CBE2D (void);
// 0x0000023B System.Void UnityEngine.Timeline.InfiniteRuntimeClip::EvaluateAt(System.Double,UnityEngine.Playables.FrameData)
extern void InfiniteRuntimeClip_EvaluateAt_m3A6147F9BB6B625BA721C1C2FCE0DFCBBDB1DF8E (void);
// 0x0000023C System.Void UnityEngine.Timeline.InfiniteRuntimeClip::DisableAt(System.Double,System.Double,UnityEngine.Playables.FrameData)
extern void InfiniteRuntimeClip_DisableAt_mBE37CED8ED2A4643EDC99BADD76AF0E344A4929E (void);
// 0x0000023D System.Void UnityEngine.Timeline.InfiniteRuntimeClip::.cctor()
extern void InfiniteRuntimeClip__cctor_mE142C87FB9D4550C966F87FB581863C614E7CD8A (void);
// 0x0000023E System.Int64 UnityEngine.Timeline.IInterval::get_intervalStart()
// 0x0000023F System.Int64 UnityEngine.Timeline.IInterval::get_intervalEnd()
// 0x00000240 System.Boolean UnityEngine.Timeline.IntervalTree`1::get_dirty()
// 0x00000241 System.Void UnityEngine.Timeline.IntervalTree`1::set_dirty(System.Boolean)
// 0x00000242 System.Void UnityEngine.Timeline.IntervalTree`1::Add(T)
// 0x00000243 System.Void UnityEngine.Timeline.IntervalTree`1::IntersectsWith(System.Int64,System.Collections.Generic.List`1<T>)
// 0x00000244 System.Void UnityEngine.Timeline.IntervalTree`1::IntersectsWithRange(System.Int64,System.Int64,System.Collections.Generic.List`1<T>)
// 0x00000245 System.Void UnityEngine.Timeline.IntervalTree`1::UpdateIntervals()
// 0x00000246 System.Void UnityEngine.Timeline.IntervalTree`1::Query(UnityEngine.Timeline.IntervalTreeNode,System.Int64,System.Collections.Generic.List`1<T>)
// 0x00000247 System.Void UnityEngine.Timeline.IntervalTree`1::QueryRange(UnityEngine.Timeline.IntervalTreeNode,System.Int64,System.Int64,System.Collections.Generic.List`1<T>)
// 0x00000248 System.Void UnityEngine.Timeline.IntervalTree`1::Rebuild()
// 0x00000249 System.Int32 UnityEngine.Timeline.IntervalTree`1::Rebuild(System.Int32,System.Int32)
// 0x0000024A System.Void UnityEngine.Timeline.IntervalTree`1::Clear()
// 0x0000024B System.Void UnityEngine.Timeline.IntervalTree`1::.ctor()
// 0x0000024C System.Double UnityEngine.Timeline.RuntimeClip::get_start()
extern void RuntimeClip_get_start_m6204EC5ADD90B46E23FFE1436928152BF15326EC (void);
// 0x0000024D System.Double UnityEngine.Timeline.RuntimeClip::get_duration()
extern void RuntimeClip_get_duration_mBA27B08B52BD7B402C449A7733AA7755FA987F6C (void);
// 0x0000024E System.Void UnityEngine.Timeline.RuntimeClip::.ctor(UnityEngine.Timeline.TimelineClip,UnityEngine.Playables.Playable,UnityEngine.Playables.Playable)
extern void RuntimeClip__ctor_m98046934573D3967A198B431434524D25C76F43C (void);
// 0x0000024F System.Void UnityEngine.Timeline.RuntimeClip::Create(UnityEngine.Timeline.TimelineClip,UnityEngine.Playables.Playable,UnityEngine.Playables.Playable)
extern void RuntimeClip_Create_m9464E5BA8F8A29AEA85775728362F8B72A742D10 (void);
// 0x00000250 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.RuntimeClip::get_clip()
extern void RuntimeClip_get_clip_m6208BBDD36E3E3EA97000F4CEC9BB1B879A1824D (void);
// 0x00000251 UnityEngine.Playables.Playable UnityEngine.Timeline.RuntimeClip::get_mixer()
extern void RuntimeClip_get_mixer_mA42F77ACA8B17C58C502EC06C7930F2033AA0589 (void);
// 0x00000252 UnityEngine.Playables.Playable UnityEngine.Timeline.RuntimeClip::get_playable()
extern void RuntimeClip_get_playable_mD336A90E2A444F4B50F3B02FABC5C6ECDE3E21BE (void);
// 0x00000253 System.Void UnityEngine.Timeline.RuntimeClip::set_enable(System.Boolean)
extern void RuntimeClip_set_enable_m61A322D87BF4A75D804C2C82FD59CACA871B8FA1 (void);
// 0x00000254 System.Void UnityEngine.Timeline.RuntimeClip::SetTime(System.Double)
extern void RuntimeClip_SetTime_m7ECE29A44A0DA3625276C56999A7D293CF642ED1 (void);
// 0x00000255 System.Void UnityEngine.Timeline.RuntimeClip::SetDuration(System.Double)
extern void RuntimeClip_SetDuration_m4AB935A8C5F597184D3F20A714704C11458FAE40 (void);
// 0x00000256 System.Void UnityEngine.Timeline.RuntimeClip::EvaluateAt(System.Double,UnityEngine.Playables.FrameData)
extern void RuntimeClip_EvaluateAt_m6D08332D99A261EE232EFFE9F94ADEC3B5E66BDE (void);
// 0x00000257 System.Void UnityEngine.Timeline.RuntimeClip::DisableAt(System.Double,System.Double,UnityEngine.Playables.FrameData)
extern void RuntimeClip_DisableAt_mE25279C9BD90378393EC0E8CB029EDFECE7CC243 (void);
// 0x00000258 System.Double UnityEngine.Timeline.RuntimeClipBase::get_start()
// 0x00000259 System.Double UnityEngine.Timeline.RuntimeClipBase::get_duration()
// 0x0000025A System.Int64 UnityEngine.Timeline.RuntimeClipBase::get_intervalStart()
extern void RuntimeClipBase_get_intervalStart_m599D96633271890B892A9D48325531F28DDE1BAD (void);
// 0x0000025B System.Int64 UnityEngine.Timeline.RuntimeClipBase::get_intervalEnd()
extern void RuntimeClipBase_get_intervalEnd_mA0819F921B8AA4BFEBF31419858BA2C975BE174B (void);
// 0x0000025C System.Void UnityEngine.Timeline.RuntimeClipBase::.ctor()
extern void RuntimeClipBase__ctor_m53B7986F56314C13A22B079840C7DA6D36501958 (void);
// 0x0000025D System.Int64 UnityEngine.Timeline.RuntimeElement::get_intervalStart()
// 0x0000025E System.Int64 UnityEngine.Timeline.RuntimeElement::get_intervalEnd()
// 0x0000025F System.Int32 UnityEngine.Timeline.RuntimeElement::get_intervalBit()
extern void RuntimeElement_get_intervalBit_mD5C7E7CDA66C4B1888D529E9344679FF88DE5A70 (void);
// 0x00000260 System.Void UnityEngine.Timeline.RuntimeElement::set_intervalBit(System.Int32)
extern void RuntimeElement_set_intervalBit_mD6D67EA4A982521E16CDF3CAC295EED22CA65C75 (void);
// 0x00000261 System.Void UnityEngine.Timeline.RuntimeElement::set_enable(System.Boolean)
// 0x00000262 System.Void UnityEngine.Timeline.RuntimeElement::EvaluateAt(System.Double,UnityEngine.Playables.FrameData)
// 0x00000263 System.Void UnityEngine.Timeline.RuntimeElement::DisableAt(System.Double,System.Double,UnityEngine.Playables.FrameData)
// 0x00000264 System.Void UnityEngine.Timeline.RuntimeElement::.ctor()
extern void RuntimeElement__ctor_mB4868C6FD6BE7F80F182AE2961580F1E31A60F68 (void);
// 0x00000265 System.Double UnityEngine.Timeline.ScheduleRuntimeClip::get_start()
extern void ScheduleRuntimeClip_get_start_mC7AEF6AF42593F08CD3403B98132D3AAEDD898D8 (void);
// 0x00000266 System.Double UnityEngine.Timeline.ScheduleRuntimeClip::get_duration()
extern void ScheduleRuntimeClip_get_duration_mE5950845592C0E89A8F622204CD37D7ABCFD62D0 (void);
// 0x00000267 System.Void UnityEngine.Timeline.ScheduleRuntimeClip::SetTime(System.Double)
extern void ScheduleRuntimeClip_SetTime_m38A382F75037FC1430B6067DAC94848D7043D1DF (void);
// 0x00000268 UnityEngine.Timeline.TimelineClip UnityEngine.Timeline.ScheduleRuntimeClip::get_clip()
extern void ScheduleRuntimeClip_get_clip_mC24FB5C0451219E6A3CF888B81B0099A34443A3B (void);
// 0x00000269 UnityEngine.Playables.Playable UnityEngine.Timeline.ScheduleRuntimeClip::get_mixer()
extern void ScheduleRuntimeClip_get_mixer_m5FFC2C73903CC0B038A4F76EB1A4F99B912FC54C (void);
// 0x0000026A UnityEngine.Playables.Playable UnityEngine.Timeline.ScheduleRuntimeClip::get_playable()
extern void ScheduleRuntimeClip_get_playable_mF7988FDD9350B256DD307DE9F9957AA1A44D9EED (void);
// 0x0000026B System.Void UnityEngine.Timeline.ScheduleRuntimeClip::.ctor(UnityEngine.Timeline.TimelineClip,UnityEngine.Playables.Playable,UnityEngine.Playables.Playable,System.Double,System.Double)
extern void ScheduleRuntimeClip__ctor_m83C75C594A568AE694E42341685277CFB517568F (void);
// 0x0000026C System.Void UnityEngine.Timeline.ScheduleRuntimeClip::Create(UnityEngine.Timeline.TimelineClip,UnityEngine.Playables.Playable,UnityEngine.Playables.Playable,System.Double,System.Double)
extern void ScheduleRuntimeClip_Create_mA8ADCDBD0B9C32427F6EB006FFB9057989981C18 (void);
// 0x0000026D System.Void UnityEngine.Timeline.ScheduleRuntimeClip::set_enable(System.Boolean)
extern void ScheduleRuntimeClip_set_enable_mA74C59B6CDC243221219534B77FA5B140A4E5989 (void);
// 0x0000026E System.Void UnityEngine.Timeline.ScheduleRuntimeClip::EvaluateAt(System.Double,UnityEngine.Playables.FrameData)
extern void ScheduleRuntimeClip_EvaluateAt_m3C3C92A67AB06B22C1B3E22B884F78A3BA42F6D8 (void);
// 0x0000026F System.Void UnityEngine.Timeline.ScheduleRuntimeClip::DisableAt(System.Double,System.Double,UnityEngine.Playables.FrameData)
extern void ScheduleRuntimeClip_DisableAt_m57233707606DE133E713FB3B89BB867BE65A8CA7 (void);
// 0x00000270 System.Double UnityEngine.Timeline.IMarker::get_time()
// 0x00000271 System.Void UnityEngine.Timeline.IMarker::set_time(System.Double)
// 0x00000272 UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.IMarker::get_parent()
// 0x00000273 System.Void UnityEngine.Timeline.IMarker::Initialize(UnityEngine.Timeline.TrackAsset)
// 0x00000274 UnityEngine.Timeline.NotificationFlags UnityEngine.Timeline.INotificationOptionProvider::get_flags()
// 0x00000275 UnityEngine.Timeline.TrackAsset UnityEngine.Timeline.Marker::get_parent()
extern void Marker_get_parent_mE72E59DAF1DBC598885CD3CA9D0A55CB68CE8510 (void);
// 0x00000276 System.Void UnityEngine.Timeline.Marker::set_parent(UnityEngine.Timeline.TrackAsset)
extern void Marker_set_parent_mCAB1A605E39909E42D623D48037DC43141173A34 (void);
// 0x00000277 System.Double UnityEngine.Timeline.Marker::get_time()
extern void Marker_get_time_mB0E90DB26C36F73A6AFB126761F1FF144A145BF6 (void);
// 0x00000278 System.Void UnityEngine.Timeline.Marker::set_time(System.Double)
extern void Marker_set_time_mF28306664964EF628F2D6D9F9F9DB2EB7DF89DED (void);
// 0x00000279 System.Void UnityEngine.Timeline.Marker::UnityEngine.Timeline.IMarker.Initialize(UnityEngine.Timeline.TrackAsset)
extern void Marker_UnityEngine_Timeline_IMarker_Initialize_mA5ED18EB0857836506FD03FB6C7E04C06AC6F320 (void);
// 0x0000027A System.Void UnityEngine.Timeline.Marker::OnInitialize(UnityEngine.Timeline.TrackAsset)
extern void Marker_OnInitialize_m95F020C003B8C4C5072BF891C9539CF0D6A2F550 (void);
// 0x0000027B System.Void UnityEngine.Timeline.Marker::.ctor()
extern void Marker__ctor_m74124BFBCDCAFE96C2A9CE59CA215B80590F6A56 (void);
// 0x0000027C System.Collections.Generic.List`1<UnityEngine.Timeline.IMarker> UnityEngine.Timeline.MarkerList::get_markers()
extern void MarkerList_get_markers_m5376FA1EA20DEC72CE47369DD8CD407869B0A2F7 (void);
// 0x0000027D System.Void UnityEngine.Timeline.MarkerList::.ctor(System.Int32)
extern void MarkerList__ctor_m2D55037070301E459BF9CAB83756B81183C6A6B8 (void);
// 0x0000027E System.Void UnityEngine.Timeline.MarkerList::Add(UnityEngine.ScriptableObject)
extern void MarkerList_Add_m401FA50EA0C98293DD36147CFA9DC8F637636128 (void);
// 0x0000027F System.Boolean UnityEngine.Timeline.MarkerList::Remove(UnityEngine.Timeline.IMarker)
extern void MarkerList_Remove_m986C1ECBCA1A1DE79FCD97D7D44EC91C19D79F64 (void);
// 0x00000280 System.Boolean UnityEngine.Timeline.MarkerList::Remove(UnityEngine.ScriptableObject,UnityEngine.Timeline.TimelineAsset,UnityEngine.Playables.PlayableAsset)
extern void MarkerList_Remove_mF1626830F54C37B15C3DBEB6044B92CBF6439790 (void);
// 0x00000281 System.Void UnityEngine.Timeline.MarkerList::Clear()
extern void MarkerList_Clear_m260B8EDEF14AC94F4BE5F10018F2215BF11927E7 (void);
// 0x00000282 System.Boolean UnityEngine.Timeline.MarkerList::Contains(UnityEngine.ScriptableObject)
extern void MarkerList_Contains_m97FF1ADAEFBD92A7F3E66B0A7C85950224B62989 (void);
// 0x00000283 System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.IMarker> UnityEngine.Timeline.MarkerList::GetMarkers()
extern void MarkerList_GetMarkers_mC440918CEEC3E0A8E872510EDEAEA0FB8832E680 (void);
// 0x00000284 System.Int32 UnityEngine.Timeline.MarkerList::get_Count()
extern void MarkerList_get_Count_mC37C47376A0DCC203DBD965E961A923F06E0082B (void);
// 0x00000285 UnityEngine.Timeline.IMarker UnityEngine.Timeline.MarkerList::get_Item(System.Int32)
extern void MarkerList_get_Item_m76D3839ACDBAB9F00DFD9CE68C63DE26050961FE (void);
// 0x00000286 System.Collections.Generic.List`1<UnityEngine.ScriptableObject> UnityEngine.Timeline.MarkerList::GetRawMarkerList()
extern void MarkerList_GetRawMarkerList_m272F6FFA3A887E15FDEFA8AAC07F1A6A0BD54A0D (void);
// 0x00000287 UnityEngine.Timeline.IMarker UnityEngine.Timeline.MarkerList::CreateMarker(System.Type,System.Double,UnityEngine.Timeline.TrackAsset)
extern void MarkerList_CreateMarker_m404F50BDABD541CF30E2F03DC6C22F917E02130B (void);
// 0x00000288 System.Boolean UnityEngine.Timeline.MarkerList::HasNotifications()
extern void MarkerList_HasNotifications_mF2F94CCD1C286DB7264738F287486890D2497124 (void);
// 0x00000289 System.Void UnityEngine.Timeline.MarkerList::UnityEngine.ISerializationCallbackReceiver.OnBeforeSerialize()
extern void MarkerList_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m6E97ABA4A5ED5881C2FC7F356FCAF68FCC45B710 (void);
// 0x0000028A System.Void UnityEngine.Timeline.MarkerList::UnityEngine.ISerializationCallbackReceiver.OnAfterDeserialize()
extern void MarkerList_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m1175CE30FB38D95504747F73994F2C86DC0E8928 (void);
// 0x0000028B System.Void UnityEngine.Timeline.MarkerList::BuildCache()
extern void MarkerList_BuildCache_m8EFF6B73A205DC746231385CAC726A74A2B485F5 (void);
// 0x0000028C System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.MarkerTrack::get_outputs()
extern void MarkerTrack_get_outputs_mCC74870CF1C68FA4B6DC21EDADEDE9936A6F21AC (void);
// 0x0000028D System.Void UnityEngine.Timeline.MarkerTrack::.ctor()
extern void MarkerTrack__ctor_mAF73693D259F16A0F2DBF71313BA854F5C71A1E8 (void);
// 0x0000028E System.Void UnityEngine.Timeline.CustomSignalEventDrawer::.ctor()
extern void CustomSignalEventDrawer__ctor_m6E78EBC2A826450A4F05E0127A6672F07D84CC0A (void);
// 0x0000028F System.Void UnityEngine.Timeline.SignalAsset::add_OnEnableCallback(System.Action`1<UnityEngine.Timeline.SignalAsset>)
extern void SignalAsset_add_OnEnableCallback_m554767948986531C6B7B0C14286F651B6B7C7203 (void);
// 0x00000290 System.Void UnityEngine.Timeline.SignalAsset::remove_OnEnableCallback(System.Action`1<UnityEngine.Timeline.SignalAsset>)
extern void SignalAsset_remove_OnEnableCallback_m99D77B52734D3A480E21F6E09918CB302409CBFB (void);
// 0x00000291 System.Void UnityEngine.Timeline.SignalAsset::OnEnable()
extern void SignalAsset_OnEnable_mF4113987FFFF4ECE91B0DFD3506EFF02E76DE29C (void);
// 0x00000292 System.Void UnityEngine.Timeline.SignalAsset::.ctor()
extern void SignalAsset__ctor_m4F0FF5113CFB816B5FF981FA2A308AEC282D6E9B (void);
// 0x00000293 System.Boolean UnityEngine.Timeline.SignalEmitter::get_retroactive()
extern void SignalEmitter_get_retroactive_m05405283A19896F77F109BE0E4DF0380CAE909EA (void);
// 0x00000294 System.Void UnityEngine.Timeline.SignalEmitter::set_retroactive(System.Boolean)
extern void SignalEmitter_set_retroactive_mC90E249F02530DA76969DA20208123CCE8A11D4F (void);
// 0x00000295 System.Boolean UnityEngine.Timeline.SignalEmitter::get_emitOnce()
extern void SignalEmitter_get_emitOnce_m8A10F3E7A7F2BF66F52B5A418632E1CCA1B613A5 (void);
// 0x00000296 System.Void UnityEngine.Timeline.SignalEmitter::set_emitOnce(System.Boolean)
extern void SignalEmitter_set_emitOnce_mB64C9B47B124F8FEAE68C41BA3CED422A96E6628 (void);
// 0x00000297 UnityEngine.Timeline.SignalAsset UnityEngine.Timeline.SignalEmitter::get_asset()
extern void SignalEmitter_get_asset_m80647A00BC1BE61E0FBD0F2CE626B0997E04C140 (void);
// 0x00000298 System.Void UnityEngine.Timeline.SignalEmitter::set_asset(UnityEngine.Timeline.SignalAsset)
extern void SignalEmitter_set_asset_mE5074F7D8760F624046A0E1B7C2D3E2662A7283C (void);
// 0x00000299 UnityEngine.PropertyName UnityEngine.Timeline.SignalEmitter::UnityEngine.Playables.INotification.get_id()
extern void SignalEmitter_UnityEngine_Playables_INotification_get_id_mFE1FFFDDE0AA375A400B8ED622D47E3C4A3462F1 (void);
// 0x0000029A UnityEngine.Timeline.NotificationFlags UnityEngine.Timeline.SignalEmitter::UnityEngine.Timeline.INotificationOptionProvider.get_flags()
extern void SignalEmitter_UnityEngine_Timeline_INotificationOptionProvider_get_flags_m440E69F2CB8F13BDB1D4D082E88A8708E1FEC98C (void);
// 0x0000029B System.Void UnityEngine.Timeline.SignalEmitter::.ctor()
extern void SignalEmitter__ctor_m6A45F5A0D313B5184EA3D27F89DAD8126178E327 (void);
// 0x0000029C System.Void UnityEngine.Timeline.SignalReceiver::OnNotify(UnityEngine.Playables.Playable,UnityEngine.Playables.INotification,System.Object)
extern void SignalReceiver_OnNotify_m2A3A5A1DB5A3FACAF334771A74677D1FB08FFBF4 (void);
// 0x0000029D System.Void UnityEngine.Timeline.SignalReceiver::AddReaction(UnityEngine.Timeline.SignalAsset,UnityEngine.Events.UnityEvent)
extern void SignalReceiver_AddReaction_m24F66AE62396EEC75BB4F5074246F39FDF6884B6 (void);
// 0x0000029E System.Int32 UnityEngine.Timeline.SignalReceiver::AddEmptyReaction(UnityEngine.Events.UnityEvent)
extern void SignalReceiver_AddEmptyReaction_m8A3DAC1ED52B65292D89FD40DEAE359D29491EFC (void);
// 0x0000029F System.Void UnityEngine.Timeline.SignalReceiver::Remove(UnityEngine.Timeline.SignalAsset)
extern void SignalReceiver_Remove_mC5F9BBA8D959629B62B7A7F5B2C2E1A684E857CF (void);
// 0x000002A0 System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.SignalAsset> UnityEngine.Timeline.SignalReceiver::GetRegisteredSignals()
extern void SignalReceiver_GetRegisteredSignals_m0834695F8B630B69EC4A606956C24ABD9FBFA953 (void);
// 0x000002A1 UnityEngine.Events.UnityEvent UnityEngine.Timeline.SignalReceiver::GetReaction(UnityEngine.Timeline.SignalAsset)
extern void SignalReceiver_GetReaction_m8B5FBD0171FB06EAE16E9A99F2E209829552EFF8 (void);
// 0x000002A2 System.Int32 UnityEngine.Timeline.SignalReceiver::Count()
extern void SignalReceiver_Count_mB3916E584C177A2D47AF76296FB0C59987A1F694 (void);
// 0x000002A3 System.Void UnityEngine.Timeline.SignalReceiver::ChangeSignalAtIndex(System.Int32,UnityEngine.Timeline.SignalAsset)
extern void SignalReceiver_ChangeSignalAtIndex_m66463FEE943CC55C585628B993F64C78257CA1FD (void);
// 0x000002A4 System.Void UnityEngine.Timeline.SignalReceiver::RemoveAtIndex(System.Int32)
extern void SignalReceiver_RemoveAtIndex_m01EE2CBC7A406CA4A1D13F4F72D591347D02DBC5 (void);
// 0x000002A5 System.Void UnityEngine.Timeline.SignalReceiver::ChangeReactionAtIndex(System.Int32,UnityEngine.Events.UnityEvent)
extern void SignalReceiver_ChangeReactionAtIndex_m803D743382D58A2637C8BA1FF00116310AFEC472 (void);
// 0x000002A6 UnityEngine.Events.UnityEvent UnityEngine.Timeline.SignalReceiver::GetReactionAtIndex(System.Int32)
extern void SignalReceiver_GetReactionAtIndex_m3F36C2C9E39F3F39679B4AE052305DDFFC8887A1 (void);
// 0x000002A7 UnityEngine.Timeline.SignalAsset UnityEngine.Timeline.SignalReceiver::GetSignalAssetAtIndex(System.Int32)
extern void SignalReceiver_GetSignalAssetAtIndex_mAB31FED39823D7220E727A204F128AD9C0B12A82 (void);
// 0x000002A8 System.Void UnityEngine.Timeline.SignalReceiver::OnEnable()
extern void SignalReceiver_OnEnable_mB77BD34EC8FE8B41A627585E4739083B7F85F3C1 (void);
// 0x000002A9 System.Void UnityEngine.Timeline.SignalReceiver::.ctor()
extern void SignalReceiver__ctor_m46381605B40F21C06F4125C7D8F45232F4D74CE6 (void);
// 0x000002AA System.Boolean UnityEngine.Timeline.SignalReceiver/EventKeyValue::TryGetValue(UnityEngine.Timeline.SignalAsset,UnityEngine.Events.UnityEvent&)
extern void EventKeyValue_TryGetValue_m7190DE6E665F07018730BAF3288F22CDFEEB9B6D (void);
// 0x000002AB System.Void UnityEngine.Timeline.SignalReceiver/EventKeyValue::Append(UnityEngine.Timeline.SignalAsset,UnityEngine.Events.UnityEvent)
extern void EventKeyValue_Append_mEE3BEB022A04AA95F1B2B56D3F8AA5F5ECCA5BD3 (void);
// 0x000002AC System.Void UnityEngine.Timeline.SignalReceiver/EventKeyValue::Remove(System.Int32)
extern void EventKeyValue_Remove_mEC52E595AEC1BFAB97DB2567AC42CE0D7C968863 (void);
// 0x000002AD System.Void UnityEngine.Timeline.SignalReceiver/EventKeyValue::Remove(UnityEngine.Timeline.SignalAsset)
extern void EventKeyValue_Remove_m3B8982FC179C5DD1853FC4846106B1CD23F57C7D (void);
// 0x000002AE System.Collections.Generic.List`1<UnityEngine.Timeline.SignalAsset> UnityEngine.Timeline.SignalReceiver/EventKeyValue::get_signals()
extern void EventKeyValue_get_signals_m3F5CB2EDFE24DEAAE67DF04898223ACBDB3CB3BD (void);
// 0x000002AF System.Collections.Generic.List`1<UnityEngine.Events.UnityEvent> UnityEngine.Timeline.SignalReceiver/EventKeyValue::get_events()
extern void EventKeyValue_get_events_mB7945E299C4249CB0BD5D2EBAB8D0DEE455FFAE3 (void);
// 0x000002B0 System.Void UnityEngine.Timeline.SignalReceiver/EventKeyValue::.ctor()
extern void EventKeyValue__ctor_m7AE5F9F6453AA2F9DB8A1612D334D1F4CB3CC549 (void);
// 0x000002B1 System.Void UnityEngine.Timeline.SignalTrack::.ctor()
extern void SignalTrack__ctor_m92CAC598FA9027691117E52EB8247D2D79D0FBA1 (void);
// 0x000002B2 UnityEngine.Timeline.GroupTrack UnityEngine.Timeline.TrackAssetExtensions::GetGroup(UnityEngine.Timeline.TrackAsset)
extern void TrackAssetExtensions_GetGroup_m9261D7FF986D8DBAB89F142B5B2F4357F1C995B9 (void);
// 0x000002B3 System.Void UnityEngine.Timeline.TrackAssetExtensions::SetGroup(UnityEngine.Timeline.TrackAsset,UnityEngine.Timeline.GroupTrack)
extern void TrackAssetExtensions_SetGroup_m0525D6627035D09A0943ADC00AAA03CC68495435 (void);
// 0x000002B4 System.Boolean UnityEngine.Timeline.GroupTrack::CanCompileClips()
extern void GroupTrack_CanCompileClips_mF95369ECAEA83FA55A283FB1425F00FBCE9D2F23 (void);
// 0x000002B5 System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.GroupTrack::get_outputs()
extern void GroupTrack_get_outputs_m1CF0F26206E1943E4C4202F7849408E9E8CA2403 (void);
// 0x000002B6 System.Void UnityEngine.Timeline.GroupTrack::.ctor()
extern void GroupTrack__ctor_m2612CD9E072D91E3F5C21F8D4F156F96A33CDBBC (void);
// 0x000002B7 UnityEngine.Playables.Playable UnityEngine.Timeline.ILayerable::CreateLayerMixer(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,System.Int32)
// 0x000002B8 UnityEngine.Playables.ScriptPlayable`1<UnityEngine.Timeline.ActivationControlPlayable> UnityEngine.Timeline.ActivationControlPlayable::Create(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,UnityEngine.Timeline.ActivationControlPlayable/PostPlaybackState)
extern void ActivationControlPlayable_Create_m61DD26626E7A44339EA08D2E15498E1897FAC23E (void);
// 0x000002B9 System.Void UnityEngine.Timeline.ActivationControlPlayable::OnBehaviourPlay(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void ActivationControlPlayable_OnBehaviourPlay_m32D87ABF400DD235012889AEB82EBD1757AE745C (void);
// 0x000002BA System.Void UnityEngine.Timeline.ActivationControlPlayable::OnBehaviourPause(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void ActivationControlPlayable_OnBehaviourPause_mB2E17A057A698FA2366269460A306B281808748F (void);
// 0x000002BB System.Void UnityEngine.Timeline.ActivationControlPlayable::ProcessFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData,System.Object)
extern void ActivationControlPlayable_ProcessFrame_mC388A38FD777597B3E79B41A5DA4286DCA0E73BE (void);
// 0x000002BC System.Void UnityEngine.Timeline.ActivationControlPlayable::OnGraphStart(UnityEngine.Playables.Playable)
extern void ActivationControlPlayable_OnGraphStart_mF0475EF758222C98681FAB8A1E175C4A0095AE31 (void);
// 0x000002BD System.Void UnityEngine.Timeline.ActivationControlPlayable::OnPlayableDestroy(UnityEngine.Playables.Playable)
extern void ActivationControlPlayable_OnPlayableDestroy_mF39699D518A1C2075AB6BCD1ECB1996704A28838 (void);
// 0x000002BE System.Void UnityEngine.Timeline.ActivationControlPlayable::.ctor()
extern void ActivationControlPlayable__ctor_m50572F236B93063098D17B06E2662F10A75F5E0C (void);
// 0x000002BF System.Double UnityEngine.Timeline.BasicPlayableBehaviour::get_duration()
extern void BasicPlayableBehaviour_get_duration_m000CA8660FB9136C46ABD05B9ACB9B7BE28C1F95 (void);
// 0x000002C0 System.Collections.Generic.IEnumerable`1<UnityEngine.Playables.PlayableBinding> UnityEngine.Timeline.BasicPlayableBehaviour::get_outputs()
extern void BasicPlayableBehaviour_get_outputs_mF90ACBACF585238FF0623BD70C14483DF91D21AE (void);
// 0x000002C1 System.Void UnityEngine.Timeline.BasicPlayableBehaviour::OnGraphStart(UnityEngine.Playables.Playable)
extern void BasicPlayableBehaviour_OnGraphStart_m921FCF14B857F191628EBB43D79A8CB674EAB8AA (void);
// 0x000002C2 System.Void UnityEngine.Timeline.BasicPlayableBehaviour::OnGraphStop(UnityEngine.Playables.Playable)
extern void BasicPlayableBehaviour_OnGraphStop_m261E6FFF9FCCA3380C3B1844ADE470605B616FFC (void);
// 0x000002C3 System.Void UnityEngine.Timeline.BasicPlayableBehaviour::OnPlayableCreate(UnityEngine.Playables.Playable)
extern void BasicPlayableBehaviour_OnPlayableCreate_m956EA471E10F9B20B77365A935A1C473E5DAD25D (void);
// 0x000002C4 System.Void UnityEngine.Timeline.BasicPlayableBehaviour::OnPlayableDestroy(UnityEngine.Playables.Playable)
extern void BasicPlayableBehaviour_OnPlayableDestroy_mE70F15932202ABBF11E850199EB2DADFC6B5955E (void);
// 0x000002C5 System.Void UnityEngine.Timeline.BasicPlayableBehaviour::OnBehaviourPlay(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void BasicPlayableBehaviour_OnBehaviourPlay_m1F24D0A73D669B74C455DB02363EF2BB473FBF65 (void);
// 0x000002C6 System.Void UnityEngine.Timeline.BasicPlayableBehaviour::OnBehaviourPause(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void BasicPlayableBehaviour_OnBehaviourPause_m2372D83D9E18CF06F9121017970D8295412E216D (void);
// 0x000002C7 System.Void UnityEngine.Timeline.BasicPlayableBehaviour::PrepareFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void BasicPlayableBehaviour_PrepareFrame_mE55DA9150027CA503D1495E3E50A9568EAA1F5E7 (void);
// 0x000002C8 System.Void UnityEngine.Timeline.BasicPlayableBehaviour::ProcessFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData,System.Object)
extern void BasicPlayableBehaviour_ProcessFrame_m413A95F400514A22EF4630952D0498DBC5347ED2 (void);
// 0x000002C9 UnityEngine.Playables.Playable UnityEngine.Timeline.BasicPlayableBehaviour::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject)
extern void BasicPlayableBehaviour_CreatePlayable_m0F957E711BFF407616E35025646C83FA4DD90C25 (void);
// 0x000002CA System.Void UnityEngine.Timeline.BasicPlayableBehaviour::.ctor()
extern void BasicPlayableBehaviour__ctor_mF8CCC76427C0B3A8E0F88C4B1853A80E5EA33C1F (void);
// 0x000002CB UnityEngine.Playables.ScriptPlayable`1<UnityEngine.Timeline.DirectorControlPlayable> UnityEngine.Timeline.DirectorControlPlayable::Create(UnityEngine.Playables.PlayableGraph,UnityEngine.Playables.PlayableDirector)
extern void DirectorControlPlayable_Create_mA64246490438157CCBE867DF9CFD0F0CD7133DE6 (void);
// 0x000002CC System.Void UnityEngine.Timeline.DirectorControlPlayable::OnPlayableDestroy(UnityEngine.Playables.Playable)
extern void DirectorControlPlayable_OnPlayableDestroy_m9426CD97470CA7619A971B2A0E8942BC6BE7AD19 (void);
// 0x000002CD System.Void UnityEngine.Timeline.DirectorControlPlayable::PrepareFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void DirectorControlPlayable_PrepareFrame_m0F8F6FBBB5483EBB76332AE013321FC6201893E9 (void);
// 0x000002CE System.Void UnityEngine.Timeline.DirectorControlPlayable::OnBehaviourPlay(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void DirectorControlPlayable_OnBehaviourPlay_m452302B8000575C35BF262260D72F789CA03DD60 (void);
// 0x000002CF System.Void UnityEngine.Timeline.DirectorControlPlayable::OnBehaviourPause(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void DirectorControlPlayable_OnBehaviourPause_m2DDA62A73F0C95CA0B220637599FD2DBD224E632 (void);
// 0x000002D0 System.Void UnityEngine.Timeline.DirectorControlPlayable::ProcessFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData,System.Object)
extern void DirectorControlPlayable_ProcessFrame_mAC2E676C1932306AE076F222A074E37BFAA8B6E7 (void);
// 0x000002D1 System.Void UnityEngine.Timeline.DirectorControlPlayable::SyncSpeed(System.Double)
extern void DirectorControlPlayable_SyncSpeed_m39C659CC0373FBC0829B8343F49F44E852729734 (void);
// 0x000002D2 System.Void UnityEngine.Timeline.DirectorControlPlayable::SyncStart(UnityEngine.Playables.PlayableGraph,System.Double)
extern void DirectorControlPlayable_SyncStart_m07109F4B30C707D4B2450C00A0675192A69260E2 (void);
// 0x000002D3 System.Void UnityEngine.Timeline.DirectorControlPlayable::SyncStop(UnityEngine.Playables.PlayableGraph,System.Double)
extern void DirectorControlPlayable_SyncStop_mB49F56038E68681AB73BA59A96E3A2F3348868DB (void);
// 0x000002D4 System.Boolean UnityEngine.Timeline.DirectorControlPlayable::DetectDiscontinuity(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void DirectorControlPlayable_DetectDiscontinuity_m5884117E980A5F64A18127E0CC14BD64B12B286D (void);
// 0x000002D5 System.Boolean UnityEngine.Timeline.DirectorControlPlayable::DetectOutOfSync(UnityEngine.Playables.Playable)
extern void DirectorControlPlayable_DetectOutOfSync_mAEB0E3DA34B2A5C7F7BBE40144198F9762D07370 (void);
// 0x000002D6 System.Void UnityEngine.Timeline.DirectorControlPlayable::UpdateTime(UnityEngine.Playables.Playable)
extern void DirectorControlPlayable_UpdateTime_mF24AE595B9899ABED3928F15FAD03CBBB5CBE809 (void);
// 0x000002D7 System.Void UnityEngine.Timeline.DirectorControlPlayable::.ctor()
extern void DirectorControlPlayable__ctor_m89F5CEC0EBACD98DFF7D480BEFEA15DD637F870C (void);
// 0x000002D8 System.Void UnityEngine.Timeline.ITimeControl::SetTime(System.Double)
// 0x000002D9 System.Void UnityEngine.Timeline.ITimeControl::OnControlTimeStart()
// 0x000002DA System.Void UnityEngine.Timeline.ITimeControl::OnControlTimeStop()
// 0x000002DB UnityEngine.Playables.ScriptPlayable`1<UnityEngine.Timeline.ParticleControlPlayable> UnityEngine.Timeline.ParticleControlPlayable::Create(UnityEngine.Playables.PlayableGraph,UnityEngine.ParticleSystem,System.UInt32)
extern void ParticleControlPlayable_Create_m8F03536B0CC6B66B506836058AA37A08EDCD5338 (void);
// 0x000002DC UnityEngine.ParticleSystem UnityEngine.Timeline.ParticleControlPlayable::get_particleSystem()
extern void ParticleControlPlayable_get_particleSystem_m63954D25FBB179CA655D5739DCB07C7DBA99C7A5 (void);
// 0x000002DD System.Void UnityEngine.Timeline.ParticleControlPlayable::set_particleSystem(UnityEngine.ParticleSystem)
extern void ParticleControlPlayable_set_particleSystem_m6B3EED45B18C3B80D641D969D2601ABBE34BA4C1 (void);
// 0x000002DE System.Void UnityEngine.Timeline.ParticleControlPlayable::Initialize(UnityEngine.ParticleSystem,System.UInt32)
extern void ParticleControlPlayable_Initialize_mCA4BB21915939852339DCECDE34EDB1B4ED36E92 (void);
// 0x000002DF System.Void UnityEngine.Timeline.ParticleControlPlayable::SetRandomSeed(UnityEngine.ParticleSystem,System.UInt32)
extern void ParticleControlPlayable_SetRandomSeed_m0D41AD36E12BF013CE346F2A562A717721FB87F9 (void);
// 0x000002E0 System.Void UnityEngine.Timeline.ParticleControlPlayable::PrepareFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void ParticleControlPlayable_PrepareFrame_m959C915531B064E7A7AC30B01E8299B21A139EF6 (void);
// 0x000002E1 System.Void UnityEngine.Timeline.ParticleControlPlayable::OnBehaviourPlay(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void ParticleControlPlayable_OnBehaviourPlay_mB1B514A71494493FB7A2B8B1F6B5F1455907B761 (void);
// 0x000002E2 System.Void UnityEngine.Timeline.ParticleControlPlayable::OnBehaviourPause(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void ParticleControlPlayable_OnBehaviourPause_m295DA59C6C8E31854EA9009A709FFFAF663428B9 (void);
// 0x000002E3 System.Void UnityEngine.Timeline.ParticleControlPlayable::Simulate(System.Single,System.Boolean)
extern void ParticleControlPlayable_Simulate_mCDD453B5F1A231F1FD9744B19EDE4386E4B3D7C6 (void);
// 0x000002E4 System.Void UnityEngine.Timeline.ParticleControlPlayable::.ctor()
extern void ParticleControlPlayable__ctor_m0E1086350E57B8CA51874D8AE9FDFF1B0AB89C66 (void);
// 0x000002E5 UnityEngine.Playables.ScriptPlayable`1<UnityEngine.Timeline.PrefabControlPlayable> UnityEngine.Timeline.PrefabControlPlayable::Create(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,UnityEngine.Transform)
extern void PrefabControlPlayable_Create_mE744622E91538A0C13C1D453F207A32956F35297 (void);
// 0x000002E6 UnityEngine.GameObject UnityEngine.Timeline.PrefabControlPlayable::get_prefabInstance()
extern void PrefabControlPlayable_get_prefabInstance_mE06FADC9D1367D2606277D5D1F8577C6CA837B4A (void);
// 0x000002E7 UnityEngine.GameObject UnityEngine.Timeline.PrefabControlPlayable::Initialize(UnityEngine.GameObject,UnityEngine.Transform)
extern void PrefabControlPlayable_Initialize_mC8F44778C03DAEFFE8F22B14216C6E838E224483 (void);
// 0x000002E8 System.Void UnityEngine.Timeline.PrefabControlPlayable::OnPlayableDestroy(UnityEngine.Playables.Playable)
extern void PrefabControlPlayable_OnPlayableDestroy_mC7498F993D6766BC8F1963BC770DACBE7AC09DCA (void);
// 0x000002E9 System.Void UnityEngine.Timeline.PrefabControlPlayable::OnBehaviourPlay(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void PrefabControlPlayable_OnBehaviourPlay_m25045FBF8EF4CE540108857ED00A5E8CF44EC66D (void);
// 0x000002EA System.Void UnityEngine.Timeline.PrefabControlPlayable::OnBehaviourPause(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void PrefabControlPlayable_OnBehaviourPause_m9015806A18FE1AF828ED2757E7F83BD499C3C09F (void);
// 0x000002EB System.Void UnityEngine.Timeline.PrefabControlPlayable::SetHideFlagsRecursive(UnityEngine.GameObject)
extern void PrefabControlPlayable_SetHideFlagsRecursive_mCA466033B6F14AC5C663F848791C0CC6C6B92716 (void);
// 0x000002EC System.Void UnityEngine.Timeline.PrefabControlPlayable::.ctor()
extern void PrefabControlPlayable__ctor_m0850EAD5A2F49DAF2B6D56326ED235D8042C9C90 (void);
// 0x000002ED UnityEngine.Playables.ScriptPlayable`1<UnityEngine.Timeline.TimeControlPlayable> UnityEngine.Timeline.TimeControlPlayable::Create(UnityEngine.Playables.PlayableGraph,UnityEngine.Timeline.ITimeControl)
extern void TimeControlPlayable_Create_m45018C6B659C011B90908D65862ADF68E9F9F251 (void);
// 0x000002EE System.Void UnityEngine.Timeline.TimeControlPlayable::Initialize(UnityEngine.Timeline.ITimeControl)
extern void TimeControlPlayable_Initialize_m7CE2820058BE601A316CCE5964E4CE3445E9980A (void);
// 0x000002EF System.Void UnityEngine.Timeline.TimeControlPlayable::PrepareFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void TimeControlPlayable_PrepareFrame_mF122A36FEFFA9BE1BC74FB551225652874D82E83 (void);
// 0x000002F0 System.Void UnityEngine.Timeline.TimeControlPlayable::OnBehaviourPlay(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void TimeControlPlayable_OnBehaviourPlay_mBFF77E285E53483F14E06EE0B56CFECD7CE13C31 (void);
// 0x000002F1 System.Void UnityEngine.Timeline.TimeControlPlayable::OnBehaviourPause(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void TimeControlPlayable_OnBehaviourPause_m9648D5058F2EC8BFC260AC603B5C03883AD284E1 (void);
// 0x000002F2 System.Void UnityEngine.Timeline.TimeControlPlayable::.ctor()
extern void TimeControlPlayable__ctor_m2324993C8F5009CC0336BB9D9396F453529CD5C7 (void);
// 0x000002F3 System.Void UnityEngine.Timeline.TimeNotificationBehaviour::set_timeSource(UnityEngine.Playables.Playable)
extern void TimeNotificationBehaviour_set_timeSource_mD0096011A303EB4C84B3DE3AAE908C51955E2F8E (void);
// 0x000002F4 UnityEngine.Playables.ScriptPlayable`1<UnityEngine.Timeline.TimeNotificationBehaviour> UnityEngine.Timeline.TimeNotificationBehaviour::Create(UnityEngine.Playables.PlayableGraph,System.Double,UnityEngine.Playables.DirectorWrapMode)
extern void TimeNotificationBehaviour_Create_mF24D48476C110D3158F32421C168A5171F4613A0 (void);
// 0x000002F5 System.Void UnityEngine.Timeline.TimeNotificationBehaviour::AddNotification(System.Double,UnityEngine.Playables.INotification,UnityEngine.Timeline.NotificationFlags)
extern void TimeNotificationBehaviour_AddNotification_mB502CDA1135E3A3F543B7B24224BB95F986EAD97 (void);
// 0x000002F6 System.Void UnityEngine.Timeline.TimeNotificationBehaviour::OnGraphStart(UnityEngine.Playables.Playable)
extern void TimeNotificationBehaviour_OnGraphStart_m8C925120572F3A69DA5E9B3DB127F1C506CE436E (void);
// 0x000002F7 System.Void UnityEngine.Timeline.TimeNotificationBehaviour::OnBehaviourPause(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void TimeNotificationBehaviour_OnBehaviourPause_m6FCF029B2259978A231F78DEC1E4F57E2D88E7C1 (void);
// 0x000002F8 System.Void UnityEngine.Timeline.TimeNotificationBehaviour::PrepareFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void TimeNotificationBehaviour_PrepareFrame_m8C38D54B9B061C4B57098EA0458E768974E193DE (void);
// 0x000002F9 System.Void UnityEngine.Timeline.TimeNotificationBehaviour::SortNotifications()
extern void TimeNotificationBehaviour_SortNotifications_m91493C483D46116C8DEE5C25AEE2F9872B3DB86C (void);
// 0x000002FA System.Boolean UnityEngine.Timeline.TimeNotificationBehaviour::CanRestoreNotification(UnityEngine.Timeline.TimeNotificationBehaviour/NotificationEntry,UnityEngine.Playables.FrameData,System.Double,System.Double)
extern void TimeNotificationBehaviour_CanRestoreNotification_m7390D6FA66D1786D1AF1412D37D74FD9EE661E2E (void);
// 0x000002FB System.Void UnityEngine.Timeline.TimeNotificationBehaviour::TriggerNotificationsInRange(System.Double,System.Double,UnityEngine.Playables.FrameData,UnityEngine.Playables.Playable,System.Boolean)
extern void TimeNotificationBehaviour_TriggerNotificationsInRange_m9607548147744987224ADD3FE6E3F339D2337284 (void);
// 0x000002FC System.Void UnityEngine.Timeline.TimeNotificationBehaviour::SyncDurationWithExternalSource(UnityEngine.Playables.Playable)
extern void TimeNotificationBehaviour_SyncDurationWithExternalSource_mEE92F71E0867E713E58EDF71B9DEE1A1E2F25925 (void);
// 0x000002FD System.Void UnityEngine.Timeline.TimeNotificationBehaviour::Trigger_internal(UnityEngine.Playables.Playable,UnityEngine.Playables.PlayableOutput,UnityEngine.Timeline.TimeNotificationBehaviour/NotificationEntry&)
extern void TimeNotificationBehaviour_Trigger_internal_m87169DADFCE06BC37BA28CFED3B0A6174DFFEAB7 (void);
// 0x000002FE System.Void UnityEngine.Timeline.TimeNotificationBehaviour::Restore_internal(UnityEngine.Timeline.TimeNotificationBehaviour/NotificationEntry&)
extern void TimeNotificationBehaviour_Restore_internal_m25E1C8121200E214969CDD9A753D6357D440726A (void);
// 0x000002FF System.Void UnityEngine.Timeline.TimeNotificationBehaviour::.ctor()
extern void TimeNotificationBehaviour__ctor_m82EC1218499AA0C697D1005358DA66DB1908FFAC (void);
// 0x00000300 System.Boolean UnityEngine.Timeline.TimeNotificationBehaviour/NotificationEntry::get_triggerInEditor()
extern void NotificationEntry_get_triggerInEditor_m0F467B8FC45EDED62344F2EB66D91998C330FC7A (void);
// 0x00000301 System.Boolean UnityEngine.Timeline.TimeNotificationBehaviour/NotificationEntry::get_prewarm()
extern void NotificationEntry_get_prewarm_mDF7D568CD5A14E53E9E11DAF6AD33F0FE7ED29DE (void);
// 0x00000302 System.Boolean UnityEngine.Timeline.TimeNotificationBehaviour/NotificationEntry::get_triggerOnce()
extern void NotificationEntry_get_triggerOnce_m40147F02406594EFEA328E29EDC9C3110E9A7654 (void);
// 0x00000303 System.Void UnityEngine.Timeline.TimeNotificationBehaviour/<>c::.cctor()
extern void U3CU3Ec__cctor_m6669D2494CB2B140029B26B2BE183C4A8DEDB476 (void);
// 0x00000304 System.Void UnityEngine.Timeline.TimeNotificationBehaviour/<>c::.ctor()
extern void U3CU3Ec__ctor_m8358614F1AC3F7F79522CD7795964B99E574C428 (void);
// 0x00000305 System.Int32 UnityEngine.Timeline.TimeNotificationBehaviour/<>c::<SortNotifications>b__12_0(UnityEngine.Timeline.TimeNotificationBehaviour/NotificationEntry,UnityEngine.Timeline.TimeNotificationBehaviour/NotificationEntry)
extern void U3CU3Ec_U3CSortNotificationsU3Eb__12_0_mCFEAA6C512A546FDC4CC69A23EF75844DFDB9189 (void);
// 0x00000306 System.Void UnityEngine.Timeline.PlayableTrack::OnCreateClip(UnityEngine.Timeline.TimelineClip)
extern void PlayableTrack_OnCreateClip_mB016C731FC0C8567F707C53BAD5B9E9B99DE2BE7 (void);
// 0x00000307 System.Void UnityEngine.Timeline.PlayableTrack::.ctor()
extern void PlayableTrack__ctor_m1ADC6FA394F1E5B1A484480F7306E35A401A2A22 (void);
// 0x00000308 System.Void UnityEngine.Timeline.TrackMediaType::.ctor(UnityEngine.Timeline.TimelineAsset/MediaType)
extern void TrackMediaType__ctor_m1357FFFF3A466DC18E97A85F4DF7577A145FFED1 (void);
// 0x00000309 System.Void UnityEngine.Timeline.TrackClipTypeAttribute::.ctor(System.Type)
extern void TrackClipTypeAttribute__ctor_m32F3A6AF3F0CCD2AC80A08276A2C84DAD6B59ADA (void);
// 0x0000030A System.Void UnityEngine.Timeline.TrackClipTypeAttribute::.ctor(System.Type,System.Boolean)
extern void TrackClipTypeAttribute__ctor_m6A7E59068246D7C95D43A99B54403748FE2E5F5F (void);
// 0x0000030B System.Void UnityEngine.Timeline.NotKeyableAttribute::.ctor()
extern void NotKeyableAttribute__ctor_m11E8B4B00459AEB31D3345A5A8045E5D21CC35CB (void);
// 0x0000030C System.Void UnityEngine.Timeline.TrackBindingTypeAttribute::.ctor(System.Type)
extern void TrackBindingTypeAttribute__ctor_m905B5F0071EF43101C6DE52F5383F8B15FF66176 (void);
// 0x0000030D System.Void UnityEngine.Timeline.TrackBindingTypeAttribute::.ctor(System.Type,UnityEngine.Timeline.TrackBindingFlags)
extern void TrackBindingTypeAttribute__ctor_m38D2D803DD43575FBB29F858067DF10911CF7D95 (void);
// 0x0000030E System.Void UnityEngine.Timeline.SupportsChildTracksAttribute::.ctor(System.Type,System.Int32)
extern void SupportsChildTracksAttribute__ctor_m3FE00570A7899A3F7CF0FB8522DE0C4C21694E51 (void);
// 0x0000030F System.Void UnityEngine.Timeline.IgnoreOnPlayableTrackAttribute::.ctor()
extern void IgnoreOnPlayableTrackAttribute__ctor_mAC076C2CED5738D08949C3F1BEB855D761191297 (void);
// 0x00000310 UnityEngine.Timeline.TimeFieldAttribute/UseEditMode UnityEngine.Timeline.TimeFieldAttribute::get_useEditMode()
extern void TimeFieldAttribute_get_useEditMode_m029ADEC4C39E6BC43084274528B66A956BDD1E15 (void);
// 0x00000311 System.Void UnityEngine.Timeline.TimeFieldAttribute::.ctor(UnityEngine.Timeline.TimeFieldAttribute/UseEditMode)
extern void TimeFieldAttribute__ctor_m91C16D6CAE58639D71BE206FFA6011A56BC9A53B (void);
// 0x00000312 System.Void UnityEngine.Timeline.FrameRateFieldAttribute::.ctor()
extern void FrameRateFieldAttribute__ctor_m1312B76429B8D8A394C3C28BC001E22877F3ABB2 (void);
// 0x00000313 System.Void UnityEngine.Timeline.HideInMenuAttribute::.ctor()
extern void HideInMenuAttribute__ctor_m3F3AFC914006D5CFC30B844C14E2E5BE671C1C20 (void);
// 0x00000314 System.Void UnityEngine.Timeline.CustomStyleAttribute::.ctor(System.String)
extern void CustomStyleAttribute__ctor_m171564751F00A5A77E810C78FAB06AF76EFEAE23 (void);
// 0x00000315 System.Void UnityEngine.Timeline.MenuCategoryAttribute::.ctor(System.String)
extern void MenuCategoryAttribute__ctor_m0AE9C26FDCD04C039FA1F3995969A6822B9957C6 (void);
// 0x00000316 UnityEngine.Timeline.ClipCaps UnityEngine.Timeline.ITimelineClipAsset::get_clipCaps()
// 0x00000317 System.Void UnityEngine.Timeline.ITimelineEvaluateCallback::Evaluate()
// 0x00000318 UnityEngine.Playables.ScriptPlayable`1<UnityEngine.Timeline.TimelinePlayable> UnityEngine.Timeline.TimelinePlayable::Create(UnityEngine.Playables.PlayableGraph,System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.TrackAsset>,UnityEngine.GameObject,System.Boolean,System.Boolean)
extern void TimelinePlayable_Create_mDBD16A025F217757ED3E323A2B2292B836B7849D (void);
// 0x00000319 System.Void UnityEngine.Timeline.TimelinePlayable::Compile(UnityEngine.Playables.PlayableGraph,UnityEngine.Playables.Playable,System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.TrackAsset>,UnityEngine.GameObject,System.Boolean,System.Boolean)
extern void TimelinePlayable_Compile_mCCE0D60E8AD0D45A14341C0FC4FA055633BE7D25 (void);
// 0x0000031A System.Void UnityEngine.Timeline.TimelinePlayable::CompileTrackList(UnityEngine.Playables.PlayableGraph,UnityEngine.Playables.Playable,System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.TrackAsset>,UnityEngine.GameObject,System.Boolean)
extern void TimelinePlayable_CompileTrackList_m94B0B3D833D78C5A070DD587705272E8D25EBB63 (void);
// 0x0000031B System.Void UnityEngine.Timeline.TimelinePlayable::CreateTrackOutput(UnityEngine.Playables.PlayableGraph,UnityEngine.Timeline.TrackAsset,UnityEngine.GameObject,UnityEngine.Playables.Playable,System.Int32)
extern void TimelinePlayable_CreateTrackOutput_mAD0C4882569BA478C8D59AA5130F8901BADE5DCF (void);
// 0x0000031C System.Void UnityEngine.Timeline.TimelinePlayable::EvaluateWeightsForAnimationPlayableOutput(UnityEngine.Timeline.TrackAsset,UnityEngine.Animations.AnimationPlayableOutput)
extern void TimelinePlayable_EvaluateWeightsForAnimationPlayableOutput_m7684913A2AF5D8265AE45AAAF46A9B006C028040 (void);
// 0x0000031D System.Void UnityEngine.Timeline.TimelinePlayable::EvaluateAnimationPreviewUpdateCallback(UnityEngine.Timeline.TrackAsset,UnityEngine.Animations.AnimationPlayableOutput)
extern void TimelinePlayable_EvaluateAnimationPreviewUpdateCallback_m55098F133C54B87A45ABAF36903EAE57CDAA16F4 (void);
// 0x0000031E UnityEngine.Playables.Playable UnityEngine.Timeline.TimelinePlayable::CreateTrackPlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.Playables.Playable,UnityEngine.Timeline.TrackAsset,UnityEngine.GameObject,System.Boolean)
extern void TimelinePlayable_CreateTrackPlayable_m18539F554343F8FF12D84137E5A0CA8FFA7E8B34 (void);
// 0x0000031F System.Void UnityEngine.Timeline.TimelinePlayable::PrepareFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void TimelinePlayable_PrepareFrame_m9124521CCE2CBF49C19EA1669DE2BAE6632C5153 (void);
// 0x00000320 System.Void UnityEngine.Timeline.TimelinePlayable::Evaluate(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void TimelinePlayable_Evaluate_m1CB17400BD2E1FE1757F8A106F67451D039C325F (void);
// 0x00000321 System.Void UnityEngine.Timeline.TimelinePlayable::CacheTrack(UnityEngine.Timeline.TrackAsset,UnityEngine.Playables.Playable,System.Int32,UnityEngine.Playables.Playable)
extern void TimelinePlayable_CacheTrack_m8C7FF43B821AC07FED21AD8C1537815FA0113B1C (void);
// 0x00000322 System.Void UnityEngine.Timeline.TimelinePlayable::ForAOTCompilationOnly()
extern void TimelinePlayable_ForAOTCompilationOnly_m6130D5C91CE6D75036FBE258FC54A70A89345C20 (void);
// 0x00000323 System.Void UnityEngine.Timeline.TimelinePlayable::.ctor()
extern void TimelinePlayable__ctor_m9A86459D9944225AE074C58C85B7F798E089FD5C (void);
// 0x00000324 System.Void UnityEngine.Timeline.TimelinePlayable::.cctor()
extern void TimelinePlayable__cctor_m0800C83345E9CC0DED4AB906A15ADE0859A1123D (void);
// 0x00000325 System.Void UnityEngine.Timeline.Extrapolation::CalculateExtrapolationTimes(UnityEngine.Timeline.TrackAsset)
extern void Extrapolation_CalculateExtrapolationTimes_m607FBFE8FBB128DA7AC236DA75A9097CA959BEE7 (void);
// 0x00000326 UnityEngine.Timeline.TimelineClip[] UnityEngine.Timeline.Extrapolation::SortClipsByStartTime(UnityEngine.Timeline.TimelineClip[])
extern void Extrapolation_SortClipsByStartTime_mCDB181CE76C4633A881347AB015409999918BF0F (void);
// 0x00000327 System.Void UnityEngine.Timeline.Extrapolation::.cctor()
extern void Extrapolation__cctor_mEED3F9AE8423558EE03C4A949B2954DD93925066 (void);
// 0x00000328 System.Void UnityEngine.Timeline.Extrapolation/<>c::.cctor()
extern void U3CU3Ec__cctor_m68412E65388EDDBD5C698C3B363320588FC91F3F (void);
// 0x00000329 System.Void UnityEngine.Timeline.Extrapolation/<>c::.ctor()
extern void U3CU3Ec__ctor_m4BEA68C8BAF3AD6FA1854F77A8584537339195A3 (void);
// 0x0000032A System.Int32 UnityEngine.Timeline.Extrapolation/<>c::<SortClipsByStartTime>b__2_0(UnityEngine.Timeline.TimelineClip,UnityEngine.Timeline.TimelineClip)
extern void U3CU3Ec_U3CSortClipsByStartTimeU3Eb__2_0_mAC857C3158459F813EB90B7EC80CC57E002F4562 (void);
// 0x0000032B System.Int32 UnityEngine.Timeline.HashUtility::CombineHash(System.Int32,System.Int32)
extern void HashUtility_CombineHash_mDB3BFB2F0222F9DFEA5A43CC152AF988732681DE (void);
// 0x0000032C System.Int32 UnityEngine.Timeline.HashUtility::CombineHash(System.Int32,System.Int32,System.Int32)
extern void HashUtility_CombineHash_mCD4724834FE836194D0AABEAF856DA6C625A73F6 (void);
// 0x0000032D System.Int32 UnityEngine.Timeline.HashUtility::CombineHash(System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashUtility_CombineHash_m288F5449B09EB88B46E520229DFF7C7F18CA51C0 (void);
// 0x0000032E System.Int32 UnityEngine.Timeline.HashUtility::CombineHash(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashUtility_CombineHash_mBB79C3AEE25C2E5895BA6F85AECCFF5B215005B6 (void);
// 0x0000032F System.Int32 UnityEngine.Timeline.HashUtility::CombineHash(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashUtility_CombineHash_mCA939EC20F8AE7BBABD2D433D3A1FB770AC30BF7 (void);
// 0x00000330 System.Int32 UnityEngine.Timeline.HashUtility::CombineHash(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashUtility_CombineHash_m1367FE812F3C9670BD59ECF0007F2383C469A29A (void);
// 0x00000331 System.Int32 UnityEngine.Timeline.HashUtility::CombineHash(System.Int32[])
extern void HashUtility_CombineHash_m8C98B06FEB8B88A93104DE6E8D02B30470718E5A (void);
// 0x00000332 System.Void UnityEngine.Timeline.IPropertyCollector::PushActiveGameObject(UnityEngine.GameObject)
// 0x00000333 System.Void UnityEngine.Timeline.IPropertyCollector::PopActiveGameObject()
// 0x00000334 System.Void UnityEngine.Timeline.IPropertyCollector::AddFromClip(UnityEngine.AnimationClip)
// 0x00000335 System.Void UnityEngine.Timeline.IPropertyCollector::AddFromClips(System.Collections.Generic.IEnumerable`1<UnityEngine.AnimationClip>)
// 0x00000336 System.Void UnityEngine.Timeline.IPropertyCollector::AddFromName(System.String)
// 0x00000337 System.Void UnityEngine.Timeline.IPropertyCollector::AddFromName(System.String)
// 0x00000338 System.Void UnityEngine.Timeline.IPropertyCollector::AddFromClip(UnityEngine.GameObject,UnityEngine.AnimationClip)
// 0x00000339 System.Void UnityEngine.Timeline.IPropertyCollector::AddFromClips(UnityEngine.GameObject,System.Collections.Generic.IEnumerable`1<UnityEngine.AnimationClip>)
// 0x0000033A System.Void UnityEngine.Timeline.IPropertyCollector::AddFromName(UnityEngine.GameObject,System.String)
// 0x0000033B System.Void UnityEngine.Timeline.IPropertyCollector::AddFromName(UnityEngine.GameObject,System.String)
// 0x0000033C System.Void UnityEngine.Timeline.IPropertyCollector::AddFromName(UnityEngine.Component,System.String)
// 0x0000033D System.Void UnityEngine.Timeline.IPropertyCollector::AddFromComponent(UnityEngine.GameObject,UnityEngine.Component)
// 0x0000033E System.Void UnityEngine.Timeline.IPropertyCollector::AddObjectProperties(UnityEngine.Object,UnityEngine.AnimationClip)
// 0x0000033F System.Void UnityEngine.Timeline.IPropertyPreview::GatherProperties(UnityEngine.Playables.PlayableDirector,UnityEngine.Timeline.IPropertyCollector)
// 0x00000340 UnityEngine.Playables.ScriptPlayable`1<UnityEngine.Timeline.TimeNotificationBehaviour> UnityEngine.Timeline.NotificationUtilities::CreateNotificationsPlayable(UnityEngine.Playables.PlayableGraph,System.Collections.Generic.IEnumerable`1<UnityEngine.Timeline.IMarker>,System.Double,UnityEngine.Playables.DirectorWrapMode)
extern void NotificationUtilities_CreateNotificationsPlayable_m2E015D453C40663721AEA2DA883EB070DEFAF586 (void);
// 0x00000341 System.Boolean UnityEngine.Timeline.NotificationUtilities::TrackTypeSupportsNotifications(System.Type)
extern void NotificationUtilities_TrackTypeSupportsNotifications_mA844D8C5CD9E01E806EFA9A16F37BD7575DB6012 (void);
// 0x00000342 System.Void UnityEngine.Timeline.TimelineClipExtensions::MoveToTrack(UnityEngine.Timeline.TimelineClip,UnityEngine.Timeline.TrackAsset)
extern void TimelineClipExtensions_MoveToTrack_m7B9992AD171A490F566871C4856C71E6DC1D28D9 (void);
// 0x00000343 System.Boolean UnityEngine.Timeline.TimelineClipExtensions::TryMoveToTrack(UnityEngine.Timeline.TimelineClip,UnityEngine.Timeline.TrackAsset)
extern void TimelineClipExtensions_TryMoveToTrack_m88EB39C25E4B43EF0A5F6DE62A576B6D3F6AE958 (void);
// 0x00000344 System.Void UnityEngine.Timeline.TimelineClipExtensions::MoveToTrack_Impl(UnityEngine.Timeline.TimelineClip,UnityEngine.Timeline.TrackAsset,UnityEngine.Object,UnityEngine.Timeline.TrackAsset)
extern void TimelineClipExtensions_MoveToTrack_Impl_mA57D81A0076D26140DF918D05C680BF0591F50BB (void);
// 0x00000345 System.Void UnityEngine.Timeline.TimelineClipExtensions::.cctor()
extern void TimelineClipExtensions__cctor_mB59452B0FD743296682255CC66A9B41704B2EA3C (void);
// 0x00000346 System.String UnityEngine.Timeline.TimelineCreateUtilities::GenerateUniqueActorName(System.Collections.Generic.List`1<UnityEngine.ScriptableObject>,System.String)
extern void TimelineCreateUtilities_GenerateUniqueActorName_mA256C1FED6CBEA0A7A41C3CD66E3617D70E7C554 (void);
// 0x00000347 System.Void UnityEngine.Timeline.TimelineCreateUtilities::SaveAssetIntoObject(UnityEngine.Object,UnityEngine.Object)
extern void TimelineCreateUtilities_SaveAssetIntoObject_m08F1B9275C4A4893711EA4281BBB3BBBEA9EEE0A (void);
// 0x00000348 System.Void UnityEngine.Timeline.TimelineCreateUtilities::RemoveAssetFromObject(UnityEngine.Object,UnityEngine.Object)
extern void TimelineCreateUtilities_RemoveAssetFromObject_mDDE3FBE78C7CBC5F771632F41076E68108475013 (void);
// 0x00000349 UnityEngine.AnimationClip UnityEngine.Timeline.TimelineCreateUtilities::CreateAnimationClipForTrack(System.String,UnityEngine.Timeline.TrackAsset,System.Boolean)
extern void TimelineCreateUtilities_CreateAnimationClipForTrack_m499C0BAB55D23B8B1759B4EFFCA6EC100B056770 (void);
// 0x0000034A System.Boolean UnityEngine.Timeline.TimelineCreateUtilities::ValidateParentTrack(UnityEngine.Timeline.TrackAsset,System.Type)
extern void TimelineCreateUtilities_ValidateParentTrack_mDE431017F5F40FDE73B7CDFACADDD1F484316CE6 (void);
// 0x0000034B System.Void UnityEngine.Timeline.TimelineCreateUtilities/<>c__DisplayClass0_0::.ctor()
extern void U3CU3Ec__DisplayClass0_0__ctor_m8C4942004C3562D0E7CEF1A8EBA62FE27E649328 (void);
// 0x0000034C System.Boolean UnityEngine.Timeline.TimelineCreateUtilities/<>c__DisplayClass0_0::<GenerateUniqueActorName>b__0(UnityEngine.ScriptableObject)
extern void U3CU3Ec__DisplayClass0_0_U3CGenerateUniqueActorNameU3Eb__0_m06DEF88A9AE88C0A9FEA42106BBB9D0E79B92D50 (void);
// 0x0000034D System.Void UnityEngine.Timeline.TimelineCreateUtilities/<>c__DisplayClass0_1::.ctor()
extern void U3CU3Ec__DisplayClass0_1__ctor_mE96B2A914FE0820EA130DFB44B1E713AD3FBFB90 (void);
// 0x0000034E System.Boolean UnityEngine.Timeline.TimelineCreateUtilities/<>c__DisplayClass0_1::<GenerateUniqueActorName>b__1(UnityEngine.ScriptableObject)
extern void U3CU3Ec__DisplayClass0_1_U3CGenerateUniqueActorNameU3Eb__1_m63691DF652C67A4727D3AD671C58FA56CA3FAEDB (void);
// 0x0000034F System.Void UnityEngine.Timeline.TimelineUndo::PushDestroyUndo(UnityEngine.Timeline.TimelineAsset,UnityEngine.Object,UnityEngine.Object)
extern void TimelineUndo_PushDestroyUndo_m502EE75014700236190DF20E9B5743E100E4E840 (void);
// 0x00000350 System.Void UnityEngine.Timeline.TimelineUndo::PushUndo(UnityEngine.Object[],System.String)
extern void TimelineUndo_PushUndo_mDAB595E45185F92C279C990AE284325133967B04 (void);
// 0x00000351 System.Void UnityEngine.Timeline.TimelineUndo::PushUndo(UnityEngine.Object,System.String)
extern void TimelineUndo_PushUndo_m1FD04DF7E2F50E65198221CDDB7B770C566037AE (void);
// 0x00000352 System.Void UnityEngine.Timeline.TimelineUndo::RegisterCreatedObjectUndo(UnityEngine.Object,System.String)
extern void TimelineUndo_RegisterCreatedObjectUndo_mDB8ECC4BFD7A3491605CA1F89CE349ED35750365 (void);
// 0x00000353 System.String UnityEngine.Timeline.TimelineUndo::UndoName(System.String)
extern void TimelineUndo_UndoName_m0E19A1C9B54C2A0735D35FF4F8DFC92EDA12E192 (void);
// 0x00000354 System.Void UnityEngine.Timeline.TimeUtility::ValidateFrameRate(System.Double)
extern void TimeUtility_ValidateFrameRate_mFDD6870A06AB0E87193D1152E370161AF173992D (void);
// 0x00000355 System.Int32 UnityEngine.Timeline.TimeUtility::ToFrames(System.Double,System.Double)
extern void TimeUtility_ToFrames_m42B1DBF623EBD722B8B393C34A3F5FF33D5F6188 (void);
// 0x00000356 System.Double UnityEngine.Timeline.TimeUtility::ToExactFrames(System.Double,System.Double)
extern void TimeUtility_ToExactFrames_m442908215ADBB76C41A4CC2C031320379E50095C (void);
// 0x00000357 System.Double UnityEngine.Timeline.TimeUtility::FromFrames(System.Int32,System.Double)
extern void TimeUtility_FromFrames_m0D428831B080A1E23EEF38CD7F15461D7FD651CC (void);
// 0x00000358 System.Double UnityEngine.Timeline.TimeUtility::FromFrames(System.Double,System.Double)
extern void TimeUtility_FromFrames_m90B7D5F87C01001AE4B9F712E8B7F517CAAF91F8 (void);
// 0x00000359 System.Boolean UnityEngine.Timeline.TimeUtility::OnFrameBoundary(System.Double,System.Double)
extern void TimeUtility_OnFrameBoundary_mE89A73D9D109EFCAAB028DEA3A545276EC8A3A68 (void);
// 0x0000035A System.Double UnityEngine.Timeline.TimeUtility::GetEpsilon(System.Double,System.Double)
extern void TimeUtility_GetEpsilon_m2DDCC59890F0A007D609E29CD1BC62B04EF6FB7B (void);
// 0x0000035B System.Boolean UnityEngine.Timeline.TimeUtility::OnFrameBoundary(System.Double,System.Double,System.Double)
extern void TimeUtility_OnFrameBoundary_mAC72C8A3F1E3E3F05F03BE07CDCAD5DE7808421B (void);
// 0x0000035C System.Double UnityEngine.Timeline.TimeUtility::RoundToFrame(System.Double,System.Double)
extern void TimeUtility_RoundToFrame_m9A9CF7FB93ABCBE05A4987D766A6640B5FCE02BD (void);
// 0x0000035D System.String UnityEngine.Timeline.TimeUtility::TimeAsFrames(System.Double,System.Double,System.String)
extern void TimeUtility_TimeAsFrames_mD7686EA3126E3A791789F999B9B9E25B6F2178AD (void);
// 0x0000035E System.String UnityEngine.Timeline.TimeUtility::TimeAsTimeCode(System.Double,System.Double,System.String)
extern void TimeUtility_TimeAsTimeCode_m448D4FC6FD14FD5816A4DA71F82218C4121ADFA1 (void);
// 0x0000035F System.Double UnityEngine.Timeline.TimeUtility::ParseTimeCode(System.String,System.Double,System.Double)
extern void TimeUtility_ParseTimeCode_mC51B6AEBBABD417C83090B0A8C1DFE5BE133A438 (void);
// 0x00000360 System.Double UnityEngine.Timeline.TimeUtility::ParseTimeSeconds(System.String,System.Double,System.Double)
extern void TimeUtility_ParseTimeSeconds_m28AB4715C13959B4AB0829540082001D158335DC (void);
// 0x00000361 System.Double UnityEngine.Timeline.TimeUtility::GetAnimationClipLength(UnityEngine.AnimationClip)
extern void TimeUtility_GetAnimationClipLength_m0C35066397FC1474ED4B04934DBAB064CC1891DB (void);
// 0x00000362 System.String UnityEngine.Timeline.TimeUtility::RemoveChar(System.String,System.Func`2<System.Char,System.Boolean>)
extern void TimeUtility_RemoveChar_mB4D81436DF7CAC6C2768E463808F0CDB4D4D7D91 (void);
// 0x00000363 UnityEngine.Playables.FrameRate UnityEngine.Timeline.TimeUtility::GetClosestFrameRate(System.Double)
extern void TimeUtility_GetClosestFrameRate_m777D1AA22EBF63B3F327B4744C8F304F5E3F59C9 (void);
// 0x00000364 UnityEngine.Playables.FrameRate UnityEngine.Timeline.TimeUtility::ToFrameRate(UnityEngine.Timeline.StandardFrameRates)
extern void TimeUtility_ToFrameRate_mB8BD26B2A1C5BF1DBAC8B55BB3CCB4D86294E1A4 (void);
// 0x00000365 System.Boolean UnityEngine.Timeline.TimeUtility::ToStandardFrameRate(UnityEngine.Playables.FrameRate,UnityEngine.Timeline.StandardFrameRates&)
extern void TimeUtility_ToStandardFrameRate_mE55BCB25BFF3ED034F50D98E31D2595C0C8D8C90 (void);
// 0x00000366 System.Void UnityEngine.Timeline.TimeUtility::.cctor()
extern void TimeUtility__cctor_m49E229AEEAC66C8DE40593C47BBC3F9552CBDA1B (void);
// 0x00000367 System.Void UnityEngine.Timeline.TimeUtility/<>c::.cctor()
extern void U3CU3Ec__cctor_m59AEBAC840298E7D2900A2B3BF20699F650D2477 (void);
// 0x00000368 System.Void UnityEngine.Timeline.TimeUtility/<>c::.ctor()
extern void U3CU3Ec__ctor_m56DDC30169ADA8554EE8A0BC9D347AD024BD36A7 (void);
// 0x00000369 System.Boolean UnityEngine.Timeline.TimeUtility/<>c::<ParseTimeCode>b__15_0(System.Char)
extern void U3CU3Ec_U3CParseTimeCodeU3Eb__15_0_m6908D437E7CEF43504892D5EBF8E7E9E9447B6C5 (void);
// 0x0000036A System.Boolean UnityEngine.Timeline.TimeUtility/<>c::<ParseTimeCode>b__15_1(System.Char)
extern void U3CU3Ec_U3CParseTimeCodeU3Eb__15_1_mCE7371E16461F9A768516B757AE99C53FF8B70FE (void);
// 0x0000036B System.Boolean UnityEngine.Timeline.TimeUtility/<>c::<ParseTimeSeconds>b__16_0(System.Char)
extern void U3CU3Ec_U3CParseTimeSecondsU3Eb__16_0_m85E57DE53E8DEBF2BBED2C0E1F354D95666F83E4 (void);
// 0x0000036C System.Single UnityEngine.Timeline.WeightUtility::NormalizeMixer(UnityEngine.Playables.Playable)
extern void WeightUtility_NormalizeMixer_m6EE0D0701870481B80B9B8AA1F259C1A786379CA (void);
static Il2CppMethodPointer s_methodPointers[876] = 
{
	ActivationMixerPlayable_Create_mB03A5A5C425D4F901ED4B55E89799A5D207EEC47,
	ActivationMixerPlayable_get_postPlaybackState_mF0384E7535C7AEA8617EADC8D9832D8C5CC46D46,
	ActivationMixerPlayable_set_postPlaybackState_m5FDC121E23D90F62C75843C5A363DE97E7DD2EB3,
	ActivationMixerPlayable_OnPlayableDestroy_mE80DBAA8FAFBA511669B4E78B55751AA5898B074,
	ActivationMixerPlayable_ProcessFrame_mDCC3B223FA8AB796778E2E8B979A227BE8DB588C,
	ActivationMixerPlayable__ctor_m513A8FE0373D3B5A4FFC829ED4DB38735128582E,
	ActivationPlayableAsset_get_clipCaps_mF49360399D36AEA60F85A01905F9CC9176F32FA2,
	ActivationPlayableAsset_CreatePlayable_m5A7D557E44615A717158ADB9CAFAD5C1FA70279A,
	ActivationPlayableAsset__ctor_m367911F439EC602657B306B2F180B46D959A87B3,
	ActivationTrack_CanCompileClips_mA606603F95C9516112052DD4B17136499DE42193,
	ActivationTrack_get_postPlaybackState_m1294D74ED475B43256FC9AEB2AB2DEDC2019FB82,
	ActivationTrack_set_postPlaybackState_m25320A2912F8EF46DEDDE08963D837CA3B1E55CE,
	ActivationTrack_CreateTrackMixer_mD0C559021DDE8B870EE86BCA9BE59DF1FB54C38F,
	ActivationTrack_UpdateTrackMode_mD01BE7C19739DBC651C645F5AD11FB6DAF5EBB1E,
	ActivationTrack_GatherProperties_m3808D2F7F5C1FAE282906617F42A80448A33F1FA,
	ActivationTrack_OnCreateClip_m14768AEC5F93456C8BB50050D14D09DF68EDD703,
	ActivationTrack__ctor_mC854FF5136E9975AF73188924C9A939FA1B973C4,
	AnimationOutputWeightProcessor__ctor_m474D5FD59685D4A13DE5307630091A0414E91E0F,
	AnimationOutputWeightProcessor_FindMixers_m82DA2CCB4F1B0AA24E3B8CFAB6DDEC8176EE2F65,
	AnimationOutputWeightProcessor_FindMixers_mF9681DE5BB91778AFCF883E4732DA6BE1A31A43D,
	AnimationOutputWeightProcessor_Evaluate_m3C0568570541A5BD6B6405B49B72CCE50714BB17,
	AnimationPlayableAsset_get_position_mC75DAA87C62AE6BC462492531727C2E45EC150F0,
	AnimationPlayableAsset_set_position_mF1539D8E2BCDC4BFABF2D1683EAD9AE7A91B4DE3,
	AnimationPlayableAsset_get_rotation_m1DBB59B5F15442C5CEA8F76CA8B23398459D32D8,
	AnimationPlayableAsset_set_rotation_mEE464480D66C9C564AC035507554C340E013EB8C,
	AnimationPlayableAsset_get_eulerAngles_m1FAFD32D61627D433AC648EDB670431434372123,
	AnimationPlayableAsset_set_eulerAngles_mEFBE1E1450804D8A5C2C44D276178B01CBA74F8A,
	AnimationPlayableAsset_get_useTrackMatchFields_m3CA7C4CD0E3AE6B315028847FD86485139BB1DB2,
	AnimationPlayableAsset_set_useTrackMatchFields_m020978DC261F7FBA5BE26BF2E3CCFFE79A7DA27C,
	AnimationPlayableAsset_get_matchTargetFields_m65CDF95B5D21B931336CDD663309ADC226C666F6,
	AnimationPlayableAsset_set_matchTargetFields_mCF18FDE6E3EA660995C822B5678EBB98D374FDB7,
	AnimationPlayableAsset_get_removeStartOffset_m38878C3AEB0FF2C0ABAEC4CF6ACA7E640CF896F9,
	AnimationPlayableAsset_set_removeStartOffset_mB16879678AA070E3D8A4C58FBA1267867E20B420,
	AnimationPlayableAsset_get_applyFootIK_m3D85EDBC855694E23CA22044F83F3EA9918FF6A7,
	AnimationPlayableAsset_set_applyFootIK_m2894B7F3E32B0CB12961AEC079B464F5F3195006,
	AnimationPlayableAsset_get_loop_mFB0E127EC34FDCB9E706B864733FB45580281A81,
	AnimationPlayableAsset_set_loop_mC3AF990F0EDBFF52B6D0C17234144B1999471B6E,
	AnimationPlayableAsset_get_hasRootTransforms_m74DF6202F2F64346F199101D528591FF02AFE48C,
	AnimationPlayableAsset_get_appliedOffsetMode_m6A5F22FBF4719ADE8C78BB1A13397E5BA186F908,
	AnimationPlayableAsset_set_appliedOffsetMode_m389C789650423968CF01A0830766D2346F2202D1,
	AnimationPlayableAsset_get_clip_m0170771CEBEA44040F1857332846B5E12183AF8E,
	AnimationPlayableAsset_set_clip_mF2E641EB15E80990B4587625F199F681FBFAE752,
	AnimationPlayableAsset_get_duration_m7FC1BA9D27EAFC31E19C71EB0AF485A955D8E615,
	AnimationPlayableAsset_get_outputs_mD795879A17D0640EDFB055B39B7C832136E216DA,
	AnimationPlayableAsset_CreatePlayable_mAC380311FD71AB8C793327307A1BCBE8CF152F29,
	AnimationPlayableAsset_CreatePlayable_m8B11A8EAC431FCD5E878FABCB683A4F9B74A6998,
	AnimationPlayableAsset_ShouldApplyOffset_m7D958577429B51630CAD52246A957F9BD850119D,
	AnimationPlayableAsset_ShouldApplyScaleRemove_m99FB3C3471F9B6C7A5E595308EF4ABCDB2E63841,
	AnimationPlayableAsset_get_clipCaps_m2BAF77E319A9EE382FF20AB13A56907312C064F8,
	AnimationPlayableAsset_ResetOffsets_m8DC6D73742DA551BB81E5BDAAC2F186D045D0EC4,
	AnimationPlayableAsset_GatherProperties_mB16CF0B019B7CF063D76CA4B69A51C227EB24EF0,
	AnimationPlayableAsset_HasRootTransforms_mF8339B890A9F6875852E02AA6FE7EBA7F2CE8161,
	AnimationPlayableAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mE7288B1EA8E582C2661277C75FDE80D8B6CFEBC1,
	AnimationPlayableAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mA584D2BD183C657C1DB990C055CE79E3880C88E2,
	AnimationPlayableAsset_OnUpgradeFromVersion_mE19EC4FA01C0D93E8FAC4E4D56FC3B5DBB2C2354,
	AnimationPlayableAsset__ctor_m6F298BFCD42F32F57484C3329B147586ECBF141B,
	AnimationPlayableAsset__cctor_m1748B5EB189D81582B97A7BBD370D03960F1FD7D,
	AnimationPlayableAssetUpgrade_ConvertRotationToEuler_mB851991DE0D6624E9C49487DFA8630FA6AC08491,
	U3Cget_outputsU3Ed__45__ctor_m0E2DA3AD05663EA90B980D7150DDAD589F0336DA,
	U3Cget_outputsU3Ed__45_System_IDisposable_Dispose_mA7F31F3D37807CE6BA6B290B62C93BEE16B0A6D7,
	U3Cget_outputsU3Ed__45_MoveNext_mF982F762F9D3069822C9C69545FA6E439F6F1DC8,
	U3Cget_outputsU3Ed__45_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m7E523BBDF4F714C6737AC3D2709BB75FCF0ED93E,
	U3Cget_outputsU3Ed__45_System_Collections_IEnumerator_Reset_mC8FF472CBE1E59FE4E740083CA6D09ACF0354C0F,
	U3Cget_outputsU3Ed__45_System_Collections_IEnumerator_get_Current_mBFDCDFF0341D56EEF2ACE33B8504A53CECE1F7AE,
	U3Cget_outputsU3Ed__45_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m89F51E97B14897204A10EB33B1DF48AD64260492,
	U3Cget_outputsU3Ed__45_System_Collections_IEnumerable_GetEnumerator_m571AD1499FE97BA88AC1996D6DBCB5FB81EA6B64,
	AnimationPreviewUpdateCallback__ctor_m9A81A41A73B25FFB193B05DB41AAD480B5019302,
	AnimationPreviewUpdateCallback_Evaluate_m4859735BDA333DF67BA53C5157909003103DC4FF,
	AnimationPreviewUpdateCallback_FetchPreviewComponents_mF1C66A832544073C8DFA760DB6F1910DED0BE2D2,
	MatchTargetFieldConstants_HasAny_m73744E801BA5F758254854BF5283EE4A8BFA13B4,
	MatchTargetFieldConstants_Toggle_m649C32A5427F1B9959B3D58468956DF2FBE237DD,
	MatchTargetFieldConstants__cctor_mC761D8CE72745FF29695B219E52D3AF62D2DC5BC,
	AnimationTrack_get_position_mBF745D40410A1F530116319EDA2ED612851C863E,
	AnimationTrack_set_position_m58583D926C865297D1D248FF3DB33E9A101115F6,
	AnimationTrack_get_rotation_m5A3EC08F5E1AD62FC72BA69294001BAC42C15251,
	AnimationTrack_set_rotation_m372BEF805EE2FEA03E5766C853CD934FADAF6FFD,
	AnimationTrack_get_eulerAngles_m9EDE81D8FD7675470DF4CFA620D911019B56C768,
	AnimationTrack_set_eulerAngles_mCF06238E5C41BE05161163DCA33ED817BF1E24CD,
	AnimationTrack_get_applyOffsets_m10D70F0C85FB9C462B4659813EDB37BF84D86B9A,
	AnimationTrack_set_applyOffsets_mF644A614EB6FB30B8604D917BA8F4A710983A9DE,
	AnimationTrack_get_trackOffset_m8D91B82F85D98F276E45FB805628232278AF2D2D,
	AnimationTrack_set_trackOffset_m66B617D407D7D8CE17EDAD849DEC34FD1E0B1CE5,
	AnimationTrack_get_matchTargetFields_m130ABF7AA0D2F38D40127029CA02993A5FA998F3,
	AnimationTrack_set_matchTargetFields_m14ED03793080CDD4BDF4D1B91418C430853D9330,
	AnimationTrack_get_infiniteClip_mE55BF71562803B5C849A335584AE526A2747E5B2,
	AnimationTrack_set_infiniteClip_mA700EED208399A4DCA3314E3F848BBE698DAB6C4,
	AnimationTrack_get_infiniteClipRemoveOffset_m18DFC76B9AFEC35654AF299A62264E925202146B,
	AnimationTrack_set_infiniteClipRemoveOffset_m7999F05C17EE0524CCC197732CADB8927A0D5454,
	AnimationTrack_get_avatarMask_m4E6BD1DAC9D92FD6CD663DADEE525A158CFE4F01,
	AnimationTrack_set_avatarMask_m97BFF1B75D7E891C249CCE0917F1CBCB886FD30D,
	AnimationTrack_get_applyAvatarMask_m2FCA8FA363B449E4420E8492F92E5270A2D4FA7E,
	AnimationTrack_set_applyAvatarMask_mEAEA18A18FB74A33D808B94F20C01A2D880A8606,
	AnimationTrack_CanCompileClips_m67F41F2F20E51CB999957ACD3D9C31B799EC3026,
	AnimationTrack_get_outputs_mF3776BF93312A616ADC9D18F061AD972626F0EB7,
	AnimationTrack_get_inClipMode_m0559987652D8B45851CF6EE37AAE259156007271,
	AnimationTrack_get_infiniteClipOffsetPosition_m23355191742B9EF9877193DCD3C77758D9435666,
	AnimationTrack_set_infiniteClipOffsetPosition_m74A78994C38BE22C6AD33C5EF4CD710CCAFDD166,
	AnimationTrack_get_infiniteClipOffsetRotation_m3A152B45D0F99F716EEFA25BDF0A932FACB02052,
	AnimationTrack_set_infiniteClipOffsetRotation_m0B8FF5AB8D6F4EE92C022D40510C5C41AD3896C2,
	AnimationTrack_get_infiniteClipOffsetEulerAngles_m0EF73054860F1F705E07F5729C9CB1CE0D4AA6B9,
	AnimationTrack_set_infiniteClipOffsetEulerAngles_m958891CA74BDFD73A39E2427084FA3777939C29E,
	AnimationTrack_get_infiniteClipApplyFootIK_m813FEE3250C60BFEF060210215BD68E3C5CC29CB,
	AnimationTrack_set_infiniteClipApplyFootIK_mCC194F75CD06FA7E9F0EE890E7A2AADA9C387BAB,
	AnimationTrack_get_infiniteClipTimeOffset_mC4376105C76EAB9E8139C426D3BD218ED21B4A7C,
	AnimationTrack_set_infiniteClipTimeOffset_m93F31C8AEFE3DE5FE275F7EB250FA98F5978AD3A,
	AnimationTrack_get_infiniteClipPreExtrapolation_m4B4906080C16E2D7C07B409787363735F8C8AB85,
	AnimationTrack_set_infiniteClipPreExtrapolation_m5A1D2B6511FB01EEDF0405DB5914F43D1289EDE3,
	AnimationTrack_get_infiniteClipPostExtrapolation_mE68867202ACB337F11C12FD75C196278FFEA34D9,
	AnimationTrack_set_infiniteClipPostExtrapolation_m8197868300D197E73BFB72D95EEC0CA3F3E69D6E,
	AnimationTrack_get_infiniteClipLoop_m0076C92D7935227835C007059EC228D6ACA5205C,
	AnimationTrack_set_infiniteClipLoop_mE52F35E3A552CD510D873A9A1865D474974B2750,
	AnimationTrack_ResetOffsets_m9CA8762D3880419B7CA71E966228FF801A0F47A4,
	AnimationTrack_CreateClip_mC2014488815D2C34B95F739D82F9A93A195FD919,
	AnimationTrack_CreateInfiniteClip_mC2EB76F9F2462F3AED209E90018A86DE5D36843B,
	AnimationTrack_CreateRecordableClip_m2F49C361C87418375F803A4760EDF011A9D960C7,
	AnimationTrack_OnCreateClip_m5A7462347F4CAA6EC0B248F534F45C89B8F38786,
	AnimationTrack_CalculateItemsHash_m69D9B8299B8E4085DE184A3FC278BB33C6CECF70,
	AnimationTrack_UpdateClipOffsets_m0DE8CEBC4F96E1F40669DC8732F2E04EBADA1331,
	AnimationTrack_CompileTrackPlayable_m8AEA486513BF42BC4682217FCB37A61218B68094,
	AnimationTrack_UnityEngine_Timeline_ILayerable_CreateLayerMixer_m2C90244DFA117930E0911876688435A62CF9C628,
	AnimationTrack_CreateMixerPlayableGraph_mC3352DAB3A0AF3D277C78B94D5DC2DB763DFD3F7,
	AnimationTrack_GetDefaultBlendCount_m34C24E878EB89D3D5B56AECED494D8375E35A64D,
	AnimationTrack_AttachDefaultBlend_mAF67FD6982E9EAA8CAFCB0106C7F324547400DFC,
	AnimationTrack_AttachOffsetPlayable_m854FE97C64D490D94150A604554C01A9433813B8,
	AnimationTrack_RequiresMotionXPlayable_m8BF0F53DE406934214E0E402295F8E432E02744C,
	AnimationTrack_UsesAbsoluteMotion_m305667FA27FD4DFC54406F5A21A4344A069EAF4C,
	AnimationTrack_HasController_m9DA394CFD76B80E53A80EC26FEEC9F1769BA6684,
	AnimationTrack_GetBinding_mFA6516C67603F256A7306024BB0DA86496F42B43,
	AnimationTrack_CreateGroupMixer_mF2B248685103E2FBA973109155FE0D5153946B8A,
	AnimationTrack_CreateInfiniteTrackPlayable_m7951EDDA36CE14693AFFF9C460C0FC3E5B732A88,
	AnimationTrack_ApplyTrackOffset_m7ACA50839B41B3AECEA66E60986AE71E9FA3F7A0,
	AnimationTrack_GetEvaluationTime_mE2271656B917685A4108E734F77ED34FC75EC3AD,
	AnimationTrack_GetSequenceTime_m366C5486DCF37C3A41612B08749F063F491ED593,
	AnimationTrack_AssignAnimationClip_m26DB250244CE6240EF4F946A4D93F311D0D1F367,
	AnimationTrack_GatherProperties_m5FE1175C9AE614E6878B582CEF3DE396837B54C2,
	AnimationTrack_GetAnimationClips_m7459AE8A7BE31D25B0625297A0C94173042A0207,
	AnimationTrack_GetOffsetMode_m8D2EA6334F72FE079612C5B71B745CAF91111C9D,
	AnimationTrack_IsRootTransformDisabledByMask_m130CFADD77EB830E9E7EA6DBBE88EFEE73E1D0C7,
	AnimationTrack_GetGenericRootNode_m40F3B83DE0D88C67592549EBDFADCD0168D63C3A,
	AnimationTrack_AnimatesRootTransform_m2D168DCB100F72EFC128C2DEC6B068E2CC216F1F,
	AnimationTrack_FindInHierarchyBreadthFirst_m021FFC49FDED402415A469E33CC690FFCC7AD1CB,
	AnimationTrack_get_openClipOffsetPosition_m75E1CFC9D51546A9F24A0EB998B6E87794C3A705,
	AnimationTrack_set_openClipOffsetPosition_m9752610280CE6027BAEE683C36C9D290B442A6DB,
	AnimationTrack_get_openClipOffsetRotation_m10B5894DD11A0AF08D99594D1FB459B6362B9303,
	AnimationTrack_set_openClipOffsetRotation_m1D1B9B56375DE34C8BEA905DCC0299B39BFC7B02,
	AnimationTrack_get_openClipOffsetEulerAngles_m355BD7957BCD76AC87769CED0927FD9728C3F2EA,
	AnimationTrack_set_openClipOffsetEulerAngles_m562570438DFB431C65AB322D1753D6210DB62787,
	AnimationTrack_get_openClipPreExtrapolation_m27E1C2DE521D2F5B0AB5569695CC1C060416FDB4,
	AnimationTrack_set_openClipPreExtrapolation_mC2D775230A229A66B1E98C131FAE66C2312E4BC3,
	AnimationTrack_get_openClipPostExtrapolation_m81FD4E8B992C6758D21790E77A57DAFB2CFB903A,
	AnimationTrack_set_openClipPostExtrapolation_m1C35F5DC4A86ADA4548AFAFA8323A118433B91FC,
	AnimationTrack_OnUpgradeFromVersion_mB41D3B6A330F4AA2FE59E61632E4B1F99C2866DF,
	AnimationTrack__ctor_mDB5857315630BBECBD1CD59097456E0F331B9264,
	AnimationTrack__cctor_mF7E0C93F99D88D2E284763DB25C116DA566C1135,
	AnimationTrackUpgrade_ConvertRotationsToEuler_m4ACA9D918164FEEDBEC2881DE13C779094D253D5,
	AnimationTrackUpgrade_ConvertRootMotion_m54DF7747856D06D2179EB95642D299E5D6A97A21,
	AnimationTrackUpgrade_ConvertInfiniteTrack_m4006E7259789D3B8829ABC5700DD2828C3EB2A6F,
	U3Cget_outputsU3Ed__49__ctor_mD93688EDAA61ADD8474DC977257C4865A2EC6389,
	U3Cget_outputsU3Ed__49_System_IDisposable_Dispose_m607FCCC43C82ECC93F674CCB5415929336502C51,
	U3Cget_outputsU3Ed__49_MoveNext_m7BDA8087249FD461975437E56C9BFE7B80C8A099,
	U3Cget_outputsU3Ed__49_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m8665A050400E9DA6702260117B51F8609806ECBE,
	U3Cget_outputsU3Ed__49_System_Collections_IEnumerator_Reset_m5D524429CEE49B55F5EE0E3015806B835110C113,
	U3Cget_outputsU3Ed__49_System_Collections_IEnumerator_get_Current_mF8DC66EBBBD2A455C22F3B8B229EE3FC09ABC96F,
	U3Cget_outputsU3Ed__49_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m3B62F5C3F8AF686D249000D59B0052197BECB161,
	U3Cget_outputsU3Ed__49_System_Collections_IEnumerable_GetEnumerator_mFC98EDB93155902AC6052BEAEBF678837BD62B2C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TimelineClip_UpgradeToLatestVersion_m3C367DA0661298065465C583C9D33AC4B63ADF0D,
	TimelineClip__ctor_m69070FAC237B4E3DC873E07AD1C3902E5C031939,
	TimelineClip_get_hasPreExtrapolation_m2D086ECAC0C10D46FE58D22AEAA9CC1EAE94C196,
	TimelineClip_get_hasPostExtrapolation_mE6565EF983300E526B31AC25C57F8B583C6B7AC6,
	TimelineClip_get_timeScale_mF76C0A2D5CDBF201F2CC01967928CA7FFC261474,
	TimelineClip_set_timeScale_m29FAE01EF4CF682E2C82169B9D6976289C2665D0,
	TimelineClip_get_start_m76BB53BEBD6B700D5A4197F72779A321DE55B296,
	TimelineClip_set_start_m476586FED5274C2691B71BC75C76E3F471332BF5,
	TimelineClip_get_duration_m4DC76F051723CC7427813C076B255BA8BB4366F7,
	TimelineClip_set_duration_m1904C6BD6C64F8CC7AD09256F559C8C62AB97001,
	TimelineClip_get_end_m4C3E4DF4B095A1D60694B379EA839A68E3C4217C,
	TimelineClip_get_clipIn_m0ABA66BE9CAD32C80313321C963C8FA9AB5FC1EB,
	TimelineClip_set_clipIn_m5ACCEA54D376384ED1264CE7F108BD64F725C67E,
	TimelineClip_get_displayName_m61712CDBEA102FB64B5E3464E4420843E1FB7111,
	TimelineClip_set_displayName_m5F725FB8B45340748ECFAA870D034C85352F54CD,
	TimelineClip_get_clipAssetDuration_mC3509BB4EF5397A55A42E02BE6A6730998B5EAF5,
	TimelineClip_get_curves_m7572F0ACC8D51E605271B6002685740F946FC761,
	TimelineClip_set_curves_mDE58D1A741554AF3E95F7BC53625E5D7F3BD212A,
	TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_defaultCurvesName_m65DD8FEFE6FC8AADA51EECD8906A9066C7EF6C18,
	TimelineClip_get_hasCurves_mDB6EF3ADD8FF4693992F7EA092F269A6F3631EFD,
	TimelineClip_get_asset_m49BF68F5E0C41EBA5145FCA0C97D7146DF016120,
	TimelineClip_set_asset_mF8539EA76B6C0F19E2AECBA025C70D605322195E,
	TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_assetOwner_m542C93F1E1B0382CCA596359A1A96DAC55539D66,
	TimelineClip_UnityEngine_Timeline_ICurvesOwner_get_targetTrack_m46FB66ACADD2B522822EB7691A4A5F1C976A368F,
	TimelineClip_get_underlyingAsset_m1DE5682815DA82061499CCF642ECDC9CAA9C1881,
	TimelineClip_set_underlyingAsset_m02D44F445992B5431831BF7C77466164CA527B85,
	TimelineClip_get_parentTrack_m3E33146230902DCAFAEAAE034F5DCBB70BE03A8C,
	TimelineClip_set_parentTrack_mC3646EF3C4198254776CFA1E47CFC2636C1BA69E,
	TimelineClip_GetParentTrack_m560CB13E873CE39EB7E3754B53B1594D2A58C37D,
	TimelineClip_SetParentTrack_Internal_m39F7D49888E7EBFC36796EAE0C7BE6C263C0FB02,
	TimelineClip_get_easeInDuration_m171A5C1C8BA1392C4CBC7F5C17A90EAF915A2145,
	TimelineClip_set_easeInDuration_mF310C781E91789DAA8AE5A593C9BA46FC173E01C,
	TimelineClip_get_easeOutDuration_mAAE6DEE05138F6902199D985A3EE6E26BCBB35EC,
	TimelineClip_set_easeOutDuration_m4666F442BADEAFAC3C8E450BCF987E403D1E42ED,
	TimelineClip_get_eastOutTime_mE21671B32EE9CB06C994A5B91D5E86741F2BB30B,
	TimelineClip_get_easeOutTime_mC16D31D13CB3953A0ECE61CCD13381318B6AC115,
	TimelineClip_get_blendInDuration_m3A18D7C0942B43C5A171422EDC1D41260C1269D7,
	TimelineClip_set_blendInDuration_m34F303216992A919EF47D01B874EEEB47C8470E3,
	TimelineClip_get_blendOutDuration_mA1BB945DFE74A6AB5EB112587ED3509CFAF43379,
	TimelineClip_set_blendOutDuration_m5013A1CF4593F805143A0E665261EBB0B00298F0,
	TimelineClip_get_blendInCurveMode_m4C612BAD55BB180A24C9FA67A8F9D84C907E170C,
	TimelineClip_set_blendInCurveMode_m4138FBEEA1484C9DDAA0C859B505E8E20B4DFE8A,
	TimelineClip_get_blendOutCurveMode_m106D929CE951F07CB31BF9DEA7B0D2B1D830F093,
	TimelineClip_set_blendOutCurveMode_m5CBC0E1382775395757B85824BFBB39571241A9E,
	TimelineClip_get_hasBlendIn_m9B9C96AAADD801969D9C97D4130428EEB6B6D368,
	TimelineClip_get_hasBlendOut_m8444D00A050FD627EA6A7DC834B5B14B2C845E51,
	TimelineClip_get_mixInCurve_mCF8C05F353EF6EF3E115DEC17FA10DB481724B45,
	TimelineClip_set_mixInCurve_m53265562D7425F388E7DC93717D8707F816E65EF,
	TimelineClip_get_mixInPercentage_m9120693B790082B5ED9FC988B3C9B5F06C946B10,
	TimelineClip_get_mixInDuration_m754EA2369F50687A58E3A883DBA5DA79C090B243,
	TimelineClip_get_mixOutCurve_mAA470F75E97A1960E50BFA0B97E4BC2E0873D687,
	TimelineClip_set_mixOutCurve_m93EA2EF70C31BC5625A419E6830AFB6DD594A729,
	TimelineClip_get_mixOutTime_mD780BF14E8EE91F808CEB62BEE0A93B30C6233A6,
	TimelineClip_get_mixOutDuration_m19A9A249A1628E222E534662B0D4CB30F4DBCB0E,
	TimelineClip_get_mixOutPercentage_m4D9124E362272E0DA49CA228C72E5CEEF39BB3CB,
	TimelineClip_get_recordable_m2BE0E30776FECD30BAE62D78399B6402682B2001,
	TimelineClip_set_recordable_m38B678C027026275DD27636B8AF8345D94D9403A,
	TimelineClip_get_exposedParameters_mB96B6748027997A9DFEF943387DD86F74876B279,
	TimelineClip_get_clipCaps_m11FD6AE29AD801B99B64A71C6C76680FA213FC0A,
	TimelineClip_Hash_mA9299778B1C618795049AB37ADD4A01A7E5331DE,
	TimelineClip_EvaluateMixOut_mA36406D289DE5A9831B821AC5A9C51DF21B47C83,
	TimelineClip_EvaluateMixIn_m206FBF82C1AF116724AC429CAF7C4759E6D28D60,
	TimelineClip_GetDefaultMixInCurve_m90A35726DC5817536F73783D5B1028F64251EDE4,
	TimelineClip_GetDefaultMixOutCurve_m07738400AEE1FB893C14562EF2EE11142E767AE6,
	TimelineClip_ToLocalTime_m975B84C7F3371F39F73AD8DA1F89C3C825D40E1E,
	TimelineClip_ToLocalTimeUnbound_m8E307B2FDD49E30A57137FD94BA001B9157DEA6F,
	TimelineClip_FromLocalTimeUnbound_mBCE77F0E508F86A77DE09CE16623A6FB374700E1,
	TimelineClip_get_animationClip_mFDFBA4582868E85C0B56242660C9EF3B4442D07D,
	TimelineClip_SanitizeTimeValue_m3185743B561388628273A3FBD9AD1C7C4AD3A9DE,
	TimelineClip_get_postExtrapolationMode_mDF06AB6BEE7906C373109C04775C0B3CA83D8685,
	TimelineClip_set_postExtrapolationMode_mD2B003EB90D2C816050F5FF12B662401BB858092,
	TimelineClip_get_preExtrapolationMode_mC20AE951A26D337093ABC65FD2DBF9C5CBE8E3FA,
	TimelineClip_set_preExtrapolationMode_m26E6222B67AFA46CC6F1EE1730C8819B19D035AF,
	TimelineClip_SetPostExtrapolationTime_m659389D7F8F07AE58FF2D1B4BCFAB9FF13691C35,
	TimelineClip_SetPreExtrapolationTime_mE9DAF291C193942B0703ADB10CA1BEF1B9D0C4DA,
	TimelineClip_IsExtrapolatedTime_m82974EEE2D3C650B90C2F5F5592FB31B7029AB6C,
	TimelineClip_IsPreExtrapolatedTime_mCA79ABD6E8F0408DA16E8BB57C9509BB092CC753,
	TimelineClip_IsPostExtrapolatedTime_m2C8FFEF3F821D6CD45A7D8F5E06F3E7705C6B1FE,
	TimelineClip_get_extrapolatedStart_m4092C97F1EB880583FA92B8FAAFC208D245630BF,
	TimelineClip_get_extrapolatedDuration_mFA3957094871DA9151A26AB04A894E771FFC1F21,
	TimelineClip_GetExtrapolatedTime_m10D665C5B1EC5CBCF3FC38E10BE6A2276D5E7405,
	TimelineClip_CreateCurves_m494AE0E2457ACD87387E039AF081F93442FACBB3,
	TimelineClip_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mDC9EFB3125995E8EFA91EB3CF27E9B2784B60DE8,
	TimelineClip_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mE0860037B34E43C2CB25567793EDB4F48B018D4E,
	TimelineClip_ToString_m56D472178DA2F04872969618EF5CA3E142E9FD9A,
	TimelineClip_ConformEaseValues_m6774B17BD4E1E6734FC21CB1F67C267DF92DD623,
	TimelineClip_CalculateEasingRatio_mE7960562253BE9447E532E1BA3709AF9B0834D75,
	TimelineClip_UpdateDirty_m3F5A0077C88F2C9D481AE93CEF2E16DCD235EC08,
	TimelineClip__cctor_mD74B4E06C1F4905731E8C1E06F0BB24BCED88317,
	TimelineClipUpgrade_UpgradeClipInFromGlobalToLocal_m59F498F944599E3F1BAF6B0D5AC37E5E38770E7C,
	TimelineAsset_UpgradeToLatestVersion_m34BA46E2D4643EFB179CBFB81706EC27785CBFA0,
	TimelineAsset_get_editorSettings_m3D5DEC0305D9E517E29CB898C789C3B42D3B1CB9,
	TimelineAsset_get_duration_m22226DF293F2D489E63D881775112FC26518E297,
	TimelineAsset_get_fixedDuration_mB9B8F8FBE5DCF918C17AC705E6D17A730DB9A69C,
	TimelineAsset_set_fixedDuration_m10FF2CD4C12508C9F4938A17B2EF7E81A5C701AB,
	TimelineAsset_get_durationMode_m58895CD3D78D4F4A912BD149FD810B3AE6AA0034,
	TimelineAsset_set_durationMode_mB082ACE2157EF8472C2C30C07DAE8DDCC6E9008D,
	TimelineAsset_get_outputs_m232A9F7BCE2E4BCFB2115B9ABB26D53C7D8C478E,
	TimelineAsset_get_clipCaps_m0F3ACD23FAE67D92F0F67A056F0AC64BEC0A3AA8,
	TimelineAsset_get_outputTrackCount_m07CED6FE6B99D62D0E5EB3C2A91863E21E1CB70B,
	TimelineAsset_get_rootTrackCount_m89C66C83C874A1D60BE068E40C5CB94033F0A7ED,
	TimelineAsset_OnValidate_m4D4626C6372A467AE9EB04DA835A8CD9EE75EDF0,
	TimelineAsset_GetRootTrack_mC41A0CF4127692DDEEBAA8BA973BE4392C49B83A,
	TimelineAsset_GetRootTracks_m6340C1C261F14F8FBAF7116F775780C75068F05F,
	TimelineAsset_GetOutputTrack_m8002892E722FE6CA9C8B8EB7D7040BE1E47D672D,
	TimelineAsset_GetOutputTracks_m324315A337B30921D4B5E740E1FC2BE81563B26F,
	TimelineAsset_GetValidFrameRate_m5F0BAEC92EDFDBE539E1A0187DC2F185DA8D5E72,
	TimelineAsset_UpdateRootTrackCache_mADE060D9C1F785CFA83E43B4CB895D772EF74749,
	TimelineAsset_UpdateOutputTrackCache_m4A4249728370EB77115CDCB08D2A08C4D3925818,
	TimelineAsset_get_flattenedTracks_m51B0499CC8CD43CB520F6B84D37FCD19C8FE4F18,
	TimelineAsset_get_markerTrack_mA1522943FBA0FDFDB0765165F00B6F9373EB01A3,
	TimelineAsset_get_trackObjects_mFE1A564170B932E65741C29D0A94B5DA55C7D676,
	TimelineAsset_AddTrackInternal_m1699C0B3168FF4D471509AA664AA92BCC3000A57,
	TimelineAsset_RemoveTrack_m34E111A2A0D031C27F8EB42DD4CD014A5883ADC2,
	TimelineAsset_CreatePlayable_m015AE0523BB6A93F3F0070BF0757ECA3199EB5FE,
	TimelineAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m4ED0C35C1033C2177A683A17F97863B2F3A8982F,
	TimelineAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m7C5218B558DC68FA00A3A7AC87FAE02F589C2808,
	TimelineAsset___internalAwake_m3BDB3D8798B27D100140D4C189285ACCFD116F18,
	TimelineAsset_GatherProperties_m966DA31A1B9D51FA370CB0DD4B3FC9036C230EEA,
	TimelineAsset_CreateMarkerTrack_m959223B301DE70DED79380138F93BF1565D2B586,
	TimelineAsset_Invalidate_m601FE157269B617EEB1D840439E36715DC81AFC6,
	TimelineAsset_UpdateFixedDurationWithItemsDuration_mE79C311415955F474C4C885E4CF8B7124CBBE008,
	TimelineAsset_CalculateItemsDuration_m2904F09544AB814380305EA97D6EB11AFDCFD0BC,
	TimelineAsset_AddSubTracksRecursive_m03EBD254DE32A5C23CE13E80EFFC4BDC51AD960F,
	TimelineAsset_CreateTrack_m327D088F33507A544DE566503CDF6593C024C1ED,
	NULL,
	NULL,
	NULL,
	TimelineAsset_DeleteClip_m61EFBF21AF49C5FE55CD70060C19B81657170B43,
	TimelineAsset_DeleteTrack_m80BEC8B29D94214E5663B0F64E725726B7EBAC08,
	TimelineAsset_MoveLastTrackBefore_mB1C0BE93519C75B57FC07BCFBCBFA912A61841E4,
	TimelineAsset_AllocateTrack_m8DFE21D5BC86310C82E23E43CE3D7D04759EA2F5,
	TimelineAsset_DeleteRecordedAnimation_m3FF55ADA0F576F30D7522D85D999E5A2A8824939,
	TimelineAsset_DeleteRecordedAnimation_mBE3F874CEAEFD795EC06C74E50F3E007890369C8,
	TimelineAsset__ctor_m75D9A08991F60CBFEFDAD23DC01B6A49A4601E4C,
	EditorSettings_get_fps_m2B7B48B6BBD590B690E91D5C761336A3454796A2,
	EditorSettings_set_fps_mC963EB8F7CAFC6A54CBF37278ADFB0311161674F,
	EditorSettings_get_frameRate_m699A6599212042DC13CB0DB91C7057B9644A0037,
	EditorSettings_set_frameRate_mE8F1DFC8C2B4203B48865F22DB7C5309932EB32B,
	EditorSettings_SetStandardFrameRate_mA67A361C75A563095119DC1E6D20FD455ED1AC1A,
	EditorSettings_get_scenePreview_m1FF2AB573D0405B69314643070555FFAF997B3C0,
	EditorSettings_set_scenePreview_m7996AD908D830ED6DB8D9FF103FE848BA9AEF553,
	EditorSettings__ctor_m239B6DF8C75E8C21F4FCA53B668E9C7E64D1A8A3,
	EditorSettings__cctor_m4FE2EB891C4975DD9E721DDF925816B926040D89,
	U3Cget_outputsU3Ed__27__ctor_mBFD68070C9454051A90D0AA75FB4D2FF2F63466F,
	U3Cget_outputsU3Ed__27_System_IDisposable_Dispose_m08F64F0AB9B4EFBB4D45A957869E4167332ACD41,
	U3Cget_outputsU3Ed__27_MoveNext_mA380099DD3A55548271DBAD6F80E42AA990F778C,
	U3Cget_outputsU3Ed__27_U3CU3Em__Finally1_m7E65569ADF800B81563544B8D264833E518A3C7C,
	U3Cget_outputsU3Ed__27_U3CU3Em__Finally2_m85B6B1B2A51FA070A90244EC2076C5E22F7CD920,
	U3Cget_outputsU3Ed__27_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m22E55FE2DC239357C1BEBBB551C332C97F221F31,
	U3Cget_outputsU3Ed__27_System_Collections_IEnumerator_Reset_m334D7D0581BE692B7764A7F29E4C53B6BD3D8350,
	U3Cget_outputsU3Ed__27_System_Collections_IEnumerator_get_Current_m2F264D817E0F1C5AE475AD9282977DEC88875D14,
	U3Cget_outputsU3Ed__27_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m22938CACD8363F1D9EF4B4B7BCC37B3ECF0554C0,
	U3Cget_outputsU3Ed__27_System_Collections_IEnumerable_GetEnumerator_m54F9DE0351DB259B654FB5C81A2B12FABB8DA0CE,
	TrackAsset_OnBeforeTrackSerialize_m5BCB0C082629BB807C2BC9F8E1623AA84EF3CDD8,
	TrackAsset_OnAfterTrackDeserialize_mF383ADAF4C3070BFB81F0478237946621E4FC850,
	TrackAsset_OnUpgradeFromVersion_m5A0523EA6441B741A60768840C9A6493DB682D22,
	TrackAsset_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m965E10D67C9FEE0BDE0BB2887C4A24B6BDEC755B,
	TrackAsset_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m6A13E9CC4CBECD81002067EAD4B9FF875071F0D1,
	TrackAsset_UpgradeToLatestVersion_m70FA58294D201156412BA790FD158D2029FDB505,
	TrackAsset_add_OnClipPlayableCreate_m7E859C1C550253310D9D6B34427DDAF0325AC228,
	TrackAsset_remove_OnClipPlayableCreate_mE0AE4E40FC4B75040AFEB6AF08B55FFA6F72CAEE,
	TrackAsset_add_OnTrackAnimationPlayableCreate_mCBC06DE4F00D3DFF308397D76B49D4F4C71058B0,
	TrackAsset_remove_OnTrackAnimationPlayableCreate_mA022F7131A984ECB7D002069487AC63834290FA1,
	TrackAsset_get_start_mA37EAE96A7EB745D0BC538509B78AEDEF05208E1,
	TrackAsset_get_end_m283E3BCE09D393EFA56FB26D5AD68326EECFF312,
	TrackAsset_get_duration_m3A3B2F6AD1F1945549C459B432CD4E469FAAECEF,
	TrackAsset_get_muted_mBA0D78639ED5132C019E69AC51A5BE56478C7A66,
	TrackAsset_set_muted_m9FCF9772AA42FB22761AA468792BE272258E426E,
	TrackAsset_get_mutedInHierarchy_m14257BCE9CD51D5A3C086E465F035A7707D666F7,
	TrackAsset_get_timelineAsset_m969726B43E66370FB81F1CCB6C012BCAD2B112A8,
	TrackAsset_get_parent_m4B5D7DF104388286053C4BE8D5ECB28615FE7DD9,
	TrackAsset_set_parent_mE5000EE0C72D1A0E0A6B916B7E0B94F85192D138,
	TrackAsset_GetClips_m467A7BE887049F3CC0F411AB220F488D1230FA76,
	TrackAsset_get_clips_m033A1CF810A017C6BD6F52190421C1474648BEB8,
	TrackAsset_get_isEmpty_m269392C25CDC0E0179FEFD5322476B8BEF7FC86B,
	TrackAsset_get_hasClips_m552EDF37A12F12705B5A016D9351380BF08D25ED,
	TrackAsset_get_hasCurves_m0F0AF8869F6E78DB23C129134B11A39E9CA4208E,
	TrackAsset_get_isSubTrack_mB3337FCA3D035259F1A6F5B9CFD6D9B6E2EDF05E,
	TrackAsset_get_outputs_mF8F332F69DBE3F1DF5C24DF6AFEE25B14F6ED4F7,
	TrackAsset_GetChildTracks_m34EE35A341030F99020F56267BAFC8FF4E98477C,
	TrackAsset_get_customPlayableTypename_m5E5465F06E569999A2839AAA5B29B6907C74718D,
	TrackAsset_set_customPlayableTypename_m091B3CFE1EEBAFC3508726A51254C6090CF84A4A,
	TrackAsset_get_curves_m3068225198516361CFB374B26B71E266079D1031,
	TrackAsset_set_curves_m61933C531CDE903FD6DCB11ABB1DEA36F6C844B6,
	TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_defaultCurvesName_m56DD3624C90F3BDDD8AC804AD16A80B4D1D5D542,
	TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_asset_mCB4DB0FF33D5C0E1A701D53C1CA54386A1DAF291,
	TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_assetOwner_m9E12631082B8FD221B0C93F5EA88E8926D629537,
	TrackAsset_UnityEngine_Timeline_ICurvesOwner_get_targetTrack_m34F7F32EC8BF3AA9219D1B02A6E8BE32D2B0BD50,
	TrackAsset_get_subTracksObjects_m18E7B9EEC20905CBE5D35805FAA819CB663ECC43,
	TrackAsset_get_locked_mCEBC35262FB276860A374085F2626DCE333FC466,
	TrackAsset_set_locked_mA8190606E6AC618DCF1CF211AD648C15E67D0F87,
	TrackAsset_get_lockedInHierarchy_mB8B7847141ACB7B33320D85B456F7B7FAD155298,
	TrackAsset_get_supportsNotifications_mFAD0EDA9BAD3DDD341161C02D5DDC658F1184729,
	TrackAsset___internalAwake_mBBCB93A3A0959E0B82B795E298DCD8B3C5E67FAB,
	TrackAsset_CreateCurves_m4EA9E9F12B65A9584E97158527FA7DC7BD11BAB2,
	TrackAsset_CreateTrackMixer_m088F7E10980285D4DFD092CFDFB0A34B9AB78DAF,
	TrackAsset_CreatePlayable_mD0CEA49410876109A48E365380586AD0E5F0CEBF,
	TrackAsset_CreateDefaultClip_m1AC3502758E2185D0747626A32D27B513AC85E0F,
	NULL,
	TrackAsset_DeleteClip_mA02A503841BACD54C29CCAF816A917F1018E1FAE,
	TrackAsset_CreateMarker_mD4F5715387220B12D0EF244C7C02F83F6040638A,
	NULL,
	TrackAsset_DeleteMarker_mA491061D745EB9073AB46DABEDDA9B4BF8963347,
	TrackAsset_GetMarkers_m4FE387892A6434D0DDD3535BD974E276372B7ADA,
	TrackAsset_GetMarkerCount_mE3D948E944448243CAC284D1D358C9A889FB44B7,
	TrackAsset_GetMarker_m2CB75ED400AECE525785B9E58020CC8EF0669A0A,
	TrackAsset_CreateClip_mA7D1A7B6ACCF5CCF9FB416E86C483BD2EC31A45F,
	TrackAsset_CreateAndAddNewClipOfType_mF1CA85A2B77A5CF003685CEBBF0A7551CCFA05E9,
	TrackAsset_CreateClipOfType_m62E140A7CBDF66DE0643A1D65601E6ECCC6DA0B1,
	TrackAsset_CreateClipFromPlayableAsset_mFCC78BCEE93E36BB92F1CC13A08CA7E11592CB44,
	TrackAsset_CreateClipFromAsset_mC48C4A19A082D69011DE9A64D73F05160DD1BD55,
	TrackAsset_GetMarkersRaw_m31260E1A58B86C26BD0E4EE80F5DC22FA64FBC14,
	TrackAsset_ClearMarkers_m4F8DA7A63459F34E3561E03BB1D198D63B0435ED,
	TrackAsset_AddMarker_m9B5E8B481DA0086DB46B0FE6219231F2B8FE63E8,
	TrackAsset_DeleteMarkerRaw_mC80F55242BE94323E2C826A8C9CD6E7C8ECA7540,
	TrackAsset_GetTimeRangeHash_m6A9D09CE4BE80DBBF10AD763E66944FBB37C33D4,
	TrackAsset_AddClip_mC0AD6626726FE95A50EFEC2DAF26D9625210FE09,
	TrackAsset_CreateNotificationsPlayable_mA46A9A1F92F6DBB21E6AA080A0491AF04F326D9B,
	TrackAsset_CreatePlayableGraph_mB09B3F258784F140E7D3F33ED4029056EA220371,
	TrackAsset_CompileClips_mAC9B684ADF9F1E0AB784A283BF350D3B5AC3D7AA,
	TrackAsset_GatherCompilableTracks_m0494F6912D73193408CB13F43099A6D5F2665C14,
	TrackAsset_GatherNotifications_mF5DBDCF22D9CD5ED6FE6D868583BB6D332AD6BFA,
	TrackAsset_CreateMixerPlayableGraph_m38CCE75D2966419D1FB327209C2320F36FB19222,
	TrackAsset_ConfigureTrackAnimation_mF10DDD7B2768DEDFDB46752A7E7A43F7E149EBBC,
	TrackAsset_SortClips_m4FE3C4820022ECF050815215CEB982DE373F85D2,
	TrackAsset_ClearClipsInternal_m60A468FD1AF2A83A9C8BB65376464216878BE994,
	TrackAsset_ClearSubTracksInternal_m8638EC88D91AC3AA5F80BF38C92E16BD67D994DE,
	TrackAsset_OnClipMove_m9FB2E0FF1003CA992D3F6841D13418262C8BF341,
	TrackAsset_CreateNewClipContainerInternal_m7DD56E4426DDBA57B25BF61EAF45938324FA9F2B,
	TrackAsset_AddChild_m208FDE1AB513103FCFA9F1C8174B976E9D1DAE76,
	TrackAsset_MoveLastTrackBefore_m4619211278763606F6FE3FC90665837417BCD966,
	TrackAsset_RemoveSubTrack_m7F68C7D6AF1FB79584D154B357F47ACDF29FDAA8,
	TrackAsset_RemoveClip_m1D64D42648A148BAE62836E46FC57C25EC9D1A60,
	TrackAsset_GetEvaluationTime_m30B94204B96340B1C2810E05607E34A4DB85067F,
	TrackAsset_GetSequenceTime_m618B6BD38BE9B1136E8405B1BEDFBAFD898896A9,
	TrackAsset_GatherProperties_m09C1A335FCE1ABA158748583AF4A641FF2EBB09D,
	TrackAsset_GetGameObjectBinding_mF2D645FA74007FD6EA6B337298F939FFB4A5B853,
	TrackAsset_ValidateClipType_mD794D7B040F112C3E8ACBF9FB601414D08B97A8F,
	TrackAsset_OnCreateClip_m0FF04313EBF6CE614CAB3C124B1F4D11B0E3AF94,
	TrackAsset_UpdateDuration_mCC46D1145F6BCB2B5DB28211C23E7E12702F7035,
	TrackAsset_CalculateItemsHash_m2928153E88198C690E2CB8486AC483555276546E,
	TrackAsset_CreatePlayable_mDD98D897F11DDC7593DF7F3029ED7A1B63285D0C,
	TrackAsset_Invalidate_m3CBA531307AEE6181C9938F174AE6D1A977115BF,
	TrackAsset_GetNotificationDuration_m8F9FB2B13DC1C6CC88617A18535730D1E012FC13,
	TrackAsset_CanCompileClips_mB7B603BB1D6782D00C14752530F66C638C174505,
	TrackAsset_CanCreateTrackMixer_mA825C739B3AA34B9F0F36C7534AAC4E84073F261,
	TrackAsset_IsCompilable_mD65DF730D54F4D1F0F0E13D42A009FDF8C59F284,
	TrackAsset_UpdateChildTrackCache_mDEEB1C096FDD5B0D035CD5C699119CD913BC6BB0,
	TrackAsset_Hash_m11C239AAFB9C1FFB1035A0B0DC5271A62CA3AD78,
	TrackAsset_GetClipsHash_mF0B665CDE32E5BEA60FD6EC88F7272CEA6A83115,
	TrackAsset_GetAnimationClipHash_m9A2D605C95C224426E2263A8693E578B77633EE7,
	TrackAsset_HasNotifications_mE386A9FE718BC4C8D368E8BA5B0FFC3BC50C5E06,
	TrackAsset_CanCompileNotifications_mEA94C527EED8871F7AB1CC74C3487ABC204C708A,
	TrackAsset_CanCreateMixerRecursive_m31242CE6BCCB6DA4F24690457585789405645FFC,
	TrackAsset__ctor_mC05CAAD737449BAF26721F82EA1972843F86FE9A,
	TrackAsset__cctor_m9E9CC7F378B533931B19CC75ACD04B10793C18BA,
	TransientBuildData_Create_m4A6E221013EDF3015DB6842AC1A51882A0E74F86,
	TransientBuildData_Clear_mA1B052EF3A05C723008A58559E4C77A989ED2FD3,
	U3Cget_outputsU3Ed__65__ctor_m36D55B6998402316E9C824384D2823CF2887FA99,
	U3Cget_outputsU3Ed__65_System_IDisposable_Dispose_m1E8207D4EAFDAEFB62061AD6D689431BD40999AB,
	U3Cget_outputsU3Ed__65_MoveNext_mB5F1A5655A42CB9A832A2B7B941A3DA555E65F36,
	U3Cget_outputsU3Ed__65_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_mA6C35B64EBEC6BDA1221AC7F991E746D1729589D,
	U3Cget_outputsU3Ed__65_System_Collections_IEnumerator_Reset_m322763B2D18B71E3A120A80829AB3CBE23709A03,
	U3Cget_outputsU3Ed__65_System_Collections_IEnumerator_get_Current_mF3FCDDFE0515DC769DD6EF0A8D6ABADA7BEE9302,
	U3Cget_outputsU3Ed__65_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m1EFEC630120231AFC3D898AFCDD0756F9561543D,
	U3Cget_outputsU3Ed__65_System_Collections_IEnumerable_GetEnumerator_mD5E8220BC3E60F92FA1CFDD991151AAE34C519B2,
	U3CU3Ec__cctor_m4CF50A8244F0E8B9535D4C7B45D8D6B2D4C3F07A,
	U3CU3Ec__ctor_m0071EE643C6662AFC30FD5F241BD4F6B05382B91,
	U3CU3Ec_U3CSortClipsU3Eb__121_0_mBD715AA0E013972CC995FFB813CBACEAFD120E71,
	TimelineHelpURLAttribute__ctor_mEB91B85CCCBEC196457E5FCB18A6C9C13E48CEBC,
	TrackColorAttribute_get_color_m5AC7558C98A60D996F1C3A9F1EB6F180FB45FFE4,
	TrackColorAttribute__ctor_mBC50DD796426AAD66A6A8BB7BE46EBB7873AA006,
	AudioClipProperties__ctor_m044736544A12AD2A937225432C158B5BDA240B83,
	AudioMixerProperties_PrepareFrame_m84E02C27BA11487134C9360A3A3F2BF90A3CDAD8,
	AudioMixerProperties__ctor_mB4EC402EBE2B6C8F3CF268598319F789AF837592,
	AudioPlayableAsset_get_bufferingTime_mE7FFA2B05A64D29C552930DFAA17A57CFE397031,
	AudioPlayableAsset_set_bufferingTime_mDA418332D841FE5667E21538BBAEC16041A07032,
	AudioPlayableAsset_get_clip_m49311E74FD58B38CC0D8ED16A0D8869F4ADAB720,
	AudioPlayableAsset_set_clip_m2A3379A7D58655D8C6B0395F262B6829FDCAD6EB,
	AudioPlayableAsset_get_loop_m579753DC7E58ACCA36A44E217C28EBF40CC3C294,
	AudioPlayableAsset_set_loop_m594EA5EE80017E2F1C15FE170CAD6F22D01EE7DD,
	AudioPlayableAsset_get_duration_m44FF0A8D8526B9EFBA604BFCDF79DB380E5B76BB,
	AudioPlayableAsset_get_outputs_m40AC20DF1E9E352D9C2EA015742D42E7F9C73E6E,
	AudioPlayableAsset_CreatePlayable_mBFEC2FDD97D15CA9376CAAF65FE217AAC6E854BB,
	AudioPlayableAsset_get_clipCaps_m7DF29DF049D9F0407E9AFD7D209AB825A8743F80,
	AudioPlayableAsset__ctor_mF10BFB16BB64A6F8D0FE62D7A0B8335ADB119A76,
	U3Cget_outputsU3Ed__16__ctor_m61F54FD6956E98C4C644EC8189368AB432C0F0D4,
	U3Cget_outputsU3Ed__16_System_IDisposable_Dispose_m72E1B27D3C90C82B4A415D51E3B2177CED2BE178,
	U3Cget_outputsU3Ed__16_MoveNext_m6D77A67836D44DDA516C67755527D4DD24E1F9A5,
	U3Cget_outputsU3Ed__16_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_mBE6C905012CCD38CF8A2DD6A4815FDE3772370D2,
	U3Cget_outputsU3Ed__16_System_Collections_IEnumerator_Reset_m026B2E69F6C94660273E8386AC24B908344881CE,
	U3Cget_outputsU3Ed__16_System_Collections_IEnumerator_get_Current_m582D28A2EE588AF6E3ACADE39F5EE74A2B852D1B,
	U3Cget_outputsU3Ed__16_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m8038F14452BCFDB7F80F35661C0BAB7CB2201C4B,
	U3Cget_outputsU3Ed__16_System_Collections_IEnumerable_GetEnumerator_mD88200543F030A8F6F9ADFBCA257B6DB811ED84E,
	AudioTrack_CreateClip_m8CBE84BCC1FF99D6E93FD790F9BAD277F2BC6D9E,
	AudioTrack_CompileClips_m641F177F94C9BA21B517CCDBF43C25ABC605BF28,
	AudioTrack_get_outputs_m8B9BE825525351885F322DCEBB5A8FA7D6AFEE89,
	AudioTrack_OnValidate_mB46DE622AEA6652C4248854BF0DB7607FA704960,
	AudioTrack__ctor_m6EEC48668D6F9248F48B2B0686291560444B8F35,
	U3Cget_outputsU3Ed__4__ctor_m190710D72768E977B81519D942515031DCF91C88,
	U3Cget_outputsU3Ed__4_System_IDisposable_Dispose_m5824B5C4F4CA22007A3FEEE9AB75AA4BA9AEC83C,
	U3Cget_outputsU3Ed__4_MoveNext_m636D185FBF8DAF5943220DF0FD5AE6FA2C7C7999,
	U3Cget_outputsU3Ed__4_System_Collections_Generic_IEnumeratorU3CUnityEngine_Playables_PlayableBindingU3E_get_Current_m75314CB64DC2DC82417162EFFB901A50D6E60C51,
	U3Cget_outputsU3Ed__4_System_Collections_IEnumerator_Reset_mBE20566179B7912EC30C3BFFC5A8781486E25E58,
	U3Cget_outputsU3Ed__4_System_Collections_IEnumerator_get_Current_m2F2377F982B6CA15109F25D7E87D68030B432BA5,
	U3Cget_outputsU3Ed__4_System_Collections_Generic_IEnumerableU3CUnityEngine_Playables_PlayableBindingU3E_GetEnumerator_m7C348C673AFB4692FE68B667BF09E0F8C8B3E6A1,
	U3Cget_outputsU3Ed__4_System_Collections_IEnumerable_GetEnumerator_mD0AB6E0CA93EA531B030519FAB433F4F25D27CD0,
	TimelineClipCapsExtensions_SupportsLooping_m149B53ABA1636A092CD5BB849C19277D93D4B935,
	TimelineClipCapsExtensions_SupportsExtrapolation_m3C0E25676F88C9DEEF4149DC2158A4A18711EE3A,
	TimelineClipCapsExtensions_SupportsClipIn_m318FDA5D82ED5E932A5E9C195668685F4571EEF9,
	TimelineClipCapsExtensions_SupportsSpeedMultiplier_mC1AAE95266F6201D78F17EBAB47D7923341BBD30,
	TimelineClipCapsExtensions_SupportsBlending_m569C3ED6D06142794DA7B1FD6C00062A209E7885,
	TimelineClipCapsExtensions_HasAll_m2596307B9954E6FCEFDDFD4A0BE87D936DE31BF9,
	TimelineClipCapsExtensions_HasAny_mA750DF1B0964785A2FC8DC2F9A05CF03D27F1670,
	ControlPlayableAsset_get_controllingDirectors_m0370250573BBDA45A8E8B086FFDE90ED5B1961CD,
	ControlPlayableAsset_set_controllingDirectors_m8D263E063F860FDF4E832CC9C284C45D49FA9270,
	ControlPlayableAsset_get_controllingParticles_mEDF1CC5E356EA9B115EF4F35F3509ABB93F5D44A,
	ControlPlayableAsset_set_controllingParticles_m31709F8C09DB20C7551FDC488A0127295FD0B219,
	ControlPlayableAsset_OnEnable_m63F67A81F41C02520284308515022DF19FA2FBE4,
	ControlPlayableAsset_get_duration_m0C2033B0C5C3E6DE7385797DE1B07F0263521878,
	ControlPlayableAsset_get_clipCaps_m09BC8BAA1440D221483F653285DA52715346F22A,
	ControlPlayableAsset_CreatePlayable_m88A68ADB8119BC891B37E1B9CCA7FD26090914D8,
	ControlPlayableAsset_ConnectPlayablesToMixer_m8020203CFDC98E5674106B99938017E8D9E798CC,
	ControlPlayableAsset_CreateActivationPlayable_m8BC4706CA44F6216AE3C3169D03BEE15BC819111,
	ControlPlayableAsset_SearchHierarchyAndConnectParticleSystem_m6D97733D38AAE740196B5AFA9C75A45AB54114B4,
	ControlPlayableAsset_SearchHierarchyAndConnectDirector_m9AD1670D0949F2D418DBF34152170810855A18AB,
	ControlPlayableAsset_SearchHierarchyAndConnectControlableScripts_m46A0974334C5CAB121787DC91ED6C1A9E07B33BC,
	ControlPlayableAsset_ConnectMixerAndPlayable_m8727A31B27ECFA1AF0B7478B8514F3D7B27C8459,
	NULL,
	ControlPlayableAsset_GetControlableScripts_mDAC0BC709F7024BAC5F520AA31F565824AE542A2,
	ControlPlayableAsset_UpdateDurationAndLoopFlag_m3B3976CA44009F140D54203A5A696A341A815669,
	ControlPlayableAsset_GetControllableParticleSystems_m182DE87B71BF287B5D828C7D48D099C0C3B4F85A,
	ControlPlayableAsset_GetControllableParticleSystems_mB7DA5089B5C09BC5876DEA587D20550184A69BC4,
	ControlPlayableAsset_CacheSubEmitters_m823886C0C0F1C8AA13129B3A9810CDA8AAF2EE7B,
	ControlPlayableAsset_GatherProperties_mEB211D7180EBC912CAEE8C6521F67AE4AC240FDB,
	ControlPlayableAsset_PreviewParticles_m611AFD61ACF20FDD50C82D09230C32B7D0E3D94E,
	ControlPlayableAsset_PreviewActivation_m0FAEA1926A9ACFC55CE3CF14992F5A4DE1F1368E,
	ControlPlayableAsset_PreviewTimeControl_m38FFD36A41ECA2BA874D7A7DA234E15EFAB2A043,
	ControlPlayableAsset_PreviewDirectors_mA5DF7CF7A9F2ECFAE8C59FEDD944BDD4C25AE118,
	ControlPlayableAsset__ctor_mCB87B77766491D451517D1082079113798B8518A,
	ControlPlayableAsset__cctor_m1343221BA07342B50EF52CA584CAFF1508E50BA5,
	U3CGetControlableScriptsU3Ed__39__ctor_m61DC7C12C95430EF9C16D27768169F05CC5784D0,
	U3CGetControlableScriptsU3Ed__39_System_IDisposable_Dispose_mF25100DBDEB7B203BC1DD6CCBE34A94797D68A46,
	U3CGetControlableScriptsU3Ed__39_MoveNext_m0A28A750413795A869B753439AC20C1E8AC2E33A,
	U3CGetControlableScriptsU3Ed__39_System_Collections_Generic_IEnumeratorU3CUnityEngine_MonoBehaviourU3E_get_Current_m288919711BCB540205566CDF9B9339EC8EBB2DB1,
	U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerator_Reset_mBCFCCD39392B14C63C54D9079BB6E3302DC858B0,
	U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerator_get_Current_m0C00993614DC7FD44F070DD625C5325A84301309,
	U3CGetControlableScriptsU3Ed__39_System_Collections_Generic_IEnumerableU3CUnityEngine_MonoBehaviourU3E_GetEnumerator_mAC5B005DF8CED15F0652A85E921526C4E9E245C9,
	U3CGetControlableScriptsU3Ed__39_System_Collections_IEnumerable_GetEnumerator_m51F55F60727D8A0C7F5CD7F862853E8B041F1910,
	ControlTrack__ctor_m0B4C0633844F60987B07ABC4467976B46AB0975D,
	DiscreteTime_get_tickValue_m0ACC9CCCEB39D170368ACD3FD094781F6D01EAFD,
	DiscreteTime__ctor_mA6613BBA0DEB0A0BCCED50838378D9D9971B9F62,
	DiscreteTime__ctor_mF5CD806CC7273F8C5CC80053A78D09604DED0491,
	DiscreteTime__ctor_mF858F11921F841F599EF51C7975430BEA8EC3B52,
	DiscreteTime__ctor_m7D7C3098B16F74497C5FC907B364C50A667EB2C8,
	DiscreteTime__ctor_mCD4A3BE5EB3142C4247190F7896378B68F428F69,
	DiscreteTime__ctor_m5C84429A52611671EE6F2D7BCD1399335D8242F5,
	DiscreteTime_OneTickBefore_m06F6D6A227D8356FCD3283CD59571DFA12943D1A,
	DiscreteTime_OneTickAfter_mD2C58DF00885E44EA0D7AF8C9F56C14F77E53EB7,
	DiscreteTime_GetTick_m52C7EE5F63087831CF4B21EF76A00655181349AA,
	DiscreteTime_FromTicks_m39AD4CB20BF1DA744A07F93B9FCA9F0F75DE9BDD,
	DiscreteTime_CompareTo_m9A2C892A6E4097A5FBC0C2D5D666E01CE12427EA,
	DiscreteTime_Equals_m3D269E4164E6E705E6DD19B557F9DBFC2CB226FA,
	DiscreteTime_Equals_m42B27C6DDF993E36A0C3D20B708986277AE6B7FD,
	DiscreteTime_DoubleToDiscreteTime_m8D83412A88CCAC93ABD5070209109957FCA85762,
	DiscreteTime_FloatToDiscreteTime_m0F57E8699783D9B2A48381F34D2116E71F3EA559,
	DiscreteTime_IntToDiscreteTime_m17C3BF4B229B41B91CCACB7F465628A9ACA4736D,
	DiscreteTime_ToDouble_m319F0D5276BC26C56CDC124BC7878DB535351348,
	DiscreteTime_ToFloat_mA77F564AE95883062A631DAF86C4F401567CEB0E,
	DiscreteTime_op_Explicit_m801A8089D31AA0C435943A08C89A83607FACD52B,
	DiscreteTime_op_Explicit_m08D29220F020CB6A40CC9D59B5B4104BBA7599D0,
	DiscreteTime_op_Explicit_m7C11B47C356E41AB9F96ED46AE8BBFA05C1B3941,
	DiscreteTime_op_Explicit_mE186D14B06F947B0B372625D4E927566B0161874,
	DiscreteTime_op_Explicit_mCA9C09F74934D46B5635C42C9984C8C84589CD30,
	DiscreteTime_op_Implicit_m8EEF2A52EFFBFC375CB1D8171DC439A22DC35ECB,
	DiscreteTime_op_Explicit_m42F903A62DF258BD11463C461991AA5DE89AA71D,
	DiscreteTime_op_Equality_m55FD471B465FFDAC78BE2413701203194CFEADC4,
	DiscreteTime_op_Inequality_m19BD9CC31D1A2FBDA0CC50BA96EA37463F404AF6,
	DiscreteTime_op_GreaterThan_m3F023E3705B46C7E548730E10C543416EACEBC45,
	DiscreteTime_op_LessThan_m3CD38C215D6813414BFB1AC88BAF5BF1796C49D1,
	DiscreteTime_op_LessThanOrEqual_m8E8286335E92963611D6C0F6598B20E61CBE2F14,
	DiscreteTime_op_GreaterThanOrEqual_m04A7C9DC1DACEBE2FF6201F7B81C5CE59518A57D,
	DiscreteTime_op_Addition_mCE0392D2D1FB0BD53AD56C9A287810D1672B2BC8,
	DiscreteTime_op_Subtraction_mA27858E3E6CA4BBD578B1CE193504549AA916FC5,
	DiscreteTime_ToString_mFE5EEC6255B3AA617F692DA9EB7DF4A5AD71FD83,
	DiscreteTime_GetHashCode_m59E889384FD1F78DC2A1220C228EAB164F8995CE,
	DiscreteTime_Min_m6FF92F93C9C652F5794A44BCADF4C1F6D15A3140,
	DiscreteTime_Max_mE6741CA89EADA7D3EF47C29C8C9D0D57CC8597B6,
	DiscreteTime_SnapToNearestTick_mBA4C390A09ED03D90BE0D8C0CD39949AAC76AF03,
	DiscreteTime_SnapToNearestTick_m563A71A5A432C7676FC6692CF68DD81FF2020AB1,
	DiscreteTime_GetNearestTick_mC4A04B29410685B1864597BCB83664F6B2F95367,
	DiscreteTime__cctor_m23DF1F1B6C69F8785475607E6188362FE65E123F,
	InfiniteRuntimeClip__ctor_m807AE0572975FDC87883AC4877442F7E986B7812,
	InfiniteRuntimeClip_get_intervalStart_m9F26CE71EA9F461A924BF1AF6892154B8077FF8D,
	InfiniteRuntimeClip_get_intervalEnd_mB05A86E64F77260F1CC9EF60285E9985106A7D41,
	InfiniteRuntimeClip_set_enable_m950C18673BCCA53BA644B8998BE3A00FC37CBE2D,
	InfiniteRuntimeClip_EvaluateAt_m3A6147F9BB6B625BA721C1C2FCE0DFCBBDB1DF8E,
	InfiniteRuntimeClip_DisableAt_mBE37CED8ED2A4643EDC99BADD76AF0E344A4929E,
	InfiniteRuntimeClip__cctor_mE142C87FB9D4550C966F87FB581863C614E7CD8A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	RuntimeClip_get_start_m6204EC5ADD90B46E23FFE1436928152BF15326EC,
	RuntimeClip_get_duration_mBA27B08B52BD7B402C449A7733AA7755FA987F6C,
	RuntimeClip__ctor_m98046934573D3967A198B431434524D25C76F43C,
	RuntimeClip_Create_m9464E5BA8F8A29AEA85775728362F8B72A742D10,
	RuntimeClip_get_clip_m6208BBDD36E3E3EA97000F4CEC9BB1B879A1824D,
	RuntimeClip_get_mixer_mA42F77ACA8B17C58C502EC06C7930F2033AA0589,
	RuntimeClip_get_playable_mD336A90E2A444F4B50F3B02FABC5C6ECDE3E21BE,
	RuntimeClip_set_enable_m61A322D87BF4A75D804C2C82FD59CACA871B8FA1,
	RuntimeClip_SetTime_m7ECE29A44A0DA3625276C56999A7D293CF642ED1,
	RuntimeClip_SetDuration_m4AB935A8C5F597184D3F20A714704C11458FAE40,
	RuntimeClip_EvaluateAt_m6D08332D99A261EE232EFFE9F94ADEC3B5E66BDE,
	RuntimeClip_DisableAt_mE25279C9BD90378393EC0E8CB029EDFECE7CC243,
	NULL,
	NULL,
	RuntimeClipBase_get_intervalStart_m599D96633271890B892A9D48325531F28DDE1BAD,
	RuntimeClipBase_get_intervalEnd_mA0819F921B8AA4BFEBF31419858BA2C975BE174B,
	RuntimeClipBase__ctor_m53B7986F56314C13A22B079840C7DA6D36501958,
	NULL,
	NULL,
	RuntimeElement_get_intervalBit_mD5C7E7CDA66C4B1888D529E9344679FF88DE5A70,
	RuntimeElement_set_intervalBit_mD6D67EA4A982521E16CDF3CAC295EED22CA65C75,
	NULL,
	NULL,
	NULL,
	RuntimeElement__ctor_mB4868C6FD6BE7F80F182AE2961580F1E31A60F68,
	ScheduleRuntimeClip_get_start_mC7AEF6AF42593F08CD3403B98132D3AAEDD898D8,
	ScheduleRuntimeClip_get_duration_mE5950845592C0E89A8F622204CD37D7ABCFD62D0,
	ScheduleRuntimeClip_SetTime_m38A382F75037FC1430B6067DAC94848D7043D1DF,
	ScheduleRuntimeClip_get_clip_mC24FB5C0451219E6A3CF888B81B0099A34443A3B,
	ScheduleRuntimeClip_get_mixer_m5FFC2C73903CC0B038A4F76EB1A4F99B912FC54C,
	ScheduleRuntimeClip_get_playable_mF7988FDD9350B256DD307DE9F9957AA1A44D9EED,
	ScheduleRuntimeClip__ctor_m83C75C594A568AE694E42341685277CFB517568F,
	ScheduleRuntimeClip_Create_mA8ADCDBD0B9C32427F6EB006FFB9057989981C18,
	ScheduleRuntimeClip_set_enable_mA74C59B6CDC243221219534B77FA5B140A4E5989,
	ScheduleRuntimeClip_EvaluateAt_m3C3C92A67AB06B22C1B3E22B884F78A3BA42F6D8,
	ScheduleRuntimeClip_DisableAt_m57233707606DE133E713FB3B89BB867BE65A8CA7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Marker_get_parent_mE72E59DAF1DBC598885CD3CA9D0A55CB68CE8510,
	Marker_set_parent_mCAB1A605E39909E42D623D48037DC43141173A34,
	Marker_get_time_mB0E90DB26C36F73A6AFB126761F1FF144A145BF6,
	Marker_set_time_mF28306664964EF628F2D6D9F9F9DB2EB7DF89DED,
	Marker_UnityEngine_Timeline_IMarker_Initialize_mA5ED18EB0857836506FD03FB6C7E04C06AC6F320,
	Marker_OnInitialize_m95F020C003B8C4C5072BF891C9539CF0D6A2F550,
	Marker__ctor_m74124BFBCDCAFE96C2A9CE59CA215B80590F6A56,
	MarkerList_get_markers_m5376FA1EA20DEC72CE47369DD8CD407869B0A2F7,
	MarkerList__ctor_m2D55037070301E459BF9CAB83756B81183C6A6B8,
	MarkerList_Add_m401FA50EA0C98293DD36147CFA9DC8F637636128,
	MarkerList_Remove_m986C1ECBCA1A1DE79FCD97D7D44EC91C19D79F64,
	MarkerList_Remove_mF1626830F54C37B15C3DBEB6044B92CBF6439790,
	MarkerList_Clear_m260B8EDEF14AC94F4BE5F10018F2215BF11927E7,
	MarkerList_Contains_m97FF1ADAEFBD92A7F3E66B0A7C85950224B62989,
	MarkerList_GetMarkers_mC440918CEEC3E0A8E872510EDEAEA0FB8832E680,
	MarkerList_get_Count_mC37C47376A0DCC203DBD965E961A923F06E0082B,
	MarkerList_get_Item_m76D3839ACDBAB9F00DFD9CE68C63DE26050961FE,
	MarkerList_GetRawMarkerList_m272F6FFA3A887E15FDEFA8AAC07F1A6A0BD54A0D,
	MarkerList_CreateMarker_m404F50BDABD541CF30E2F03DC6C22F917E02130B,
	MarkerList_HasNotifications_mF2F94CCD1C286DB7264738F287486890D2497124,
	MarkerList_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m6E97ABA4A5ED5881C2FC7F356FCAF68FCC45B710,
	MarkerList_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m1175CE30FB38D95504747F73994F2C86DC0E8928,
	MarkerList_BuildCache_m8EFF6B73A205DC746231385CAC726A74A2B485F5,
	MarkerTrack_get_outputs_mCC74870CF1C68FA4B6DC21EDADEDE9936A6F21AC,
	MarkerTrack__ctor_mAF73693D259F16A0F2DBF71313BA854F5C71A1E8,
	CustomSignalEventDrawer__ctor_m6E78EBC2A826450A4F05E0127A6672F07D84CC0A,
	SignalAsset_add_OnEnableCallback_m554767948986531C6B7B0C14286F651B6B7C7203,
	SignalAsset_remove_OnEnableCallback_m99D77B52734D3A480E21F6E09918CB302409CBFB,
	SignalAsset_OnEnable_mF4113987FFFF4ECE91B0DFD3506EFF02E76DE29C,
	SignalAsset__ctor_m4F0FF5113CFB816B5FF981FA2A308AEC282D6E9B,
	SignalEmitter_get_retroactive_m05405283A19896F77F109BE0E4DF0380CAE909EA,
	SignalEmitter_set_retroactive_mC90E249F02530DA76969DA20208123CCE8A11D4F,
	SignalEmitter_get_emitOnce_m8A10F3E7A7F2BF66F52B5A418632E1CCA1B613A5,
	SignalEmitter_set_emitOnce_mB64C9B47B124F8FEAE68C41BA3CED422A96E6628,
	SignalEmitter_get_asset_m80647A00BC1BE61E0FBD0F2CE626B0997E04C140,
	SignalEmitter_set_asset_mE5074F7D8760F624046A0E1B7C2D3E2662A7283C,
	SignalEmitter_UnityEngine_Playables_INotification_get_id_mFE1FFFDDE0AA375A400B8ED622D47E3C4A3462F1,
	SignalEmitter_UnityEngine_Timeline_INotificationOptionProvider_get_flags_m440E69F2CB8F13BDB1D4D082E88A8708E1FEC98C,
	SignalEmitter__ctor_m6A45F5A0D313B5184EA3D27F89DAD8126178E327,
	SignalReceiver_OnNotify_m2A3A5A1DB5A3FACAF334771A74677D1FB08FFBF4,
	SignalReceiver_AddReaction_m24F66AE62396EEC75BB4F5074246F39FDF6884B6,
	SignalReceiver_AddEmptyReaction_m8A3DAC1ED52B65292D89FD40DEAE359D29491EFC,
	SignalReceiver_Remove_mC5F9BBA8D959629B62B7A7F5B2C2E1A684E857CF,
	SignalReceiver_GetRegisteredSignals_m0834695F8B630B69EC4A606956C24ABD9FBFA953,
	SignalReceiver_GetReaction_m8B5FBD0171FB06EAE16E9A99F2E209829552EFF8,
	SignalReceiver_Count_mB3916E584C177A2D47AF76296FB0C59987A1F694,
	SignalReceiver_ChangeSignalAtIndex_m66463FEE943CC55C585628B993F64C78257CA1FD,
	SignalReceiver_RemoveAtIndex_m01EE2CBC7A406CA4A1D13F4F72D591347D02DBC5,
	SignalReceiver_ChangeReactionAtIndex_m803D743382D58A2637C8BA1FF00116310AFEC472,
	SignalReceiver_GetReactionAtIndex_m3F36C2C9E39F3F39679B4AE052305DDFFC8887A1,
	SignalReceiver_GetSignalAssetAtIndex_mAB31FED39823D7220E727A204F128AD9C0B12A82,
	SignalReceiver_OnEnable_mB77BD34EC8FE8B41A627585E4739083B7F85F3C1,
	SignalReceiver__ctor_m46381605B40F21C06F4125C7D8F45232F4D74CE6,
	EventKeyValue_TryGetValue_m7190DE6E665F07018730BAF3288F22CDFEEB9B6D,
	EventKeyValue_Append_mEE3BEB022A04AA95F1B2B56D3F8AA5F5ECCA5BD3,
	EventKeyValue_Remove_mEC52E595AEC1BFAB97DB2567AC42CE0D7C968863,
	EventKeyValue_Remove_m3B8982FC179C5DD1853FC4846106B1CD23F57C7D,
	EventKeyValue_get_signals_m3F5CB2EDFE24DEAAE67DF04898223ACBDB3CB3BD,
	EventKeyValue_get_events_mB7945E299C4249CB0BD5D2EBAB8D0DEE455FFAE3,
	EventKeyValue__ctor_m7AE5F9F6453AA2F9DB8A1612D334D1F4CB3CC549,
	SignalTrack__ctor_m92CAC598FA9027691117E52EB8247D2D79D0FBA1,
	TrackAssetExtensions_GetGroup_m9261D7FF986D8DBAB89F142B5B2F4357F1C995B9,
	TrackAssetExtensions_SetGroup_m0525D6627035D09A0943ADC00AAA03CC68495435,
	GroupTrack_CanCompileClips_mF95369ECAEA83FA55A283FB1425F00FBCE9D2F23,
	GroupTrack_get_outputs_m1CF0F26206E1943E4C4202F7849408E9E8CA2403,
	GroupTrack__ctor_m2612CD9E072D91E3F5C21F8D4F156F96A33CDBBC,
	NULL,
	ActivationControlPlayable_Create_m61DD26626E7A44339EA08D2E15498E1897FAC23E,
	ActivationControlPlayable_OnBehaviourPlay_m32D87ABF400DD235012889AEB82EBD1757AE745C,
	ActivationControlPlayable_OnBehaviourPause_mB2E17A057A698FA2366269460A306B281808748F,
	ActivationControlPlayable_ProcessFrame_mC388A38FD777597B3E79B41A5DA4286DCA0E73BE,
	ActivationControlPlayable_OnGraphStart_mF0475EF758222C98681FAB8A1E175C4A0095AE31,
	ActivationControlPlayable_OnPlayableDestroy_mF39699D518A1C2075AB6BCD1ECB1996704A28838,
	ActivationControlPlayable__ctor_m50572F236B93063098D17B06E2662F10A75F5E0C,
	BasicPlayableBehaviour_get_duration_m000CA8660FB9136C46ABD05B9ACB9B7BE28C1F95,
	BasicPlayableBehaviour_get_outputs_mF90ACBACF585238FF0623BD70C14483DF91D21AE,
	BasicPlayableBehaviour_OnGraphStart_m921FCF14B857F191628EBB43D79A8CB674EAB8AA,
	BasicPlayableBehaviour_OnGraphStop_m261E6FFF9FCCA3380C3B1844ADE470605B616FFC,
	BasicPlayableBehaviour_OnPlayableCreate_m956EA471E10F9B20B77365A935A1C473E5DAD25D,
	BasicPlayableBehaviour_OnPlayableDestroy_mE70F15932202ABBF11E850199EB2DADFC6B5955E,
	BasicPlayableBehaviour_OnBehaviourPlay_m1F24D0A73D669B74C455DB02363EF2BB473FBF65,
	BasicPlayableBehaviour_OnBehaviourPause_m2372D83D9E18CF06F9121017970D8295412E216D,
	BasicPlayableBehaviour_PrepareFrame_mE55DA9150027CA503D1495E3E50A9568EAA1F5E7,
	BasicPlayableBehaviour_ProcessFrame_m413A95F400514A22EF4630952D0498DBC5347ED2,
	BasicPlayableBehaviour_CreatePlayable_m0F957E711BFF407616E35025646C83FA4DD90C25,
	BasicPlayableBehaviour__ctor_mF8CCC76427C0B3A8E0F88C4B1853A80E5EA33C1F,
	DirectorControlPlayable_Create_mA64246490438157CCBE867DF9CFD0F0CD7133DE6,
	DirectorControlPlayable_OnPlayableDestroy_m9426CD97470CA7619A971B2A0E8942BC6BE7AD19,
	DirectorControlPlayable_PrepareFrame_m0F8F6FBBB5483EBB76332AE013321FC6201893E9,
	DirectorControlPlayable_OnBehaviourPlay_m452302B8000575C35BF262260D72F789CA03DD60,
	DirectorControlPlayable_OnBehaviourPause_m2DDA62A73F0C95CA0B220637599FD2DBD224E632,
	DirectorControlPlayable_ProcessFrame_mAC2E676C1932306AE076F222A074E37BFAA8B6E7,
	DirectorControlPlayable_SyncSpeed_m39C659CC0373FBC0829B8343F49F44E852729734,
	DirectorControlPlayable_SyncStart_m07109F4B30C707D4B2450C00A0675192A69260E2,
	DirectorControlPlayable_SyncStop_mB49F56038E68681AB73BA59A96E3A2F3348868DB,
	DirectorControlPlayable_DetectDiscontinuity_m5884117E980A5F64A18127E0CC14BD64B12B286D,
	DirectorControlPlayable_DetectOutOfSync_mAEB0E3DA34B2A5C7F7BBE40144198F9762D07370,
	DirectorControlPlayable_UpdateTime_mF24AE595B9899ABED3928F15FAD03CBBB5CBE809,
	DirectorControlPlayable__ctor_m89F5CEC0EBACD98DFF7D480BEFEA15DD637F870C,
	NULL,
	NULL,
	NULL,
	ParticleControlPlayable_Create_m8F03536B0CC6B66B506836058AA37A08EDCD5338,
	ParticleControlPlayable_get_particleSystem_m63954D25FBB179CA655D5739DCB07C7DBA99C7A5,
	ParticleControlPlayable_set_particleSystem_m6B3EED45B18C3B80D641D969D2601ABBE34BA4C1,
	ParticleControlPlayable_Initialize_mCA4BB21915939852339DCECDE34EDB1B4ED36E92,
	ParticleControlPlayable_SetRandomSeed_m0D41AD36E12BF013CE346F2A562A717721FB87F9,
	ParticleControlPlayable_PrepareFrame_m959C915531B064E7A7AC30B01E8299B21A139EF6,
	ParticleControlPlayable_OnBehaviourPlay_mB1B514A71494493FB7A2B8B1F6B5F1455907B761,
	ParticleControlPlayable_OnBehaviourPause_m295DA59C6C8E31854EA9009A709FFFAF663428B9,
	ParticleControlPlayable_Simulate_mCDD453B5F1A231F1FD9744B19EDE4386E4B3D7C6,
	ParticleControlPlayable__ctor_m0E1086350E57B8CA51874D8AE9FDFF1B0AB89C66,
	PrefabControlPlayable_Create_mE744622E91538A0C13C1D453F207A32956F35297,
	PrefabControlPlayable_get_prefabInstance_mE06FADC9D1367D2606277D5D1F8577C6CA837B4A,
	PrefabControlPlayable_Initialize_mC8F44778C03DAEFFE8F22B14216C6E838E224483,
	PrefabControlPlayable_OnPlayableDestroy_mC7498F993D6766BC8F1963BC770DACBE7AC09DCA,
	PrefabControlPlayable_OnBehaviourPlay_m25045FBF8EF4CE540108857ED00A5E8CF44EC66D,
	PrefabControlPlayable_OnBehaviourPause_m9015806A18FE1AF828ED2757E7F83BD499C3C09F,
	PrefabControlPlayable_SetHideFlagsRecursive_mCA466033B6F14AC5C663F848791C0CC6C6B92716,
	PrefabControlPlayable__ctor_m0850EAD5A2F49DAF2B6D56326ED235D8042C9C90,
	TimeControlPlayable_Create_m45018C6B659C011B90908D65862ADF68E9F9F251,
	TimeControlPlayable_Initialize_m7CE2820058BE601A316CCE5964E4CE3445E9980A,
	TimeControlPlayable_PrepareFrame_mF122A36FEFFA9BE1BC74FB551225652874D82E83,
	TimeControlPlayable_OnBehaviourPlay_mBFF77E285E53483F14E06EE0B56CFECD7CE13C31,
	TimeControlPlayable_OnBehaviourPause_m9648D5058F2EC8BFC260AC603B5C03883AD284E1,
	TimeControlPlayable__ctor_m2324993C8F5009CC0336BB9D9396F453529CD5C7,
	TimeNotificationBehaviour_set_timeSource_mD0096011A303EB4C84B3DE3AAE908C51955E2F8E,
	TimeNotificationBehaviour_Create_mF24D48476C110D3158F32421C168A5171F4613A0,
	TimeNotificationBehaviour_AddNotification_mB502CDA1135E3A3F543B7B24224BB95F986EAD97,
	TimeNotificationBehaviour_OnGraphStart_m8C925120572F3A69DA5E9B3DB127F1C506CE436E,
	TimeNotificationBehaviour_OnBehaviourPause_m6FCF029B2259978A231F78DEC1E4F57E2D88E7C1,
	TimeNotificationBehaviour_PrepareFrame_m8C38D54B9B061C4B57098EA0458E768974E193DE,
	TimeNotificationBehaviour_SortNotifications_m91493C483D46116C8DEE5C25AEE2F9872B3DB86C,
	TimeNotificationBehaviour_CanRestoreNotification_m7390D6FA66D1786D1AF1412D37D74FD9EE661E2E,
	TimeNotificationBehaviour_TriggerNotificationsInRange_m9607548147744987224ADD3FE6E3F339D2337284,
	TimeNotificationBehaviour_SyncDurationWithExternalSource_mEE92F71E0867E713E58EDF71B9DEE1A1E2F25925,
	TimeNotificationBehaviour_Trigger_internal_m87169DADFCE06BC37BA28CFED3B0A6174DFFEAB7,
	TimeNotificationBehaviour_Restore_internal_m25E1C8121200E214969CDD9A753D6357D440726A,
	TimeNotificationBehaviour__ctor_m82EC1218499AA0C697D1005358DA66DB1908FFAC,
	NotificationEntry_get_triggerInEditor_m0F467B8FC45EDED62344F2EB66D91998C330FC7A,
	NotificationEntry_get_prewarm_mDF7D568CD5A14E53E9E11DAF6AD33F0FE7ED29DE,
	NotificationEntry_get_triggerOnce_m40147F02406594EFEA328E29EDC9C3110E9A7654,
	U3CU3Ec__cctor_m6669D2494CB2B140029B26B2BE183C4A8DEDB476,
	U3CU3Ec__ctor_m8358614F1AC3F7F79522CD7795964B99E574C428,
	U3CU3Ec_U3CSortNotificationsU3Eb__12_0_mCFEAA6C512A546FDC4CC69A23EF75844DFDB9189,
	PlayableTrack_OnCreateClip_mB016C731FC0C8567F707C53BAD5B9E9B99DE2BE7,
	PlayableTrack__ctor_m1ADC6FA394F1E5B1A484480F7306E35A401A2A22,
	TrackMediaType__ctor_m1357FFFF3A466DC18E97A85F4DF7577A145FFED1,
	TrackClipTypeAttribute__ctor_m32F3A6AF3F0CCD2AC80A08276A2C84DAD6B59ADA,
	TrackClipTypeAttribute__ctor_m6A7E59068246D7C95D43A99B54403748FE2E5F5F,
	NotKeyableAttribute__ctor_m11E8B4B00459AEB31D3345A5A8045E5D21CC35CB,
	TrackBindingTypeAttribute__ctor_m905B5F0071EF43101C6DE52F5383F8B15FF66176,
	TrackBindingTypeAttribute__ctor_m38D2D803DD43575FBB29F858067DF10911CF7D95,
	SupportsChildTracksAttribute__ctor_m3FE00570A7899A3F7CF0FB8522DE0C4C21694E51,
	IgnoreOnPlayableTrackAttribute__ctor_mAC076C2CED5738D08949C3F1BEB855D761191297,
	TimeFieldAttribute_get_useEditMode_m029ADEC4C39E6BC43084274528B66A956BDD1E15,
	TimeFieldAttribute__ctor_m91C16D6CAE58639D71BE206FFA6011A56BC9A53B,
	FrameRateFieldAttribute__ctor_m1312B76429B8D8A394C3C28BC001E22877F3ABB2,
	HideInMenuAttribute__ctor_m3F3AFC914006D5CFC30B844C14E2E5BE671C1C20,
	CustomStyleAttribute__ctor_m171564751F00A5A77E810C78FAB06AF76EFEAE23,
	MenuCategoryAttribute__ctor_m0AE9C26FDCD04C039FA1F3995969A6822B9957C6,
	NULL,
	NULL,
	TimelinePlayable_Create_mDBD16A025F217757ED3E323A2B2292B836B7849D,
	TimelinePlayable_Compile_mCCE0D60E8AD0D45A14341C0FC4FA055633BE7D25,
	TimelinePlayable_CompileTrackList_m94B0B3D833D78C5A070DD587705272E8D25EBB63,
	TimelinePlayable_CreateTrackOutput_mAD0C4882569BA478C8D59AA5130F8901BADE5DCF,
	TimelinePlayable_EvaluateWeightsForAnimationPlayableOutput_m7684913A2AF5D8265AE45AAAF46A9B006C028040,
	TimelinePlayable_EvaluateAnimationPreviewUpdateCallback_m55098F133C54B87A45ABAF36903EAE57CDAA16F4,
	TimelinePlayable_CreateTrackPlayable_m18539F554343F8FF12D84137E5A0CA8FFA7E8B34,
	TimelinePlayable_PrepareFrame_m9124521CCE2CBF49C19EA1669DE2BAE6632C5153,
	TimelinePlayable_Evaluate_m1CB17400BD2E1FE1757F8A106F67451D039C325F,
	TimelinePlayable_CacheTrack_m8C7FF43B821AC07FED21AD8C1537815FA0113B1C,
	TimelinePlayable_ForAOTCompilationOnly_m6130D5C91CE6D75036FBE258FC54A70A89345C20,
	TimelinePlayable__ctor_m9A86459D9944225AE074C58C85B7F798E089FD5C,
	TimelinePlayable__cctor_m0800C83345E9CC0DED4AB906A15ADE0859A1123D,
	Extrapolation_CalculateExtrapolationTimes_m607FBFE8FBB128DA7AC236DA75A9097CA959BEE7,
	Extrapolation_SortClipsByStartTime_mCDB181CE76C4633A881347AB015409999918BF0F,
	Extrapolation__cctor_mEED3F9AE8423558EE03C4A949B2954DD93925066,
	U3CU3Ec__cctor_m68412E65388EDDBD5C698C3B363320588FC91F3F,
	U3CU3Ec__ctor_m4BEA68C8BAF3AD6FA1854F77A8584537339195A3,
	U3CU3Ec_U3CSortClipsByStartTimeU3Eb__2_0_mAC857C3158459F813EB90B7EC80CC57E002F4562,
	HashUtility_CombineHash_mDB3BFB2F0222F9DFEA5A43CC152AF988732681DE,
	HashUtility_CombineHash_mCD4724834FE836194D0AABEAF856DA6C625A73F6,
	HashUtility_CombineHash_m288F5449B09EB88B46E520229DFF7C7F18CA51C0,
	HashUtility_CombineHash_mBB79C3AEE25C2E5895BA6F85AECCFF5B215005B6,
	HashUtility_CombineHash_mCA939EC20F8AE7BBABD2D433D3A1FB770AC30BF7,
	HashUtility_CombineHash_m1367FE812F3C9670BD59ECF0007F2383C469A29A,
	HashUtility_CombineHash_m8C98B06FEB8B88A93104DE6E8D02B30470718E5A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NotificationUtilities_CreateNotificationsPlayable_m2E015D453C40663721AEA2DA883EB070DEFAF586,
	NotificationUtilities_TrackTypeSupportsNotifications_mA844D8C5CD9E01E806EFA9A16F37BD7575DB6012,
	TimelineClipExtensions_MoveToTrack_m7B9992AD171A490F566871C4856C71E6DC1D28D9,
	TimelineClipExtensions_TryMoveToTrack_m88EB39C25E4B43EF0A5F6DE62A576B6D3F6AE958,
	TimelineClipExtensions_MoveToTrack_Impl_mA57D81A0076D26140DF918D05C680BF0591F50BB,
	TimelineClipExtensions__cctor_mB59452B0FD743296682255CC66A9B41704B2EA3C,
	TimelineCreateUtilities_GenerateUniqueActorName_mA256C1FED6CBEA0A7A41C3CD66E3617D70E7C554,
	TimelineCreateUtilities_SaveAssetIntoObject_m08F1B9275C4A4893711EA4281BBB3BBBEA9EEE0A,
	TimelineCreateUtilities_RemoveAssetFromObject_mDDE3FBE78C7CBC5F771632F41076E68108475013,
	TimelineCreateUtilities_CreateAnimationClipForTrack_m499C0BAB55D23B8B1759B4EFFCA6EC100B056770,
	TimelineCreateUtilities_ValidateParentTrack_mDE431017F5F40FDE73B7CDFACADDD1F484316CE6,
	U3CU3Ec__DisplayClass0_0__ctor_m8C4942004C3562D0E7CEF1A8EBA62FE27E649328,
	U3CU3Ec__DisplayClass0_0_U3CGenerateUniqueActorNameU3Eb__0_m06DEF88A9AE88C0A9FEA42106BBB9D0E79B92D50,
	U3CU3Ec__DisplayClass0_1__ctor_mE96B2A914FE0820EA130DFB44B1E713AD3FBFB90,
	U3CU3Ec__DisplayClass0_1_U3CGenerateUniqueActorNameU3Eb__1_m63691DF652C67A4727D3AD671C58FA56CA3FAEDB,
	TimelineUndo_PushDestroyUndo_m502EE75014700236190DF20E9B5743E100E4E840,
	TimelineUndo_PushUndo_mDAB595E45185F92C279C990AE284325133967B04,
	TimelineUndo_PushUndo_m1FD04DF7E2F50E65198221CDDB7B770C566037AE,
	TimelineUndo_RegisterCreatedObjectUndo_mDB8ECC4BFD7A3491605CA1F89CE349ED35750365,
	TimelineUndo_UndoName_m0E19A1C9B54C2A0735D35FF4F8DFC92EDA12E192,
	TimeUtility_ValidateFrameRate_mFDD6870A06AB0E87193D1152E370161AF173992D,
	TimeUtility_ToFrames_m42B1DBF623EBD722B8B393C34A3F5FF33D5F6188,
	TimeUtility_ToExactFrames_m442908215ADBB76C41A4CC2C031320379E50095C,
	TimeUtility_FromFrames_m0D428831B080A1E23EEF38CD7F15461D7FD651CC,
	TimeUtility_FromFrames_m90B7D5F87C01001AE4B9F712E8B7F517CAAF91F8,
	TimeUtility_OnFrameBoundary_mE89A73D9D109EFCAAB028DEA3A545276EC8A3A68,
	TimeUtility_GetEpsilon_m2DDCC59890F0A007D609E29CD1BC62B04EF6FB7B,
	TimeUtility_OnFrameBoundary_mAC72C8A3F1E3E3F05F03BE07CDCAD5DE7808421B,
	TimeUtility_RoundToFrame_m9A9CF7FB93ABCBE05A4987D766A6640B5FCE02BD,
	TimeUtility_TimeAsFrames_mD7686EA3126E3A791789F999B9B9E25B6F2178AD,
	TimeUtility_TimeAsTimeCode_m448D4FC6FD14FD5816A4DA71F82218C4121ADFA1,
	TimeUtility_ParseTimeCode_mC51B6AEBBABD417C83090B0A8C1DFE5BE133A438,
	TimeUtility_ParseTimeSeconds_m28AB4715C13959B4AB0829540082001D158335DC,
	TimeUtility_GetAnimationClipLength_m0C35066397FC1474ED4B04934DBAB064CC1891DB,
	TimeUtility_RemoveChar_mB4D81436DF7CAC6C2768E463808F0CDB4D4D7D91,
	TimeUtility_GetClosestFrameRate_m777D1AA22EBF63B3F327B4744C8F304F5E3F59C9,
	TimeUtility_ToFrameRate_mB8BD26B2A1C5BF1DBAC8B55BB3CCB4D86294E1A4,
	TimeUtility_ToStandardFrameRate_mE55BCB25BFF3ED034F50D98E31D2595C0C8D8C90,
	TimeUtility__cctor_m49E229AEEAC66C8DE40593C47BBC3F9552CBDA1B,
	U3CU3Ec__cctor_m59AEBAC840298E7D2900A2B3BF20699F650D2477,
	U3CU3Ec__ctor_m56DDC30169ADA8554EE8A0BC9D347AD024BD36A7,
	U3CU3Ec_U3CParseTimeCodeU3Eb__15_0_m6908D437E7CEF43504892D5EBF8E7E9E9447B6C5,
	U3CU3Ec_U3CParseTimeCodeU3Eb__15_1_mCE7371E16461F9A768516B757AE99C53FF8B70FE,
	U3CU3Ec_U3CParseTimeSecondsU3Eb__16_0_m85E57DE53E8DEBF2BBED2C0E1F354D95666F83E4,
	WeightUtility_NormalizeMixer_m6EE0D0701870481B80B9B8AA1F259C1A786379CA,
};
extern void TransientBuildData_Clear_mA1B052EF3A05C723008A58559E4C77A989ED2FD3_AdjustorThunk (void);
extern void DiscreteTime__ctor_mA6613BBA0DEB0A0BCCED50838378D9D9971B9F62_AdjustorThunk (void);
extern void DiscreteTime__ctor_mF5CD806CC7273F8C5CC80053A78D09604DED0491_AdjustorThunk (void);
extern void DiscreteTime__ctor_mF858F11921F841F599EF51C7975430BEA8EC3B52_AdjustorThunk (void);
extern void DiscreteTime__ctor_m7D7C3098B16F74497C5FC907B364C50A667EB2C8_AdjustorThunk (void);
extern void DiscreteTime__ctor_mCD4A3BE5EB3142C4247190F7896378B68F428F69_AdjustorThunk (void);
extern void DiscreteTime__ctor_m5C84429A52611671EE6F2D7BCD1399335D8242F5_AdjustorThunk (void);
extern void DiscreteTime_OneTickBefore_m06F6D6A227D8356FCD3283CD59571DFA12943D1A_AdjustorThunk (void);
extern void DiscreteTime_OneTickAfter_mD2C58DF00885E44EA0D7AF8C9F56C14F77E53EB7_AdjustorThunk (void);
extern void DiscreteTime_GetTick_m52C7EE5F63087831CF4B21EF76A00655181349AA_AdjustorThunk (void);
extern void DiscreteTime_CompareTo_m9A2C892A6E4097A5FBC0C2D5D666E01CE12427EA_AdjustorThunk (void);
extern void DiscreteTime_Equals_m3D269E4164E6E705E6DD19B557F9DBFC2CB226FA_AdjustorThunk (void);
extern void DiscreteTime_Equals_m42B27C6DDF993E36A0C3D20B708986277AE6B7FD_AdjustorThunk (void);
extern void DiscreteTime_ToString_mFE5EEC6255B3AA617F692DA9EB7DF4A5AD71FD83_AdjustorThunk (void);
extern void DiscreteTime_GetHashCode_m59E889384FD1F78DC2A1220C228EAB164F8995CE_AdjustorThunk (void);
extern void MarkerList_get_markers_m5376FA1EA20DEC72CE47369DD8CD407869B0A2F7_AdjustorThunk (void);
extern void MarkerList__ctor_m2D55037070301E459BF9CAB83756B81183C6A6B8_AdjustorThunk (void);
extern void MarkerList_Add_m401FA50EA0C98293DD36147CFA9DC8F637636128_AdjustorThunk (void);
extern void MarkerList_Remove_m986C1ECBCA1A1DE79FCD97D7D44EC91C19D79F64_AdjustorThunk (void);
extern void MarkerList_Remove_mF1626830F54C37B15C3DBEB6044B92CBF6439790_AdjustorThunk (void);
extern void MarkerList_Clear_m260B8EDEF14AC94F4BE5F10018F2215BF11927E7_AdjustorThunk (void);
extern void MarkerList_Contains_m97FF1ADAEFBD92A7F3E66B0A7C85950224B62989_AdjustorThunk (void);
extern void MarkerList_GetMarkers_mC440918CEEC3E0A8E872510EDEAEA0FB8832E680_AdjustorThunk (void);
extern void MarkerList_get_Count_mC37C47376A0DCC203DBD965E961A923F06E0082B_AdjustorThunk (void);
extern void MarkerList_get_Item_m76D3839ACDBAB9F00DFD9CE68C63DE26050961FE_AdjustorThunk (void);
extern void MarkerList_GetRawMarkerList_m272F6FFA3A887E15FDEFA8AAC07F1A6A0BD54A0D_AdjustorThunk (void);
extern void MarkerList_CreateMarker_m404F50BDABD541CF30E2F03DC6C22F917E02130B_AdjustorThunk (void);
extern void MarkerList_HasNotifications_mF2F94CCD1C286DB7264738F287486890D2497124_AdjustorThunk (void);
extern void MarkerList_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m6E97ABA4A5ED5881C2FC7F356FCAF68FCC45B710_AdjustorThunk (void);
extern void MarkerList_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m1175CE30FB38D95504747F73994F2C86DC0E8928_AdjustorThunk (void);
extern void MarkerList_BuildCache_m8EFF6B73A205DC746231385CAC726A74A2B485F5_AdjustorThunk (void);
extern void NotificationEntry_get_triggerInEditor_m0F467B8FC45EDED62344F2EB66D91998C330FC7A_AdjustorThunk (void);
extern void NotificationEntry_get_prewarm_mDF7D568CD5A14E53E9E11DAF6AD33F0FE7ED29DE_AdjustorThunk (void);
extern void NotificationEntry_get_triggerOnce_m40147F02406594EFEA328E29EDC9C3110E9A7654_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[34] = 
{
	{ 0x060001B0, TransientBuildData_Clear_mA1B052EF3A05C723008A58559E4C77A989ED2FD3_AdjustorThunk },
	{ 0x0600020E, DiscreteTime__ctor_mA6613BBA0DEB0A0BCCED50838378D9D9971B9F62_AdjustorThunk },
	{ 0x0600020F, DiscreteTime__ctor_mF5CD806CC7273F8C5CC80053A78D09604DED0491_AdjustorThunk },
	{ 0x06000210, DiscreteTime__ctor_mF858F11921F841F599EF51C7975430BEA8EC3B52_AdjustorThunk },
	{ 0x06000211, DiscreteTime__ctor_m7D7C3098B16F74497C5FC907B364C50A667EB2C8_AdjustorThunk },
	{ 0x06000212, DiscreteTime__ctor_mCD4A3BE5EB3142C4247190F7896378B68F428F69_AdjustorThunk },
	{ 0x06000213, DiscreteTime__ctor_m5C84429A52611671EE6F2D7BCD1399335D8242F5_AdjustorThunk },
	{ 0x06000214, DiscreteTime_OneTickBefore_m06F6D6A227D8356FCD3283CD59571DFA12943D1A_AdjustorThunk },
	{ 0x06000215, DiscreteTime_OneTickAfter_mD2C58DF00885E44EA0D7AF8C9F56C14F77E53EB7_AdjustorThunk },
	{ 0x06000216, DiscreteTime_GetTick_m52C7EE5F63087831CF4B21EF76A00655181349AA_AdjustorThunk },
	{ 0x06000218, DiscreteTime_CompareTo_m9A2C892A6E4097A5FBC0C2D5D666E01CE12427EA_AdjustorThunk },
	{ 0x06000219, DiscreteTime_Equals_m3D269E4164E6E705E6DD19B557F9DBFC2CB226FA_AdjustorThunk },
	{ 0x0600021A, DiscreteTime_Equals_m42B27C6DDF993E36A0C3D20B708986277AE6B7FD_AdjustorThunk },
	{ 0x0600022F, DiscreteTime_ToString_mFE5EEC6255B3AA617F692DA9EB7DF4A5AD71FD83_AdjustorThunk },
	{ 0x06000230, DiscreteTime_GetHashCode_m59E889384FD1F78DC2A1220C228EAB164F8995CE_AdjustorThunk },
	{ 0x0600027C, MarkerList_get_markers_m5376FA1EA20DEC72CE47369DD8CD407869B0A2F7_AdjustorThunk },
	{ 0x0600027D, MarkerList__ctor_m2D55037070301E459BF9CAB83756B81183C6A6B8_AdjustorThunk },
	{ 0x0600027E, MarkerList_Add_m401FA50EA0C98293DD36147CFA9DC8F637636128_AdjustorThunk },
	{ 0x0600027F, MarkerList_Remove_m986C1ECBCA1A1DE79FCD97D7D44EC91C19D79F64_AdjustorThunk },
	{ 0x06000280, MarkerList_Remove_mF1626830F54C37B15C3DBEB6044B92CBF6439790_AdjustorThunk },
	{ 0x06000281, MarkerList_Clear_m260B8EDEF14AC94F4BE5F10018F2215BF11927E7_AdjustorThunk },
	{ 0x06000282, MarkerList_Contains_m97FF1ADAEFBD92A7F3E66B0A7C85950224B62989_AdjustorThunk },
	{ 0x06000283, MarkerList_GetMarkers_mC440918CEEC3E0A8E872510EDEAEA0FB8832E680_AdjustorThunk },
	{ 0x06000284, MarkerList_get_Count_mC37C47376A0DCC203DBD965E961A923F06E0082B_AdjustorThunk },
	{ 0x06000285, MarkerList_get_Item_m76D3839ACDBAB9F00DFD9CE68C63DE26050961FE_AdjustorThunk },
	{ 0x06000286, MarkerList_GetRawMarkerList_m272F6FFA3A887E15FDEFA8AAC07F1A6A0BD54A0D_AdjustorThunk },
	{ 0x06000287, MarkerList_CreateMarker_m404F50BDABD541CF30E2F03DC6C22F917E02130B_AdjustorThunk },
	{ 0x06000288, MarkerList_HasNotifications_mF2F94CCD1C286DB7264738F287486890D2497124_AdjustorThunk },
	{ 0x06000289, MarkerList_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m6E97ABA4A5ED5881C2FC7F356FCAF68FCC45B710_AdjustorThunk },
	{ 0x0600028A, MarkerList_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m1175CE30FB38D95504747F73994F2C86DC0E8928_AdjustorThunk },
	{ 0x0600028B, MarkerList_BuildCache_m8EFF6B73A205DC746231385CAC726A74A2B485F5_AdjustorThunk },
	{ 0x06000300, NotificationEntry_get_triggerInEditor_m0F467B8FC45EDED62344F2EB66D91998C330FC7A_AdjustorThunk },
	{ 0x06000301, NotificationEntry_get_prewarm_mDF7D568CD5A14E53E9E11DAF6AD33F0FE7ED29DE_AdjustorThunk },
	{ 0x06000302, NotificationEntry_get_triggerOnce_m40147F02406594EFEA328E29EDC9C3110E9A7654_AdjustorThunk },
};
static const int32_t s_InvokerIndices[876] = 
{
	7794,
	5887,
	4785,
	4814,
	1435,
	6037,
	5887,
	2118,
	6037,
	5818,
	5887,
	4785,
	1221,
	6037,
	2708,
	4809,
	6037,
	4710,
	6037,
	1436,
	6037,
	6028,
	4909,
	5931,
	4825,
	6028,
	4909,
	5818,
	4715,
	5887,
	4785,
	5818,
	4715,
	5818,
	4715,
	5887,
	4785,
	5818,
	5887,
	4785,
	5913,
	4809,
	5840,
	5913,
	2118,
	6272,
	7897,
	8561,
	5887,
	6037,
	2708,
	8564,
	6037,
	6037,
	4785,
	6037,
	9162,
	8973,
	4785,
	6037,
	5818,
	5919,
	6037,
	5913,
	5913,
	5913,
	4710,
	6037,
	6037,
	7895,
	8062,
	9162,
	6028,
	4909,
	5931,
	4825,
	6028,
	4909,
	5818,
	4715,
	5887,
	4785,
	5887,
	4785,
	5913,
	4809,
	5818,
	4715,
	5913,
	4809,
	5818,
	4715,
	5818,
	5913,
	5818,
	6028,
	4909,
	5931,
	4825,
	6028,
	4909,
	5818,
	4715,
	5840,
	4741,
	5887,
	4785,
	5887,
	4785,
	5887,
	4785,
	6037,
	4254,
	4809,
	4254,
	4809,
	5887,
	6037,
	390,
	1221,
	1222,
	5887,
	1438,
	816,
	1570,
	8561,
	3413,
	4254,
	7294,
	811,
	814,
	2192,
	2192,
	2708,
	2708,
	4809,
	1869,
	1609,
	4254,
	5818,
	8149,
	6028,
	4909,
	5931,
	4825,
	6028,
	4909,
	5887,
	4785,
	5887,
	4785,
	4785,
	6037,
	9162,
	8973,
	8973,
	8973,
	4785,
	6037,
	5818,
	5919,
	6037,
	5913,
	5913,
	5913,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6037,
	4809,
	5818,
	5818,
	5840,
	4741,
	5840,
	4741,
	5840,
	4741,
	5840,
	5840,
	4741,
	5913,
	4809,
	5840,
	5913,
	4809,
	5913,
	5818,
	5913,
	4809,
	5913,
	5913,
	5913,
	4809,
	5913,
	4809,
	5913,
	4809,
	5840,
	4741,
	5840,
	4741,
	5840,
	5840,
	5840,
	4741,
	5840,
	4741,
	5887,
	4785,
	5887,
	4785,
	5818,
	5818,
	5913,
	4809,
	5968,
	5840,
	5913,
	4809,
	5840,
	5840,
	5968,
	5818,
	4715,
	5913,
	5887,
	5887,
	4349,
	4349,
	9116,
	9116,
	3697,
	3697,
	3697,
	5913,
	8017,
	5887,
	4785,
	5887,
	4785,
	4741,
	4741,
	3346,
	3346,
	3346,
	5840,
	5840,
	7371,
	4809,
	6037,
	6037,
	5913,
	6037,
	8017,
	2219,
	9162,
	8973,
	6037,
	5913,
	5840,
	5840,
	4741,
	5887,
	4785,
	5913,
	5887,
	5887,
	5887,
	6037,
	4247,
	5913,
	4247,
	5913,
	8610,
	6037,
	6037,
	5913,
	5913,
	5913,
	4809,
	4809,
	2118,
	6037,
	6037,
	6037,
	2708,
	6037,
	6037,
	6037,
	5839,
	8357,
	1177,
	0,
	0,
	0,
	3413,
	3413,
	4809,
	1177,
	4809,
	4809,
	6037,
	5968,
	4856,
	5840,
	4741,
	4785,
	5818,
	4715,
	6037,
	9162,
	4785,
	6037,
	5818,
	6037,
	6037,
	5919,
	6037,
	5913,
	5913,
	5913,
	6037,
	6037,
	4785,
	6037,
	6037,
	6037,
	8973,
	8973,
	8973,
	8973,
	5840,
	5840,
	5840,
	5818,
	4715,
	5818,
	5913,
	5913,
	4809,
	5913,
	5913,
	5818,
	5818,
	5818,
	5818,
	5913,
	5913,
	5913,
	4809,
	5913,
	4809,
	5913,
	5913,
	5913,
	5913,
	5913,
	5818,
	4715,
	5818,
	5818,
	6037,
	4809,
	1221,
	2118,
	5913,
	0,
	3413,
	2085,
	0,
	3413,
	5913,
	5887,
	4247,
	4254,
	4254,
	4254,
	4254,
	4254,
	5913,
	6037,
	4809,
	3413,
	5887,
	4809,
	815,
	813,
	812,
	4809,
	4809,
	1222,
	1416,
	6037,
	6037,
	6037,
	6037,
	5913,
	4809,
	4809,
	3413,
	4809,
	2192,
	2192,
	2708,
	4254,
	3413,
	4809,
	6037,
	5887,
	1222,
	6037,
	5840,
	5818,
	5818,
	5818,
	6037,
	5887,
	5887,
	8669,
	5818,
	5818,
	5818,
	6037,
	9162,
	9171,
	6037,
	4785,
	6037,
	5818,
	5919,
	6037,
	5913,
	5913,
	5913,
	9162,
	6037,
	1872,
	4809,
	5820,
	1449,
	6037,
	2733,
	6037,
	5968,
	4856,
	5913,
	4809,
	5818,
	4715,
	5840,
	5913,
	2118,
	5887,
	6037,
	4785,
	6037,
	5818,
	5919,
	6037,
	5913,
	5913,
	5913,
	4254,
	812,
	5913,
	6037,
	6037,
	4785,
	6037,
	5818,
	5919,
	6037,
	5913,
	5913,
	5913,
	8564,
	8564,
	8564,
	8564,
	8564,
	7895,
	7895,
	5818,
	4715,
	5818,
	4715,
	6037,
	5840,
	5887,
	2118,
	8184,
	1422,
	1422,
	962,
	7747,
	7242,
	0,
	8761,
	2708,
	4254,
	7742,
	8373,
	2708,
	8373,
	8373,
	7742,
	8373,
	6037,
	9162,
	4785,
	6037,
	5818,
	5913,
	6037,
	5913,
	5913,
	5913,
	6037,
	9092,
	4740,
	4786,
	4741,
	4856,
	4785,
	2427,
	5839,
	5839,
	5888,
	8604,
	3995,
	3345,
	3413,
	8683,
	8691,
	8685,
	8613,
	8860,
	8609,
	8856,
	8682,
	8602,
	8605,
	8603,
	8604,
	7872,
	7872,
	7872,
	7872,
	7872,
	7872,
	8014,
	8014,
	5913,
	5887,
	8014,
	8014,
	8610,
	8865,
	8683,
	9162,
	4814,
	5888,
	5888,
	4715,
	2220,
	1310,
	9162,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	5840,
	5840,
	1421,
	1421,
	5913,
	5918,
	5918,
	4715,
	4741,
	4741,
	2220,
	1310,
	0,
	0,
	5888,
	5888,
	6037,
	0,
	0,
	5887,
	4785,
	0,
	0,
	0,
	6037,
	5840,
	5840,
	4741,
	5913,
	5918,
	5918,
	454,
	454,
	4715,
	2220,
	1310,
	0,
	0,
	0,
	0,
	0,
	5913,
	4809,
	5840,
	4741,
	4809,
	4809,
	6037,
	5913,
	4785,
	4809,
	3413,
	1068,
	6037,
	3413,
	5913,
	5887,
	4247,
	5913,
	1169,
	5818,
	6037,
	6037,
	6037,
	5913,
	6037,
	6037,
	8973,
	8973,
	6037,
	6037,
	5818,
	4715,
	5818,
	4715,
	5913,
	4809,
	5930,
	5886,
	6037,
	1437,
	2708,
	3995,
	4809,
	5913,
	4254,
	5887,
	2471,
	4785,
	2471,
	4247,
	4247,
	6037,
	6037,
	1601,
	2708,
	4785,
	4809,
	5913,
	5913,
	6037,
	6037,
	8761,
	8373,
	5818,
	5913,
	6037,
	0,
	7278,
	2733,
	2733,
	1435,
	4814,
	4814,
	6037,
	5840,
	5913,
	4814,
	4814,
	4814,
	4814,
	2733,
	2733,
	2733,
	1435,
	2118,
	6037,
	7795,
	4814,
	2733,
	2733,
	2733,
	1435,
	4741,
	2734,
	2734,
	1625,
	3419,
	4814,
	6037,
	0,
	0,
	0,
	7280,
	5913,
	4809,
	2720,
	8378,
	2733,
	2733,
	2733,
	2755,
	6037,
	7281,
	5913,
	2088,
	4814,
	2733,
	2733,
	8973,
	6037,
	7797,
	4809,
	2733,
	2733,
	2733,
	6037,
	4814,
	7282,
	1312,
	4814,
	2733,
	2733,
	6037,
	6833,
	405,
	4814,
	7763,
	8960,
	6037,
	5818,
	5818,
	5818,
	9162,
	6037,
	2015,
	4809,
	6037,
	4785,
	4809,
	2692,
	6037,
	4809,
	2702,
	2702,
	6037,
	5887,
	4785,
	6037,
	6037,
	4809,
	4809,
	0,
	0,
	6463,
	234,
	461,
	460,
	2690,
	2690,
	391,
	2733,
	2733,
	961,
	9162,
	6037,
	9162,
	8973,
	8761,
	9162,
	9162,
	6037,
	1872,
	8062,
	7405,
	6866,
	6509,
	6374,
	6299,
	8669,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6776,
	8564,
	8373,
	7917,
	7222,
	9162,
	8149,
	8373,
	8373,
	7504,
	7917,
	6037,
	3413,
	6037,
	3413,
	7742,
	8373,
	8373,
	8373,
	8761,
	8964,
	8060,
	8017,
	8018,
	8017,
	7873,
	8017,
	7320,
	8017,
	7469,
	7469,
	7374,
	7374,
	8615,
	8149,
	8627,
	8628,
	7877,
	9162,
	9162,
	6037,
	3516,
	3516,
	3516,
	8863,
};
static const Il2CppTokenRangePair s_rgctxIndices[7] = 
{
	{ 0x02000039, { 13, 19 } },
	{ 0x0600012B, { 0, 2 } },
	{ 0x0600012C, { 2, 2 } },
	{ 0x0600012D, { 4, 2 } },
	{ 0x06000175, { 6, 1 } },
	{ 0x06000178, { 7, 2 } },
	{ 0x060001F7, { 9, 4 } },
};
extern const uint32_t g_rgctx_T_t4F48D32C1E4625B636F11ED1885363AA741A5046;
extern const uint32_t g_rgctx_T_t4F48D32C1E4625B636F11ED1885363AA741A5046;
extern const uint32_t g_rgctx_T_t732DF6F42B3C639217FA152C6E8D4FBDA2B3002E;
extern const uint32_t g_rgctx_T_t732DF6F42B3C639217FA152C6E8D4FBDA2B3002E;
extern const uint32_t g_rgctx_T_t480A926730D3A28EF9ECE9D8D79A8DB512EB7754;
extern const uint32_t g_rgctx_T_t480A926730D3A28EF9ECE9D8D79A8DB512EB7754;
extern const uint32_t g_rgctx_T_t9832385973A3EB9D96FE5E2790FF45442C77D6A9;
extern const uint32_t g_rgctx_T_tD2104479CD2F140BE83C8868F7EE97C2A9E53995;
extern const uint32_t g_rgctx_T_tD2104479CD2F140BE83C8868F7EE97C2A9E53995;
extern const uint32_t g_rgctx_List_1_tAD9DBB7BDE3B42EC3C56D93670D2D6CAEEDF9578;
extern const uint32_t g_rgctx_List_1__ctor_m1E34F738C6B5185DA9FF6D9A4248F95E4789C49F;
extern const uint32_t g_rgctx_GameObject_GetComponentsInChildren_TisT_t9134924AD4E7BC6F3582AEC4CA36B7C5AC61FA66_m5232098D970480BC9A464746926D056B36E9795F;
extern const uint32_t g_rgctx_GameObject_GetComponents_TisT_t9134924AD4E7BC6F3582AEC4CA36B7C5AC61FA66_m308523092FF6ED5A1656DAB4CFEB1A612823C426;
extern const uint32_t g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B_IInterval_get_intervalStart_m75AB28EF8036396E98453DBAA806435F99F35B40;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B_IInterval_get_intervalEnd_m24B1F744B434ABA16174C6B9DDCF352DD8343CBA;
extern const uint32_t g_rgctx_List_1_tE2C44929BD9BB68129C86E5DDC9FBAFC630E57D6;
extern const uint32_t g_rgctx_List_1_Add_m83B97DD971361719E1C334A5189B347BE7945151;
extern const uint32_t g_rgctx_IntervalTree_1_set_dirty_m8BDA66BB88FD28A517136C7F8A7213BACD2403D3;
extern const uint32_t g_rgctx_List_1_get_Count_m41B978D0FD028DC1663D3154429E07AE7839E790;
extern const uint32_t g_rgctx_IntervalTree_1_get_dirty_m9722685FB044E0DD9DCD6B82CF77C6BB1D34EFF3;
extern const uint32_t g_rgctx_IntervalTree_1_Rebuild_mF3F832F8E3FE3A7C670154D8EC3B2297406B6706;
extern const uint32_t g_rgctx_IntervalTree_1_Query_mD8C8CFF57210BA3DF42738B21D2D0A200CE51FD9;
extern const uint32_t g_rgctx_IntervalTree_1_QueryRange_m84E84E5B3AF621D082ED6B2037174FA5ADAA654C;
extern const uint32_t g_rgctx_List_1_get_Item_mF18FAA34A7F4F2F4481999AC4C521EFCD33F3CDD;
extern const uint32_t g_rgctx_List_1_set_Item_m919FD10B9B1EB10EA65B1C6562ADDF7C508E5223;
extern const uint32_t g_rgctx_List_1_tB14B3D5B8D94A8EB8EA9575345F99EFAD2A36B41;
extern const uint32_t g_rgctx_List_1_Add_mDC5EF750DF195A5F845D3FAC5EDEE73E2D63E6F1;
extern const uint32_t g_rgctx_List_1_get_Capacity_m1C43465AC495490CB38ED647CE049FAB7537A15B;
extern const uint32_t g_rgctx_IntervalTree_1_Rebuild_mF3EB594B6C2E8D62DB87A40C0B7BBA84795010D6;
extern const uint32_t g_rgctx_List_1_Clear_m7A85665AFF0C6EA2D8F53E78A18E025173414D8B;
extern const uint32_t g_rgctx_List_1__ctor_m9D0FAC1D82CC29F417002AF02CF7D49AF48F5E3D;
static const Il2CppRGCTXDefinition s_rgctxValues[32] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t4F48D32C1E4625B636F11ED1885363AA741A5046 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4F48D32C1E4625B636F11ED1885363AA741A5046 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t732DF6F42B3C639217FA152C6E8D4FBDA2B3002E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t732DF6F42B3C639217FA152C6E8D4FBDA2B3002E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t480A926730D3A28EF9ECE9D8D79A8DB512EB7754 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t480A926730D3A28EF9ECE9D8D79A8DB512EB7754 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t9832385973A3EB9D96FE5E2790FF45442C77D6A9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tD2104479CD2F140BE83C8868F7EE97C2A9E53995 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD2104479CD2F140BE83C8868F7EE97C2A9E53995 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tAD9DBB7BDE3B42EC3C56D93670D2D6CAEEDF9578 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m1E34F738C6B5185DA9FF6D9A4248F95E4789C49F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentsInChildren_TisT_t9134924AD4E7BC6F3582AEC4CA36B7C5AC61FA66_m5232098D970480BC9A464746926D056B36E9795F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponents_TisT_t9134924AD4E7BC6F3582AEC4CA36B7C5AC61FA66_m308523092FF6ED5A1656DAB4CFEB1A612823C426 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B_IInterval_get_intervalStart_m75AB28EF8036396E98453DBAA806435F99F35B40 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t053A107E867E3D8222FB71CF0C88EBAB22227F7B_IInterval_get_intervalEnd_m24B1F744B434ABA16174C6B9DDCF352DD8343CBA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tE2C44929BD9BB68129C86E5DDC9FBAFC630E57D6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m83B97DD971361719E1C334A5189B347BE7945151 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_set_dirty_m8BDA66BB88FD28A517136C7F8A7213BACD2403D3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m41B978D0FD028DC1663D3154429E07AE7839E790 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_get_dirty_m9722685FB044E0DD9DCD6B82CF77C6BB1D34EFF3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_Rebuild_mF3F832F8E3FE3A7C670154D8EC3B2297406B6706 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_Query_mD8C8CFF57210BA3DF42738B21D2D0A200CE51FD9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_QueryRange_m84E84E5B3AF621D082ED6B2037174FA5ADAA654C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mF18FAA34A7F4F2F4481999AC4C521EFCD33F3CDD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Item_m919FD10B9B1EB10EA65B1C6562ADDF7C508E5223 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tB14B3D5B8D94A8EB8EA9575345F99EFAD2A36B41 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_mDC5EF750DF195A5F845D3FAC5EDEE73E2D63E6F1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Capacity_m1C43465AC495490CB38ED647CE049FAB7537A15B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IntervalTree_1_Rebuild_mF3EB594B6C2E8D62DB87A40C0B7BBA84795010D6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m7A85665AFF0C6EA2D8F53E78A18E025173414D8B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m9D0FAC1D82CC29F417002AF02CF7D49AF48F5E3D },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Timeline_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Timeline_CodeGenModule = 
{
	"Unity.Timeline.dll",
	876,
	s_methodPointers,
	34,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	7,
	s_rgctxIndices,
	32,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
