﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 UnityEngine.Playables.PlayState UnityEngine.Playables.PlayableDirector::get_state()
extern void PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C (void);
// 0x00000002 UnityEngine.Playables.DirectorWrapMode UnityEngine.Playables.PlayableDirector::get_extrapolationMode()
extern void PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD (void);
// 0x00000003 UnityEngine.Playables.PlayableAsset UnityEngine.Playables.PlayableDirector::get_playableAsset()
extern void PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C (void);
// 0x00000004 UnityEngine.Playables.PlayableGraph UnityEngine.Playables.PlayableDirector::get_playableGraph()
extern void PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F (void);
// 0x00000005 System.Void UnityEngine.Playables.PlayableDirector::Play(UnityEngine.Playables.FrameRate)
extern void PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B (void);
// 0x00000006 System.Void UnityEngine.Playables.PlayableDirector::set_time(System.Double)
extern void PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1 (void);
// 0x00000007 System.Double UnityEngine.Playables.PlayableDirector::get_time()
extern void PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357 (void);
// 0x00000008 System.Void UnityEngine.Playables.PlayableDirector::Evaluate()
extern void PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842 (void);
// 0x00000009 System.Void UnityEngine.Playables.PlayableDirector::PlayOnFrame(UnityEngine.Playables.FrameRate)
extern void PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5 (void);
// 0x0000000A System.Void UnityEngine.Playables.PlayableDirector::Play()
extern void PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988 (void);
// 0x0000000B System.Void UnityEngine.Playables.PlayableDirector::Stop()
extern void PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E (void);
// 0x0000000C System.Void UnityEngine.Playables.PlayableDirector::Pause()
extern void PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104 (void);
// 0x0000000D UnityEngine.Object UnityEngine.Playables.PlayableDirector::GetReferenceValue(UnityEngine.PropertyName,System.Boolean&)
extern void PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8 (void);
// 0x0000000E UnityEngine.Object UnityEngine.Playables.PlayableDirector::GetGenericBinding(UnityEngine.Object)
extern void PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020 (void);
// 0x0000000F UnityEngine.Playables.PlayState UnityEngine.Playables.PlayableDirector::GetPlayState()
extern void PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE (void);
// 0x00000010 UnityEngine.Playables.DirectorWrapMode UnityEngine.Playables.PlayableDirector::GetWrapMode()
extern void PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A (void);
// 0x00000011 UnityEngine.Playables.PlayableGraph UnityEngine.Playables.PlayableDirector::GetGraphHandle()
extern void PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896 (void);
// 0x00000012 UnityEngine.ScriptableObject UnityEngine.Playables.PlayableDirector::Internal_GetPlayableAsset()
extern void PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24 (void);
// 0x00000013 System.Void UnityEngine.Playables.PlayableDirector::SendOnPlayableDirectorPlay()
extern void PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0 (void);
// 0x00000014 System.Void UnityEngine.Playables.PlayableDirector::SendOnPlayableDirectorPause()
extern void PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1 (void);
// 0x00000015 System.Void UnityEngine.Playables.PlayableDirector::SendOnPlayableDirectorStop()
extern void PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7 (void);
// 0x00000016 System.Void UnityEngine.Playables.PlayableDirector::PlayOnFrame_Injected(UnityEngine.Playables.FrameRate&)
extern void PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F (void);
// 0x00000017 UnityEngine.Object UnityEngine.Playables.PlayableDirector::GetReferenceValue_Injected(UnityEngine.PropertyName&,System.Boolean&)
extern void PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106 (void);
// 0x00000018 System.Void UnityEngine.Playables.PlayableDirector::GetGraphHandle_Injected(UnityEngine.Playables.PlayableGraph&)
extern void PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC (void);
static Il2CppMethodPointer s_methodPointers[24] = 
{
	PlayableDirector_get_state_m49AFA6EADEACC4A020AB14F4FA6F32FC1925A93C,
	PlayableDirector_get_extrapolationMode_m942C1A4A49D9D81DF54B742A0EFE22ED6D6BCDDD,
	PlayableDirector_get_playableAsset_m02BE3315FD9BF897F49AE020F3FE4278529FEB7C,
	PlayableDirector_get_playableGraph_m57400FA3DC53BEB44ECEEFCA0A07EC7B8DAEAB7F,
	PlayableDirector_Play_m6816CC7327CAE3BDA0B6AB7A73EED4315D2DC57B,
	PlayableDirector_set_time_mCC149D4694C248ABAD39BE32912168655BD7A8D1,
	PlayableDirector_get_time_m97D770710A5150E8E72DE2A5677E37D59C4BE357,
	PlayableDirector_Evaluate_m642F91B545243B203F7517EF5F44F09FFD3C7842,
	PlayableDirector_PlayOnFrame_mBD1EEDB85731D65E97110798197A72D2079ED9D5,
	PlayableDirector_Play_m937BA3BFAA11918A42D9D7874C0668DDD4B40988,
	PlayableDirector_Stop_m60A3AA3874D92B4740A312ECA0E76210D04F207E,
	PlayableDirector_Pause_mC5749A3523008A3FD9E9E001999A88EE030FD104,
	PlayableDirector_GetReferenceValue_m635841386147673FFBBEF0CD9DA908337F3C97C8,
	PlayableDirector_GetGenericBinding_mEA8A86CEFAD08BEC596E06C3E1B1E0095E69D020,
	PlayableDirector_GetPlayState_m530EE60FE30CAAB5BCA57F96C93964A26DD254BE,
	PlayableDirector_GetWrapMode_m91F64F0166340F3C55911879B448B32C3271686A,
	PlayableDirector_GetGraphHandle_m71F1BC34DF71AAACDDC44ACC74FFD66200875896,
	PlayableDirector_Internal_GetPlayableAsset_m74CF2B5E24E39114ED3A256185175BCC2CF31F24,
	PlayableDirector_SendOnPlayableDirectorPlay_m7F75DBA4355DAA92F53AC337BB952069B63081A0,
	PlayableDirector_SendOnPlayableDirectorPause_m1B8EE7CBD23957C664AA417A9261194DFFFADFE1,
	PlayableDirector_SendOnPlayableDirectorStop_m4E9AEB579B8EA66ECC6FA9BE23BBF7973AB3EDD7,
	PlayableDirector_PlayOnFrame_Injected_mCEFF5F4072CEE5D6747E9D87FAB0F2E734432D0F,
	PlayableDirector_GetReferenceValue_Injected_mE46AABB378E696DF9A413D98D24E1AA0A312D106,
	PlayableDirector_GetGraphHandle_Injected_m87F152D8E0409BB86B61BBC264DF9D1017E80FCC,
};
static const int32_t s_InvokerIndices[24] = 
{
	5887,
	5887,
	5913,
	5920,
	4754,
	4741,
	5840,
	6037,
	4754,
	6037,
	6037,
	6037,
	2091,
	4254,
	5887,
	5887,
	5920,
	5913,
	6037,
	6037,
	6037,
	4707,
	2055,
	4707,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_DirectorModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_DirectorModule_CodeGenModule = 
{
	"UnityEngine.DirectorModule.dll",
	24,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
